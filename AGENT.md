# Mirraw Desktop - Rails 4.2.5 E-commerce Platform

## Commands
- **Test**: `bundle exec rspec` (single: `bundle exec rspec spec/path/to/file_spec.rb`)
- **Build**: `bundle exec rake assets:precompile` 
- **Server**: `rails s` (development server)
- **Console**: `rails c` 
- **DB**: `rake db:migrate`, `rake db:seed`
- **Lint**: `rubocop` (if configured)
- **Solr**: `bundle exec rake sunspot:solr:batch_index` (search indexing)

## Architecture
- **Framework**: Rails 4.2.5, Ruby 2.2.4
- **Database**: PostgreSQL 
- **Search**: Apache Solr (sunspot)
- **Jobs**: Sidekiq (background processing)
- **Cache**: Memcache (dalli), action caching
- **Assets**: AWS S3 + CloudFront (paperclip)
- **Payments**: PayU, Razorpay, Stripe, PayPal
- **Shipping**: Multiple courier APIs (BlueDart, DTDC, Gati, etc.)

## Code Style
- **Models**: Use concerns for shared behavior, protected_attributes for mass assignment
- **Controllers**: Include helper modules, use caches_action for performance
- **Views**: HAML templates, avoid logic in views
- **Testing**: RSpec 3.0, FactoryGirl for fixtures
- **Naming**: snake_case for files/methods, CamelCase for classes
- **Security**: Strong parameters, sanitize user inputs, validate against log4j patterns
