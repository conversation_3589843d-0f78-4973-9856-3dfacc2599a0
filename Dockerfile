# Base image
FROM ruby:2.2.4

# Setup environment variables that will be available to the instance
ENV APP_HOME /production

LABEL maintainer="<EMAIL>"

RUN rm /etc/apt/sources.list
RUN echo "deb http://archive.debian.org/debian/ jessie main" | tee -a /etc/apt/sources.list
RUN echo "deb-src http://archive.debian.org/debian/ jessie main" | tee -a /etc/apt/sources.list
RUN echo "Acquire::Check-Valid-Until false;" | tee -a /etc/apt/apt.conf.d/10-nocheckvalid
RUN echo 'Package: *\nPin: origin "archive.debian.org"\nPin-Priority: 500' | tee -a /etc/apt/preferences.d/10-archive-pin

# Installation of dependencies
RUN apt-get update -qq \
  && apt-get install -y --force-yes\
      # Needed for certain gems
    build-essential \
    postgresql-client \
    ca-certificates \
    qt4-dev-tools \
    libqt4-dev \
    libqt4-core \
    libqt4-gui \
         # Needed for postgres gem
    libpq-dev \
         # Needed for asset compilation
    nodejs \
    # the following are taken from paperclip-dockerfile
    linux-headers-amd64 \
    nasm \
    vim \
    libturbojpeg1 libturbojpeg1-dev \
    # The following are used to trim down the size of the image by removing unneeded data
  && apt-get clean autoclean \
  && apt-get autoremove -y \
  && rm -rf \
    /var/lib/apt \
    /var/lib/dpkg \
    /var/lib/cache \
    /var/lib/log
# Add our Gemfile
# and install gems

# redis-cli installed as we've moved mweb's cache policy to redis
RUN cd /tmp &&\
  curl http://download.redis.io/redis-stable.tar.gz | tar xz &&\
  make -C redis-stable &&\
  cp redis-stable/src/redis-cli /usr/local/bin &&\
  rm -rf /tmp/redis-stable



#ADD Gemfile* $APP_HOME/
#RUN bundle install
COPY Gemfile* /tmp/
WORKDIR /tmp
RUN gem install bundler -v "$(grep -A 1 "BUNDLED WITH" Gemfile.lock | tail -n 1)"
RUN bundle install --without development test

# Create a directory for our application
# and set it as the working directory
RUN mkdir $APP_HOME
WORKDIR $APP_HOME

# Copy over our application code
COPY . $APP_HOME

COPY ./DigiCertSHA2ExtendedValidationServerCA.crt /usr/local/share/ca-certificates/DigiCertSHA2ExtendedValidationServerCA.crt
COPY ./DigiCertGlobalRootG2.crt /usr/local/share/ca-certificates/DigiCertGlobalRootG2.crt
COPY ./DigiCertHighAssuranceEVRootCA.crt.pem /usr/local/share/ca-certificates/DigiCertHighAssuranceEVRootCA.crt

RUN update-ca-certificates

# RUN RAILS_ENV=production bundle exec rake tmp:cache:clear

RUN export $(cat .env.alpine | xargs) && RAILS_ENV=production bundle exec rake assets:precompile --trace

# Expose port 3000 to the Docker host, so we can access it
# from the outside.
#EXPOSE 3000
COPY ./bin/docker-entrypoint.sh /bin/docker-entrypoint.sh
RUN chmod +x /bin/docker-entrypoint.sh
ENTRYPOINT ["/bin/docker-entrypoint.sh"]


# From paperclip dockerfile
RUN mkdir -p /tmp/build
WORKDIR /tmp/build
ADD https://github.com/webmproject/libwebp/archive/v0.6.0.tar.gz /tmp/build/v0.6.0.tar.gz
RUN tar xvf v0.6.0.tar.gz
RUN cd libwebp-0.6.0 && sh autogen.sh && ./configure --disable-static && make && make install
ADD https://mirraw.s3.amazonaws.com/libraries/image-magick/ImageMagick-6.9.2-10.tar.gz /tmp/build/ImageMagick-6.9.2-10.tar.gz
RUN tar xvf ImageMagick-6.9.2-10.tar.gz
RUN cd ImageMagick-6.9.2-10 && ./configure --disable-static --with-webp --with-quantum-depth=8 && make && make install
RUN ldconfig /usr/local/lib
WORKDIR $APP_HOME
# RUN chmod +x bin/rake && mkdir log && ln -sf /dev/stderr log/admin.log





# Run our app
CMD ["bundle", "exec", "rails", "server", "-b", "0.0.0.0"]
