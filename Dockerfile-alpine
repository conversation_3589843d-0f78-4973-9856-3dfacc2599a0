# Base image
FROM ruby:2.2.4-alpine

# Setup environment variables that will be available to the instance
ENV APP_HOME /production

LABEL maintainer="<EMAIL>"

# Installation of dependencies
RUN apk update \
&& apk add --virtual build-dependencies \
&& apk add --no-cache build-base postgresql-dev curl-dev musl-dev nodejs bash git make gcc g++ pkgconfig libxml2-dev libxslt-dev tzdata
RUN apk add ca-certificates
RUN rm -rf /var/cache/apk/*

# Add our Gemfile and install gems
COPY Gemfile* /tmp/
WORKDIR /tmp
RUN gem install bundler -v "$(grep -A 1 "BUNDLED WITH" Gemfile.lock | tail -n 1)"
RUN bundle config build.nokogiri --use-system-libraries
RUN bundle config git.allow_insecure true && bundle install --without development test

# Create a directory for our application and set it as the working directory
RUN mkdir $APP_HOME
WORKDIR $APP_HOME

# Copy over our application code
COPY . $APP_HOME

COPY ./DigiCertSHA2ExtendedValidationServerCA.crt /usr/local/share/ca-certificates/DigiCertSHA2ExtendedValidationServerCA.crt
COPY ./DigiCertGlobalRootG2.crt /usr/local/share/ca-certificates/DigiCertGlobalRootG2.crt

RUN update-ca-certificates

RUN export $(cat .env.alpine | xargs) && RAILS_ENV=production bundle exec rake assets:precompile

#RUN bundle exec rake assets:precompile


# Expose port 3000 to the Docker host, so we can access it from the outside.
#EXPOSE 3000

COPY ./bin/docker-entrypoint.sh /bin/docker-entrypoint.sh
RUN chmod +x /bin/docker-entrypoint.sh
ENTRYPOINT ["/bin/docker-entrypoint.sh"]

# Run our app
CMD ["bundle", "exec", "rails", "server", "-b", "0.0.0.0"]
