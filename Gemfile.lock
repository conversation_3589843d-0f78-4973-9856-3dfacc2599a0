GIT
  remote: https://bitbucket.org/blacklife/sunspot.git
  revision: 3f2027e1955b3f5d5df4458e667b2505b8afa6d1
  branch: grouping_in_solr_with_master
  specs:
    sunspot (2.2.7)
      pr_geohash (~> 1.0)
      rsolr (>= 1.1.1, < 3)
    sunspot_rails (2.2.7)
      nokogiri
      rails (>= 3)
      sunspot (= 2.2.7)

GIT
  remote: https://<EMAIL>/blacklife/paypal-express.git
  revision: 1714cc618c453f8e3c86c519f575d1b80ee13af3
  branch: capture_invoice_number
  specs:
    paypal-express (0.8.1)
      activesupport (>= 2.3)
      attr_required (>= 0.0.5)
      rest-client

GIT
  remote: https://github.com/ShieldSquare/SS_Connector_RoR
  revision: 37b6edfd1f5ecdb7affd5a990bb84770d061a042
  branch: ror_rel_v2.1.0
  specs:
    ss2 (2.1.0)
      addressable
      httparty

GIT
  remote: https://github.com/guinex/razorpay-ruby.git
  revision: 334fbd3ec447e5ea4ec9a408dfc554e2780a6f71
  specs:
    razorpay (1.1.0)
      httparty (~> 0.8)

GIT
  remote: https://github.com/guinex/sunspot_index_queue.git
  revision: 38620268d3b4f1a116564189407a22aee80ea260
  specs:
    sunspot_index_queue (1.1.4)
      sunspot (>= 1.1.0)

GIT
  remote: https://github.com/mimemagicrb/mimemagic.git
  revision: a4b038c6c1b9d76dac33d5711d28aaa9b4c42c66
  ref: a4b038c6c1b9d76dac33d5711d28aaa9b4c42c66
  specs:
    mimemagic (0.3.0)

GIT
  remote: https://github.com/ryanb/nested_form.git
  revision: 78ad0469e681f159072e04c9c57d8722c9826295
  specs:
    nested_form (0.3.2)

GIT
  remote: https://gitlab.com/mirraw-gems/paypal-ruby-sdk.git
  revision: 3e943671ead664ecf690772d376884e1d41ffd1f
  specs:
    paypal-sdk-rest (1.3.3)
      multi_json (~> 1.0)
      uuidtools (~> 2.1)
      xml-simple

GIT
  remote: https://<EMAIL>/blacklife/aws-sdk-ruby.git
  revision: a15b3294e9b0c57da03d972562d5ea8635f216c0
  branch: aws_sdk_1.8.1
  specs:
    aws-sdk (1.8.1)
      httparty (~> 0.7)
      json (~> 1.4)
      nokogiri (>= 1.4.4)
      uuidtools (~> 2.1)

GIT
  remote: https://<EMAIL>/blacklife/bluedart.git
  revision: eb06270895e9adffa5b203a8c5cd9807d1ae8348
  branch: bluedart_https_bug
  specs:
    bluedart (0.2.2)
      httparty
      nokogiri (>= 1.5.11)
      nori (>= 2.4.0)

GIT
  remote: https://<EMAIL>/blacklife/ruby-measurement.git
  revision: 3341b915a5218c300ee25ec09cbcd27294143727
  specs:
    ruby-measurement (1.2.3)

GIT
  remote: https://<EMAIL>/blacklife/ship_delight.git
  revision: 9798748b7733656af79ee9bae0f961ef76f19753
  branch: tracking_url_changed
  specs:
    ship_delight_api (0.1.0)

GEM
  remote: http://rubygems.org/
  specs:
    Ascii85 (1.0.3)
    CFPropertyList (2.3.2)
    TableCSV (0.1.0)
      nokogiri (>= 1.5.11)
    actionmailer (4.2.5)
      actionpack (= 4.2.5)
      actionview (= 4.2.5)
      activejob (= 4.2.5)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 1.0, >= 1.0.5)
    actionpack (4.2.5)
      actionview (= 4.2.5)
      activesupport (= 4.2.5)
      rack (~> 1.6)
      rack-test (~> 0.6.2)
      rails-dom-testing (~> 1.0, >= 1.0.5)
      rails-html-sanitizer (~> 1.0, >= 1.0.2)
    actionpack-action_caching (1.2.0)
      actionpack (>= 4.0.0, < 6)
    actionpack-page_caching (1.1.0)
      actionpack (>= 4.0.0, < 6)
    actionview (4.2.5)
      activesupport (= 4.2.5)
      builder (~> 3.1)
      erubis (~> 2.7.0)
      rails-dom-testing (~> 1.0, >= 1.0.5)
      rails-html-sanitizer (~> 1.0, >= 1.0.2)
    activejob (4.2.5)
      activesupport (= 4.2.5)
      globalid (>= 0.3.0)
    activemodel (4.2.5)
      activesupport (= 4.2.5)
      builder (~> 3.1)
    activerecord (4.2.5)
      activemodel (= 4.2.5)
      activesupport (= 4.2.5)
      arel (~> 6.0)
    activerecord-deprecated_finders (1.0.4)
    activerecord-import (0.16.1)
      activerecord (>= 3.2)
    activesupport (4.2.5)
      i18n (~> 0.7)
      json (~> 1.7, >= 1.7.7)
      minitest (~> 5.1)
      thread_safe (~> 0.3, >= 0.3.4)
      tzinfo (~> 1.1)
    acts-as-taggable-on (3.5.0)
      activerecord (>= 3.2, < 5)
    acts_as_follower (0.2.1)
    addressable (2.6.0)
      public_suffix (>= 2.0.2, < 4.0)
    afm (0.2.2)
    ahoy_email (1.0.3)
      actionmailer (>= 4.2)
      addressable (>= 2.3.2)
      nokogiri
      safely_block (>= 0.1.1)
    airbrake (7.2.0)
      airbrake-ruby (~> 2.5)
    airbrake-ruby (2.8.1)
    akami (1.3.1)
      gyoku (>= 0.4.0)
      nokogiri
    annotate (2.7.1)
      activerecord (>= 3.2, < 6.0)
      rake (>= 10.4, < 12.0)
    ansi (1.5.0)
    arel (6.0.4)
    ast (2.3.0)
    attr_required (1.0.1)
    awesome_nested_set (3.1.3)
      activerecord (>= 4.0.0, < 5.2)
    awesome_print (1.7.0)
    aws-ses (0.6.0)
      builder
      mail (> 2.2.5)
      mime-types
      xml-simple
    barby (0.6.5)
    bcrypt (3.1.12)
    better_errors (2.1.1)
      coderay (>= 1.0.0)
      erubis (>= 2.6.6)
      rack (>= 0.9.0)
    binding_of_caller (0.7.2)
      debug_inspector (>= 0.0.1)
    bootstrap-sass (*******)
      sass (~> 3.2)
    bootstrap3-datetimepicker-rails (4.17.47)
      momentjs-rails (>= 2.8.1)
    bourbon (4.2.7)
      sass (~> 3.4)
      thor (~> 0.19)
    browser (2.2.0)
    bson (4.2.1)
    builder (3.2.4)
    bullet (5.6.0)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.10.0)
    byebug (9.0.5)
    callsite (0.0.11)
    cancancan (2.1.2)
    capybara (2.7.1)
      addressable
      mime-types (>= 1.16)
      nokogiri (>= 1.3.3)
      rack (>= 1.0.0)
      rack-test (>= 0.5.4)
      xpath (~> 2.0)
    capybara-webkit (1.11.1)
      capybara (>= 2.3.0, < 2.8.0)
      json
    carrierwave (1.2.1)
      activemodel (>= 4.0.0)
      activesupport (>= 4.0.0)
      mime-types (>= 1.16)
    chartkick (2.2.2)
    childprocess (0.5.9)
      ffi (~> 1.0, >= 1.0.11)
    chosen-rails (1.5.2)
      coffee-rails (>= 3.2)
      railties (>= 3.0)
      sass-rails (>= 3.2)
    chunky_png (1.3.7)
    ckeditor (4.2.4)
      cocaine
      orm_adapter (~> 0.5.0)
    climate_control (0.2.0)
    cocaine (0.5.8)
      climate_control (>= 0.0.3, < 1.0)
    code_analyzer (0.4.7)
      sexp_processor
    coderay (1.1.1)
    coffee-rails (4.2.2)
      coffee-script (>= 2.2.0)
      railties (>= 4.0.0)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    combine_pdf (0.2.32)
      ruby-rc4 (>= 0.1.5)
    concurrent-ruby (1.1.9)
    connection_pool (2.2.0)
    crass (1.0.6)
    curb (0.9.3)
    daemons (1.2.3)
    dalli (2.7.6)
    database_cleaner (1.5.3)
    debug_inspector (0.0.2)
    declarative (0.0.10)
    declarative-option (0.1.0)
    delayed_paperclip (2.10.0)
      paperclip (>= 3.3)
    devise (3.5.10)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 3.2.6, < 5)
      responders
      thread_safe (~> 0.1)
      warden (~> 1.2.3)
    devise-async (0.10.2)
      devise (>= 3.2, < 4.0)
    diff-lcs (1.2.5)
    docile (1.1.5)
    domain_name (0.5.20160615)
      unf (>= 0.0.5, < 1.0.0)
    e2mmap (0.1.0)
    errbase (0.1.1)
    erubis (2.7.0)
    et-orbi (1.2.7)
      tzinfo
    exception_notification (4.4.0)
      actionmailer (>= 4.0, < 7)
      activesupport (>= 4.0, < 7)
    excon (0.51.0)
    execjs (2.7.0)
    exifr (1.2.4)
    factory_girl (4.1.0)
      activesupport (>= 3.0.0)
    factory_girl_rails (4.1.0)
      factory_girl (~> 4.1.0)
      railties (>= 3.0.0)
    faker (1.6.5)
      i18n (~> 0.5)
    faraday (0.15.4)
      multipart-post (>= 1.2, < 3)
    fastimage (2.0.0)
      addressable (~> 2)
    fcm (0.0.7)
      faraday (= 0.15.4)
    ffi (1.9.14)
    fission (0.5.0)
      CFPropertyList (~> 2.2)
    fog (1.38.0)
      fog-aliyun (>= 0.1.0)
      fog-atmos
      fog-aws (>= 0.6.0)
      fog-brightbox (~> 0.4)
      fog-cloudatcost (~> 0.1.0)
      fog-core (~> 1.32)
      fog-dynect (~> 0.0.2)
      fog-ecloud (~> 0.1)
      fog-google (<= 0.1.0)
      fog-json
      fog-local
      fog-openstack
      fog-powerdns (>= 0.1.1)
      fog-profitbricks
      fog-rackspace
      fog-radosgw (>= 0.0.2)
      fog-riakcs
      fog-sakuracloud (>= 0.0.4)
      fog-serverlove
      fog-softlayer
      fog-storm_on_demand
      fog-terremark
      fog-vmfusion
      fog-voxel
      fog-vsphere (>= 0.4.0)
      fog-xenserver
      fog-xml (~> 0.1.1)
      ipaddress (~> 0.5)
    fog-aliyun (0.1.0)
      fog-core (~> 1.27)
      fog-json (~> 1.0)
      ipaddress (~> 0.8)
      xml-simple (~> 1.1)
    fog-atmos (0.1.0)
      fog-core
      fog-xml
    fog-aws (0.10.0)
      fog-core (~> 1.38)
      fog-json (~> 1.0)
      fog-xml (~> 0.1)
      ipaddress (~> 0.8)
    fog-brightbox (0.11.0)
      fog-core (~> 1.22)
      fog-json
      inflecto (~> 0.0.2)
    fog-cloudatcost (0.1.2)
      fog-core (~> 1.36)
      fog-json (~> 1.0)
      fog-xml (~> 0.1)
      ipaddress (~> 0.8)
    fog-core (1.42.0)
      builder
      excon (~> 0.49)
      formatador (~> 0.2)
    fog-dynect (0.0.3)
      fog-core
      fog-json
      fog-xml
    fog-ecloud (0.3.0)
      fog-core
      fog-xml
    fog-google (0.1.0)
      fog-core
      fog-json
      fog-xml
    fog-json (1.0.2)
      fog-core (~> 1.0)
      multi_json (~> 1.10)
    fog-local (0.3.0)
      fog-core (~> 1.27)
    fog-openstack (0.1.8)
      fog-core (>= 1.40)
      fog-json (>= 1.0)
      ipaddress (>= 0.8)
    fog-powerdns (0.1.1)
      fog-core (~> 1.27)
      fog-json (~> 1.0)
      fog-xml (~> 0.1)
    fog-profitbricks (0.0.5)
      fog-core
      fog-xml
      nokogiri
    fog-rackspace (0.1.1)
      fog-core (>= 1.35)
      fog-json (>= 1.0)
      fog-xml (>= 0.1)
      ipaddress (>= 0.8)
    fog-radosgw (0.0.5)
      fog-core (>= 1.21.0)
      fog-json
      fog-xml (>= 0.0.1)
    fog-riakcs (0.1.0)
      fog-core
      fog-json
      fog-xml
    fog-sakuracloud (1.7.5)
      fog-core
      fog-json
    fog-serverlove (0.1.2)
      fog-core
      fog-json
    fog-softlayer (1.1.3)
      fog-core
      fog-json
    fog-storm_on_demand (0.1.1)
      fog-core
      fog-json
    fog-terremark (0.1.0)
      fog-core
      fog-xml
    fog-vmfusion (0.1.0)
      fission
      fog-core
    fog-voxel (0.1.0)
      fog-core
      fog-xml
    fog-vsphere (0.8.0)
      fog-core
      rbvmomi (~> 1.8)
    fog-xenserver (0.2.3)
      fog-core
      fog-xml
    fog-xml (0.1.2)
      fog-core
      nokogiri (~> 1.5, >= 1.5.11)
    font-awesome-rails (*******)
      railties (>= 3.2, < 9.0)
    font_assets (0.1.14)
      rack
    formatador (0.2.5)
    friendly_id (5.1.0)
      activerecord (>= 4.0.0)
    fspath (3.0.0)
    fugit (1.5.2)
      et-orbi (~> 1.1, >= 1.1.8)
      raabro (~> 1.4)
    fuzzy_match (2.1.0)
    gatikwe_api (0.1.5)
    gdata_19 (1.1.5)
    geocoder (1.3.7)
    get_process_mem (0.2.1)
    globalid (0.4.2)
      activesupport (>= 4.2.0)
    gon (6.1.0)
      actionpack (>= 3.0)
      json
      multi_json
      request_store (>= 1.0)
    google-api-client (0.10.3)
      addressable (~> 2.3)
      googleauth (~> 0.5)
      httpclient (~> 2.7)
      hurley (~> 0.1)
      memoist (~> 0.11)
      mime-types (>= 1.6)
      representable (~> 3.0)
      retriable (>= 2.0, < 4.0)
    google_currency (3.4.0)
      money (~> 6.7)
    googleauth (0.8.0)
      faraday (~> 0.12)
      jwt (>= 1.4, < 3.0)
      memoist (~> 0.16)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (~> 0.7)
    groupdate (3.2.0)
      activesupport (>= 3)
    gyoku (1.3.1)
      builder (>= 2.1.2)
    haml (5.0.4)
      temple (>= 0.8.0)
      tilt
    haml-rails (1.0.0)
      actionpack (>= 4.0.1)
      activesupport (>= 4.0.1)
      haml (>= 4.0.6, < 6.0)
      html2haml (>= 1.0.1)
      railties (>= 4.0.1)
    has_barcode (0.2.3)
      activesupport (>= 3)
      barby
      i18n
      rake
    hashery (2.1.2)
    hashie (3.6.0)
    heroku-api (0.4.2)
      excon (~> 0.45)
      multi_json (~> 1.8)
    heroku_san (4.4.0)
      heroku-api (>= 0.1.2)
      json
      rake
    hirefire-resource (0.3.11)
    hodel_3000_compliant_logger (0.1.1)
    html2haml (2.2.0)
      erubis (~> 2.7.0)
      haml (>= 4.0, < 6)
      nokogiri (>= 1.6.0)
      ruby_parser (~> 3.5)
    http-cookie (1.0.2)
      domain_name (~> 0.5)
    httparty (0.8.3)
      multi_json (~> 1.0)
      multi_xml
    httpclient (2.8.3)
    httpi (2.4.2)
      rack
      socksify
    hurley (0.2)
    i18n (0.8.6)
    image_optim (0.23.0)
      exifr (~> 1.2, >= 1.2.2)
      fspath (~> 3.0)
      image_size (~> 1.3)
      in_threads (~> 1.3)
      progress (~> 3.0, >= 3.0.1)
    image_optim_pack (0.2.3)
      fspath (>= 2.1, < 4)
      image_optim (~> 0.19)
    image_size (1.4.2)
    in_threads (1.3.1)
    inflecto (0.0.2)
    ipaddress (0.8.3)
    iso_country_codes (0.7.5)
    jbuilder (2.7.0)
      activesupport (>= 4.2.0)
      multi_json (>= 1.2)
    jquery-rails (4.3.1)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    jquery-ui-rails (6.0.1)
      railties (>= 3.2.16)
    json (1.8.6)
    jwt (2.1.0)
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    koala (2.4.0)
      addressable
      faraday
      multi_json (>= 1.3.0)
    launchy (2.4.3)
      addressable (~> 2.3)
    logger-application (0.0.2)
    lograge (0.11.2)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    lol_dba (2.0.3)
      actionpack (>= 3.0, < 5.0)
      activerecord (>= 3.0, < 5.0)
      railties (>= 3.0, < 5.0)
    loofah (2.13.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    machinist (2.0)
    mail (2.7.1)
      mini_mime (>= 0.1.1)
    mail_safe (0.3.4)
      actionmailer (>= 3.0.0)
    maxminddb (0.1.7)
    mechanize (2.7.4)
      domain_name (~> 0.5, >= 0.5.1)
      http-cookie (~> 1.0)
      mime-types (>= 1.17.2, < 3)
      net-http-digest_auth (~> 1.1, >= 1.1.1)
      net-http-persistent (~> 2.5, >= 2.5.2)
      nokogiri (~> 1.6)
      ntlm-http (~> 0.1, >= 0.1.1)
      webrobots (>= 0.0.9, < 0.2)
    memcachier (0.0.2)
    memoist (0.16.0)
    meta_request (0.4.3)
      callsite (~> 0.0, >= 0.0.11)
      rack-contrib (>= 1.1, < 3)
      railties (>= 3.0.0, < 5.2.0)
    method_source (0.8.2)
    mime-types (2.99.3)
    mini_magick (4.5.1)
    mini_mime (1.1.2)
    mini_portile2 (2.4.0)
    minitest (5.15.0)
    momentjs-rails (2.17.1)
      railties (>= 3.1)
    money (6.9.0)
      i18n (>= 0.6.4, < 0.9)
    mongo (2.4.1)
      bson (>= 4.2.1, < 5.0.0)
    multi_fetch_fragments (0.0.17)
    multi_json (1.13.1)
    multi_xml (0.6.0)
    multipart-post (2.0.0)
    net-http-digest_auth (1.4)
    net-http-persistent (2.9.4)
    net-sftp (2.1.2)
      net-ssh (>= 2.6.5)
    net-ssh (3.2.0)
    netrc (0.11.0)
    newrelic_rpm (8.11.0)
    nokogiri (1.9.1)
      mini_portile2 (~> 2.4.0)
    nori (2.6.0)
    ntlm-http (0.1.1)
    oauth2 (1.4.1)
      faraday (>= 0.8, < 0.16.0)
      jwt (>= 1.0, < 3.0)
      multi_json (~> 1.3)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 3)
    oink (0.10.1)
      activerecord
      hodel_3000_compliant_logger
    omniauth (1.9.0)
      hashie (>= 3.4.6, < 3.7.0)
      rack (>= 1.6.2, < 3)
    omniauth-facebook (5.0.0)
      omniauth-oauth2 (~> 1.2)
    omniauth-google-oauth2 (0.6.0)
      jwt (>= 2.0)
      omniauth (>= 1.1.1)
      omniauth-oauth2 (>= 1.5)
    omniauth-oauth2 (1.6.0)
      oauth2 (~> 1.1)
      omniauth (~> 1.9)
    omniauth-paypal-oauth2 (1.4.14)
      json (~> 1.7)
      omniauth-oauth2 (~> 1.5)
    orm_adapter (0.5.0)
    os (1.0.0)
    paper_trail (7.1.3)
      activerecord (>= 4.0, < 5.2)
      request_store (~> 1.1)
    paperclip (4.3.7)
      activemodel (>= 3.2.0)
      activesupport (>= 3.2.0)
      cocaine (~> 0.5.5)
      mime-types
      mimemagic (= 0.3.0)
    paperclip-optimizer (2.0.0)
      image_optim (~> 0.19)
      paperclip (>= 3.4)
    paranoia (2.4.0)
      activerecord (>= 4.0, < 5.2)
    parser (*******)
      ast (~> 2.2)
    paypal-sdk-core (0.3.4)
      multi_json (~> 1.0)
      xml-simple
    paypal-sdk-invoice (1.117.0)
      paypal-sdk-core (~> 0.3.0)
    pdf-reader (2.1.0)
      Ascii85 (~> 1.0.0)
      afm (~> 0.2.1)
      hashery (~> 2.0)
      ruby-rc4
      ttfunk
    pg (0.18.4)
    phonelib (0.6.8)
    plupload-rails (1.2.1)
      rails (>= 3.1)
    polyamorous (1.1.0)
      activerecord (>= 3.0)
    power_assert (0.3.0)
    powerpack (0.1.1)
    pr_geohash (1.0.0)
    progress (3.1.1)
    promise (0.3.1)
    protected_attributes (1.1.4)
      activemodel (>= 4.0.1, < 5.0)
    pry (0.10.4)
      coderay (~> 1.1.0)
      method_source (~> 0.8.1)
      slop (~> 3.4)
    public_suffix (3.0.3)
    puma (3.11.2)
    puma_worker_killer (0.0.6)
      get_process_mem (~> 0.2)
      puma (>= 2.7, < 4)
    raabro (1.4.0)
    rack (1.6.13)
    rack-attack (5.4.2)
      rack (>= 1.0, < 3)
    rack-cache (1.7.1)
      rack (>= 0.4)
    rack-contrib (1.8.0)
      rack (~> 1.4)
    rack-cors (1.0.3)
    rack-pjax (1.1.0)
      nokogiri (~> 1.5)
      rack (>= 1.1)
    rack-protection (1.5.3)
      rack
    rack-ssl-enforcer (0.2.9)
    rack-test (0.6.3)
      rack (>= 1.0)
    rack-timeout (0.4.2)
    rails (4.2.5)
      actionmailer (= 4.2.5)
      actionpack (= 4.2.5)
      actionview (= 4.2.5)
      activejob (= 4.2.5)
      activemodel (= 4.2.5)
      activerecord (= 4.2.5)
      activesupport (= 4.2.5)
      bundler (>= 1.3.0, < 2.0)
      railties (= 4.2.5)
      sprockets-rails
    rails-deprecated_sanitizer (1.0.4)
      activesupport (>= 4.2.0.alpha)
    rails-dom-testing (1.0.9)
      activesupport (>= 4.2.0, < 5.0)
      nokogiri (~> 1.6)
      rails-deprecated_sanitizer (>= 1.0.1)
    rails-html-sanitizer (1.4.2)
      loofah (~> 2.3)
    rails-observers (0.1.5)
      activemodel (>= 4.0)
    rails_admin (1.4.3)
      builder (~> 3.1)
      coffee-rails (~> 4.0)
      font-awesome-rails (>= 3.0, < 5)
      haml (>= 4.0, < 6)
      jquery-rails (>= 3.0, < 5)
      jquery-ui-rails (>= 5.0, < 7)
      kaminari (>= 0.14, < 2.0)
      nested_form (~> 0.3)
      rack-pjax (>= 0.7)
      rails (>= 4.0, < 6)
      remotipart (~> 1.3)
      sass-rails (>= 4.0, < 6)
    rails_best_practices (1.18.1)
      activesupport
      code_analyzer (>= 0.4.3)
      erubis
      i18n
      json
      require_all
      ruby-progressbar
    railties (4.2.5)
      actionpack (= 4.2.5)
      activesupport (= 4.2.5)
      rake (>= 0.8.7)
      thor (>= 0.18.1, < 2.0)
    rainbow (2.1.0)
    rake (11.3.0)
    rbvmomi (1.8.2)
      builder
      nokogiri (>= 1.4.1)
      trollop
    recaptcha (4.9.0)
      json
    redis (3.3.5)
    remotipart (1.4.4)
    representable (3.0.4)
      declarative (< 0.1.0)
      declarative-option (< 0.2.0)
      uber (< 0.2.0)
    request_store (1.3.1)
    require_all (1.4.0)
    responders (2.4.0)
      actionpack (>= 4.2.0, < 5.3)
      railties (>= 4.2.0, < 5.3)
    rest-client (2.0.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    retriable (3.1.2)
    roo (2.1.1)
      nokogiri (~> 1)
      rubyzip (~> 1.1, < 2.0.0)
    rqrcode (0.10.1)
      chunky_png (~> 1.0)
    rsolr (2.5.0)
      builder (>= 2.1.2)
      faraday (>= 0.9, < 3, != 2.0.0)
    rspec-core (3.0.4)
      rspec-support (~> 3.0.0)
    rspec-expectations (3.0.4)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.0.0)
    rspec-mocks (3.0.4)
      rspec-support (~> 3.0.0)
    rspec-rails (3.0.0)
      actionpack (>= 3.0)
      activesupport (>= 3.0)
      railties (>= 3.0)
      rspec-core (~> 3.0.0)
      rspec-expectations (~> 3.0.0)
      rspec-mocks (~> 3.0.0)
      rspec-support (~> 3.0.0)
    rspec-support (3.0.4)
    rubocop (0.41.2)
      parser (>= *******, < 3.0)
      powerpack (~> 0.1)
      rainbow (>= 1.99.1, < 3.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (~> 1.0, >= 1.0.1)
    ruby-graphviz (1.2.2)
    ruby-ole (1.2.12)
    ruby-progressbar (1.8.1)
    ruby-rc4 (0.1.5)
    ruby_parser (3.10.1)
      sexp_processor (~> 4.9)
    rubyntlm (0.6.0)
    rubyzip (1.2.0)
    rufus-scheduler (3.8.1)
      fugit (~> 1.1, >= 1.1.6)
    safely_block (0.2.1)
      errbase
    sass (3.4.22)
    sass-rails (5.0.7)
      railties (>= 4.0.0, < 6)
      sass (~> 3.1)
      sprockets (>= 2.8, < 4.0)
      sprockets-rails (>= 2.0, < 4.0)
      tilt (>= 1.1, < 3)
    savon (2.11.1)
      akami (~> 1.2)
      builder (>= 2.1.2)
      gyoku (~> 1.2)
      httpi (~> 2.3)
      nokogiri (>= 1.4.0)
      nori (~> 2.4)
      wasabi (~> 3.4)
    scoped_search (3.2.2)
      activerecord (>= 3.2.0)
    selenium-webdriver (2.53.4)
      childprocess (~> 0.5)
      rubyzip (~> 1.0)
      websocket (~> 1.0)
    sexp_processor (4.10.0)
    shadowfax_api (0.1.0)
    shortener (0.6.2)
      rails (>= 3.0.7)
      voight_kampff (~> 1.0.2)
    shoulda-matchers (4.0.1)
      activesupport (>= 4.2.0)
    sidekiq (5.0.0)
      concurrent-ruby (~> 1.0)
      connection_pool (~> 2.2, >= 2.2.0)
      rack-protection (>= 1.5.0)
      redis (~> 3.3, >= 3.3.3)
    sidekiq-failures (1.0.1)
      sidekiq (>= 4.0.0)
    sidekiq-scheduler (3.1.1)
      e2mmap
      redis (>= 3, < 5)
      rufus-scheduler (~> 3.2)
      sidekiq (>= 3)
      thwait
      tilt (>= 1.4.0)
    signet (0.11.0)
      addressable (~> 2.3)
      faraday (~> 0.9)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simplecov (0.12.0)
      docile (~> 1.1.0)
      json (>= 1.8, < 3)
      simplecov-html (~> 0.10.0)
    simplecov-html (0.10.0)
    sitemap_generator (5.1.0)
      builder
    slop (3.6.0)
    soap4r-ng (2.0.3)
      httpclient (~> 2.6)
      logger-application (~> 0.0.2)
    socksify (1.7.0)
    spreadsheet (1.1.2)
      ruby-ole (>= 1.0)
    spring (2.0.2)
      activesupport (>= 4.2)
    sprockets (3.7.2)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.2.2)
      actionpack (>= 4.0)
      activesupport (>= 4.0)
      sprockets (>= 3.0.0)
    sqlite3 (1.3.11)
    squeel (1.2.3)
      activerecord (>= 3.0)
      activesupport (>= 3.0)
      polyamorous (~> 1.1.0)
    state_machines (0.5.0)
    state_machines-activemodel (0.5.0)
      activemodel (>= 4.1, < 5.2)
      state_machines (>= 0.5.0)
    state_machines-activerecord (0.5.0)
      activerecord (>= 4.1, < 5.2)
      state_machines-activemodel (>= 0.5.0)
    stripe (3.31.1)
      faraday (~> 0.10)
    sunspot_solr (2.5.0)
    temple (0.8.0)
    test-unit (3.1.9)
      power_assert
    thor (0.20.3)
    thread_safe (0.3.6)
    thwait (0.2.0)
      e2mmap
    tilt (2.0.8)
    trollop (2.1.2)
    ttfunk (1.5.1)
    turbolinks (5.0.1)
      turbolinks-source (~> 5)
    turbolinks-source (5.0.3)
    turn (0.9.6)
      ansi
    tzinfo (1.2.9)
      thread_safe (~> 0.1)
    uber (0.1.0)
    uglifier (3.2.0)
      execjs (>= 0.3.0, < 3)
    unf (0.1.4)
      unf_ext
    unf_ext (*******)
    unicode-display_width (1.1.0)
    uniform_notifier (1.10.0)
    uuidtools (2.1.5)
    voight_kampff (1.0.4)
      rack (~> 1.4)
    warden (1.2.7)
      rack (>= 1.0)
    wasabi (3.5.0)
      httpi (~> 2.0)
      nokogiri (>= 1.4.2)
    web-console (2.3.0)
      activemodel (>= 4.0)
      binding_of_caller (>= 0.7.2)
      railties (>= 4.0)
      sprockets-rails (>= 2.0, < 4.0)
    webrobots (0.1.2)
    websocket (1.2.3)
    wicked_pdf (1.0.6)
    will_paginate (3.1.0)
    wisper (2.0.0)
    wisper-activejob (1.0.0)
      activejob (>= 4.0.0)
      wisper
    wkhtmltopdf-binary (0.12.5.4)
    xml-simple (1.1.5)
    xpath (2.0.0)
      nokogiri (~> 1.3)

PLATFORMS
  ruby

DEPENDENCIES
  TableCSV
  actionpack-action_caching
  actionpack-page_caching
  activerecord-deprecated_finders
  activerecord-import
  acts-as-taggable-on
  acts_as_follower (~> 0.2.1)
  ahoy_email (~> 1.0, >= 1.0.2)
  airbrake (~> 7.2)
  annotate
  awesome_nested_set
  awesome_print
  aws-sdk!
  aws-ses (~> 0.6.0)
  better_errors
  binding_of_caller
  bluedart!
  bootstrap-sass (= *******)
  bootstrap3-datetimepicker-rails (~> 4.17.37)
  bourbon
  browser
  bullet
  byebug
  cancancan (~> 2.0)
  capybara
  capybara-webkit
  carrierwave (~> 1.0)
  chartkick
  chosen-rails
  ckeditor
  coffee-rails (~> 4.2.2)
  combine_pdf
  connection_pool
  curb
  daemons (~> 1.2, >= 1.2.3)
  dalli
  database_cleaner
  delayed_paperclip
  devise
  devise-async
  exception_notification
  factory_girl_rails (= 4.1.0)
  faker
  fastimage
  fcm
  fog
  font_assets
  friendly_id (~> 5.1.0)
  fuzzy_match
  gatikwe_api
  gdata_19 (~> 1.1.3)
  geocoder
  gon
  google-api-client (~> 0.9)
  google_currency (~> 3.4.0)
  groupdate
  haml-rails
  has_barcode
  heroku_san
  hirefire-resource
  httparty
  httpclient
  image_optim_pack
  iso_country_codes (~> 0.7.2)
  jbuilder
  jquery-rails
  json
  koala
  launchy
  lograge
  lol_dba
  machinist (>= 2.0.0.beta2)
  mail_safe
  maxminddb (= 0.1.7)
  mechanize
  memcachier
  meta_request
  mimemagic!
  mini_magick
  mongo
  multi_fetch_fragments
  nested_form!
  net-sftp
  newrelic_rpm
  oink
  omniauth-facebook (~> 5.0)
  omniauth-google-oauth2 (~> 0.6.0)
  omniauth-paypal-oauth2
  paper_trail
  paperclip
  paperclip-optimizer
  paranoia (~> 2.0)
  paypal-express!
  paypal-sdk-invoice
  paypal-sdk-rest!
  pdf-reader
  pg
  phonelib
  plupload-rails
  promise
  protected_attributes
  pry
  puma
  puma_worker_killer
  rack-attack (~> 5.4.2)
  rack-cache
  rack-cors
  rack-ssl-enforcer
  rack-timeout
  rails (= 4.2.5)
  rails-observers
  rails_admin (~> 1.4.3)
  rails_best_practices
  razorpay!
  recaptcha (= 4.9.0)
  request_store
  rest-client
  roo (~> 2.1.0)
  rqrcode
  rsolr (~> 2.1)
  rspec-rails (= 3.0)
  rubocop
  ruby-graphviz
  ruby-measurement!
  rubyntlm (>= 0.4)
  sass-rails (~> 5.0)
  savon (>= 2.9)
  scoped_search
  selenium-webdriver
  shadowfax_api
  ship_delight_api!
  shortener
  shoulda-matchers (~> 4.0, <= 4.0.1)
  sidekiq (= 5.0.0)
  sidekiq-failures
  sidekiq-scheduler
  simplecov
  sitemap_generator
  soap4r-ng
  spreadsheet (~> 1.0, >= 1.0.8)
  spring
  sprockets
  sqlite3
  squeel
  ss2!
  state_machines-activerecord
  stripe
  sunspot_index_queue!
  sunspot_rails!
  sunspot_solr
  test-unit (~> 3.0)
  thor (~> 0.19)
  turbolinks (~> 5.0.0)
  turn
  uglifier (>= 1.3.0)
  web-console (~> 2.0)
  wicked_pdf
  will_paginate (~> 3.0)
  wisper (~> 2.0)
  wisper-activejob (~> 1.0)
  wkhtmltopdf-binary (~> 0.12.3)

RUBY VERSION
   ruby 2.2.4p230

BUNDLED WITH
   1.17.3
