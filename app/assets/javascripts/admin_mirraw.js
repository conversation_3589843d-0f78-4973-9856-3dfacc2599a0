var MR = MR || {};
var MR = (function(window, document, Mirraw) {
  MR.bestsellers = {
    datePicker: function() {
      var today = new Date();
      var startYear = new Date(2000, 0, 1);

      $(".datepicker").attr("readonly", true); 

      $(".datepicker.start_date").datepicker({
        dateFormat: 'yy-mm-dd',
        maxDate: today,
        minDate: startYear,
        changeYear: true,
        changeMonth: true,
        yearRange: "2000:" + new Date().getFullYear(),
        showButtonPanel: true,
        onSelect: function(selectedDate) {
          var selected = new Date(selectedDate);
          $(".datepicker.end_date").datepicker("option", "minDate", selected);
          $(".datepicker.start_date").removeClass("warning"); 
          $(".datepicker.start_date").attr("placeholder", ""); 
        }
      });

      $(".datepicker.end_date").datepicker({
        dateFormat: 'yy-mm-dd',
        maxDate: today,
        minDate: startYear,
        changeYear: true,
        changeMonth: true,
        yearRange: "2000:3000",
        showButtonPanel: true,
        onSelect: function() {
          $(".datepicker.end_date").removeClass("warning"); 
          $(".datepicker.end_date").attr("placeholder", ""); 
        }
      });

      $("form").on("submit", function(event) {
        var startDate = $(".datepicker.start_date").val();
        var endDate = $(".datepicker.end_date").val();

        $(".datepicker").removeClass("warning"); 
        $(".datepicker").attr("placeholder", ""); 

        if ((startDate && !endDate) || (!startDate && endDate)) {
          if (!startDate) {
            $(".datepicker.start_date").addClass("warning");
            $(".datepicker.start_date").attr("placeholder", "Select Start Date");
          }
          if (!endDate) {
            $(".datepicker.end_date").addClass("warning");
            $(".datepicker.end_date").attr("placeholder", "Select End Date");
          }
          event.preventDefault();
        }
      });
    }
  };
  return Mirraw;
})(this, this.document, MR);

$(document).ready(function() {
  MR.bestsellers.datePicker();
});



$(document).ready(function() {
  $('.export-btn').click(function(e) {
    $('#download_report_field').val('true');
  });

  $('.submit-data').click(function(e) {
    $('#download_report_field').val('');
  });
});

new DataTable('.custom-data-table',
{
  scrollX:true
});