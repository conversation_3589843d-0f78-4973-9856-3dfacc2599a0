paramPickupDone = (idVal) ->
  type: "POST"
  data:
    id:idVal
  url: '/designer_orders/mark_pickup_done'
  datatype: 'script'     

$ ->
  $('.pickup_done_button').on('click', (e) ->
    e.preventDefault()
    submit_btn = $(this)
    dos_id     = submit_btn.data('dos-id')
    international = submit_btn.data('international')
    track = submit_btn.closest('form').find('#tracking').val()
    shipper_name = submit_btn.closest('form').find('#shipper_id option:selected').text()
    submit_btn.val('Processing...')
    submit_btn.prop('disabled', true)
    if international
      tracking_required = gon.international_ship.indexOf(shipper_name)
    else
      tracking_required = gon.domestic_ship.indexOf(shipper_name)
    if (track == 'undefined' || track == '') && tracking_required <= -1
      submit_btn.val('Dispatch')
      submit_btn.prop('disabled', false)
      new PNotify({title: 'Missing Tracking Details', text: 'Please enter Tracking number', type: 'error', styling: 'bootstrap3'})
    else if international == true && gon.international_ship.indexOf(shipper_name) <= -1
      $.ajax
        url: '/designer_orders/check_duplicate_tracking_num'
        data:
          tracking: track
        dataType: 'json'
        type: 'GET'

        success: (data, status) ->
          if data.success == 'not duplicate'
            $('#vendor_tracking_form_'+dos_id).submit()
          else
            new PNotify({title: 'Cannot Dispatch', text: data.error_text, type: 'error', styling: 'bootstrap3'})
        complete: ->
          submit_btn.val('Dispatch')
          submit_btn.prop('disabled', false)
    else
      $('#vendor_tracking_form_'+dos_id).submit()
  )

  if $("#create_pickup_message").length == 1
    $("#create_pickup_message").dialog
      autoOpen: true
      closeOnEscape: false
      draggable: false
      dialogClass: 'create_pickup_message'

    $('#create_pickup_message #create_pickup_link').on('click', (e) ->
      e.preventDefault()
      $("#create_pickup_message").dialog('close')
      window.open($(this).attr('href'), '_blank')
    )

    $('#create_pickup_message #defer_pickup_link').on('click', (e) ->
      e.preventDefault()
      $("#create_pickup_message").dialog('close')
      $.ajax $(this).attr('href')
    )

$ ->
  $('.courier_name_select_rtv').on('change', ->
    if $(this).val() == 'Other'
      $('.courier_name_tracking').show()
      $('#courier_name, #tracking_number').prop('required',true)
    else
      $('.courier_name_tracking').hide()
      $('#courier_name, #tracking_number').prop('required',false)
      $('#courier_name, #tracking_number').val('')
    )

$ ->
  ShowHideAttributes()

  $('.designer_order_index_filters #type_of_filter').on('change', ->
    ShowHideAttributes())

ShowHideAttributes = () ->
  value = $('.designer_order_index_filters #type_of_filter').val()
  if value == 'Order Status'
    $('.designer_order_index_filters #option_dropdown, #reportrange').removeClass('hide')
    $('.designer_order_index_filters #tracking_text_field').addClass('hide')
    $('.designer_order_index_filters #track_order_num').prop('required',false)
  else if value == 'Order Number'
    $('.designer_order_index_filters #option_dropdown,#reportrange').addClass('hide')
    $('.designer_order_index_filters #tracking_text_field').removeClass('hide')
    $('.designer_order_index_filters #track_order_num').prop('placeholder','Enter Order Number')
    $('.designer_order_index_filters #track_order_num').prop('required',true)
  else if value == 'Tracking Number'
    $('.designer_order_index_filters #option_dropdown,#reportrange').addClass('hide')
    $('.designer_order_index_filters #tracking_text_field').removeClass('hide')
    $('.designer_order_index_filters #track_order_num').prop('placeholder','Enter Tracking Num')
    $('.designer_order_index_filters #track_order_num').prop('required',true)
  else if value == 'Gharpay Orders'
    $('.designer_order_index_filters #tracking_text_field').addClass('hide')
    $('.designer_order_index_filters #track_order_num').prop('required',false)
    $('.designer_order_index_filters #option_dropdown').addClass('hide')

ShowHideTrackingNum = (dos_block) ->
  designer_order_id = dos_block.attr('data-id')
  ship_to           = dos_block.attr('data-ship-to')
  if (ship_to == 'mirraw' && gon.international_ship.indexOf(dos_block.find('option:selected').text()) > -1) || (ship_to == 'customer' && gon.domestic_ship.indexOf(dos_block.find('option:selected').text()) > -1)
    $('.tracking_number_label_' + designer_order_id).hide()
    $('.tracking_number_' + designer_order_id).hide()
    $('.state_' + designer_order_id).hide()
  else
    $('.tracking_number_label_' + designer_order_id).show()
    $('.tracking_number_' + designer_order_id).show()
    $('.state_' + designer_order_id).show()

$('.shipper_id').on('change', (e) ->
  ShowHideTrackingNum($(this)))

$(document).ready ->
  $.each $('.admin_shipper_id'), ->
    ShowHideTrackingNum($(this))

  if document.getElementById('daterange_selector')

    cb = (start, end, label) ->
      console.log start.toISOString(), end.toISOString(), label
      $('#daterange_period').val $('.daterangepicker.dropdown-menu .ranges li.active').text()
      $('#reportrange #daterange_selector').val start.format('D MMMM, YYYY') + ' - ' + end.format('D MMMM, YYYY')
      #alert("Callback has fired: [" + start.format('MMMM D, YYYY') + " to " + end.format('MMMM D, YYYY') + ", label = " + label + "]");
      return

    range_hash = 
      'Today': [
        moment()
        moment()
      ]
      'Yesterday': [
        moment().subtract(1, 'days')
        moment().subtract(1, 'days')
      ]
      'Last 7 Days': [
        moment().subtract(6, 'days')
        moment()
      ]
      'Last 30 Days': [
        moment().subtract(29, 'days')
        moment()
      ]
      'This Month': [
        moment().startOf('month')
        moment().endOf('month')
      ]
      'Last Month': [
        moment().subtract(1, 'month').startOf('month')
        moment().subtract(1, 'month').endOf('month')
      ]
      'Last 2 Months': [
        moment().subtract(2, 'month')
        moment()
      ]
      'Last 3 Months': [
        moment().subtract(3, 'month')
        moment()
      ]
      'Last 6 Months': [
        moment().subtract(6, 'month').startOf('month')
        moment()
      ]
    set_start_date = range_hash[$('#daterange_period').val()]
    optionSet1 = 
      startDate: if set_start_date != undefined and set_start_date.length > 0 then set_start_date[0] else if $('#daterange_period').val() == '' then moment().subtract(3, 'month') else moment().subtract(2, 'month')
      endDate: moment()
      minDate: '01/01/2012'
      maxDate: moment().format('DD/MM/YYYY')
      showDropdowns: true
      showWeekNumbers: true
      timePicker: false
      timePickerIncrement: 1
      timePicker12Hour: true
      ranges: range_hash
      opens: 'left'
      buttonClasses: [ 'btn btn-default' ]
      applyClass: 'btn-small btn-primary'
      cancelClass: 'btn-small'
      format: 'DD/MM/YYYY'
      separator: ' to '
      locale:
        applyLabel: 'Submit'
        cancelLabel: 'Clear'
        fromLabel: 'From'
        toLabel: 'To'
        customRangeLabel: 'Custom'
        daysOfWeek: [
          'Su'
          'Mo'
          'Tu'
          'We'
          'Th'
          'Fr'
          'Sa'
        ]
        monthNames: [
          'January'
          'February'
          'March'
          'April'
          'May'
          'June'
          'July'
          'August'
          'September'
          'October'
          'November'
          'December'
        ]
        firstDay: 1
    date_value = $('#daterange_selector').val() or moment().subtract(3, 'month').format('D MMMM, YYYY') + ' - ' + moment().format('D MMMM, YYYY')
    $('#reportrange #daterange_selector').val date_value
    $('#reportrange').daterangepicker optionSet1, cb
    # $('#reportrange').on('show.daterangepicker', function() {
    #   console.log("show event fired");
    # });
    # $('#reportrange').on('hide.daterangepicker', function() {
    #   console.log("hide event fired");
    # });
    # $('#reportrange').on('apply.daterangepicker', function(ev, picker) {
    #   console.log("apply event fired, start/end dates are " + picker.startDate.format('D MMMM, YYYY') + " to " + picker.endDate.format('D MMMM, YYYY'));
    # });
    # $('#reportrange').on('cancel.daterangepicker', function(ev, picker) {
    #   console.log("cancel event fired");
    # });
    $('#options1').click ->
      $('#reportrange').data('daterangepicker').setOptions optionSet1, cb
      return
    $('#options2').click ->
      $('#reportrange').data('daterangepicker').setOptions optionSet2, cb
      return
    $('#destroy').click ->
      $('#reportrange').data('daterangepicker').remove()
      return
    return

$ ->
  $('.select_orders').change ->
    $('.shipper_names, .shipment_number, .dispatch_shipment').hide()

  $('#get-shippers').click ->
    if ($('.select_orders:checkbox:checked').length < 2)
      alert 'Select atleast two orders to proceed'
      return false
    else
      dos_ids = []
      $.each $('.select_orders:checkbox:checked'), ->
        dos_ids.push $(this).val()
      $.get '/designers/get_domestic_shipper_names', { dos_ids: dos_ids },
        (data) ->
          if data.error == undefined
            $('#shipping_company').empty()
            $.each data.shippers, (key, value) ->
              $('#shipping_company').append $('<option></option>').val(value).html(key)
              $('#edit_inv_link').attr('href', '/designers/edit_invoice/'+data.encoded_dos_id)
              $('.shipper_names, .dispatch_shipment').show()
              $('#dispatch_through_clickpost').val(data.clickpost_serviceable)
              if !data.rapid
                $('.shipment_number').show()
          else
            new PNotify({title: 'Cannot dispatch', text: data.error, type: 'error', styling: 'bootstrap3'})

  $('.create_issue_form').on 'submit', (e) ->
    e.preventDefault()
    id = $(this).attr('id')
    item_id = $('#'+id+' #item_id').val()
    designer_id = $('#'+id+ ' #designer_id').val()
    note_issue_type = $('#'+id+ ' #note_issue_type').val()
    notes_reason = $('#'+id+ ' #note_reason').val()
    received_status = $('#'+id+ " input[name='received_status']:checked").val()
    if note_issue_type == 'Buyer Return Followup' 
      notes_reason = received_status + ', ' + notes_reason
    $.post '/line_items/add_vendor_notes', { item_id: item_id, designer_id: designer_id, note_issue_type: note_issue_type, note_reason: notes_reason},
      (data) ->
        if data.error == undefined
          $('#create_vendor_issue_modal_'+item_id).modal('hide')
          new PNotify({title: 'Successful', text: data.message, type: 'success', styling: 'bootstrap3'})
        else
          new PNotify({title: 'Failed', text: data.error, type: 'error', styling: 'bootstrap3'})

  $('.issue_type_select').change ->
    item_id = $(this).attr('data-id')
    if $(this).val() == 'Buyer Return Followup'
      $('#create_issue_form_'+item_id+ ' #received_status_check').show()
    else
      $('#create_issue_form_'+item_id+ ' #received_status_check').hide()

$ ->
  search_input = document.getElementById('search_list')
  if search_input
    myAwesomeComplete = new Awesomplete(search_input, list: Object.keys(gon.questions))
    search_input.addEventListener 'awesomplete-select', (e) ->
      $('#area_'+gon.questions[e.text.value]).trigger('click')
      $('html,body').animate({ scrollTop: $('#q_'+gon.questions[e.text.value]).offset().top }, 1000);

  if location.hash
    $(location.hash).trigger('click')
    $('html,body').animate({ scrollTop: $(location.hash).offset().top }, 1000);
    window.history.replaceState(null, null, window.location.pathname);

  $('.faq-ul.most-asked a').click ->
    $('html,body').animate({ scrollTop: $('#'+$(this).data('id')).offset().top }, 1000);

$ ->
  $('.warehouse_tracking_details .box_number').on 'change', (e) ->
    warehouse_order_id = $(this).closest('.warehouse_tracking_details').find("#warehouse_order_id").val()
    result = document.querySelector('#result_'+warehouse_order_id)
    result.innerHTML = '<b>Quantity in each Box</b>'
    elm = $(this).val()
    i = 0
    while i < parseInt(elm)
      wrapper = document.createElement('div')
      wrapper.innerHTML = '<input type="number" name = "Box_'+(i+1)+'" placeholder="Box ' + (i+1) + '" min="1" required=true class="form-control col-md-12" id="Box_'+(i+1)+'" style="margin-top:3%;" />'
      result.appendChild wrapper
      i++

  $('.warehouse_tracking_details').on 'submit', (e) ->
    e.preventDefault()
    $(this).find(".warehouse_order_dispatched").attr("disabled", true).val('Processing...')
    elm = this
    $.ajax
      url: '/designer_orders/mark_warehouse_order_dispatched'
      data: $(this).serialize()
      dataType: 'json'
      type: 'POST'
      success: (data,status) ->
        warehouse_order_id = $(elm).find('#warehouse_order_id').val()
        result = document.querySelector('#warehouse_tracking_details_'+warehouse_order_id)
        if data.success == 'Successfully Dispatched'
          $(elm).attr("style","display:none")
          result.innerHTML = "<div  class='alert alert-success'>
                              #{data.success}</div>
                              <b>
                              Shipper Name: #{$(elm).find('#shipper_name').val()}<br>
                              Tracking number: #{$(elm).find('#tracking').val()}<br>
                              Total Box: #{data.total_box}<br>
                              Quantity: #{data.quantity}</b>"
        else
          result.innerHTML = "<div  class='alert alert-danger'>#{data.success}</div>"
          $(".warehouse_order_dispatched").attr("disabled",false).val("Submit")