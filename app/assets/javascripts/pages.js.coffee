//= require list.min
returnPolicy = $('#return_policy').dialog(
  resizable: false
  autoOpen: false
  height: 600
  width: 800
  buttons:
    'I Agree':
      click: ->
        window.location = $(this).data('link')
        return
      text: 'I Agree'
      class: 'btn btn-primary'
    'Close':
      click: ->
        $(this).dialog 'close'
        return
      text: 'Close'
      class: 'btn btn-primary'
)
$ ->
  $('.categories_list').hover ->
    id = $(this).data('attr-id')
    $('.category_ques').hide()
    $('#'+id).show()

  $('.help_center_tabs').click ->
    $('.help_center_tabs').removeClass('active')
    $(this).addClass('active')
    $('.categories_group').hide()
    $('.category_ques').hide()
    id = $(this).data('attr-id')
    $('.categories_list_'+id).show()
    $('#active_class').data('attr-id',id)
    if $('#active_class').data('attr-id') == 1
      $('.order_select').hide()
      $('#order_number_label').hide()
      $('#mail_issue_help_center').addClass('col-md-offset-4')
    else
      $('.order_select').show()
      $('#order_number_label').show()
      $('#mail_issue_help_center').removeClass('col-md-offset-4')

  $(document).on 'click','#email_us', ->
    that = $(this)
    str =""
    taggingValues = ['options','sub_option_1','sub_option_2']
    for value, index in taggingValues
      customAttribute = value.replace(/_/g, '-')
      if(typeof(that.data(customAttribute))!= 'undefined')
        $('#' + value).val(that.data(customAttribute))
        str += $('#' + value).val()+' > '
    $('#issue_text_help_center').val(str.slice(0, -2))
    $('#order_number_text').val($('.order_details:visible').find('#product_no').text())
    $('#support_text_id').val(that.data('support-text-id'))
    if gon.account_present == true
      $('#email_id_help_center').val(gon.email).attr('readonly',true)
    if $('.order_details').is(':visible')   
      $('#order_number_label').show()
    else  
      $('#order_number_label').hide()


 $('#recent_order_number').click ->
    selectedOrder($(this).context.innerHTML,$(this).data('valid'),$(this).data('country'))


  $('#track_link_help_center').click ->
    order_number = $('#order_number_text').val()
    if order_number != null
      window.open "/orders/"+order_number ,"_blank" 

  $("#return_order_help_center").on 'click', (e) ->
    e.preventDefault()
    order_number = $('#order_number_text').val()
    user_id = $(this).attr('data-user-id')
    link    = window.location.origin + '/user/' + user_id + '/order/' + order_number + '/return_items'
    if !$('#return_link').hasClass('order_track_btn_inactive')
      returnPolicy.data('link', link).dialog 'open'

  $('#order_ack_link_help_center').click ->
    order_number = $('#order_number_text').val()
    if order_number != null
      $.ajax(mailOrderAcknowledgement(order_number))

  mailOrderAcknowledgement =(order_number) ->
    type: 'POST'
    data:
      number: order_number
    url: '/orders/send_order_ack_email'
    beforeSend: ->
      $('#loading-image').show()
    complete: ->
      $('#loading-image').hide()
      alert('Order Acknowledgement Mail sent successfully')
    success: (data,success,jqhxr) ->

  selectedOrder=(number,validity,country)->
    $('.email_panel').removeClass('blur')
    $('.email_form_help_center').show()
    $('.tracking_buttons_help_center').show()
    $('.login_order_select').hide()
    $('.order_heading').addClass('stage_passed')
    $('#order_number_text').val(number)
    $('#country').val(country)
    $('#order_details').append('<div id=order_number_help_center> Your Order No: <strong>'+number+'<strong></div>')
    if validity == false
      $('#return_order_help_center').attr('title', 'Return Duration Validity is Expired')
      $('#return_order_help_center a').removeClass 'order_track_btn'
      $('#return_order_help_center a').addClass 'order_track_btn_inactive'
    else if validity == 'cancel'
      $('#return_order_help_center').attr('title', 'Order is canceled.')
      $('#return_order_help_center a').removeClass 'order_track_btn'
      $('#return_order_help_center a').addClass 'order_track_btn_inactive'
    else if validity == 'new'
      $('#return_order_help_center').attr('title', 'Order not yet shipped.')
      $('#return_order_help_center a').removeClass 'order_track_btn'
      $('#return_order_help_center a').addClass 'order_track_btn_inactive'
    else if validity == true
      $('#return_order_help_center a').removeClass 'order_track_btn_inactive'
      $('#return_order_help_center a').addClass 'order_track_btn'
      $('#return_order_help_center').attr('title', 'Place a new Return / Exchange request.')

  $('#selected_category_question').click ->
    selectQuestionOnClick()

  $('#order_details').click ->
    $('.login_order_select').show()
    $('.order_heading').removeClass('stage_passed')
    $('.email_form_help_center').hide()
    $(this).empty()

  selectQuestionOnClick=() ->
    $('.categories_list_0').show()
    $('#selected_tabs_selector').addClass('selected_tabs')
    $('#active_class').data('attr-id',0)
    $('.order_select').show()
    $('#order_number_label').show()
    $('.help_center_tabs').show()
    $('.issue_heading').removeClass('stage_passed')
    $('.order_heading').removeClass('stage_passed')
    $('#selected_category_question').hide()
    $('#selected_category').empty()
    $('#selected_question').empty()
    $('.help_center_tabs').removeClass('active')
    $('.help_center_tabs').first().addClass('active')
    $('.order_select').addClass('blur')
    $('.email_panel').addClass('blur')
    $('.email_form_help_center').hide()
    $('.login_order_select').hide()
    $('#selected_category').removeClass('selected_category_class')
    $('#selected_question').removeClass('selected_question_class')
    $('#order_details').empty()
    $('#error_order_not_found').empty()
    $('#navtabs_helpcenter').show()
    $('#mail_issue_help_center').removeClass('col-md-offset-4')
    $('#coupon_id_tag').hide()
    $('#coupon_id_field').attr('disabled','true')

  paramNewGetOrdersFromAccount =() ->
    type: 'GET'
    url: '/orders/get_order_details'
    # beforeSend: ->
    #   $('.order_menu').append('<div id="load">Loading....</div>')
    complete: ->
      $('#load').hide()
    success: (data,success,jqhxr) ->
      if data.orders_list != null
        order_info = ''
        for order_no,orders of data.orders_list
          if orders['state'] == 'Sane'
            orders['state'] = 'Confirmed'
          else
            orders['state'] = 'Not Yet Confirmed'
            $('#status').show()
          order_info += '<div class="order_details">
                         <div class="product_desc">
                         <div class="order_info">
                         <div id="product_no">'+order_no+'</div>
                         <div id="product_image"></div>
                         </div> 
                         <div class="product_detail">
                         <div id="item_name">'+orders['items']+'</div>
                         <div id="item_total">'+orders['total']+'</div>
                         </div>
                         <div class="product_status">
                         <div id="confirmed" class="btn btn-info">'+orders['state']+'</div><br>
                         <div id="status">'+orders['created_at']+'</div>
                         </div> </div> </div>'
        $('.orders_data').prepend(order_info)
      else
        $('.orders_data').append('<div class="error_oops_order">Sorry You Have Not Placed Any Orders Yet</div>')
    error: ->
      alert('Something went wrong')

$ ->
  $('.order_menu').not(':eq(0)').hide()

  $.ajax(paramNewGetOrdersFromAccount())

  $(document).on 'click','.product_desc', ->
    $('.product_questions, .question').show(500)
    $('.list_all_orders').css 'display','table'
    $('.product_desc').not(this).parent().hide(400)

  $('.query').click ->
    setTimeout (-> $('.order_details').show() ), 200
    $('.question_level_two, .q_answers, .q_answer ,.list_all_orders').hide()
    $(this).addClass('selected')
    $('.query').not(this).removeClass('selected')
    if $('.query').hasClass('selected')
      query_id = $('.selected').data('attrId')
      $('.order_menu').hide()
      $('#'+query_id).show()
    $('.product_questions').show()

  $('.question').click ->
    $(this).siblings('.question_level_two, .q_answers').fadeToggle(300)
    $('.question_level_two, .q_answers, .q_answer').not($(this).siblings()).hide(300)

  $('.question_level_two').click ->
    $(this).children('.q_answer').fadeToggle(300)
    $('.q_answer').not($(this).children()).hide(300)

  $('.list_all_orders').click ->
    setTimeout (-> $('.order_details').show() ), 200
    $('.product_questions, .q_answer, .list_all_orders').hide()
    