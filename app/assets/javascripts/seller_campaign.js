var MR = MR || {};
MR = (function(window, document, Mirraw){
  MR.sellerCampaign = {
    datePicker: function() {
      var selectedStartDate = $(".datepicker.start_date").val() || null;
      var selectedEndDate = $(".datepicker.end_date").val() || null;

      $(".datepicker.start_date").datepicker({
        dateFormat: 'yy-mm-dd',
        defaultDate: selectedStartDate,
        onSelect: function(selectedDate) {
          var selected = new Date(selectedDate);
          $(".datepicker.end_date").datepicker("option", "minDate", selected);
        }
      });

      $(".datepicker.end_date").datepicker({
        dateFormat: 'yy-mm-dd',
        defaultDate: selectedEndDate,
        minDate: selectedStartDate ? new Date(selectedStartDate) : null
      });
    }
  };
  return Mirraw;
})(this, this.document, MR);