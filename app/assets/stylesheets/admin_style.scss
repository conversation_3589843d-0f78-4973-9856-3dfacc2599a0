body {
 	padding:0px 30px 0px 30px;
 	font-size :13px;
 	background : #151515;
 	color :#fff;
 }

.text_common_black{
  color: black;
}

 .admin_design {
    .admin_design_catalog{
      border: 1px solid white;
      padding: 8px;
      margin-right: 15px;
      float: left;
      height: 80%;
    }
    input[type=text]{
    color :black;
    }
  }

 .sender{
    margin-top:120px;
  }
  .receiver{
    margin-top:100px;
  }


select{
  color:black;
  padding:8px;
  border-radius:3px;
}

 .date_container{
 	background: #fff;
 	padding:10px;
 	color: #000;
 	width:400px;
 }

 .table .table{
 	background-color: transparent;
	color:#fff;
 }

 .list_view_table{
 	width:100%;
 }

 .default_table_width{
 	width: auto;
 }

 .medium_font_size{
 	font-size: 13px;
 }

 .large_font_size{
  font-size: 16px;
 }

 .well{
 	color: #000;
 }

 .invoice{
 	color: #000;
 }

 .label-new{
 	background: #0431B4;
 }
 .label-pending{
 	background: #F78181;
 }

 .label-sane{
 	background: #5E610B;
 }

 .label-complete{
 	background: green;
 }

 .label-cancel{
 	background: #B18904;
 }

 .label-dispatched{
 	background: #B18904;
 }

 .label-cancel_complete{
 	background: purple;
 }

 .label-ready_for_dispatch{
 	background: #088A85;
 }

 #phone_number{
  display: block;
  margin-top: 5px;
}

 .label-partial_dispatch{
 	background: #04B486;
 }

 .label-pickedup{
 	background: #8A084B;
 }

 .label-confirmed{
 	background :darkgreen;
 } 

 .label-fraud{
 	background: #d9534f;
 }

 .label-failed{
 	background :#d9534f;
 }

 .label-completed{
 	background :green;
 }

 .label-GOOD{
  background :green;
 }

 .label-OK{
  background :blue;
 }

 .label-BAD{
  background :red;
 }

.dropdown-menu {
	margin: 2px 0px 0px -45px;
	font-size:13px;
}
.modal-content{
  width: 900px;
  margin-left: -150px;
}
.padding_10_px{
  padding:10px;
}
.modal-body{
padding:30px;
}
.error{
color:red;
}
.custom_qc_model_button{
  border:none; 
  border-radius: 10px;
  margin-left: 10px;
  width: 80px;
}

.coupon_card{
  border-radius: 15px;
  background: #ffffff;
  padding: 10px;
  width: 30%;
  float: left;
  margin-right: 3%;
  cursor: pointer;
  h4,h5{
    color: black;
    text-align: center;
    margin: 10px;
  }
}

#designer_page_title {
  .page-title {
    width: 100%;
    height: 65px;
    padding: 10px 0;

    .title_left {
      width: 45%;
      float: left;
      display: block;
    }

    .title_right {
      width: 55%;
      float: left;
      display: block;
    }
  }
}

#designer_help_center {
  font-size: 12pt;
  font-family: 'book antiqua';
  .question_hr {
    margin-bottom: 20px;
    border-top: 1px dashed grey;
    margin-top: 0px;
  }
  ul.faq-ul {
    padding-left: 0;
    list-style-type: none !important;

    li {
      padding-bottom: 3px;
      font-size: small;
    }
  }
  h5 {
    padding-left: 10px;
  }
  h4 {
    font-weight: bold;
    color: white;
  }
  .panel {
    padding: 0px 10px 0px 10px;
    background: #ddecf3;
    color: black;
  }
  a {
    cursor: pointer;
  }

  strong {
    color: black;
  }

  #faq_accordion {
    table td {
      padding: 2px;
    }
  }

  .accordion .panel {
    margin-bottom: 5px;
    border-radius: 0;
    border-bottom: 1px solid #efefef;
  }
  .accordion .panel-heading {
    background: #F2F5F7;
    padding: 4px;
    width: 100%;
    display: block;
  }
  .accordion .panel:hover {
    background: #F2F5F7;
  }

  .x_panel {
    position: relative;
    width: 100%;
    margin-bottom: 10px;
    padding: 10px 17px;
    display: inline-block;
    background: #EDEDED;
    border: 1px solid rgba(237, 237, 237, 0.38);
    -webkit-column-break-inside: avoid;
    -moz-column-break-inside: avoid;
    column-break-inside: avoid;
    opacity: 1;
    -moz-transition: all .2s ease;
    -o-transition: all .2s ease;
    -webkit-transition: all .2s ease;
    -ms-transition: all .2s ease;
    transition: all .2s ease;
    color: black;

    .x_title {
      border-bottom: 2px solid #d6d9dc;
      padding: 1px 5px 6px;
      margin-bottom: 10px;
      h2 {
        margin: 5px 0 6px;
        float: left;
        display: block;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
      span {
        color: #BDBDBD;
      }
      .x_content {
        padding: 0 5px 6px;
        position: relative;
        width: 100%;
        float: left;
        clear: both;
        margin-top: 5px;
      }
    }
  }
}

#duplicate-inventory {
  .panel-body {
    color: black;
  }
  .x_panel {
    position: relative;
    width: 100%;
    margin-bottom: 15px;
    padding: 10px 17px;
    display: inline-block;
    background-color: black !important;
    border: 1px solid rgba(237, 237, 237, 0.38);
    color: white;
    .x_title {
      border-bottom: 2px solid #d6d9dc;
      padding: 1px 5px 6px;
      margin-bottom: 8px;
      width: 100%;
      font-size: medium;
    }
    .x_content {
      padding: 0 2px 2px;
      position: relative;
      width: 100%;
      float: left;
      margin-top: 0px;
      h4 {
        font-size: 16px;
        font-weight: 500;
      }
    }

    a:hover {
      cursor: pointer;
    }
  }

  .text-left {
    text-align: left
  }
}

#admin_notification, #delayed_download_icon {
  .dropdown-menu {
    color: black;
    width: 255px;
    margin: 1px 0px 0px -128px;
    float: left;
    font-size: 12px;
    list-style: none outside none;
    padding: 0;
    position: absolute;
    top: 100%;
    z-index: 1000;
    border: 1px solid #D9DEE4;
    border-top-right-radius: 0;

    ul.list-group {
      margin-bottom: 1px;
    }
  }
}

#ck-button {
  margin:4px;
  background-color:#EFEFEF;
  border-radius:4px;
  border:1px solid #D0D0D0;
  overflow:auto;
  display: inline-block;
  vertical-align: middle;
  label {
    float:left;
    margin-bottom: 0px;
    span {
      text-align:center;
      padding:5px 5px;
      display:block;
    }
    input {
      display: none;
    }
  }
  input:checked + span {
    background-color:#1f6fc3;
    color:#fff;
  }
}

#order-notes {
  td, th {
    border-bottom: 1px solid #827d7d;
    border-top: none;
    input {
      color: black;
    }
  }
}
#order-notes_filter {
  display: none;
}

.x_panel {
  position: relative;
  width: 100%;
  margin-bottom: 10px;
  display: inline-block;
  background: #151515;
  border: 1px solid rgba(237, 237, 237, 0.38);
  -webkit-column-break-inside: avoid;
  -moz-column-break-inside: avoid;
  color: white;
  opacity: 1;
  img {
    width: 50%
  }
  .x_title {
    border-bottom: 2px solid #717477;
    background: black;
    padding: 15px;
    display: inline-block;
    margin-bottom: 10px;
    h4 {
      margin: 5px 0 6px;
      float: left;
      display: block;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      small {
        margin-left: 10px;
      }
    }
  }
  .x_content {
    padding: 15px;
    position: relative;
    width: 100%;
    float: left;
    clear: both;
    margin-top: 5px;
    h4 {
      font-size: 16px;
      font-weight: 500;
    }
  }
}
.shift_down{
  padding-top: 38px;
}

.well-lg{
  border-radius: 14px;
  padding: 15px;
  min-height:600px;
}
.well{
  overflow: auto;
  color: #fff;
  background-color: #000000;
  border: 1px solid #202020;
}
#review_table th, #review_table td{
  border-top: 1px solid #292929 !important;
}
#review_table .btn{border-radius: 20px;}
.card {
  box-shadow: inset 1px 1px 20px 7px rgba(183, 183, 183, 0.2);
  transition: 0.3s;
}
.card:hover {
  box-shadow: inset 0px 0px 20px 6px rgba(117, 117, 117, 0.2);

}
.user_image{
  border-radius: 50%;
}
.chart_type{
  display:none;
}

.grading_prmotion{
  .row{
    .promotable{
      padding: 0px;
      padding-top: 20px;
      padding-bottom: 20px;
      min-height: 240px;
      max-height: 240px;
      .promotable_details{
        padding-top: 5px;
        width: 100px;
        text-align: center;
      }
      .profile{
        margin-left: 0%;
        z-index: 1000;
        position: inherit;
        margin-top: 0px;
        border: 1px solid rgba(52, 73, 94, 0.44);
        padding: 3px;
        background: #fff;
        height: 101px;
        width: 100px;
        padding-top: 17px;
        font-weight: bold;
      }
    }
  }
}



#admin_notification{
  ul{
    width: 260% !important;
    margin: 2px 0px 0px -265px !important;
    li{
      padding: 5px 5px 0px 5px;;
      a{
        padding: 8px 8px !important;
        background-color: gainsboro;
        display: block;
        .message{
          display: block !important;
          font-size: 11px;
          max-height: 60px;
          overflow: hidden;
        }
        .time{
          font-size: 11px;
          font-style: italic;
          font-weight: bold;
          position: absolute;
          right: 10px;
        }
      }
    }
    li:last-child {
      margin-bottom: 5px;
    }
  }
  li.read{
    a{
      background-color: #f5eeee;
    }
  }
}

.mail_list {
  width: 100%;
  border-bottom: 0px solid #DBDBDB;
  margin-bottom: 2px;
  display: inline-block;
  padding-bottom: 8px;

  .left {
    width: 25%;
    float: left;
    margin-right: 3%
  }
  .right {
    width: 70%;
    float: left
  }
  h3 {
    font-size: 15px;
    font-weight: bold;
    margin: 0px 0 6px;
  }
  h3 small {
    float: right;
    color: #ADABAB;
    font-size: 11px;
    line-height: 20px;
  }
  .badge {
    padding: 3px 6px;
    font-size: 8px;
    background: #BAB7B7
  }
  ul.timeline li {
    position: relative;
    border-bottom: 1px solid #e8e8e8;
    clear: both;
  }
  .timeline .block {
    margin: 0;
    border-left: 3px solid #e8e8e8;
    overflow: visible;
    padding: 10px 15px;
    margin-left: 105px;
  }
  .timeline.widget {
    min-width: 0;
    max-width: inherit;
  }
  .timeline.widget .block {
    margin-left: 5px;
  }
  .timeline .tags {
    position: absolute;
    top: 15px;
    left: 0;
    width: 84px;
  }
  .timeline .tag {
    display: block;
    height: 30px;
    font-size: 13px;
    padding: 8px;
  }
  .timeline .tag span {
    display: block;
    overflow: hidden;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .tag {
    line-height: 1;
    background: #1ABB9C;
    color: #fff !important;
  }
  .timeline h2.title {
    position: relative;
    font-size: 16px;
    margin: 0;
  }
  .timeline h2.title:before {
    content: "";
    position: absolute;
    left: -23px;
    top: 3px;
    display: block;
    width: 14px;
    height: 14px;
    border: 3px solid #d2d3d2;
    border-radius: 14px;
    background: #f9f9f9;
  }
  .timeline .byline {
    padding: .25em 0;
  }
  .mail_list_column {
    border-left: 1px solid #DBDBDB;
    border-right: 1px solid #DBDBDB;
  }
  .tabs-left, .tabs-right {
    border-bottom: none;
    padding-top: 2px;
  }
  .tabs-left {
    border-right: 1px solid #F7F7F7;
  }
  .tabs-right {
    border-left: 1px solid #F7F7F7;
  }
  .tabs-left>li, .tabs-right>li {
    float: none;
    margin-bottom: 2px;
  }
  .tabs-left>li {
    margin-right: -1px;
  }
  .tabs-right>li {
    margin-left: -1px;
  }
}

#grading_panel {
  background-color: #000;
  border: 1px solid #444; 
  border-radius: 8px;
  width: 41%;
  padding: 20px;
  margin-left: auto;
  margin-right: auto;
  margin-top: 33px;
  .panel-title{
    font-size: 24px;
  }
  .panel-heading {
    text-align: center;
    border-bottom: 1px solid #444;
    }
    .form-control{
    border: 1px solid #555;
    }
}
input.search-field {
  color: black;
}
#grading_tag_search{
  margin-top: -9px;
  .search-field{
    width: 24%;
  }
}
#grading_tag_details {
  font-size: 1.5rem;
  .upload-btn{
    width: 14rem;
  }
}
#design_grading_search{
  margin-bottom: 10px;
}

#grading_tag_actions{
.create-grading-tag-btn,
.demo-csv-btn{
  float: right;
  // margin-top: -63px;
  margin-top: -10px;
  margin-bottom: 7px;
  margin-left: 4px;
}
}
.grading-tag-title{
  // text-align: center;
  margin-bottom: 32px;
}

#manual_grading_panel
{
 .panel-main-div{
  height: 433px; 
  width:100%; 
  display: flex; 
  justify-content: center; 
  align-items: center;
 }
 .panel-sub-div{
  border:1px solid white; 
  padding: 8px;
  margin-right: 15px; 
  float:left; 
  height: 413px; 
  width: 34%;
 }
 .form-group{
  margin-top:27px;
 }
 .submit{
  display:flex; 
  justify-content:center;
 }
 .demo-link{
  display: inline-block;
  text-align: center;
  cursor: pointer;
  font-size: 14px;
 }
}
.form-group.custom_csv_file {
  text-align: center;
  input{
    width: 100%;
    text-align: center;
  }
}

.seller-campaign{
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.refund-reason{
  font-size: 14px;
  font-weight: bold;
  border: 2px solid #d43f3a;
  border-radius: 0.25em;
  padding-left: 0.6em;
}
.two-buttons {
  display: flex;
  border-bottom: none !important;
}

.two-buttons a{
  margin: 0 5px;
}

.label-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}

.luxe-label {
  flex: 0 0 auto;      
  white-space: nowrap; 
  padding: 9px 10px;
  margin-left: 5%;
  font-size: 11px;
  font-weight: bold;
  color: black;
}

.datepicker.start_date::placeholder {
  color: rgb(190, 74, 59);
}
.datepicker.end_date::placeholder {
  color: rgb(190, 74, 59);
}
