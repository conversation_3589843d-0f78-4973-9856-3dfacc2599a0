@import 'bootstrap';
@import 'bourbon';
@import 'variables';
@import 'mixins';
@import 'external_new_red';
@import 'main_new_red';
@import 'store_new_red';
@import 'wishlists';

//@import 'carts';
@import 'orders1_red';
//@import 'coupons1';
//@import 'timeline_events1';
@import 'designs1_red';

@import 'main_red';
@import 'menu_red';
@import 'menu_custom_red';
@import 'about_page';
@import 'sell_page';

@import "catalog_red";
@import 'subscriptions_red';

@import 'cancel_cod_order';

.integration-input-bottom-margin {
  margin-bottom: 10px;
}

.integration-label-vertial-align {
  vertical-align:middle;
}

.link_style{
  color: $background-color !important;
  font-size: 14px !important;
}

#QR_code_box{
  display: none !important;
}
.desktop_email_footer{
  text-align: center;
  #e_subscribe{
    padding: 22px 0px 0px 0px;
    .user_email_footer{
      padding: 12px;
      border: $border-box;
      width: 100%;
      max-width: 380px;
      height: 60px;
      font-style: italic;
    }
    .email_footer_submit{
      border: 0px;
      background: $background-color;
      color: $white;
      margin-left: -6px;
      height: 60px;
      width: 100px;
      line-height: normal;
    }
    span{
      font-size: 20px;
      color: $global-text-color;
      line-height: 30px;
      letter-spacing: 1px;
    }
    .subscribe_msg{
      color: #808080;
      margin-bottom: 15px;
      font-style: italic;
      line-height: 20px;
      padding-top: 12px;
    }
    .footer_msg{
      margin-top: 10px;
      color: $background-color;
    }
  }
}
.footer_main_content{
  margin-top: 25px;
}

.error_email{
  color: #BA2828;
}
.eid-design-title, .eid-design-price, .eid-title{
  color: $global-text-color !important;
}
.eid-view-more{
  border: none !important;
}
#show_return_policy{
  background-color: white;
}
.ui-dialog-title{
  color: white !important;
}

body.users, .user_panel {
  .profile-img {
    img {
      max-width: 190px;
      max-height: 190px;
    }
  }
}

.return_policy_ul b{
  color: black;
}
