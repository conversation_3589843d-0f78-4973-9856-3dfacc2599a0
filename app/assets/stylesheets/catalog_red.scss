// Place all the styles related to the store controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/


body.store {
  .footer-info {
    width: 1170px;
    padding:35px;
    background-color: #252525;
    @include box-shadow(0 0 2px 0 #6b6969);
    box-sizing:border-box;
    background-color: white;
    font-size: 14px;
    line-height: 18px;
    margin: 22px auto;
    text-align: justify;
    .price-list-wrapper{
      width: 470px;
      float: right;
      margin: 0 0 0 30px;
      table{
        margin-top: 15px;
        thead{
          tr{
            th{
              text-align: left;
              padding: 0 8px 8px 0;
            }
            .title-header{
              width: 330px;
            }
          }
        }
        tbody{
          tr{
            td{
              font-size: 12px;
              padding: 0 8px 8px 0;
              a{
                font-size: inherit;
              }
            }
          }
        }
      }
      .updated-date{
        font-size: 12px;
        padding: 10px 5px;
      }
    }
    .seo-list-table{
      width: 100%;
      tr{
        line-height: 20px;
        &:nth-child(even) {
          background-color: #decace;
        }
        td{
          border: 1px solid #eee;
          padding: 10px;
        }
      }
    }
    h1, h2, h3, h4, h5, h6{
      color: #303030;
      font-weight: bold;
      margin-bottom: 10px;
      margin-top: 3px;
    }
    h1{
      font-size: 19px;
    }
    h2{
      font-size: 18px;
    }
    h3 {
      font-size: 16px;
    }
    h4{
      font-size: 15px;
    }
    h5{
      font-size: 13px;
    }
    h6{
      font-size: 11px;
    }
    p{
      font-size: 14px;
      color: #303030;
      text-align: justify;
      line-height: 24px;
      a{
        font-weight: bold;
      }
    }
    b{
      color: #303030 !important;
      font-size: 14px;
    }
    a{
      color: $background-color !important;
      font-size: 14px;
    }
    b{
      font-weight: bold;
    }
    ul{
      font-size: 14px;
      list-style-type: disc;
      margin-left: 30px;
      margin-bottom: 0.6rem;
    }
    ol{
      list-style-type: decimal;
      margin-left: 30px;
      margin-bottom: 0.6rem;
    }
    ul > li, ol > li{
      font-size: 14px;
      text-align: justify;
      line-height: 25px;
      margin-left: 0;
    }
  }

  #top_content {
    &.read-more{
      height:18px;
      overflow:hidden;
    }
    text-align: justify;
    margin-top: 6px;
    h1,h2,h3,h4,h5,h6 {
      color: #d4d3d3;
    }
    p{
      padding-bottom: 0px;
    }
    #hidden_content {
      > * {
        color:#d4d3d3;
      }
    }
    > * {
      color: #d4d3d3;
    }
  }

  a#view-more-top-content,a#view-more-seo-post {
    font-size: 1em;
    text-align:center;
    color:#008CBA !important;
    padding-bottom:5px;
    &:hover {
      cursor: pointer;
    }
  }
  
  .announcement {
    background-color:#D24E52;
    padding:20px 60px;
    text-align:center;
    color:white;    
    margin:0 auto;
    margin-top:30px;
    font-family: $global-font-family;
    font-style:italic;
    font-size:1.2em;
  }

  .kind_selected {
    background-color:black;
    padding:10px;
  }
  
  
  .fixed_sub_nav {
    position:fixed;
    top:0;
    left:0;
    width:100%;
  }

  .absolute_sub_nav {
    position:absolute;
    left:0;
    top:70px;
  }
  

  .sub_nav {
      background-color: #393939;      
      color: white;
      width: 100%;
      z-index: 10;
      font-size:0.9em;
      padding-top:10px;
      padding-bottom:10px;

  }
  
  #filter-chip-box{
    #filter-chips{
      background: transparent;
      display: inline-block;
      border-radius: 2px;
      padding: 10px 0px;
      .chip{
        display: inline-block;
        font-size: 12px;
        line-height: 14px;
        background-color: $light-grey;
        color: $global-text-color;
        padding: 6px;
        margin-right: 5px;
        .closebtn{
          color: $global-text-color;
          cursor: pointer;
          padding-right: 3px;
        }
      }
    }
  }
  .btn-custom {
    transition: all 0.5s;
    cursor: pointer;
    padding: 12px 10px;
    display: inline-block;
    border: $light-border-box;
    &:hover{
      border-color: #ededed;
      background: #ededed;
    }
  }

  .flickr_pagination_store{
    text-align: center;
    border-top: $light-border-box;
    .previous_page, .next_page{
      display: inline-flex;
      color: $global-text-color !important;
      border-color: #585858;
      cursor: pointer;
    }
    .previous_page{
      margin-right: 8px;
      border: 0px !important;
      float: left;
      &:hover{
        background: none !important;
      }
      &:before{
        content: "";
        border-left: 2px solid #211e1e;
        border-top: 2px solid #382d2d;
        height: 10px;
        width: 10px;
        -webkit-transform: rotate(-45deg);
        -moz-transform: rotate(-45deg);
        -ms-transform: rotate(-45deg);
        -o-transform: rotate(-45deg);
        transform: rotate(-45deg);
        margin-top: 4px;
        margin-right: 5px;
      }
    }
    .next_page{
      margin-left: 8px;
      border: 0px !important;
      float: right;
      &:hover{
        background: none !important;
      }
      &:after{
        content: "";
        border-right: 2px solid #211e1e;
        border-top: 2px solid #382d2d;
        height: 10px;
        width: 10px;
        -webkit-transform: rotate(45deg);
        -moz-transform: rotate(45deg);
        -ms-transform: rotate(45deg);
        -o-transform: rotate(45deg);
        transform: rotate(45deg);
        margin-top: 4px;
        margin-left: 5px;
      }
    }
    em.current{
      background-color: #c7c7c7;
      color: #171717 !important;
      border-color: #5f5e5e;
      font-style: normal;
      font-weight: 700;
      margin: 0px 5px;
    }
    span.gap{
      margin: 0px 5px;
    }
    a{
      cursor: pointer;
    }
    a:hover{
      background-color: #c7c7c7;
      border-color: #5f5e5e;
      color: #0c0b0b !important;
    }
  }

  .content {
    margin: 0 auto;
    position: relative;
    width: 950px;
    padding: 20px;
    ul, li {
      margin:0;
    }
  
    .sub_nav_main_kind {
      overflow: hidden;
      padding: 4px 0 3px;
      width: 180px;
      background: url(image_path("subnavline.png")) no-repeat scroll right top transparent;
      text-align:center;
      li {
        padding: 2px 5px;
        list-style: none outside none;
        a:hover {
          background-color: black;
          padding: 10px;
        }
      }
    }
    
    .sub_nav_children {
      margin-left: 5px;
      li {
        float: left;
        line-height: 16px;
        overflow: hidden;
        white-space: nowrap;
        padding:5px 0px;
        width:80px;
        text-align:center;
        list-style: none outside none;
        a:hover {
          background-color: black;
          padding: 10px; 
        }
      }
    }
  }         
  
  #sort_tag {
    font-size:1em;
    margin-top:8px;
    text-align:center;
  }

   
  .filters {
    background: url(image_path("filter_bg.png")) repeat-x scroll left top #252525;
    border-top: 1px solid #454545;
    padding: 10px 0px 10px 0px;
    margin-bottom: 20px;
    height:20px;
    
    .filter {
      padding:0px 20px 0px 20px;
      border-right:1px solid #433F3F;
      text-align:center;
      min-height:80px;
      margin-right:0px; 
      
      #filter_tag {
        font-size:0.625em;
        margin-bottom:5px;
        text-align:center;
      }
      
      #slider-range {
        margin:10px 0px 0px 5px;
      }
    }
  }

  #bigfronttext {
    font-size:5em;
    float:right;
    width:40%;
    color:#fff;
  }

  .category_filters {
    width: 100%;
    height:40px;

    .sort_dropdown {
      padding-top:5px;
    }    
  }
    
  #img-load { 
    position:absolute; 
    background-color:transparent;
    left:640px;
    top:300px;
    background: none;
    animation: spin 4s ease-in-out infinite;
  }

  #clear-all-btn{
    display:none;
    .clear_all_filters{
      cursor:pointer;
      margin: 0px;
    }
  }
  
  .catalog2_facet{
    margin: auto;
    margin-top: 10px;
    .design_display_block{
      padding: 0 0 0 15px;
    }
    #design-row-block{
      border: $light-border-box;
    }
    .facet-search{
      border: $light-border-box;
    }
   .faceted_search_sort_block{
      color: $global-text-color;
      span{
        position: absolute;
        top: 9px;
        padding: 0px 6px;
        color: #959595;
      }
      .sort-by{
        color: $global-text-color;
        font-size: 16px;
        float: right;
        margin: 12px 10px;
        font-size: 14px;
      }
      select.change_facet, select.change_sort_facet{
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        width: 180px !important;
        height: 36px;
        padding: 6px;
        background-color: transparent;
        color: #303030;
        border: none;
        border-radius: 0px;
        cursor: pointer;
        &:focus{
          box-shadow: none;
        }
        option{
          color: #3c3b3b;
          background-color: #f5f5f5;
        }
      }
      &:after{
        content: "";
        position: absolute;
        right: 8px;
        top: 10px;
        border-right: $border-box;
        border-bottom: $border-box;
        width: 10px;
        height: 10px;
        transform: 90deg;
        transform: rotate(45deg);
      }
    }
    
    .showing_catalog2_facet_count{
      font-size: 13px;
      color: #959595 !important;
      padding: 10px 0;
    }
  
    .faceted_search_heading{
      font-size: 14px;
      float:left;
      margin-right: 10px;
      padding: 10px 0;
      font-weight: bold;
    }
   
  }
  
  .clear_all, .clear_all_filters{
    color: $global-text-color !important;
    background-color: $light-grey;
    font-weight: normal;
    text-decoration: none;
    line-height: 15px;
    border: none;
    font-size: 12px !important;
    margin-top: 6px;
    padding: 6px;
  }

  .faceted_search {
    padding: 0px !important;
    border: $light-border-box;
    .follow-feature{
      padding-bottom: 15px;
      border-bottom: 1px solid $light-grey;
      margin-bottom: 15px;
    }
    .search_block {
      border-bottom: 1px solid $light-grey;
      padding-left:10px;
      color: $global-text-color;
      .search_block_name {
        color: $global-text-color;
        padding: 10px 4px 10px 4px;
        cursor: pointer;
        text-transform: uppercase;
        font-size: 12px;
        .glyphicon{
          float: right;
          font-size: 11px;
          cursor: pointer;
          color: #9C9C9C;
        }
      }
      
      .width_94{
        width: 85% !important;
      }
      
      #cod_filter{
        height: 20% !important;
        margin-top: 5%;
        .pincode_error_message{
          color: red;
          display: none;
        }
      }

      #price_list{
        #price-slide-range{
          height: 3px;
          background: #a09c9c;
          border: none;
          .ui-slider-range.ui-widget-header{
            position: absolute;
            z-index: 1;
            font-size: .7em;
            display: block;
            background-position: 0 0;
            background: $light-red-bg;
            border: 1px solid $light-red-text;
          }
          .ui-slider-handle{
            border: 1px solid $light-red-text;
            background: $light-red-bg;
            font-weight: normal;
            color: #454545;
            height: 12px;
            width: 12px;
            border-radius: 50%;
            cursor: pointer;
            -webkit-transition: width .5s, height .5s;
            transition: width .5s, height .5s;
            &:hover{
              width: 14px;
              height: 14px;
            }
          }
        }
        .price-plus-sign{
          font-size: 10px;
          margin-left: -2px;
          vertical-align: middle;
        }
      }

      #price_list,#category_list,#designer_list,#rating_list,#discount_list,#availability_list, .facet_list {
        overflow:hidden;
        font-size:13px;
        padding-right: 4px;
        .label-white{
          color: $global-text-color;
        }
        input.price, input.discount {
          width: 20%;
        }

        .carat-text{
          text-transform: capitalize;
          color: #959595;
        }
        input[type = 'text'] {
          border-radius:3px;
          padding: 5px;
        }
        
        .form-group{
          .glyphicon{
            font-size: 11px;
            color: #aba8a8;
          }
          input.cod_filter_button, input.cod_filter_clear, input.carat_button, input.price_button, input.discount_button{
            background-color: #ededed;
            border: none;
            padding: 10px 8px;
          }
          input.search, input.carat, input.price, input.discount, input.pincode{
            background: none;
            border: none;
            border-bottom: 2px solid #8c8c8c;
            border-radius: 0px;
            padding-bottom: 2px;
            font-size: 14px;
            display: inline-block;
            &:focus{
              box-shadow: none;
              border-bottom: 2px solid $light-red-bg;
            }
          }
          input.price{
            padding-left: 15px;
          }
          span.currency-symbol{
            position: absolute;
            top: 14px;
            left: 2px;
            font-size: 10px;
            color: #afadad;
          }
        }

        ul.list {
          list-style-type:none;
          padding:0px;
          overflow: auto;
          height: 210px;
          &::-webkit-scrollbar{
            width: 4px;
          }
          &::-webkit-scrollbar-thumb{
            border-radius: 3px;
            background-color: #b11f2d;
          }
          &::-webkit-scrollbar-track{
            background-color: #eee;
          }
          label {
            font-weight: normal;
            font-size:12px;
            cursor:pointer;
            
            input[type='radio']{
              vertical-align:middle;
              margin-bottom:5px;
            }
            
            input[type='checkbox']{
              vertical-align:bottom;
            }
            .color-circle{
              border-radius: 50%;
              width: 18px;
              height: 18px;
              display: inline-block;
              position: relative;
              top: 2px;
              opacity: 0.8;
              border: 1px solid;
            }
            .multicolor-value{
              background: -webkit-linear-gradient(45deg, #edf1ee 0%, #8BC34A 25%, #FFEB3B 25%, #FFC107 40%, #FF5722 40%, #E91E63 60%, #673AB7 60%, #3F51B5 75%, #03A9F4 75%, #040404 100%);
              background: linear-gradient(45deg, #edf1ee 0%, #8BC34A 25%, #FFEB3B 25%, #FFC107 40%, #FF5722 40%, #E91E63 60%, #673AB7 60%, #3F51B5 75%, #03A9F4 75%, #040404 100%);
            }
          }
          .checkbox-fa{
            overflow-y: auto;
            width: 100%;
            height: 75%;
          }
        }
      }
      
      #availability_list {
        height:40px;
      }
    }
  }
  .store-breadcrumb {
    display: flex;
    margin-top: 5px;
    margin-bottom: 10px;
    li:not(:first-child):before {
      content: '/';
      margin-left: 0px;
      margin-right: 0px;
    }
    a {
      font-size: 12px !important;
      &:hover{
        color: $light-red-bg !important;
      }
    }
    li {
      font-size: 12px !important;
      display:inline-block;
    }
    .final{
      font-weight: bold;
    }
  }

  #store-top-info{
    padding-right: 0px;
    padding-left: 15px;
    .store-notice{
      background: #decacf;
      padding: 9px;
      border: 2px solid $background-color;
      color: $global-text-color;
      border-radius: 4px;
      span.glyphicon-gift{
        margin-right: 5px;
        color: $global-text-color !important;
      }
    }
    #store-top-content{
      margin-top: 15px;
      hr{
        margin-top: 6px;
        margin-bottom: 6px;
      }
      .bmgn-stepwise-desc{
        cursor: pointer;
        margin-top: 15px;
        #bmgn_stepwise_desc{
          .bmgn-header{
            padding: 10px;
            border: 1px solid #b7a1a3;
            background: linear-gradient(to left, #f5dcdc, #fff);
            .bmgn-label{
              color: $background-color;
            }
            .arrow{
              &:after{
                content: "";
                position: absolute;
                right: 18px;
                top: 78px;
                border-right: 1px solid $background-color;
                border-bottom: 1px solid $background-color;
                width: 10px;
                height: 10px;
                transform: rotate(45deg);
              }
            }
          }
          .bmgn-body{
            display: none;
            border: 1px solid #b7a1a3;
            height: 300px;
            overflow-y: scroll !important;
            .bmgn-steps{
              padding: 10px 15px;
              line-height: 20px;
              b{
                color: $global-text-color !important;
              }
              ul{
                list-style: circle;
                margin-left: 30px;
              }
              .bullet{
                display: list-item;
                margin: 0px 10px;
              }
              .bmgn-step1, .bmgn-step2, .bmgn-step3{
                margin: 20px 0px;
                &:not(:last-child){
                  border-bottom: 1px solid #cfcaca;
                  padding-bottom: 20px;
                }
                img{
                  border: $light-border-box;
                }
              }
              .li-image{
                list-style: none;
                margin: 5px 0px;
              }
              .bmgn-step3{
                .list-style-alpha{
                  list-style: none;
                  margin-left: 10px;
                }
                .bmgn-step3-desc{
                  margin: 10px 0px;
                }
              }
            }
          }
        }
      }
    }
  }
  
  .coupon_facet {
  width: 250px;
  padding: 5px;
  text-align: center;
  border: 2px dashed grey;
  margin: 0px 30px 20px 20px;
    
  p{
    font-weight:bold;
    }
  .coupon_facet_offer{
    font-size:20px;
    margin:5px 0px
    }
  .coupon_facet_text{
    margin-bottom:1px;
    }
  }
  
  .facet_follow_button{
    padding:0px;border-top:none;margin-bottom:10px
  }
  
  .facet_follow{
    margin-left:20px;
  }

  .category-top-seller-box{
    border: 1px solid #292828;
    .title_wrap{
      font-size: 16px;
      color: #fff;
      text-align: center;
      margin-top: 6px;
      text-transform: capitalize;
    }
  }
    #featured_product, #recently_viewed_product{
    margin-left: 30px;
  }
}

.follow-feature ul li {
  border-right: none !important;
}

.radio, .checkbox {
  label {
    min-height: 14px !important;
    font-size: 12px !important;
    padding-left: 20px;
  }
}

ul.listings li.listing-card .listing-social {
  .add_to_cart {
    padding: 14px;
    display: none;
  }
  span.discount {
    display: none !important;
    border-color: #E01F48;
    height: 30px;
    right: 15px;
    width: 90px;
    top: 45px;
    text-align: center;
    font-weight: bold;
    color: white;
    padding-top: 8px;
  }
  .other_designer_button{
    color: $white !important;
    background-color:#212121;
    margin-top: 19px;
    z-index:0;
    position: relative;
    text-align: center;
    font-size:17px;
  }
}

@media only screen and (max-width:400px) {
  ul.listings li.listing-card .listing-social {
    .add_to_cart {
      margin-top: -10%;
      -webkit-margin-top: -10%;
    }
    .discount {
      top: -20%;
      -webkit-top: -20%;
    }
  }
}

@media (min-width:400px) and (max-width:500px) {
  ul.listings li.listing-card .listing-social {
    .add_to_cart {
      margin-top: -17%;
      -webkit-margin-top: -17%;
    }
    .discount {
      top: -42%;
      -webkit-top: -42%;
    }
  }
}

@media (min-width:600px) and (max-width:700px) {
  ul.listings li.listing-card .listing-social {
    bottom: 24%;
  }
}

@media (min-width:700px) and (max-width:768px) {
  ul.listings li.listing-card .listing-social {
    bottom: 15%;
  }
}

.change_facet{
  color: $global-text-color;
  width: auto;
  // margin-right: 15px;
  margin-left: 18px;
  overflow: hidden;
  .rating_stars{
    padding-left: 0px;
    &:before{
      content: "\2605";
      display: inline-block;
      color: #ffb60c;
      font-size: 17px;
    }
    &:after{
      display: none;
    }
  }
}

.ratings-score{
  .star{
    img{
      background: none !important;
    }
  }
}

//faced super categories block css
#super-categories-facet{
  border: $light-border-box;
  .margin_bottom_10{
    text-align: center;
  }
  .super-cat-div{
    text-align: center;
    img{
      background: none;
      width: 70px;
      height: 70px;
    }
  }
  #super_category_list{
    text-align: center;
    .bx-wrapper{
      margin: auto !important;
      width: 100% !important;
      img{
        display: inline-block !important;
      }
      .bx-prev{
        left: -30px;
      }
      .bx-next{
        right: -30px;
      }
      .bx-controls-direction{
        a{
        top: 50%;
        z-index: 0;
        }
      }
      .bx-viewport{
        box-shadow: none;
        left: -18px;
        border: none;
        background: none;
        height: 100% !important;
      }
      ul{
        overflow: hidden;
        padding: 0px;
        .mCSB_container{
          height: 110px;

        }
        li{
          clear:none;
          .change_facet{
            text-align: center;
            color: black;
            label{
              display: inline-block;
              color: black;
              img{
                margin-top: 22px;
              }
            }
            label:after{
              margin-left: 58%;
            }
            label:before{
              margin-left: 58%;
            }
          }
        }
      }
    }
  }
}
.super-style{
  ul{
    padding-left: 0px !important;
    margin: 15px 0px auto;
    height: 138px !important;
    overflow: hidden;
    display: -webkit-flex;
    -webkit-justify-content: center;
    -moz-justify-content: center;
    display: flex;
    justify-content: center;
    .mCSB_container{
      height: 110px;
    }
    li{
      width: 138px;
      display: inline;
      text-align: center;
      margin-bottom: 0px !important;
      .change_facet{
        label{
          .super-cat-div{
            padding-top:22px;
          }
        }
        label:after{
          margin-left: 50%;
        }
        label:before{
          margin-left: 50%;
        }
      }
    }
  }
}
// #recently-viewed-product-carousel{
//   width: 95% !important;
//   margin: auto;
// }