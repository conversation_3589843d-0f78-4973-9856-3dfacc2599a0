/* Hierarchical Menu Styles */

.super-menu-item {
  position: relative;
  
  .super-menu-link {
    font-weight: bold;
    font-size: 16px;
    color: #333;
    text-transform: uppercase;
    letter-spacing: 1px;
    
    &:hover {
      color: #e91e63;
      text-decoration: none;
    }
  }
  
  .megamenu-box {
    .menu-section {
      padding: 20px 15px;
      border-right: 1px solid #eee;
      
      &:last-child {
        border-right: none;
      }
      
      .menu-title {
        font-size: 14px;
        font-weight: 600;
        color: #666;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 2px solid #e91e63;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
      
      .menu-column-wrapper {
        list-style: none;
        padding: 0;
        margin: 0;
        
        .menu-column-list {
          margin-bottom: 15px;
          
          .menu-column-link {
            font-size: 13px;
            font-weight: 500;
            color: #444;
            text-decoration: none;
            display: block;
            margin-bottom: 8px;
            
            &:hover {
              color: #e91e63;
            }
          }
          
          .menu-item-wrapper {
            list-style: none;
            padding: 0;
            margin: 0;
            padding-left: 10px;
            
            li {
              margin-bottom: 5px;
              
              a {
                font-size: 12px;
                color: #666;
                text-decoration: none;
                
                &:hover {
                  color: #e91e63;
                }
              }
            }
          }
        }
      }
    }
  }
  
  &:hover .megamenu-box {
    display: block;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .super-menu-item {
    .megamenu-box {
      .menu-section {
        padding: 15px 10px;
        border-right: none;
        border-bottom: 1px solid #eee;
        
        .menu-title {
          font-size: 13px;
        }
      }
    }
  }
}

/* Animation for smooth transitions */
.megamenu-box {
  transition: all 0.3s ease-in-out;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
}

.super-menu-item:hover .megamenu-box {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* Ensure backward compatibility with existing menu styles */
.menu-list:not(.super-menu-item) {
  /* Existing menu styles remain unchanged */
}
