.search_container {
  display: flex;
  flex-direction: row;
}

.search_params {
  flex: 1;
  margin: 0 10px;
}

.centered-form {
  text-align: center;
  margin: 0 auto;
  width: 50%;
}

#order_numbers {
  width: 50%;
  height: 50px;
  color: black;
}

.custom-submit-button {
  width: 50%;
}

.csv_upload {
  margin-left: 43%;
}

.custom-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.custom-container .left-side-btn form {
  display: flex;
  margin-bottom: 20px;
  align-items: center;
}

.custom-container .left-side-btn form input {
  margin: 0 10px;
  height: 40px;
}

.custom-container .left-side-btn form select {
  margin-right: 20px;
}

.custom-container .left-side-btn form .btn.btn-primary {
  width: 100%;
  max-width: 100px;
}

.custom-container .right-side-btn form {
  display: flex;
}

.export-btn.btn.btn-primary {
  width: 100%;
  max-width: 117px;
  margin: 15px 0 0 auto;
  height: 35px;
  position: absolute;
  right: 0;
  top: 0;
}

.right-side-btn .btn.btn-primary:nth-child(1) {
  width: 100%;
  max-width: 110px;
}

.custom-data-table {
  width: 100%;
  margin: 30px 0 50px;
  border-top: 1px solid aliceblue;
  padding: 0;
  position: relative;
  z-index: 1;

}

.custom-data-table thead th {
  padding: 10px 8px;
  font-size: 14px;
  font-weight: bold;
  border: 1px solid;
  position: sticky;
  top: 0;
  background: #000;
}
.custom-data-table thead .img-box {
  width: 7%;
}
.custom-data-table thead .list-item {
  width: 10%;
}
.custom-data-table tbody td {
  font-size: 16px;
  padding: 10px;
  border: 1px solid;
}

.custom-data-table tr {
  border: 1px solid;
}

.custom-table {
  border-top: 1px solid #fff;
  margin-top: 20px;
  position: relative;
  ul {
    padding: 0;
    list-style: none;
    font-size: 13px;
  }
}

.custom-data-table tbody td img {
  width: 100%;
  height: 105px;
}

.custom-table .pagination {
  position: absolute;
  bottom: -54px;
  right: 0;
}

.custom-table .pagination .previous_page.disabled {
  color: #777;
  padding: 5px 10px;
  font-size: 16px;
  font-weight: 600;
}

.custom-table .pagination .current {
  background: #428bca;
  padding: 5px 10px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 100px;
}

.custom-table .pagination a {
  color: #fff;
  font-size: 16px;
  padding: 5px 10px;
}

.div-input-field1 {
  display: flex;
  justify-content: space-between;
  width: 80%;
}

.div-input-field2 {
  display: flex;
  margin-top: 25px;
  align-items: center;
}

.div-input-field2 .input1 {
  margin-right: 30px;
}

.leftside-btn {
  width: 100%;
}

.div-input-field {
  position: relative;
}

input.btn.btn-primary.submit-data {
  width: 100%;
  max-width: 165px;
  padding: 10px 0;
  height: 10%;
}

.div-input-field label {
  margin-right: 5px;
}

input#designer_id,
input#design_id,
input#end_date,
input#start_date,
input#designer_name {
  height: 35px;
  padding: 0 10px;
  color: #000
}
.design_block {
  display: flex;
  width: 100%;
  align-items: center;
  padding: 20px 0;
}
.design_block textarea#design_ids {
  width: 100%;
  max-width: 400px;
  height: 35px;
}
.design_block label {
  font-size: 16px;
  margin-right: 20px;
}
.design_block .btn-success {
  padding: 10px 50px;
  font-size: 16px;
  font-weight: 600;
}
@media screen and (max-width:1499px) {
  .custom-data-table thead th {
    padding: 8px 50px;
  }
  
}
.input1 {
  display: flex;
  flex-direction: column;
}

span.next_page.disabled {
  color: #777;
}
.main-class {
  display: flex;
  justify-content: space-between;
}

form.main-form {
  width: 30%;
}
/* campaign page css */
.campaign_page
.new_seller_button {
  display: flex;
  justify-content: end;
  margin: 20px 0 35px;
}
.campaign_page .participation {
  margin-bottom: 60px;
}
.campaign_page
.table-responsive,th,td{
  border-bottom: none;
}
.campaign_page
.table-responsive td{
  border: 1px solid;
  font-size: 16px;
  padding: 15px 10px;
}
.campaign_page .btn-success {
  padding: 10px 25px;
  font-size: 16px;
}
.campaign_page .table-responsive td:last-child {
  text-align: center;
}
.campaign_page
.table-responsive th {
  font-size: 18px;
  text-align: center;
  border: 1px solid;
}
.campaign_page .btn-sm {
  padding: 5px 15px;
  font-size: 13px;
  line-height: 1.5;
  border-radius: 3px;
  font-weight: bold;
}
.campaign_page
.table-responsive table{
  margin-bottom: 0;
}
.campaign_page
.table-responsive table{
  border: 1px solid #fff;
}
.campaign_page .pagination {
  display: flex;
  justify-content: center;
}
.campaign_page .pagination a{
  padding: 0 5px;
  color: #fff;
}
.campaign_page .pagination a .current {
  color: #fff;
  padding-left: 5px;
  font-weight: 600;
  border-bottom: 1px solid;
}
.campaign_page .campaign_header {
  font-size: 40px;
  font-weight: bold;
  position: relative;
}
.campaign_page em.current {
  color: #428bca;
  padding: 0 5px;
}
.campaign_page .campaign_header:after{
  position: absolute;
  content: "";
  height: 1px;
  width: 250px;
  background: #fff;
  bottom: -8px;
  left: 0;
}
.campaign_participants {
  text-align: center;
}
.campaign_page 
.table-responsive th:first-child {
  width: 10%;
}
.campaign_page .filters {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 40px 0;
}
.seller_admin_portal .pagination {
  display: flex;
  justify-content: center;
  a {
    padding: 0 5px;
}
em.current {
  color: maroon;
  padding: 0 5px;
  font-weight: 600;
}
}
.date_filter {
  margin-bottom: 25px;
  text-align: end;
  .form-group {
    margin-right: 10px;
}
input.btn.btn-primary {
  width: 100%;
  max-width: 100px;
  margin: 0;
}
 input#end_date, input#start_date {
  width: 100%;
  max-width: 125px;
}
}
.page-title .title_left h4 {
  font-size: 24px;
  padding: 20px 0;
  margin: 0 0 0 30px;
}
.particate_msg p {
  font-size: 16px;
  text-align: center;
}
.qc-rate-form {
  #designer_ids {
    resize: vertical;
  }

  .qc-rate-container {
    display: flex;
    flex-wrap: wrap;
  }

  .radio-group {
    display: flex;
    flex-direction: row;
    
    .radio-option {
      display: flex;
      align-items: center;
      
      input[type="radio"] {
        display: none;
        
        &:checked + label {
          background-color: #0056b3;
          border: 1px solid #dadada;
        }
      }
      
      label {
        display: inline-block;
        background-color: #4ca3ff;
        color: white;
        padding: 0.75rem 1.5rem;
        border: 1px solid #151515;
        cursor: pointer;
        font-size: 1.25rem;
        text-align: center;
        min-width: 70px;
      }
    }
  }
  
  .qc-rate-submit {
    background-color: #4ca3ff;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #0056b3;
    }
  }

  @media (max-width: 992px) {
    .qc-rate-selector {
      padding-right: 0px;
      width: auto;
    }

    .qc-rate-container {
      margin-top: 5px;
    }

    .radio-group {
      display: flex;
      flex-wrap: wrap;
    }
  }
}

.designer-table {
  .designer-search {
    margin-bottom: 5px;
    display: flex;
    flex-wrap: wrap;
    width: 100%;

    .designer-input-container {
      max-width: 50em;
      min-width: 50%;
    }

    .qc-option-container {
      min-width: 20%;
    }

    .designer-submit-container {
      .designer-submit {
        border-radius: 0px;
      }
    }
  }

  @media (max-width: 992px) {
    .designer-search {
      margin-top: 5px;
    }
  }

  @media (max-width: 768px) {
    .qc-option-container {
      min-width: 30%;
    }
  }

  @media (max-width: 580px) {
    .designer-search div {
      width: 100%;
      margin-top: 5px;
    }
  }
}