@import "font-awesome";

#design_all_images_modal .modal-dialog {
    width: 100%;
    height: 90vh;
    position: absolute;
    margin: 0;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    justify-content: center;
}

.modal-content {
    width: 80%;
    margin-left: 0%;
    height:100%;
    display:flex;
    flex-direction: column;
}

.modal-content .design-images-header {
    height: 10%;
}

.design-images-header .modal-title {
    color: #757575;
}

.design-image-list .slide {
    display: flex;
    width: 50%;
    align-items: center;
}

.modal-content .design-image-list {
    height: 90%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0px;
    overflow: scroll;
}



#design_all_images_modal .carousel,
#design_all_images_modal .carousel-inner ,
#design_all_images_modal .carousel-item {
    height: 100%;
}

.modal-content .design-image-list .carousel-item img{
    margin: auto;
    height: 100%;
    width: auto;
    object-fit: cover;
}

.modal-content .design-image-list a{
    all: unset;
    font-size: 2rem;
    color: #757575;
}

.modal-content .design-image-list a:hover{
    cursor:pointer;
}

.btn.show_all_images {
    margin-top: 1rem;
}

.section1-table {
  border: none;
  font-size: 25pt;
  width: 100%;
  border-collapse: collapse;
}

.big-label {
  font-size: 30pt;
  font-weight: bold;
}

.small-label {
  font-size: 18pt;
  font-weight: bold;
}

.center-content {
  text-align: center;
}

.barcode-img {
  width: 40%;
}

.shipment-number {
  margin-top: 5px;
}