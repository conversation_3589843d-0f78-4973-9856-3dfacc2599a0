$white: #FFFFFF;
$primary: #670e19;
$primary_dark: #600c16;
$primary_light: #b11f2b;
$primary_faded: #7b2134;

.coupon_caption{
  font-size: 15px;
  font-weight: bold;
}

.coupon_table
{
 table{
  width: 80%;
  border-collapse: collapse;
  border-spacing: 0;
  border: 2px solid black;

th {
  padding: 20px;
  border: 2px solid black;
  background-color: #B0E0E6;
  font-size:18px;
  }

td  {
  padding: 10px;
  border: 2px solid black;
  font-size: 16px;
    }
  }
}

.icon-style {
  vertical-align: middle;

  i {
    float: left;
  }

  .target {
    max-width: 60%;
    display: inline-block;
    float: left;
    padding-left: 22px;

    .days {
      color: #6f7372 !important;
      font-size: small;
    }
  }

  .fa.status {
    font-size: xx-large;
  }

  .fa.metrics {
    font-size: 50px;
  }
}

.success-icon {
  @extend .icon-style;
  color: #1abc9c;
}

.fail-icon, .warning-icon {
  @extend .icon-style;
  color: #d9534f;
}

.alert-icon {
  @extend .icon-style;
  color: #f39c12;
}

.tile_stats_count .left_metric {
  height: 102px;
  width: 10%;
  float: left;
  border-left: 2px solid #ADB2B5;
  margin-top: 10px;
}

.fixed-header-class {
  .fixed_headers {
    table-layout: fixed;
    border-collapse: collapse;

    thead {
      tr {
        display: flex;
        position: relative;
      }
    }
    tbody {
      display: block;
      overflow: auto;
      width: 100%;
      max-height: 500px;
    }
  }
}

#designer_help_center {
  font-family: 'book antiqua, palatino, serif';
  font-size: 12pt;
  color: black;
  .question_hr {
    margin-bottom: 20px;
    border-top: 1px dashed grey;
    margin-top: 0px;
  }
  ul.faq-ul {
    padding-left: 0;
    list-style-type: none !important;

    li {
      padding-bottom: 3px;
      font-size: small;
    }
  }
  h5 {
    padding-left: 10px;
  }
  h4 {
    font-weight: bold;
    color: black;
  }
  .panel {
    padding: 0px 10px 0px 10px;
    background: #ddecf3;
  }
  a {
    cursor: pointer;
  }
  #faq_accordion {
    table td {
      padding: 2px;
    }
  }
}
.link {
  cursor: pointer;
}
.nav-sm .container.body .left_col {
  width: 70px;
  padding: 0;
  z-index: 9999;
  position: absolute;
}
.nav-sm .hidden-small {
  visibility: hidden;
}
.nav-sm .container.body .right_col {
  padding: 10px 20px;
  margin-left: 70px;
  z-index: 2;
}
.nav-sm .navbar.nav_title {
  width: 70px;
}
.nav-sm .navbar.nav_title a span {
  display: none;
}
.nav-sm .navbar.nav_title a i {
  font-size: 27px;
  margin: 13px 0 0 3px;
}
.site_title i {
  border: 1px solid #EAEAEA;
  padding: 5px 6px;
  border-radius: 50%;
}
.nav-sm .main_container .top_nav {
  display: block;
  margin-left: 70px;
  z-index: 2;
}
.nav-sm .nav.side-menu li a {
  text-align: center !important;
  font-weight: 400;
  font-size: 10px;
  padding: 10px 5px;
}
.nav-sm .nav.child_menu li.active,
.nav-sm .nav.side-menu li.active-sm {
  border-right: 5px solid $primary_light;
}
.nav-sm ul.nav.child_menu ul,
.nav-sm .nav.side-menu li.active-sm ul ul {
    position: static;
    width: 200px;
    background: none;
}
.nav-sm > .nav.side-menu > li.active-sm > a {
  color: $primary_light !important;
}
.nav-sm .nav.side-menu li a i.toggle-up {
  display: none !important;
}
.nav-sm .nav.side-menu li a i {
  font-size: 25px !important;
  text-align: center;
  width: 100% !important;
  margin-bottom: 5px;
}
.nav-sm ul.nav.child_menu {
  left: 100%;
  position: absolute;
  top: 0;
  width: 210px;
  z-index: 4000;
  background: #3E5367;
  display: none;
}
.nav-sm ul.nav.child_menu li {
  padding: 0 10px;
}
.nav-sm ul.nav.child_menu li a {
  text-align: left !important;
}
.nav-sm .profile {
  display: none;
}
.menu_section {
  margin-bottom: 35px;
}
.menu_section h3 {
  padding-left: 23px;
  color: #fff;
  text-transform: uppercase;
  letter-spacing: .5px;
  font-weight: bold;
  margin-bottom: 0;
  margin-top: 0;
  text-shadow: 1px 1px #000;
}
.menu_section >ul {
  margin-top: 10px;
}
.profile_pic {
  width: 35%;
  float: left;
}
.img-circle.profile_img {
  width: 75%;
  margin-left: 15%;
  z-index: 1000;
  position: inherit;
  margin-top: 20px;
  border: 1px solid rgba(52, 73, 94, 0.44);
  padding: 3px;
  background: #fff;
  height: 56px;
}
.img-circle.no-image {
  padding-top: 17px;
  font-weight: bold;
}
.profile_info {
  padding: 4px 10px 10px;
  width: 65%;
  float: left;
  #rating_div {
    color: gold;
    margin-top: -15px;
    .lead {
      margin-bottom: 0px;
    }
    .glyphicon {
      font-size: large;
    }
    .glyphicon-star {
      color: rgba(255, 215, 0, 0.86);
    }
  }
  span {
    font-size: 13px;
    line-height: 30px;
    color: #BAB8B8;
  }
  h2 {
    font-size: 14px;
    color: #ECF0F1;
    margin: 0;
    font-weight: 300;
    width: 135px;
    word-wrap: break-word;
  }
}
.profile.img_2 {
  text-align: center;
}
.profile.img_2 .profile_pic {
  width: 100%;
}
.profile.img_2 .profile_pic .img-circle.profile_img {
  width: 50%;
  margin: 10px 0 0;
}
.profile.img_2 .profile_info {
  padding: 15px 10px 0;
  width: 100%;
  margin-bottom: 10px;
  float: left;
}
.main_menu span.fa {
  float: right;
  text-align: center;
  margin-top: 5px;
  font-size: 10px;
  min-width: inherit;
  color: #C4CFDA;
}
.active a span.fa {
  text-align: right !important;
  margin-right: 4px;
}
.nav-sm .menu_section {
  margin: 0;
}
.nav-sm span.fa, 
.nav-sm .menu_section h3 {
  display: none;
}
.nav-sm li li span.fa {
  display: inline-block;
}
.nav_menu {
  float: left;
  background: #EDEDED;
  border-bottom: 1px solid #D9DEE4;
  margin-bottom: 10px;
  width: 100%;
  position: relative;
}
@media (min-width: 480px) {
  .nav_menu {
    position: static;
  }
}
.nav-md .container.body .col-md-3.left_col {
  width: 230px;
  padding: 0;
  position: absolute;
  display: flex;
}
body .container.body .right_col {
  background: #F7F7F7;
  fieldset {
    border: 1px groove #ddd !important;
    padding: 0 1.4em 1.4em 1.4em !important;
    margin: 0 0 1.5em 0 !important;
    -webkit-box-shadow:  0px 0px 0px 0px #000;
            box-shadow:  0px 0px 0px 0px #000;
  }
  legend {
    width:inherit;
    padding:0 10px;
    border-bottom:none;
  }
  .notice {
    margin-left:25px;
  }
  a {
    cursor: pointer;
    color: #09407b;
  }

  .text-medium {
    font-size: 14px;
  }
  .form-horizontal {
    label {
      vertical-align: -webkit-baseline-middle;
    }
  }
  .field_with_errors {
    input, select, textarea {
      border:3px solid #E74C3C;
    }
    .message {
      margin-top: 0px !important;
      color: #D00;
    }
    display: inline;
  }

  #error_explanation {
    padding: 7px;
    color: #E74C3C;
    ul li {
      font-size:1em;
      font-weight:bold;
    }
  }
  .table.text-center th {
    text-align: center;
  }
}
#login-form{
  .form-style{
    word-wrap: break-word;
    overflow:hidden;
  }

  .row {
    padding-top: 20px
  }

  h2 {
    text-align: center;
    padding-bottom : 10px;
  }

  li {
    color: #ff0000 ;
  }

  .link {
    text-decoration: underline;
  }

  .pane {
    margin-top: 20px;
    border-radius: 20px;
    padding-bottom: 20px;
  }

  .colorgraph {
    height: 3px;
    border-top: 0;
    padding: 0px;
    background: #c4e17f;
    border-radius: 5px;
    background-image: -webkit-linear-gradient(left, #c4e17f, #c4e17f 12.5%, #f7fdca 12.5%, #f7fdca 25%, #fecf71 25%, #fecf71 37.5%, #f0776c 37.5%, #f0776c 50%, #db9dbe 50%, #db9dbe 62.5%, #c49cde 62.5%, #c49cde 75%, #669ae1 75%, #669ae1 87.5%, #62c2e4 87.5%, #62c2e4);
    background-image: -moz-linear-gradient(left, #c4e17f, #c4e17f 12.5%, #f7fdca 12.5%, #f7fdca 25%, #fecf71 25%, #fecf71 37.5%, #f0776c 37.5%, #f0776c 50%, #db9dbe 50%, #db9dbe 62.5%, #c49cde 62.5%, #c49cde 75%, #669ae1 75%, #669ae1 87.5%, #62c2e4 87.5%, #62c2e4);
    background-image: -o-linear-gradient(left, #c4e17f, #c4e17f 12.5%, #f7fdca 12.5%, #f7fdca 25%, #fecf71 25%, #fecf71 37.5%, #f0776c 37.5%, #f0776c 50%, #db9dbe 50%, #db9dbe 62.5%, #c49cde 62.5%, #c49cde 75%, #669ae1 75%, #669ae1 87.5%, #62c2e4 87.5%, #62c2e4);
    background-image: linear-gradient(to right, #c4e17f, #c4e17f 12.5%, #f7fdca 12.5%, #f7fdca 25%, #fecf71 25%, #fecf71 37.5%, #f0776c 37.5%, #f0776c 50%, #db9dbe 50%, #db9dbe 62.5%, #c49cde 62.5%, #c49cde 75%, #669ae1 75%, #669ae1 87.5%, #62c2e4 87.5%, #62c2e4);
  }
}
#order_quality_report {
  table {
    display: block;
    overflow-x: auto;
  }
}
.nav-md .container.body .right_col {
  padding: 10px 20px 0;
  margin-left: 230px;
}
.nav_title {
  width: 230px;
  float: left;
  background: #EDEDED;
  border-radius: 0;
  height: 57px;
}
@media (max-width: 991px) {
  .nav-md .container.body .right_col, .nav-md .container.body .top_nav {
    width: 100%;
    margin: 0;
  }
  .nav-md .container.body .col-md-3.left_col {
    display: none;
  }
  .nav-md .container.body .right_col {
    width: 100%;
    padding-right: 0
  }
  .right_col {
    padding: 10px !important;
  }
}
@media (max-width: 1200px) {
  .x_title h2 {
    width: 62%;
    font-size: 17px;
  }
  .tile, .graph {
    zoom: 85%;
    height: inherit;
  }
}
@media (max-width: 1270px) and (min-width: 192px) {
  .x_title h2 small {
    display: none
  }
}

/**  ------------------------------------------  **/

.blue {
  color: #3498DB;
}
.purple {
  color: #9B59B6;
}
.green {
  color: $primary_light;
}
.light-green {
  color: #28a745;
}
.aero {
  color: #9CC2CB;
}
.red {
  color: #E74C3C;
}
.dark {
  color: #34495E;
}
.dark-red{
  color: $primary;
}
.states_css {
  .replacement_pending, .pending {
    color: #d9534f;
    font-size: medium;
  }
  .dispatched, .completed {
    color: #5cb85c;
    font-size: medium;
  }
  .canceled, .rto {
    color: #777;
    font-size: medium;
  }
  .critical {
    color: #8a6d3b;
    font-size: medium;
  }
  .pickedup {
    color: #8a6d3b;
    font-size: medium;
  }
  .buyer_returned {
    color: #8a6d3b;
    font-size: medium;
  }
}
.border-blue {
  border-color: #3498DB !important;
}
.border-purple {
  border-color: #9B59B6 !important;
}
.border-green {
  border-color: $primary_light !important;
}
.border-aero {
  border-color: #9CC2CB !important;
}
.border-red {
  border-color: #E74C3C !important;
}
.border-dark {
  border-color: #34495E !important;
}
.border-panel {
  border-color: #0a4e7b !important;
}
.designer_orders {
  .items-header-box {
    background-color: #ececec;
  }
  .note_highlight {
    background-color: rgba(244, 124, 98, 0.71);
    color: black;
    padding: 3px;
    font-weight: bold;
  }
}

.bg-white {
  background: #fff !important;
  border: 1px solid #fff !important;
  color: #73879C;
}
.bg-green {
  background: $primary_light !important;
  border: 1px solid $primary_light !important;
  color: #fff;
}
.bg-red {
  background: #E74C3C !important;
  border: 1px solid #E74C3C !important;
  color: #fff;
}
.bg-blue {
  background: #3498DB !important;
  border: 1px solid #3498DB !important;
  color: #fff;
}
.bg-orange {
  background: #F39C12 !important;
  border: 1px solid #F39C12 !important;
  color: #fff;
}
.bg-purple {
  background: #9B59B6 !important;
  border: 1px solid #9B59B6 !important;
  color: #fff;
}
.bg-blue-sky {
  background: #50C1CF !important;
  border: 1px solid #50C1CF !important;
  color: #fff;
}
.container {
  width: 100%;
  padding: 0
}
.navbar-nav>li>a, .navbar-brand, .navbar-nav>li>a {
  color: #fff !important;
}
.top_nav .nav>li>a:focus, .top_nav .nav>li>a:hover, .top_nav .nav .open>a, .top_nav .nav .open>a:focus, .top_nav .nav .open>a:hover {
  background: #D9DEE4;
}
body {
  color: #394450;
  background: $primary;
  /*#ECF0F1; #FCFCFC*/
  font-family: "Helvetica Neue", Roboto, Arial, "Droid Sans", sans-serif;
  font-size: 13px;
  font-weight: 400;
  line-height: 1.471;
}
.main_container {
  #header_logo {
    background: url("/assets/logo-red.png");
    float: left;
    height:54px;
    width:125px;
    display:inline;
    position: relative;
    padding:0px;
    margin-left: 15%;
    background-size: 125px;
    background-repeat: no-repeat;
    a {
      display: block;
    }
  }
  .image-circle{
    background: url("/assets/mirraw_logo.png");
    border-radius: 5px;
  }
  .logged_out_header {
    height: 75px;
    display: block;
    padding-top: 1px;
    background: #ededec;
    ul {
      list-style: none;
    }
    li.logo {
      margin-left: -15px;
      border-right: none;
    }
  }
  .top_nav {
    display: block;
    margin-left: 230px;
  }
  #login-form{
    .pane {
      border-radius: 20px;
      border: 2px solid #ada5a5;
      padding: 20px 20px 20px 20px;
      color: #fff;
      box-shadow: 6px 6px 3px #242e38;

      a {
        cursor: pointer;
      }
    }
  }
}
.no-padding {
  padding: 0 !important;
}
.page-title {
  width: 100%;
  height: 65px;
  padding: 10px 0;
  .title_left {
    width: 45%;
    float: left;
    display: block;
    h3 {
      margin: 9px 0;
    }
  }
  .title_right {
    width: 55%;
    float: left;
    display: block;
    .pull-right {
      margin: 10px 0;
    }
  }
}
.fixed_height_320 {
  height: 320px;
}
.fixed_height_390 {
  height: 390px;
}
.fixed_height_200 {
  height: 200px;
}
.overflow_hidden {
  overflow: hidden
}
.progress-bar-dark {
  background-color: #34495E !important;
}
.progress-bar-gray {
  background-color: #BDC3C7 !important;
}
table.no-margin .progress {
  margin-bottom: 0;
}
.main_content {
  padding: 10px 20px;
}
.col-md-55 {
  width: 50%;
  margin-bottom: 10px;
}
@media (max-width: 992px) {
  .tile_stats_count {
    margin-bottom: 10px;
    border-bottom: 1px solid #D9DEE4;
    padding-bottom: 10px;
  }
}
@media (min-width: 992px) and (max-width: 1100px) {
  .tile_stats_count .count {
    font-size: 35px !important;
  }
}
@media(max-width:768px) {
  .tile_stats_count .count {
    font-size: 30px !important;
  }
  .tile_stats_count .right span {
    font-size: 12px;
  }
}
@media (min-width: 768px) {
  .col-md-55 {
    width: 20%;
  }
}
@media (min-width: 992px) {
  .col-md-55 {
    width: 20%;
  }
}
@media (min-width: 1200px) {
  .col-md-55 {
    width: 20%;
  }
}
@media (min-width: 192px) and (max-width: 1270px) {
  table.tile_info span.right {
    margin-right: 7px;
    float: left;
  }
}
.center-margin {
  margin: 0 auto;
  float: none!important;
}
.col-md-55, .col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xs-12, .col-sm-12, .col-md-12, .col-lg-12 {
  position: relative;
  min-height: 1px;
  float: left;
  padding-right: 10px;
  padding-left: 10px;
}
.row {
  margin-right: -10px;
  margin-left: -10px;
}
.grid_slider .col-md-6 {
  padding: 0 40px;
}
.col-md-6{
  overflow-x: auto;
}
h1, .h1, h2, .h2, h3, .h3 {
  margin-top: 10px;
  margin-bottom: 10px;
}
a {
  color: $primary_light;
  text-decoration: none;
}
a, a:visited, a:focus, a:active, :visited, :focus, :active, .btn:focus, .btn:active:focus, .btn.active:focus, .btn.focus, .btn:active.focus, .btn.active.focus {
  outline: 0;
}
a:hover, a:focus {
  text-decoration: none
}
.navbar {
  margin-bottom: 0
}
.navbar-header {
  background: #34495E;
}
.navbar-right {
  margin-right: 0;
}
.navbar-right:last-child{
  margin-right: 0 !important;
}
.top_nav {
  .navbar-right {
    margin: 0;
    width: 70%;
    float: right;
    li {
      display: inline-block;
      float: right;
      position: static;
    }
  }
  .dropdown-menu li {
    width: 100%;
    a {
      width: 100%;
      padding: 12px 20px;
    }
  }
  li a i {
    font-size: 15px
  }
}
@media (min-width: 480px) {
  .top_nav .navbar-right li {
    position: relative;
  }
}
.navbar-static-top {
  position: fixed;
  top: 0;
  width: 100%;
}
.sidebar-header {
  border-bottom: 0;
  margin-top: 46px;
}
.sidebar-header:first-of-type {
  margin-top: 0
}
.nav.side-menu> li {
  position: relative;
  display: block;
  cursor: pointer;
}
.nav.side-menu> li > a {
  margin-bottom: 6px;
}
.nav.side-menu> li > a:hover {
  color: #F2F5F7 !important;
}
.nav.side-menu>li>a:hover, .nav>li>a:focus {
  text-decoration: none;
  background: transparent;
}
.nav.child_menu li:hover,
.nav.child_menu li.active {
  background-color: rgba(255, 255, 255, 0.06);
}
.nav.child_menu li {
  padding-left: 36px;
}
.nav-md ul.nav.child_menu li:before {
  background: $primary_faded;
  bottom: auto;
  content: "";
  height: 8px;
  left: 23px;
  margin-top: 15px;
  position: absolute;
  right: auto;
  width: 8px;
  z-index: 1;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
}
.nav-md ul.nav.child_menu li:after {
  border-left: 1px solid $primary_faded;
  bottom: 0;
  content: "";
  left: 27px;
  position: absolute;
  top: 0;
}
.nav.side-menu>li>a, .nav.child_menu>li>a {
  color: #E7E7E7;
  font-weight: 500;
}
.nav.child_menu li li:hover, 
.nav.child_menu li li.active {
  background: none;
}
.nav.child_menu li li a:hover, 
.nav.child_menu li li a.active {
  color: #fff;
}
.nav>li>a {
  position: relative;
  display: block;
  padding: 13px 15px 12px;
}
.nav.side-menu> li.current-page, .nav.side-menu> li.active {
  border-right: 5px solid $primary_light;
}
.nav li.current-page {
  background: rgba(255, 255, 255, 0.05);
}
.nav li li li.current-page {
  background: none;
}
.nav li li.current-page a {
  color: #fff;
}
.nav.side-menu> li.active > a {
  text-shadow: rgba(0, 0, 0, 0.25) 0 -1px 0;
  background: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(0%, #5b6479), color-stop(100%, #4c5566)), #686e78;
  background: -webkit-linear-gradient($primary_dark, $primary_dark), $primary;
  background: -moz-linear-gradient($primary_dark , $primary_dark), $primary;
  background: -o-linear-gradient($primary_dark, $primary_dark), $primary;
  background: linear-gradient($primary_dark, $primary_dark), $primary;
  -webkit-box-shadow: rgba(0, 0, 0, 0.25) 0 1px 0, inset rgba(255, 255, 255, 0.16) 0 1px 0;
  -moz-box-shadow: rgba(0, 0, 0, 0.25) 0 1px 0, inset rgba(255, 255, 255, 0.16) 0 1px 0;
  box-shadow: rgba(0, 0, 0, 0.25) 0 1px 0, inset rgba(255, 255, 255, 0.16) 0 1px 0;
}
.navbar-brand, .navbar-nav>li>a {
  font-weight: 500;
  color: #ECF0F1 !important;
  margin-left: 0 !important;
  line-height: 32px;
}
.site_title {
  text-overflow: ellipsis;
  overflow: hidden;
  font-weight: 400;
  font-size: 22px;
  width: 100%;
  color: #ECF0F1 !important;
  margin-left: 0 !important;
  line-height: 59px;
  display: block;
  height: 55px;
  margin: 0;
  padding-left: 10px;
}
.site_title:hover, .site_title:focus {
  text-decoration: none
}
.nav.navbar-nav>li>a {
  color: #515356 !important;
}
.nav.top_menu>li>a {
  position: relative;
  display: block;
  padding: 10px 15px;
  color: #34495E !important;
}
.nav>li>a:hover, .nav>li>a:focus {
  background-color: transparent;
}
.top_search {
  padding: 0;
  .form-control {
    border-right: 0;
    box-shadow: inset 0 1px 0px rgba(0, 0, 0, 0.075);
    border-radius: 25px 0px 0px 25px;
    padding-left: 20px;
    border: 1px solid rgba(221, 226, 232, 0.49);
  }
  .form-control:focus {
    border: 1px solid rgba(221, 226, 232, 0.49);
    border-right: 0;
  }
  .input-group-btn input {
    border-radius: 0px 25px 25px 0px;
    border: 1px solid rgba(221, 226, 232, 0.49);
    border-left: 0;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    margin-bottom: 0 !important;
    .btn {
      color: #fff;
    }
    .form-control {
      color: #000;
    }
  }
}
.toggle {
  float: left;
  margin: 0;
  padding-top: 16px;
  width: 70px;
}
.toggle a {
  padding: 15px 15px 0;
  margin: 0;
  cursor: pointer;
}
.toggle a i {
  font-size: 26px;
}
.nav.child_menu > li > a {
  color: rgba(255, 255, 255, 0.75);
  font-size: 12px;
  padding: 9px;
}
.panel_toolbox {
  float: right;
  min-width: 70px;
}
.panel_toolbox>li {
  float: left;
  cursor: pointer;
}
.panel_toolbox>li>a {
  padding: 5px;
  color: #C5C7CB;
  font-size: 14px;
}
.panel_toolbox>li>a:hover {
  background: #F5F7FA;
}
.line_30 {
  line-height: 30px;
}
.main_menu_side {
  padding: 0;
}
.bs-docs-sidebar .nav>li>a {
  display: block;
  padding: 4px 6px;
}
footer {
  display: block;
  padding: 15px 20px;
  color: $white;
}
@media (min-width: 768px) {
  footer.logged_in_footer {
    margin-left: 230px;
  }
}
.nav-sm footer {
  margin-left: 70px ;  
}
.tile-stats.sparkline {
  padding: 10px;
  text-align: center;
}
.jqstooltip {
  background: #34495E !important;
  width: 30px !important;
  height: 22px !important;
  text-decoration: none;
}
.tooltip {
  display: block !important;
}
.tiles {
  border-top: 1px solid #ccc;
  margin-top: 15px;
  padding-top: 5px;
  margin-bottom: 0;
}
.tile {
  overflow: hidden;
}
.top_tiles {
  margin-bottom: 0;
}
.top_tiles .tile span {}
.top_tiles .tile h2 {
  font-size: 30px;
  line-height: 30px;
  margin: 3px 0 7px;
  font-weight: bold;
}
article.media {
  width: 100%;
}

/* *********  custom accordion  **************************** */

*, *:before, *:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
#integration-list {
  width: 100%;
  margin: 0 auto;
  display: table;
  ul {
    padding: 0;
    margin: 20px 0;
    color: #555;
  }
  ul > li {
    list-style: none;
    border-top: 1px solid #ddd;
    display: block;
    padding: 15px;
    overflow: hidden;
  }
  ul:last-child {
    border-bottom: 1px solid #ddd;
  }
  ul > li:hover {
    background: #efefef;
  }
}
.expand {
  display: block;
  text-decoration: none;
  color: #555;
  cursor: pointer;
}
.expand h2 {
  width: 85%;
  float: left;
}
h2 {
  font-size: 18px;
  font-weight: 400;
}
#left, #right {
  display: table;
}
#sup {
  display: table-cell;
  vertical-align: middle;
  width: 80%;
}
.detail a {
  text-decoration: none;
  color: #C0392B;
  border: 1px solid #C0392B;
  padding: 6px 10px 5px;
  font-size: 13px;
  margin-right: 7px;
}
.detail {
  margin: 10px 0 10px 0px;
  display: none;
  line-height: 22px;
  height: 150px;
}
.detail span {
  margin: 0;
}
.right-arrow {
  width: 10px;
  float: right;
  font-weight: bold;
  font-size: 20px;
}
.accordion {
  .panel {
    margin-bottom: 5px;
    border-radius: 0;
    border-bottom: 1px solid #efefef;
  }
  .panel-heading {
    background: #fff;
    padding: 4px;
    width: 100%;
    display: block;
  }
}
.x_panel {
  position: relative;
  width: 100%;
  margin-bottom: 10px;
  padding: 10px 17px;
  display: inline-block;
  /*background: #fff;*/
  background: #EDEDED;
  border: 1px solid rgba(237, 237, 237, 0.38);
  -webkit-column-break-inside: avoid;
  -moz-column-break-inside: avoid;
  column-break-inside: avoid;
  opacity: 1;
  -moz-transition: all .2s ease;
  -o-transition: all .2s ease;
  -webkit-transition: all .2s ease;
  -ms-transition: all .2s ease;
  transition: all .2s ease;

  #reportrange {
    background: #fff;
    cursor: pointer;
    padding: 5px 10px;
    border: 1px solid #ccc
  }
  .x_title {
    /*border-bottom: 2px solid #E6E9ED;*/
    border-bottom: 2px solid #d6d9dc;
    padding: 1px 5px 6px;
    margin-bottom: 10px;
    .filter {
      width: 40%;
      float: right;
    }
    h2 {
      margin: 5px 0 6px;
      float: left;
      display: block;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      small {
        margin-left: 10px;
      }
    }
    span {
      color: #BDBDBD;
    }
  }
  .x_content {
    padding: 0 5px 6px;
    position: relative;
    width: 100%;
    float: left;
    clear: both;
    margin-top: 5px;
    h4 {
      font-size: 16px;
      font-weight: 500;
    }
    hr {
      border-top: 1px solid #d2cfcf;
    }
  }
}
legend {
  padding-bottom: 7px;
}
.modal-title {
  margin: 0;
  line-height: 1.42857143;
}
.demo-placeholder {
  height: 280px;
}

/* *********  /custom accordion  **************************** */


/* *********  dashboard widget  **************************** */

table.tile h3, table.tile h4, table.tile span {
  font-weight: bold;
  vertical-align: middle !important;
}
table.tile th, table.tile td {
  text-align: center;
}
table.tile th {
  border-bottom: 1px solid #E6ECEE;
}
table.tile td {
  padding: 5px 0;
}
table.tile td ul {
  text-align: left;
  padding-left: 0
}
table.tile td ul li {
  list-style: none;
  width: 100%;
}
table.tile td ul li a {
  width: 100%
}
table.tile td ul li a big {
  right: 0;
  float: right;
  margin-right: 13px;
}
table.tile_info {
  width: 100%;
  td {
    text-align: left;
    padding: 1px;
    font-size: 15px
    p {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin: 0;
      line-height: 28px;
    }
    i {
      margin-right: 8px;
      font-size: 17px;
      float: left;
      width: 18px;
      line-height: 28px
    }
  }
  td:first-child {
    width: 83%;
  }
}
td span {
  line-height: 28px;
}
.sidebar-widget {
  overflow: hidden;
}
.error-number {
  font-size: 90px;
  line-height: 90px;
  margin: 20px 0;
}
.col-middle {
  margin-top: 5%;
}
.mid_center {
  width: 370px;
  margin: 0 auto;
  text-align: center;
  padding: 10px 20px;
}
h3.degrees {
  font-size: 22px;
  font-weight: 400;
  text-align: center;
}
.degrees:after {
  content: "o";
  position: relative;
  top: -12px;
  font-size: 13px;
  font-weight: 300;
}
.daily-weather .day {
  font-size: 14px;
  border-top: 2px solid rgba(115, 135, 156, 0.36);
  text-align: center;
  border-bottom: 2px solid rgba(115, 135, 156, 0.36);
  padding: 5px 0;
}
.weather-days .col-sm-2 {
  overflow: hidden;
  width: 16.66666667%;
}
.weather .row {
  margin-bottom: 0
}

/* *********  tables styling  ******************************* */

.bulk-actions {
  display: none;
}
.hide-table-row {
  display: none;
}
table.countries_list {
  width: 100%;
}
table.countries_list td {
  padding: 0 10px;
  line-height: 30px;
  border-top: 1px solid #eeeeee;
}
.dataTables_paginate a {
  padding: 6px 9px !important;
  background: #ddd !important;
  border-color: #ddd !important;
}
.paging_full_numbers a.paginate_active {
  background-color: rgba(38, 185, 154, 0.59) !important;
  border-color: rgba(38, 185, 154, 0.59) !important;
}
button.DTTT_button, div.DTTT_button, a.DTTT_button {
  border: 1px solid #E7E7E7 !important;
  background: #E7E7E7 !important;
  box-shadow: none !important;
}
table.jambo_table {
  border: 1px solid rgba(221, 221, 221, 0.78);
}
table.jambo_table thead {
  background: rgba(52, 73, 94, 0.94);
  color: #ECF0F1;
}
table.jambo_table tbody tr:hover td {
  background: rgba(38, 185, 154, 0.07);
  border-top: 1px solid rgba(38, 185, 154, 0.11);
  border-bottom: 1px solid rgba(38, 185, 154, 0.11);
}
table.jambo_table tbody tr.selected {
  background: rgba(38, 185, 154, 0.16);
}
table.jambo_table tbody tr.selected td {
  border-top: 1px solid rgba(38, 185, 154, 0.40);
  border-bottom: 1px solid rgba(38, 185, 154, 0.40);
}
.dataTables_paginate a {
  background: #ff0000;
}
.dataTables_wrapper {
  position: relative;
  clear: both;
  zoom: 1;
  /* Feeling sorry for IE */
}
.dataTables_processing {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 250px;
  height: 30px;
  margin-left: -125px;
  margin-top: -15px;
  padding: 14px 0 2px 0;
  border: 1px solid #ddd;
  text-align: center;
  color: #999;
  font-size: 14px;
  background-color: $white;
}
.dataTables_length {
  width: 40%;
  float: left;
}
.dataTables_filter {
  width: 50%;
  float: right;
  text-align: right;
}
.dataTables_info {
  width: 60%;
  float: left;
}
.dataTables_paginate {
  float: right;
  text-align: right;
}
table.dataTable th.focus,
table.dataTable td.focus {
    outline: 2px solid $primary_light !important;
    outline-offset: -1px;
}

/* Pagination nested */

.paginate_disabled_previous, .paginate_enabled_previous, .paginate_disabled_next, .paginate_enabled_next {
  height: 19px;
  float: left;
  cursor: pointer;
  *cursor: hand;
  color: #111 !important;
}
.paginate_disabled_previous:hover, .paginate_enabled_previous:hover, .paginate_disabled_next:hover, .paginate_enabled_next:hover {
  text-decoration: none !important;
}
.paginate_disabled_previous:active, .paginate_enabled_previous:active, .paginate_disabled_next:active, .paginate_enabled_next:active {
  outline: none;
}
.paginate_disabled_previous, .paginate_disabled_next {
  color: #666 !important;
}
.paginate_disabled_previous, .paginate_enabled_previous {
  padding-left: 23px;
}
.paginate_disabled_next, .paginate_enabled_next {
  padding-right: 23px;
  margin-left: 10px;
}
.paginate_disabled_previous {
  background: url('../images/back_disabled.png') no-repeat top left;
}
.paginate_enabled_previous {
  background: url('../images/back_enabled.png') no-repeat top left;
}
.paginate_enabled_previous:hover {
  background: url('../images/back_enabled_hover.png') no-repeat top left;
}
.paginate_disabled_next {
  background: url('../images/forward_disabled.png') no-repeat top right;
}
.paginate_enabled_next {
  background: url('../images/forward_enabled.png') no-repeat top right;
}
.paginate_enabled_next:hover {
  background: url('../images/forward_enabled_hover.png') no-repeat top right;
}
table.display {
  margin: 0 auto;
  clear: both;
  width: 100%;
}
table.display thead th {
  padding: 8px 18px 8px 10px;
  border-bottom: 1px solid black;
  font-weight: bold;
  cursor: pointer;
  cursor: hand;
}
table.display tfoot th {
  padding: 3px 18px 3px 10px;
  border-top: 1px solid black;
  font-weight: bold;
}
table.display tr.heading2 td {
  border-bottom: 1px solid #aaa;
}
table.display td {
  padding: 3px 10px;
}
table.display td.center {
  text-align: center;
}
table.display thead th:active, table.display thead td:active {
  outline: none;
}
.dataTables_scroll {
  clear: both;
}
.dataTables_scrollBody {
  *margin-top: -1px;
  -webkit-overflow-scrolling: touch;
}
.top, .bottom {}
.top .dataTables_info {
  float: none;
}
.clear {
  clear: both;
}
.dataTables_empty {
  text-align: center;
}
tfoot input {
  margin: 0.5em 0;
  width: 100%;
  color: #444;
}
tfoot input.search_init {
  color: #999;
}
td.group {
  background-color: #d1cfd0;
  border-bottom: 2px solid #A19B9E;
  border-top: 2px solid #A19B9E;
}
td.details {
  background-color: #d1cfd0;
  border: 2px solid #A19B9E;
}
.example_alt_pagination div.dataTables_info {
  width: 40%;
}
.paging_full_numbers {
  width: 400px;
  height: 22px;
  line-height: 22px;
}
.paging_full_numbers a:active {
  outline: none
}
.paging_full_numbers a:hover {
  text-decoration: none;
}
.paging_full_numbers a.paginate_button, .paging_full_numbers a.paginate_active {
  border: 1px solid #aaa;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  padding: 2px 5px;
  margin: 0 3px;
  cursor: pointer;
  *cursor: hand;
}
.paging_full_numbers a.paginate_button {
  background-color: #ddd;
}
.paging_full_numbers a.paginate_button:hover {
  background-color: #ccc;
  text-decoration: none !important;
}
.paging_full_numbers a.paginate_active {
  background-color: #99B3FF;
}
table.display tr.even.row_selected td {
  background-color: #B0BED9;
}
table.display tr.odd.row_selected td {
  background-color: #9FAFD1;
}
div.box {
  height: 100px;
  padding: 10px;
  overflow: auto;
  border: 1px solid #8080FF;
  background-color: #E5E5FF;
}

/* *********  /tables styleing  ****************************** */


/* *********  /dashboard widget  **************************** */


/* *********  widgets  *************************************** */

ul.msg_list {
  li {
    background: rgba(204, 204, 204, 0.49);
    padding: 5px;
    display: flex;
    margin: 6px 6px 0;
    width: 96% !important;
    a {
      padding: 3px 5px !important;
      cursor: pointer;
      .image img {
        border-radius: 2px 2px 2px 2px;
        -webkit-border-radius: 2px 2px 2px 2px;
        float: left;
        margin-right: 10px;
        width: 11%;
      }
      .time {
        font-size: 11px;
        font-style: italic;
        font-weight: bold;
        position: absolute;
        right: 10px;
      }
    }
  }
  li.read {
    background: #f7f7f7 !important;
  }
  li:last-child {
    margin-bottom: 6px;
    padding: 10px;
  }
}
ul.msg_list li a .message,ul.msg_list li .message {
  display: block !important;
  font-size: 11px;
  max-height: 60px;
  overflow: hidden
}
.dropdown-menu.msg_list span {
  white-space: normal;
}
.dropdown-menu {
  border: medium none;
  box-shadow: none;
  display: none;
  float: left;
  font-size: 12px;
  left: 0;
  list-style: none outside none;
  padding: 0;
  position: absolute;
  text-shadow: none;
  top: 100%;
  z-index: 1000;
  border: 1px solid #D9DEE4;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  
}
.dropdown-menu>li>a {
  color: $primary_light;
}
.nav.nav-tabs {
  li.active > a, a:hover {
    background-color: #cedfef;
  }
}
.navbar-nav .open .dropdown-menu {
  position: absolute;
  background: #fff;
  margin-top: 0;
  border: 1px solid #D9DEE4;
  -webkit-box-shadow: none;
  right: 0;
  left: auto;
  width: 220px;
}
.navbar-nav .open .dropdown-menu.msg_list {
  width: 300px;
}
.info-number .badge {
  font-size: 10px;
  font-weight: normal;
  line-height: 13px;
  padding: 2px 6px;
  position: absolute;
  right: 2px;
  top: 8px;
}
ul.to_do {
  padding: 0;
}
ul.to_do li {
  background: #f3f3f3;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  position: relative;
  padding: 7px;
  margin-bottom: 5px;
  list-style: none;
}
ul.to_do p {
  margin: 0;
}
.dashboard-widget {
  background: #f6f6f6;
  border-top: 5px solid #79C3DF;
  border-radius: 3px;
  padding: 5px 10px 10px;
  .dashboard-widget-title {
    font-weight: normal;
    border-bottom: 1px solid #c1cdcd;
    margin: 0 0 10px 0;
    padding-bottom: 5px;
    padding-left: 40px;
    line-height: 30px;

    i {
      font-size: 100%;
      margin-left: -35px;
      margin-right: 10px;
      color: #33a1c9;
      padding: 3px 6px;
      border: 1px solid #abd9ea;
      border-radius: 5px;
      background: #fff;
    }
  }
}
ul.quick-list {
  width: 45%;
  padding-left: 0;
  display: inline-block;
  li {
    padding-left: 10px;
    list-style: none;
    margin: 0;
    padding-bottom: 6px;
    padding-top: 4px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    i {
      padding-right: 10px;
      color: #757679;
    }
  }
}
.dashboard-widget-content {
  padding-top: 9px;
  .bad_record {
    border: 2px solid #ca5b5b;
  }
  .sidebar-widget {
    width: 50%;
    display: inline-block;
    vertical-align: top;
    background: #fff;
    border: 1px solid #abd9ea;
    border-radius: 5px;
    text-align: center;
    float: right;
    padding: 2px;
    margin-top: 10px;
  }
  .form-group input[type="checkbox"]{
    max-width: 20px;
  }
}
.widget_summary {
  width: 100%;
  display: inline-flex;
  .w_left {
    float: left;
    text-align: left;
  }
  .w_center {
    float: left;
  }
  .w_right {
    float: left;
    text-align: right;
    span {
      font-size: 20px;
    }
  }
}
.w_20 {
  width: 20%
}
.w_25 {
  width: 25%
}
.w_55 {
  width: 55%
}
h5.graph_title {
  text-align: left;
  margin-left: 10px
}
h5.graph_title i {
  margin-right: 10px;
  font-size: 17px
}
span.right {
  float: right;
  font-size: 14px !important
}
.tile_info a {
  text-overflow: ellipsis;
}
.sidebar-footer {
  bottom: 0px;
  clear: both;
  display: block;
  padding: 5px 0 0 0;
  position: fixed;
  width: 230px;
  z-index: 1000;
  background: $primary;
  a {
    padding: 7px 0 3px;
    text-align: center;
    width: 25%;
    font-size: 17px;
    display: block;
    float: left;
    background: $primary_dark;
  }
  a:hover {
    background: $primary_faded;
  }
}
.tile_count {
  margin-bottom: 20px;
  margin-top: 20px;
  div:first-child .left {
    border: 0;
  }
  .tile_stats_count {
    border-left: 0px solid #333;
    padding: 0;
    .left {
      width: 8%;
      float: left;
      height: 65px;
      border-left: 2px solid #ADB2B5;
      margin-top: 10px;
    }
    .right {
      padding-left: 10px;
      height: 100%;
      text-overflow: ellipsis;
      overflow: hidden;
      span {
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .count {
      font-size: 40px;
      line-height: 47px;
      font-weight: 600;
      small {
        font-size: 20px;
        line-height: 20px;
        font-weight: 600;
      }
    }
  }
}
.count_bottom i {
  width: 12px;
}
.dashboard_graph {
  background: #fff;
  padding: 7px 10px;
}
.dashboard_graph .col-md-9, .dashboard_graph .col-md-3 {
  padding: 0;
}
a.user-profile {
  color: #5E6974 !important;
}
.user-profile img {
  width: 29px;
  height: 29px;
  border-radius: 50%;
  margin-right: 10px;
}
ul.top_profiles {
  height: 330px;
  width: 100%;

  li {
    margin: 0;
    padding: 3px 5px;
  }
  li:nth-child(odd) {
    background-color: #eee;
  }
}
.media {
  .profile_thumb {
    border: 1px solid;
    width: 50px;
    height: 50px;
    margin: 5px 10px 5px 0;
    border-radius: 50%;
    padding: 9px 12px;

    i {
      font-size: 30px;
    }
  }
  .date {
    background: #ccc;
    width: 52px;
    margin-right: 10px;
    border-radius: 10px;
    padding: 5px;

    .month {
      margin: 0;
      text-align: center;
      color: #fff;
    }
    .day {
      text-align: center;
      color: #fff;
      font-size: 27px;
      margin: 0;
      line-height: 27px;
      font-weight: bold;
    }
  }
}
.event .media-body a.title {
  font-weight: bold;
}
.event .media-body p {
  margin-bottom: 0;
}
h4.graph_title {
  margin: 7px;
  text-align: center;
}

/* *********  /widgets designer messages index page *************************************** */


/* *********  iconts-display  **************************** */

.fontawesome-icon-list {
  .fa-hover a:hover {
    background-color: #ddd;
    color: #fff;
    text-decoration: none;
  }
  .fa-hover a {
    display: block;
    line-height: 32px;
    height: 32px;
    padding-left: 10px;
    border-radius: 4px;
  }
  .fa-hover a:hover .fa {
    font-size: 28px;
    vertical-align: -6px;
  }
  .fa-hover a .fa {
    width: 32px;
    font-size: 16px;
    display: inline-block;
    text-align: right;
    margin-right: 10px;
  }
}
.main_menu .fa {
  width: 26px;
  opacity: .99;
  display: inline-block;
  font-family: FontAwesome;
  font-style: normal;
  font-weight: normal;
  font-size: 18px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* *********  /iconts-display  **************************** */


/* *********  Tile stats  **************************** */

.tile-stats {
  position: relative;
  display: block;
  margin-bottom: 12px;
  border: 1px solid #E4E4E4;
  -webkit-border-radius: 5px;
  overflow: hidden;
  padding-bottom: 5px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 5px;
  -moz-background-clip: padding;
  border-radius: 5px;
  background-clip: padding-box;
  background: #FFF;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;

  .icon {
    color: #BAB8B8;
    position: absolute;
    right: 53px;
    top: 22px;
    z-index: 1;
  }
  .icon i {
    margin: 0;
    font-size: 60px;
    line-height: 0;
    vertical-align: bottom;
    padding: 0;
  }
  .count {
    font-size: 38px;
    font-weight: bold;
    line-height: 1.65857143
  }
  h3 {
    color: #BAB8B8;
  }
  p {
    margin-top: 5px;
    font-size: 12px;
  }
}
.tile-stats:hover .icon i {
  animation-name: tansformAnimation;
  animation-duration: .5s;
  animation-iteration-count: 1;
  color: rgba(58, 58, 58, 0.41);
  animation-timing-function: ease;
  animation-fill-mode: forwards;
  -webkit-animation-name: tansformAnimation;
  -webkit-animation-duration: .5s;
  -webkit-animation-iteration-count: 1;
  -webkit-animation-timing-function: ease;
  -webkit-animation-fill-mode: forwards;
  -moz-animation-name: tansformAnimation;
  -moz-animation-duration: .5s;
  -moz-animation-iteration-count: 1;
  -moz-animation-timing-function: ease;
  -moz-animation-fill-mode: forwards;
}
.tile-stats .count, .tile-stats h3, .tile-stats p {
  position: relative;
  margin: 0;
  margin-left: 10px;
  z-index: 5;
  padding: 0;
}
.tile-stats > .dash-box-footer {
  position: relative;
  text-align: center;
  margin-top: 5px;
  padding: 3px 0;
  color: #fff;
  color: rgba(255, 255, 255, 0.8);
  display: block;
  z-index: 10;
  background: rgba(0, 0, 0, 0.1);
  text-decoration: none;
}
.tile-stats > .dash-box-footer:hover {
  color: #fff;
  background: rgba(0, 0, 0, 0.15);
}
.tile-stats > .dash-box-footer:hover {
  color: #fff;
  background: rgba(0, 0, 0, 0.15);
}
table.tile_info {
  padding: 10px 15px;

  span.right {
    margin-right: 0;
    float: right;
    position: absolute;
    right: 4%;
  }
}
.tile:hover {
  text-decoration: none;
}
.tile_header {
  border-bottom: transparent;
  padding: 7px 15px;
  margin-bottom: 15px;
  background: #E7E7E7;
}
.tile_head h4 {
  margin-top: 0;
  margin-bottom: 5px;
}
.tiles-bottom {
  padding: 5px 10px;
  margin-top: 10px;
  background: rgba(194, 194, 194, 0.3);
  text-align: left;
}

/* *********  /Tile stats  **************************** */


/* *********  /inbox design  **************************** */

a.star {
  color: #428bca !important
}
.mail_content {
  background: none repeat scroll 0 0 $white;
  border-radius: 4px;
  margin-top: 20px;
  min-height: 500px;
  padding: 10px 11px;
  width: 100%;
}
.list-btn-mail {
  margin-bottom: 15px;
}
.list-btn-mail.active {
  border-bottom: 1px solid #39B3D7;
  padding: 0 0 14px;
}
.list-btn-mail > i {
  float: left;
  font-size: 18px;
  font-style: normal;
  width: 33px;
}
.list-btn-mail > .cn {
  background: none repeat scroll 0 0 #39B3D7;
  border-radius: 12px;
  color: $white;
  float: right;
  font-style: normal;
  padding: 0 5px;
}
.button-mail {
  margin: 0 0 15px !important;
  text-align: left;
  width: 100%;
}
.buttons, button, .btn {
  margin-bottom: 5px;
  margin-right: 5px;
}
.btn-group-vertical .btn, .btn-group .btn {
  margin-bottom: 0;
  margin-right: 0;
}
.mail_list_column {
  border-left: 1px solid #DBDBDB;
  border-right: 1px solid #DBDBDB;
}
.mail_list {
  width: 100%;
  border-bottom: 0px solid #DBDBDB;
  margin-bottom: 2px;
  display: inline-block;
  padding-bottom: 8px;

  .left {
    width: 25%;
    float: left;
    margin-right: 3%
  }
  .right {
    width: 70%;
    float: left
  }
  h3 {
    font-size: 15px;
    font-weight: bold;
    margin: 0px 0 6px;
  }
  h3 small {
    float: right;
    color: #ADABAB;
    font-size: 11px;
    line-height: 20px;
  }
  .badge {
    padding: 3px 6px;
    font-size: 8px;
    background: #BAB7B7
  }
}
@media (max-width: 767px) {
  .mail_list {
    margin-bottom: 5px;
    display: inline-block;
  }
}
.mail_heading h4 {
  font-size: 18px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
  margin-top: 20px;
}
.attachment {
  margin-top: 30px;
}
.attachment ul {
  width: 100%;
  list-style: none;
  padding-left: 0;
  display: inline-block;
  margin-bottom: 30px;
}
.attachment ul li {
  float: left;
  width: 150px;
  margin-right: 10px;
  margin-bottom: 10px;
}
.attachment ul li img {
  height: 150px;
  border: 1px solid #ddd;
  padding: 5px;
  margin-bottom: 10px;
}
.attachment ul li span {
  float: right;
}
.attachment .file-name {
  float: left;
}
.attachment .links {
  width: 100%;
  display: inline-block;
}

/* *********  /inbox design   **************************** */


/* *********  form design  **************************** */

.editor.btn-toolbar {
  zoom: 1;
  background: #F7F7F7;
  margin: 5px 2px;
  padding: 3px 0;
  border: 1px solid #EFEFEF;
}
.input-group {
  margin-bottom: 10px;
}
.ln_solid {
  border-top: 1px solid #e5e5e5;
  color: $white;
  background-color: $white;
  height: 1px;
  margin: 20px 0;
}
span.section {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: 20px;
  font-size: 21px;
  line-height: inherit;
  color: #333;
  border: 0;
  border-bottom: 1px solid #e5e5e5;
}
.form-control {
  border-radius: 0;
  width: 100%;
}
.form-horizontal .control-label {
  padding-top: 8px
}
.form-control:focus {
  border-color: #CCD0D7;
  box-shadow: none !important;
}
legend {
  font-size: 18px;
  color: inherit;
}
.checkbox-inline {
  margin-left: 10px;
}
.form-horizontal .form-group {
  margin-right: 0;
  margin-left: 0;
}
.form-control-feedback {
  margin-top: 8px;
  height: 23px;
  color: #bbb;
  line-height: 24px;
  font-size: 15px;
}
.form-control-feedback.left {
  border-right: 1px solid #ccc;
  left: 13px;
}
.form-control-feedback.right {
  border-left: 1px solid #ccc;
  right: 13px;
}
.form-control.has-feedback-left {
  padding-left: 45px;
}
.form-control.has-feedback-right {
  padding-right: 45px;
}
.form-group {
  margin-bottom: 10px;
}
.validate {
  margin-top: 10px;
}
.invalid-form-error-message {
  margin-top: 10px;
  padding: 5px;
}
.invalid-form-error-message.filled {
  border-left: 2px solid #E74C3C;
}
p.parsley-success {
  color: #468847;
  background-color: #DFF0D8;
  border: 1px solid #D6E9C6;
}
p.parsley-error {
  color: #B94A48;
  background-color: #F2DEDE;
  border: 1px solid #EED3D7;
}
ul.parsley-errors-list {
  list-style: none;
  color: #E74C3C;
  padding-left: 0;
}
input.parsley-error, textarea.parsley-error, select.parsley-error {
  background: #FAEDEC;
  border: 1px solid #E85445;
}
.btn-group .parsley-errors-list {
  display: none;
}
.bad input, .bad select, .bad textarea {
  border: 1px solid #CE5454;
  box-shadow: 0 0 4px -2px #CE5454;
  position: relative;
  left: 0;
  -moz-animation: .7s 1 shake linear;
  -webkit-animation: 0.7s 1 shake linear;
}
.item input, .item textarea {
  -webkit-transition: 0.42s;
  -moz-transition: 0.42s;
  transition: 0.42s;
}

/* alerts (when validation fails) */

.item .alert {
  float: left;
  margin: 0 0 0 20px;
  padding: 3px 10px;
  color: #FFF;
  border-radius: 3px 4px 4px 3px;
  background-color: #CE5454;
  max-width: 170px;
  white-space: pre;
  position: relative;
  left: -15px;
  opacity: 0;
  z-index: 1;
  transition: 0.15s ease-out;
}
.item .alert::after {
  content: '';
  display: block;
  height: 0;
  width: 0;
  border-color: transparent #CE5454 transparent transparent;
  border-style: solid;
  border-width: 11px 7px;
  position: absolute;
  left: -13px;
  top: 1px;
}
.item.bad .alert {
  left: 0;
  opacity: 1;
}

.inl-bl {
  display: inline-block;
}
.well .markup-heading {}
.well .markup {
  background: #fff;
  color: #777;
  position: relative;
  padding: 45px 15px 15px;
  margin: 15px 0 0 0;
  background-color: #fff;
  border-radius: 0 0 4px 4px;
  box-shadow: none;
}
.well .markup::after {
  content: "Example";
  position: absolute;
  top: 15px;
  left: 15px;
  font-size: 12px;
  font-weight: bold;
  color: #bbb;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* ***** autocomplete ***** */

.autocomplete-suggestions {
  border: 1px solid #e4e4e4;
  background: #F4F4F4;
  cursor: default;
  overflow: auto;
}
.autocomplete-suggestion {
  padding: 2px 5px;
  font-size: 1.2em;
  white-space: nowrap;
  overflow: hidden;
}
.autocomplete-selected {
  background: #f0f0f0;
}
.autocomplete-suggestions strong {
  font-weight: normal;
  color: #3399ff;
  font-weight: bolder;
}
/* ***** /autocomplete *****/

/* ***** buttons ********/
.btn {
  border-radius: 3px;
}
a.btn-success, a.btn-primary, a.btn-warning, a.btn-danger {
  color: #fff;
}
.btn-success {
  background: #26B99A;
  border: 1px solid #169F85;
}
.btn-success:hover, .btn-success:focus, .btn-success:active, .btn-success.active, .open .dropdown-toggle.btn-success {
  background: #169F85;
}
.btn-dark {
  color: #E9EDEF;
  background-color: #4B5F71;
  border-color: #364B5F;
}
.btn-dark:hover, .btn-dark:focus, .btn-dark:active, .btn-dark.active, .open .dropdown-toggle.btn-dark {
  color: $white;
  background-color: #394D5F;
  border-color: #394D5F;
}
.btn-round {
  border-radius: 30px;
}
.btn.btn-app {
  position: relative;
  padding: 15px 5px;
  margin: 0 0 10px 10px;
  min-width: 80px;
  height: 60px;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  text-align: center;
  color: #666;
  border: 1px solid #ddd;
  background-color: #fafafa;
  font-size: 12px;
}
.btn.btn-app > .fa, .btn.btn-app > .glyphicon, .btn.btn-app > .ion {
  font-size: 20px;
  display: block;
}
.btn.btn-app:hover {
  background: #f4f4f4;
  color: #444;
  border-color: #aaa;
}
.btn.btn-app:active, .btn.btn-app:focus {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  -moz-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.btn.btn-app > .badge {
  position: absolute;
  top: -3px;
  right: -10px;
  font-size: 10px;
  font-weight: 400;
}
/* ***** /buttons *******/

/* *********  /form design  **************************** */

/* *********  calender dropdown  **************************** */

.daterangepicker.dropdown-menu {
  font-size: 13px;
  padding: 0;
  overflow: hidden;
}
.daterangepicker.picker_1 {
  background: #34495E;
  color: #ECF0F1;
}
.daterangepicker.picker_1 table.table-condensed thead tr:first-child {
  background: $primary_light;
}
.daterangepicker table.table-condensed thead tr:first-child th {
  line-height: 28px;
  text-align: center;
}
.daterangepicker.picker_1 table.table-condensed thead tr {
  background: #213345;
}
.daterangepicker table.table-condensed thead tr {
  line-height: 14px;
}
.daterangepicker table.table-condensed tbody tr:first-child td {
  padding-top: 10px;
}
.daterangepicker table.table-condensed th:first-child, .daterangepicker table.table-condensed td:first-child {
  padding-left: 12px
}
.daterangepicker table.table-condensed th:last-child, .daterangepicker table.table-condensed td:last-child {
  padding-right: 12px
}
.table-condensed>thead>tr>th, .table-condensed>tbody>tr>th, .table-condensed>tfoot>tr>th, .table-condensed>thead>tr>td, .table-condensed>tbody>tr>td, .table-condensed>tfoot>tr>td {
  padding: 5px 7px;
  text-align: center;
}
.daterangepicker table.table-condensed tbody tr:last-child td {
  padding-bottom: 10px;
}
.daterangepicker.picker_2 table.table-condensed thead tr:first-child {
  color: inherit;
}
.daterangepicker.picker_2 table.table-condensed thead tr {
  color: $primary_light;
}
.daterangepicker.picker_3 table.table-condensed thead tr:first-child {
  background: $primary_light;
  color: #ECF0F1;
}
.daterangepicker.picker_4 table.table-condensed tbody td {
  background: #ECF0F1;
  color: #34495E;
  border: 1px solid #fff;
  padding: 4px 7px;
}
.daterangepicker.picker_4 table.table-condensed tbody td.active {
  background: #536A7F;
  color: #fff;
}
.daterangepicker.picker_4 table.table-condensed thead tr:first-child {
  background: #34495E;
  color: #ECF0F1;
}
.xdisplay_input {
  width: 240px;
  overflow: hidden;
  padding: 0;
}
.xdisplay {
  background-color: #fff;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  border: 1px solid #ccc;
  margin-bottom: 20px;
  border: 1px solid rgba(0, 0, 0, .15);
  border-radius: 4px;
  width: 230px;
  overflow: hidden;
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
  box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
}
.daterangepicker.opensright .ranges, .daterangepicker.opensright .calendar, .daterangepicker.openscenter .ranges, .daterangepicker.openscenter .calendar {
  float: right;
}
.daterangepicker.dropdown-menu .calendar {}
.daterangepicker table {
  width: 100%;
  margin: 0;
}
.daterangepicker td, .daterangepicker th {
  text-align: center;
  width: 20px;
  height: 20px;
  cursor: pointer;
  white-space: nowrap;
}
.daterangepicker td.off {
  color: #999;
}
.daterangepicker td.disabled {
  color: #999;
}
.daterangepicker td.available:hover, .daterangepicker th.available:hover {
  background: #eee;
  color: #34495E;
}
.daterangepicker td.in-range {
  background: #E4E7EA;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.daterangepicker td.available + td.start-date {
  -webkit-border-radius: 4px 0 0 4px;
  -moz-border-radius: 4px 0 0 4px;
  border-radius: 4px 0 0 4px;
}
.daterangepicker td.in-range + td.end-date {
  -webkit-border-radius: 0 4px 4px 0;
  -moz-border-radius: 0 4px 4px 0;
  border-radius: 0 4px 4px 0;
}
.daterangepicker td.start-date.end-date {
  -webkit-border-radius: 4px !important;
  -moz-border-radius: 4px !important;
  border-radius: 4px !important;
}
.daterangepicker td.active, .daterangepicker td.active:hover {
  background-color: #536A7F;
  color: #fff;
}
.daterangepicker td.week, .daterangepicker th.week {
  font-size: 80%;
  color: #ccc;
}
.daterangepicker select.monthselect, .daterangepicker select.yearselect {
  font-size: 12px;
  padding: 1px;
  height: auto;
  margin: 0;
  cursor: default;
  height: 30px;
  border: 1px solid #ADB2B5;
  line-height: 30px;
  border-radius: 0px !important;
}
.daterangepicker select.monthselect {
  margin-right: 2%;
  width: 56%;
}
.daterangepicker select.yearselect {
  width: 40%;
}
.daterangepicker select.hourselect, .daterangepicker select.minuteselect, .daterangepicker select.ampmselect {
  width: 50px;
  margin-bottom: 0;
}
.daterangepicker_start_input {
  float: left;
}
.daterangepicker_end_input {
  float: left;
  padding-left: 11px;
}
.daterangepicker th.month {
  width: auto;
}
.daterangepicker .daterangepicker_start_input label, .daterangepicker .daterangepicker_end_input label {
  color: #333;
  display: block;
  font-size: 11px;
  font-weight: normal;
  height: 20px;
  line-height: 20px;
  margin-bottom: 2px;
  text-shadow: #fff 1px 1px 0px;
  text-transform: uppercase;
  width: 74px;
}
.daterangepicker .ranges input {
  font-size: 11px;
}
.daterangepicker .ranges .input-mini {
  background-color: #eee;
  border: 1px solid #ccc;
  border-radius: 4px;
  color: #555;
  display: block;
  font-size: 11px;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  margin: 0 0 10px 0;
  padding: 0 6px;
  width: 74px;
}
.daterangepicker .ranges .input-mini:hover {
  cursor: pointer;
}
.daterangepicker .ranges ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.daterangepicker .ranges li {
  font-size: 13px;
  background: #f5f5f5;
  border: 1px solid #f5f5f5;
  color: #536A7F;
  padding: 3px 12px;
  margin-bottom: 8px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  cursor: pointer;
}
.daterangepicker .ranges li.active, .daterangepicker .ranges li:hover {
  background: #536A7F;
  border: 1px solid #536A7F;
  color: #fff;
}
.daterangepicker .calendar {
  display: none;
  max-width: 270px;
}
.daterangepicker.show-calendar .calendar {
  display: block;
}
.daterangepicker .calendar.single .calendar-date {
  border: none;
}
.daterangepicker.single .ranges, .daterangepicker.single .calendar {
  float: none;
}
.daterangepicker .ranges {
  width: 160px;
  text-align: left;
  margin: 4px;
}
.daterangepicker .ranges .range_inputs>div {
  float: left;
}
.daterangepicker .ranges .range_inputs>div:nth-child(2) {
  padding-left: 11px;
}
.daterangepicker.opensleft .ranges, .daterangepicker.opensleft .calendar {
  float: left;
  margin: 4px;
}

/* *********  /calender dropdown  **************************** */


/* *********  form textarea  **************************** */

textarea {
  padding: 10px;
  vertical-align: top;
  width: 200px;
}
textarea:focus {
  outline-style: solid;
  outline-width: 2px;
}
.btn_ {
  display: inline-block;
  padding: 3px 9px;
  margin-bottom: 0;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  color: #333333;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
  background-color: #f5f5f5;
  background-image: -moz-linear-gradient(top, $white, #e6e6e6);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from($white), to(#e6e6e6));
  background-image: -webkit-linear-gradient(top, $white, #e6e6e6);
  background-image: -o-linear-gradient(top, $white, #e6e6e6);
  background-image: linear-gradient(to bottom, $white, #e6e6e6);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='$whiteff', endColorstr='#ffe6e6e6', GradientType=0);
  border-color: #e6e6e6 #e6e6e6 #bfbfbf;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  border: 1px solid #cccccc;
  border-bottom-color: #b3b3b3;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, .2), 0 1px 2px rgba(0, 0, 0, .05);
  -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, .2), 0 1px 2px rgba(0, 0, 0, .05);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .2), 0 1px 2px rgba(0, 0, 0, .05);
}

/* *********  /form textarea  **************************** */


/* *********  glyphicons  **************************** */

.bs-glyphicons {
  margin: 0 -10px 20px;
  overflow: hidden
}
.bs-glyphicons-list {
  padding-left: 0;
  list-style: none
}
.bs-glyphicons li {
  float: left;
  width: 25%;
  height: 115px;
  padding: 10px;
  font-size: 10px;
  line-height: 1.4;
  text-align: center;
  background-color: #f9f9f9;
  border: 1px solid #fff
}
.bs-glyphicons .glyphicon {
  margin-top: 5px;
  margin-bottom: 10px;
  font-size: 24px
}
.bs-glyphicons .glyphicon-class {
  display: block;
  text-align: center;
  word-wrap: break-word
}
.bs-glyphicons li:hover {
  color: #fff;
  background-color: $primary_light
}
@media (min-width: 768px) {
  .bs-glyphicons {
    margin-right: 0;
    margin-left: 0
  }
  .bs-glyphicons li {
    width: 12.5%;
    font-size: 12px
  }
}

/* *********  /glyphicons  **************************** */


/* *********  tabs  **************************** */

ul.bar_tabs {
  /* border: 1px solid #ff0000; */
  overflow: visible;
  background: #F5F7FA;
  height: 25px;
  margin: 21px 0 14px;
  padding-left: 14px;
  position: relative;
  z-index: 1;
  width: 100%;
  border-bottom: 1px solid #E6E9ED;
}
ul.bar_tabs > li {
  border: 1px solid #E6E9ED;
  color: #333 !important;
  margin-top: -17px;
  margin-left: 8px;
  background: #fff;
  border-bottom: none;
  border-radius: 4px 4px 0 0;
}
ul.bar_tabs > li.active {
  border-right: 6px solid #D3D6DA;
  border-top: 0;
  margin-top: -15px;
}
ul.bar_tabs > li.active a {
  background: #fff;
  border-color: transparent;
}
ul.bar_tabs > li a {
  padding: 10px 17px;
  background: #F5F7FA;
  margin: 0;
  border-radius: 0;
}
ul.bar_tabs.right {
  padding-right: 14px;
}
ul.bar_tabs.right li {
  float: right
}
a:focus {
  outline: none;
}

/* *********  /tabs  **************************** */


/* *********  timeline  **************************** */

ul.timeline li {
  position: relative;
  border-bottom: 1px solid #e8e8e8;
  clear: both;
}
.timeline .block {
  margin: 0;
  border-left: 3px solid #e8e8e8;
  overflow: visible;
  padding: 10px 15px;
  margin-left: 105px;
}
.timeline.widget {
  min-width: 0;
  max-width: inherit;
}
.timeline.widget .block {
  margin-left: 5px;
}
.timeline .tags {
  position: absolute;
  top: 15px;
  left: 0;
  width: 84px;
}
.timeline .tag {
  display: block;
  height: 30px;
  font-size: 13px;
  padding: 8px;
}
.timeline .tag span {
  display: block;
  overflow: hidden;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.tag {
  line-height: 1;
  background: $primary_light;
  color: #fff !important;
}
.timeline h2.title {
  position: relative;
  font-size: 16px;
  margin: 0;
}
.timeline h2.title:before {
  content: "";
  position: absolute;
  left: -23px;
  top: 3px;
  display: block;
  width: 14px;
  height: 14px;
  border: 3px solid #d2d3d2;
  border-radius: 14px;
  background: #f9f9f9;
}
.timeline .byline {
  padding: .25em 0;
}
.byline {
  -webkit-font-smoothing: antialiased;
  font-style: italic;
  font-size: .9375em;
  line-height: 1.3;
  color: #aab6aa;
}
ul.social li {
  border: 0;
}

/* *********  /timeline  **************************** */


/* *********  notifications  **************************** */


/* Pnotify by Hunter Perrin :: 2.0.1 */

.ui-pnotify {
  top: 25px;
  right: 25px;
  position: absolute;
  height: auto;
  /* Ensures notices are above everything */
  z-index: 9999;
}

/* Hides position: fixed from IE6 */

html > body > .ui-pnotify {
  position: fixed;
}
.ui-pnotify .ui-pnotify-shadow {
  -webkit-box-shadow: 0px 2px 10px rgba(50, 50, 50, 0.5);
  -moz-box-shadow: 0px 2px 10px rgba(50, 50, 50, 0.5);
  box-shadow: 0px 2px 10px rgba(50, 50, 50, 0.5);
}
.ui-pnotify-container {
  background-position: 0 0;
  padding: .8em;
  height: 100%;
  margin: 0;
}
.ui-pnotify-sharp {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.ui-pnotify-title {
  display: block;
  margin-bottom: .4em;
  margin-top: 0;
}
.ui-pnotify-text {
  display: block;
}
.ui-pnotify-icon, .ui-pnotify-icon span {
  display: block;
  float: left;
  margin-right: .2em;
}

/* Alternate stack initial positioning. */

.ui-pnotify.stack-topleft, .ui-pnotify.stack-bottomleft {
  left: 25px;
  right: auto;
}
.ui-pnotify.stack-bottomright, .ui-pnotify.stack-bottomleft {
  bottom: 25px;
  top: auto;
}
.ui-pnotify-closer, .ui-pnotify-sticker {
  float: right;
  margin-left: .2em;
}

/* theming */

.alert-success {
  color: #3c763d;
  background-color: #d6e9c6;
  border-color: #afd68f;
}
.alert-info {
  color: #31708f;
  background-color: rgba(188, 232, 241, 0.44);
  border-color: rgba(52, 152, 219, 0.22);
}
.alert-warning {
  color: #8a6d3b;
  background-color: #fcf8e3;
  border-color: #faebcc;
}
.alert-danger, .alert-error {
  color: #a94442;
  background-color: #ebccd1;
  border-color: #d4abb2;
}
.alert-dark {
  color: #E9EDEF;
  background-color: rgba(52, 73, 94, 0.88);
  border-color: rgba(52, 73, 94, 0.88);
}
/* /theming */


/* Pnotify */
/* TODO: This class seems not use */
.custom-notifications {
  position: fixed;
  margin: 15px;
  right: 0;
  float: right;
  width: 400px;
  z-index: 4000;
  bottom: 0;
}
/* Pnotify */

/* *********  verticle tabs  **************************** */

/*!
 * bootstrap-vertical-tabs - v1.2.1
 * https://dbtek.github.io/bootstrap-vertical-tabs
 * 2014-11-07
 * Copyright (c) 2014 İsmail Demirbilek
 * License: MIT
 */

.tabs-left, .tabs-right {
  border-bottom: none;
  padding-top: 2px;
}
.tabs-left {
  border-right: 1px solid #F7F7F7;
}
.tabs-right {
  border-left: 1px solid #F7F7F7;
}
.tabs-left>li, .tabs-right>li {
  float: none;
  margin-bottom: 2px;
}
.tabs-left>li {
  margin-right: -1px;
}
.tabs-right>li {
  margin-left: -1px;
}
.tabs-left>li.active>a, .tabs-left>li.active>a:hover, .tabs-left>li.active>a:focus {
  border-bottom-color: #F7F7F7;
  border-right-color: transparent;
}
.tabs-right>li.active>a, .tabs-right>li.active>a:hover, .tabs-right>li.active>a:focus {
  border-bottom: 1px solid #F7F7F7;
  border-left-color: transparent;
}
.tabs-left>li>a {
  border-radius: 4px 0 0 4px;
  margin-right: 0;
  display: block;
  background: #F7F7F7;
  text-overflow: ellipsis;
  overflow: hidden;
}
.tabs-right>li>a {
  border-radius: 0 4px 4px 0;
  margin-right: 0;
  background: #F7F7F7;
  text-overflow: ellipsis;
  overflow: hidden;
}
.sideways {
  margin-top: 50px;
  border: none;
  position: relative;
}
.sideways>li {
  height: 20px;
  width: 120px;
  margin-bottom: 100px;
}
.sideways>li>a {
  border-bottom: 1px solid #ddd;
  border-right-color: transparent;
  text-align: center;
  border-radius: 4px 4px 0px 0px;
}
.sideways>li.active>a, .sideways>li.active>a:hover, .sideways>li.active>a:focus {
  border-bottom-color: transparent;
  border-right-color: #ddd;
  border-left-color: #ddd;
}
.sideways.tabs-left {
  left: -50px;
}
.sideways.tabs-right {
  right: -50px;
}
.sideways.tabs-right>li {
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);
}
.sideways.tabs-left>li {
  -webkit-transform: rotate(-90deg);
  -moz-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  -o-transform: rotate(-90deg);
  transform: rotate(-90deg);
}

/* *********  /campaign portal  **************************** */
.campaign-board{
  display: flex;
  justify-content: center;
  align-items: center;
}
.campaign-panel{
  background: white !important;
  width: 60% !important;
}
.card-campaign {
  border: 1px solid #dee2e6;
  border-radius: 0.55rem;
}


.card-body-campaign {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card-title {
  color: #007bff;
  font-weight: 600;
}

.card-text {
  color: #6c757d;
  padding: 0 10px;
}

.campaign-row .col-md-4.mb-4 {
  margin-bottom: 40px;
}
.card-text strong {
  color: #343a40;
}

.alert {
  margin-top: 1.5rem;
}

.campaign-container{
  padding: 2rem;
}

.campaign-row {
  margin-bottom: 2rem;
}.campaign-row .col-md-4.mb-4 {
  margin-bottom: 40px;
}
.card-text strong {
  color: #343a40;
}

.alert {
  margin-top: 1.5rem;
}

.campaign-container{
  padding: 2rem;
}
.seller_admin_portal{
  .campaign-row {
    margin-bottom: 2rem;
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    .campaign_columns {
      margin-bottom: 20px;
      width: 30%;
      margin-right: 30px;
      .card-body-campaign.d-flex.flex-column {
        padding: 20px;
        border-radius: 20px;
      }
      .card-campaign.shadow-sm {
        border: 1px solid;
        border: 2px solid #f1f1f1;
        padding: 20px;
        border-radius: 20px;
        .campaign_portal_card_info {
          h5.card-title{
            color: #670b19;
            text-transform: capitalize;
            font-size: 22px;
            padding-bottom: 20px;
            font-weight: 600;
            border-bottom: 1px solid #670b19;
            }
            p.card-text.text-muted {
              color: #3c4345;
              font-size: 14px;
          }
            .campaign_portal_date_format {
              font-size: 15px;
              display: flex;
              justify-content: space-between;
              padding: 20px 0;
          }
      }
          .campaign_portal_button {
            text-align: center;
            .btn-danger,.btn-success {
              color: #fff !important;
          }
        }
    }
    }
  }
}
/* *********  ecommerce  **************************** */

.price {
  font-size: 40px;
  font-weight: 400;
  color: #26B99A;
  margin: 0;
}
.prod_title {
  border-bottom: 1px solid #DFDFDF;
  padding-bottom: 5px;
  margin: 30px 0;
  font-size: 20px;
  font-weight: 400;
}
.product-image img {
  width: 90%;
}
.prod_color li {
  margin: 0 10px;
}
.prod_color li p {
  margin-bottom: 0;
}
.prod_size li {
  padding: 0;
}
.prod_color .color {
  width: 25px;
  height: 25px;
  border: 2px solid rgba(51, 51, 51, 0.28) !important;
  padding: 2px;
  border-radius: 50px;
}
.product_gallery a {
  width: 100px;
  height: 100px;
  float: left;
  margin: 10px;
  border: 1px solid #e5e5e5;
}
.product_gallery a img {
  width: 100%;
  margin-top: 15px;
}
.product_price {
  margin: 20px 0;
  padding: 5px 10px;
  background-color: $white;
  text-align: left;
  border: 2px dashed #E0E0E0;
}
.price-tax {
  font-size: 18px;
}
.product_social {
  margin: 20px 0;
}
.product_social ul li a i {
  font-size: 35px;
}
.popover{
  max-width: 363px
}
.popover-content p {
  font-size: 12px;
  text-justify: inter-word;
  font-weight: normal;
}
.replacement_rtv_note{
  margin: 15px;
  font-size: 10px;
  padding-top: 0px;
}
.glyphicon.glyphicon-info-sign.review_info {
  font-size: 18px;
  top: 5px;
}

@media (min-width: 992px) {
  .order_count_width {
      width: 12%;
  }
}
.form-group.required label:after {
  content:" *";
  color:red;
}
/* *********  /ecommerce  **************************** */

.affiliate-designs{
  .designs-detail{
    list-style-type: none;
    float: left;
    clear: left;
    margin-right: -10px;
    padding: 0px;
    width: 100%;
    .design-image{
      margin-top: 15px;
      border: 1px solid #eee;
      background: $white;
      img{
        margin: 0 auto;
      }
      .create-link{
        outline: none;
        appearance: none;
        -webkit-appearance: none;
        border: 1px solid $primary_light;
        height: auto;
        text-align: center;
        background: $white;
        color: $primary_light;
        line-height: 2.6;
        font-size: 12px;
        cursor: pointer;
        margin: 5px 10px -10px 10px;
      }
      .delayed_download_icon{
        position: absolute;
        right: 25px;
        top: 25px;
        background: #f7f7f7;
        width: 20px;
        height: 20px;
        text-align: center;
        color: #303030;
        border-radius: 1px;
        &:hover{
          color: $primary !important;
        }
      }
      .created-link{
        opacity: 0;
        height: 0;
      }
      .popover{
        width: 100px;
        background: rgb(216, 182, 189);
        color: $primary;
        border: 1px solid $primary;
        .popover-content{
          font-size: 12px;
          padding: 2px;
        }
        .arrow{
          border-right-color: $primary;
          left: -10px;
          &:after{
            border-right-color: rgb(216, 182, 189);
          }
        }
      }
    }
  }
}
.affiliate-logo{
  position: absolute;
  left: 120px;
  right: 0;
  text-align: center;
  color: #b11f2b;
  font-weight: bold;
  top: 17px;
  width: 10%;
  margin: 0 auto;
  .logo-name{
    font-size: 16px;
  }
  .beta{
    font-size: 10px;
  }
}
.affiliate-date-form{
  margin-top: 15px;
  .performance-date-range{
    background-color: #e0dddd;
    width: 38%;
    display: inline-block;
    .form-control{
      background-color: #e0dddd;
      border: none;
      padding: 0 5px;
    }
  }
  .affiliate-get-data{
    margin: 0;
    background: transparent;
    border: 0;
    border-radius: 0;
    background: #d57681;
  }
}
.affiliate-tile{
  background-color: #e0dddd;
  padding: 15px 0;
  margin: 15px 0;
  .right{
    text-align: center;
    padding-left: 0 !important;
  }
  .tile_stats_count{
    .count{
      font-size: 16px;
    }
    &:not(:last-child){
      border-right: 1px solid #303030;
    }
  }
}

.search-input-div {
  position: relative; 

  .form-control {
    width: 100%;  
    padding-right: 30px;
  }

  .search-input-addon {
    position: absolute; 
    right: 10px;
    top: 50%; 
    transform: translateY(-50%);
    font-size: 16px;  
    pointer-events: none;
  }

  .glyphicon-search {
    color: #000;
  }
}

#selected-categories {
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  list-style-type: none;
  
  li {
    background-color: #ecebeb;
    padding: 2px 12px;
    border-radius: 25px;
    margin: 4px;
    display: flex;
    align-items: center;
    font-size: 15px;
    width: fit-content;

    button.remove {
      background-color: transparent;
      border: none;
      font-size: 16px;
      color: #000000;
      margin-left: 10px;
      cursor: pointer;
      transition: color 0.2s, transform 0.2s;
      display: flex;
      align-items: center;

      &:hover {
        color: #d9363e;
        transform: scale(1.2);
      }
    }
  }
}

.dropdown-menu-categories
{
    display: block;
    position: relative;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ccc;
    padding: 10px;
    background-color: #f9f9f9;
    width: 100%;
    .category-item {
      display: flex;
      align-items: center;
      gap: 12px;
      input[type="checkbox"] {
        margin: 0px;
      }
      label {
        margin: 0px;
      }
    }
  
}