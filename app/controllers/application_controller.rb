class ApplicationController < ActionController::Base

  # Prevent CSRF attacks by raising an exception.
  # For APIs, you may want to use :null_session instead.
  protect_from_forgery with: :exception
  protect_from_forgery with: :null_session, if: Proc.new {|c| c.request.xhr? }

  before_filter :run_first
  before_filter :redirect_admin_urls, unless: :is_app_request?
  before_filter :redirect_affiliate_urls, unless: :is_app_request?
  before_filter :currency_values_view, unless: :is_app_request?
  before_filter :set_cart, unless: :is_app_request?
  before_filter :set_theme, unless: :is_app_request?
  before_filter :ensure_domain  if Rails.env.production? #(|| Rails.env.staging?) && ENV['MAINTAINANCE_ON'].to_i == 1
  before_filter :mobile_redirect, if: Proc.new { |a| !a.is_app_request? && a.check_if_mobile_valid? }
  before_filter :set_unbxd_cookie, unless: :is_app_request?
  before_filter :store_location, unless: :devise_controller?
  before_filter :set_subscription_image, unless: :is_app_request?
  before_filter :set_static_menu, unless: :is_app_request?
  before_filter :set_dynamic_cookie, unless: :is_app_request?
  before_filter :set_testing_environment, unless: :is_app_request?
  before_filter :dynamic_value_for_pages
  # skip_before_filter :verify_authenticity_token
  helper_method :offer_message_pages?

  APP_DOMAIN = 'www.mirraw.com'
  US_COUNTRY = 'united states'

  def append_info_to_payload(payload)
    super
    payload[:host] = request.host
    payload[:remote_ip] = request.remote_ip
    payload[:ip] = request.ip
    payload[:x_forwarded_for] = request.env['HTTP_X_FORWARDED_FOR']
    payload[:true_client_ip] = request.headers['True-Client-IP']
    payload[:remote_addr] = request.remote_addr
    payload[:http_true_client_ip] = request.env["HTTP_TRUE_CLIENT_IP"]
    payload[:user_agent] = request.user_agent
  end


  def call_shield_square
    @shieldsquare_call_type ||= 1 #//Update corresponding value as per CallType in the below table.
    @shieldsquare_response = Ss2.shieldsquare_ValidateRequest("shield_square_user_name", @shieldsquare_call_type, "", request, cookies)
    if @shieldsquare_response.responsecode == 0
      logger.debug "Allow the user request"
    elsif @shieldsquare_response.responsecode == 3
      logger.debug "Block This request"
      redirect_to("https://www.mirraw.com/block") && return
    elsif @shieldsquare_response.responsecode == -1
      logger.debug "Please reach out to ShieldSquare support team for assistance"
      logger.debug "Allow the user request"
    end
  end

  def ip_blocker
    if request.path.include?('/etc/passwd') ||
      request.path.include?('wp-admin') ||
      request.path.include?('wp-login') ||
      request.path.include?('acunetix_wvs_security_test') ||
      request.path.include?('win.ini') ||
      (IP_BLOCK_LIST.include?(request.ip) ||
      IP_BLOCK_LIST.include?(request.env["HTTP_TRUE_CLIENT_IP"]))
      redirect_to("/block") && return
    end
  end

  def run_first
    if is_app_request?
      Design.country_code      = 'IN'
    else
      call_shield_square if ENV['SS2_KEY'].present? && !(params[:controller] == 'errors' && params[:action] == 'block_page')
      ip_blocker if ENV['IP_BLOCK_ENABLE'].present? && !(params[:controller] == 'errors' && params[:action] == 'block_page')
      session[:token] = current_account.token if account_signed_in? && current_account.user? && session[:token].blank?
      Account.current_account = current_account

      if (params[:controller] == 'store') || (params[:controller] == 'designs' && params[:action] == 'show') || (params[:controller] == 'orders') || (params[:controller] == 'carts' && params[:action] == 'show') || (params[:controller] == 'coupons' && params[:action] == 'all')
        session[:previous_request_url] = session[:current_request_url]
        session[:current_request_url] = Zlib.crc32 request.original_url.to_s
        #Creating array for saving multi channel utm_source, utm_medium, utm_campaign
        session[:utm_campaign] = '' unless session[:utm_campaign].present?
        session[:utm_source] = '' unless session[:utm_source].present?
        session[:utm_medium] = '' unless session[:utm_medium].present?
        session[:utm_expid] = params[:utm_expid] if params[:utm_expid].present?

        if session[:utm_term].to_s.size > 100
          session[:utm_term] = session[:utm_term][/(([\w-]+\.){1,3}\w{2,6})(?:\/|$)/]
        end
        if session[:icn_term].to_s.size > 100
          session[:icn_term] = session[:icn_term][/\icn=[\w\d\-_]+/i] || session[:icn_term][/(([\w-]+\.){1,3}\w{2,6})(?:\/|$)/]
        end

        if session[:previous_request_url] != session[:current_request_url]
          session[:utm_campaign].concat(",#{params[:utm_campaign]}") if params[:utm_campaign].present?
          session[:utm_source].concat(",#{params[:utm_source]}") if params[:utm_source].present?
          session[:utm_medium].concat(",#{params[:utm_medium]}") if params[:utm_medium].present?

          session[:utm_term] = params[:utm_term] if params[:utm_term].present?
          session[:utm_content] = params[:utm_content] if params[:utm_content].present?
          session[:icn_term] = params[:icn_term] if params[:icn_term].present?

          if params[:utm_source].blank? && params[:utm_campaign].blank?
            if params[:gclid].present?
              session[:utm_source].concat(",google")
              session[:utm_medium].concat(",cpc")
              session[:utm_campaign].concat(",none")
            elsif request.referer.present? && request.referer.exclude?('utm_source') && request.referer.include?('google')
              session[:utm_source].concat(",google")
              session[:utm_medium].concat(",organic")
              session[:utm_campaign].concat(",none")
              session[:utm_term] ||= request.referer.to_s[/(([\w-]+\.){1,3}\w{2,6})(?:\/|$)/]
            elsif request.referer.present? && request.referer.exclude?('utm_source') && request.referer.include?('bing')
                session[:utm_source].concat(",bing")
              session[:utm_medium].concat(",organic")
              session[:utm_campaign].concat(",none")
              session[:utm_term] ||= request.referer.to_s[/(([\w-]+\.){1,3}\w{2,6})(?:\/|$)/]
            elsif request.referer.present? && request.referer.exclude?('utm_source') && request.referer.include?('yahoo')
              session[:utm_source].concat(",yahoo")
              session[:utm_medium].concat(",organic")
              session[:utm_campaign].concat(",none")
              session[:utm_term] ||= request.referer.to_s[/(([\w-]+\.){1,3}\w{2,6})(?:\/|$)/]
            elsif request.referer.present?
              session[:utm_term] ||= request.referer.to_s[/(([\w-]+\.){1,3}\w{2,6})(?:\/|$)/]
            end
          end
        end

        #Saving only last five entries of utm_source, utm_medium, utm_campaign in session
        if session[:utm_campaign].present? && session[:utm_campaign].count(',') >= 6
          del_ca = session[:utm_campaign].index(',',1).to_i
          session[:utm_campaign] = session[:utm_campaign].slice!(del_ca..1000)
        end
        if session[:utm_source].present? && session[:utm_source].count(',') >= 6
          del_s = session[:utm_source].index(',',1).to_i
          session[:utm_source] = session[:utm_source].slice!(del_s..231)
        end
        if session[:utm_medium].present? && session[:utm_medium].count(',') >= 6
          del_m = session[:utm_medium].index(',',1).to_i
          session[:utm_medium] = session[:utm_medium].slice!(del_m..231)
        end

        if request.referer.present? &&  request.referer.include?('icn=')
          session[:icn_term] ||= request.referer.to_s[/\icn=[\w\d\-_]+/i]
        end
        if request.referer.present? &&  session[:utm_term].blank?
          session[:utm_term] ||= request.referer.to_s[/(([\w-]+\.){1,3}\w{2,6})(?:\/|$)/]
        end
      end
      case params[:controller]
      when 'fashion_updates'
        cookies[:custom_ckeditor_ui] ='fashion_updates'
      when 'rails_admin/main', 'admin'
        cookies[:custom_ckeditor_ui] = nil
      end
    end
  end

  rescue_from Koala::Facebook::APIError do |exception|
    # redirect to fb auth dialog
    logger.info "#{exception.fb_error_type}"
    if ['toggle_fb_autopost'].include?(params[:action])
      redirect_to account_omniauth_authorize_path(:facebook, scope: "manage_pages,publish_pages")
    else
      redirect_to account_omniauth_authorize_path(:facebook)
    end
  end

  def user_for_paper_trail
    account_signed_in? ? current_account.id : 'Public'  # or whatever
  end

  def ensure_domain
    if ENV['MAINTAINANCE_ON'].to_i == 1 && request.env['HTTP_HOST'].include?(ENV['ASSETS_HOST'])
      query_string = ''
      if request.query_parameters.is_a?(Hash)
        request.query_parameters.each{|k,v| query_string +="#{k}=#{v}&" }
        query_string = "?#{query_string[0..-2]}"
      end
      return redirect_to "#{ENV['MIRRAW_MOBILE_DOMAIN']}#{request.path}#{query_string}"
    end
    if request.env.present? && request.env["HTTP_HOST"].present?  
      if request.env['HTTP_HOST'] == 'mirraw-staging.herokuapp.com'
      # do nothing
      elsif request.env['HTTP_HOST'].include?('www1.mirraw.com') || request.env['HTTP_HOST'].include?('www2.mirraw.com') || request.env['HTTP_HOST'].include?('origin1.mirraw.com') || request.env['HTTP_HOST'].include?('origin.mirraw.com') || request.env['HTTP_HOST'].include?('nordr') || request.env['HTTP_HOST'].include?('54321-www.mirraw.com')
      # do nothing
      elsif request.env['HTTP_HOST'] != APP_DOMAIN
      # HTTP 301 is a "permanent" redirect
      redirect_to "https://#{APP_DOMAIN}#{request.path}", :status => 301
      end
    end  
  end

  def store_return_to
    session[:return_to] = request.url
  end

  def store_location
    session['prev_url'] = request.fullpath if request.get? && (params[:controller] != 'designs' || params[:action] == 'show') && !request.xhr?
  end

  def current_ability
    @current_ability ||= Ability.new(current_account)
  end

  def current_graph
    @current_graph ||= Koala::Facebook::API.new(session[:token])
  end

  def after_sign_in_path_for(resource)
    if resource.designer?
      designer_path(current_account.designer)
    elsif resource.user?
      location = session['prev_url']
      return location || session[:return_to] || request.env['omniauth.origin'] || root_path
    else
      return session[:return_to] || request.env['omniauth.origin'] || root_path
    end
  end

  def signed_in_root_path(resource)
    if resource.designer?
      designer_path(current_account.designer)
    else
      root_path
    end
  end

  def after_sign_out_path_for(resource_or_scope)
    if params[:designer].present? && params[:designer]
      flash[:notice] = I18n.t(:signed_out, :scope => [ :devise, :sessions ])
      sellers_sign_in_path
    else
      root_path
    end
  end

  def authenticate_account!(*args)
    if is_app_request?
      begin
        current_designer  = Designer.monetize_designer(params[:token].gsub("\\r","\r").gsub("\\n","\n"))
        if current_designer.blank?
          raise
        else
          @current_account = current_designer.account
          @designer = current_designer
        end
      rescue
        head :unauthorized
      end
    else
      super
    end
  end

  # def delayed_job_admin_authentication
  #   authenticate_account!
  #   authorize! :read, DelayedJobAdmin
  # end

  rescue_from CanCan::AccessDenied do |exception|
    respond_to do |format|
      format.any(:html, :pdf) do
        flash[:alert] = exception.message
        redirect_to(request.env["HTTP_REFERER"].nil? ? main_app.root_url : request.env["HTTP_REFERER"])
      end
      format.json { head :forbidden, content_type: 'text/html' }
    end
  end

  def designer_list
    if Rails.env.production?
      Rails.cache.fetch('designer_list', :expires_in => 24.hours) { Designer.graded.published }
    else
      Designer.graded.published
    end
  end

  def currency_values_view
    session[:country].try(:deep_symbolize_keys!)
    client_ip = request.headers['HTTP_X_FORWARDED_FOR']
    if client_ip.present?
      first_ip = client_ip.split(',').first.to_s.strip
      ip_lookup = promise{MAXMIND_IP.lookup(first_ip)}
    else
      ip_lookup = promise{MAXMIND_IP.lookup(request.ip)}
    end
    if session[:country].blank? || session[:country][:country_code].blank?
      if DETECT_CURRENCY
        if params[:country_code].present?# && browser.bot?
          country_code = params[:country_code]
        elsif request.headers['HTTP_CLOUDFRONT_VIEWER_COUNTRY'].present?
          country_code = request.headers['HTTP_CLOUDFRONT_VIEWER_COUNTRY']
        elsif request.env['HTTP_HOST'].present? && request.env['HTTP_HOST'].include?('54321-www.mirraw.com')
          country_code = 'US'
        elsif request.headers['COUNTRY'].present?
          country_code = request.headers['COUNTRY']
        else
          country_code = ip_lookup.country.iso_code
        end
        set_currency(country_code)
        @display_currency_m = true if session[:country][:name].downcase != 'india'
      else
        set_currency('US')
      end
      session[:country][:city] = ip_lookup.city.name || ''
    end

    if session[:actual_country].blank?
      # Setting actual country as per response set by akamai
      if session[:country][:name].present? && request.headers['HTTP_CLOUDFRONT_VIEWER_COUNTRY'].present?
        session[:actual_country] = session[:country][:name].downcase || (CurrencyConvert.currency_convert_memcached.select{|c| c.country_code == request.headers['HTTP_CLOUDFRONT_VIEWER_COUNTRY']}.try(:country).try(:downcase)).presence || US_COUNTRY
      elsif request.env['HTTP_HOST'].present? && request.env['HTTP_HOST'].include?('54321-www.mirraw.com')
        session[:actual_country] = US_COUNTRY
      elsif request.headers['COUNTRY'].present? && ENV['AKAMAI'].present? && ENV['AKAMAI'].to_i == 1
        session[:actual_country] = session[:country][:name].downcase || US_COUNTRY
      else
        session[:actual_country] = ip_lookup.country.name.try(:downcase) || US_COUNTRY
      end
    end
    Design.country_code    = session[:country][:country_code] || request.headers['HTTP_CLOUDFRONT_VIEWER_COUNTRY']  || request.headers['COUNTRY'] || ip_lookup.country.iso_code || 'US'

    if Rails.env.seller? && Design.country_code.to_s.upcase != 'IN'
      set_currency('IN')
      Design.country_code      = 'IN'
      session[:actual_country] = 'india'
    end
    @actual_country        = session[:actual_country]
    @symbol                = session[:country][:symbol]
    @country_code          = session[:country][:country_code]
    @delivery_city         = session[:country][:city].presence || ip_lookup.city.name || ''
    @conversion_rate       = CONVERSION_RATES[@country_code] || session[:country][:rate]
    @hex_symbol            = session[:country][:hex_symbol]
    @offer_message         = promise{ Promotions.offer_message(@country_code) }
    @timer_promotion        = promise { PromotionPipeLine.active_promotion_for_timer }
    return @conversion_rate,@symbol
  end

  def designers_show(designer)
    @designer = designer
    @ga_list = "DesignerPage"
    @express_delivery_count = DesignerOrder.unscoped.where(designer_id: designer.id).where(confirmed_at: 3.month.ago.beginning_of_day..Date.today.end_of_day).where(state: 'pending').joins(:order).where('orders.express_delivery is not null').count
    @designer_orders = DesignerOrder.unscoped.where(designer_id: designer.id).where(created_at: Designer::NO_MONTHS.months.ago.beginning_of_month..Time.now).group("to_char(created_at, 'YYYYMM')").order('to_char_created_at_yyyymm').count
    @total_orders = designer.total_orders_excluding_warehouse(designer.id)
    @cancel_orders = DesignerOrder.where(designer_id: designer.id, created_at: Designer::NO_MONTHS.months.ago.beginning_of_day..Date.today.end_of_day).where("(cancel_reason in (?) and state = 'canceled') or state = 'vendor_canceled'",['Out of Stock','Vendor canceled']).where('confirmed_at is not null').count
    @sla_critical_orders_count = @designer.designer_orders.sla_violated.count
    @warehouse_orders = WarehouseOrder.where(designer_id: designer.id).where(created_at: Designer::NO_MONTHS.months.ago.beginning_of_day..Date.today.end_of_day).where(state: 'pending').group(:state).count
    @announcements = Announcement.live
    @seller_campaigns = SellerCampaign.where('end_date >= ?', Date.today).order(created_at: :desc)
    @reviews_count_last_90_day = VENDOR_SUSPENSION_METRIC['exception_list'].include?(@designer.id) || (@designer.banned? || @designer.on_hold?) ? 0 : (@designer.reviews.approved.where('created_at::DATE > ?', 90.days.ago).count)
    @seo = SeoList.where(:label => @designer.cached_slug).first

    # @vacation_mode_on = @designer.vacation_mode_on?
    # @coupons = @designer.coupons.live.advertise
    # @followers = @designer.followers

    unless @designer.name?
      respond_to do |format|
        format.html { redirect_to edit_designer_path(@designer), notice: 'Please setup your profile.'}
      end
    else
      # @design = Design.new
      # @design.variants.build
      # @design.retail = true

      # Temp hack
      if request.xhr?
        respond_to do |format|
          format.js {render :template => '/designers/show' }
        end
      else
        respond_to do |format|
          format.html {render :template => '/designers/show', layout: 'seller'}
        end
      end
    end
  end

  def designer_additional_discount_variables
    tomorrow = Date.tomorrow
    @start_date = @designer.additional_discount_start_date.present? ? @designer.additional_discount_start_date.strftime("%A, %d %B, %Y") : tomorrow.strftime("%A, %d %B, %Y")
    @end_date = @designer.additional_discount_end_date.present? ? @designer.additional_discount_end_date.strftime("%A, %d %B, %Y") : tomorrow.strftime("%A, %d %B, %Y")
    @current_discount_percent = @designer.vendor_additional_discount_percent.to_i
    @drop_down_data = VENDOR_ADDITIONAL_DISCOUNT_DROPDOWN_LIST.split(",").map{|text| [ (/\(.*\)/.match(text) || text).to_s.strip,text.to_i] }
  end

  def offer_message_pages?
    (params[:controller] == 'designs' && params[:action] == 'show') ||
    (params[:controller] == 'store' && ['catalog2', 'full_image', 'collection'].include?(params[:action])) ||
    (params[:controller] == 'dynamic_landing_pages' && params[:action] == 'show') ||
    (params[:controller] == 'pages' && params[:action] == 'eid')
  end

  def dynamic_value_for_pages
    if controller_name == 'designs' && action_name == 'show' && @design.present?
      ecomm_prodid = @design['id']
      @ecomm_pagetype = 'product'
      ecomm_totalvalue = @design['discount_price'] * @rate
      @ecomm_category = @design['category_name']
  
    elsif controller_name == 'store' &&  action_name == 'catalog_page'
      if params[:kind].present?
        @ecomm_pagetype = 'category'
        @ecomm_category = @category.try(:name) || params[:kind]
      elsif params[:collection]
        @ecomm_pagetype = 'category'
        @ecomm_category = params[:collection]
      elsif params[:q].present?
        @ecomm_pagetype = 'searchresults'
        @ecomm_category = params[:q]
      end
      if @store_page.try(:[], 'designs').present?
        ecomm_prodid = @store_page['designs'].collect{|ds| ds['id']}
      end
    elsif controller_name == 'pages' && action_name == 'home'
      @ecomm_pagetype = 'home'
  
    elsif controller_name == 'store' && action_name == 'dynamic_landing_page' && @dynamic_landing_page.present?
      @ecomm_pagetype = 'other'
      @ecomm_category = @dynamic_landing_page.name
  
    elsif (controller_name == 'pages' || controller_name == 'store') && action_name == 'landing' && @landing.present?
      @ecomm_pagetype = 'other'
      @ecomm_category = @landing.label
  
    elsif controller_name == 'pages' && action_name == 'coupons'
      @ecomm_pagetype = 'other'
      @ecomm_category = 'coupons'
  
    elsif controller_name == 'pages' && action_name == 'eid'
      @ecomm_pagetype = 'other'
      @ecomm_category = 'eid'
  
    elsif controller_name == 'sessions' && action_name == 'guest_login'
      @ecomm_pagetype = 'guest_login'
    elsif controller_name == 'carts' && action_name == 'show' && @cart.present?
      ecomm_prodid = @cart.line_items.collect(&:design_id)
      @ecomm_pagetype = 'cart'
      # ecomm_totalvalue = @cart.item_total(1) - @cart.total_discounts
      @ecomm_category = @cart.line_items.to_a.map{|line_item|  line_item.nil? ? nil : line_item.design.categories.pluck(:name).first}
  
  
    elsif controller_name == 'orders' && action_name == 'new' && @cart.present?
      ecomm_prodid = @cart.line_items.collect(&:design_id)
      @ecomm_pagetype = 'ordernew'
      # ecomm_totalvalue = @cart.item_total(1) - @cart.total_discounts
      @ecomm_category = @cart.line_items.map{|line_item| line_item.design.categories.first.name}
  
    elsif controller_name == 'orders' && action_name == 'show' && @track
      ecomm_prodid = @order.line_items.collect(&:design_id)
      @ecomm_pagetype = 'purchase'
      ecomm_totalvalue = @order.total
      @ecomm_category = @order.line_items.map{|line_item| line_item.design.categories.first.name}
  
    else
      @ecomm_pagetype = 'other'
    end
  end

  def check_admin_account
    unless current_account.admin?
      redirect_to root_path, notice: 'You are not authorized to access this page.'
    end
  end

  def is_app_request?
    (request.headers['Device-ID'].present? && request.headers['Device-ID'] == API_DEVICE_ID)
  end

  def check_if_mobile_valid?
    ['rack_api'].exclude?(params[:controller])
  end


  private

  def gtm_data_layer
    @gtm_data_layer ||= []
  end

  def redirect_affiliate_urls
    return if devise_controller?
    if account_signed_in? && current_account.accountable_type == 'Affiliate' && !params[:controller].in?(['affiliate', 'affiliate_designs'])
      redirect_to affiliate_path
    end
  end

  def redirect_admin_urls
    if Rails.env.seller? && !account_signed_in? && ['store','designs','messages'].include?(params[:controller])
      redirect_to sellers_sign_in_path
    end
    if account_signed_in?
      if current_account.admin?
        if (request.url.downcase.include? DESKTOP_SITE_URL.downcase) && (DESKTOP_SITE_URL.downcase != ADMIN_URL.downcase )
          redirect_to (request.url.downcase).sub(DESKTOP_SITE_URL.downcase,ADMIN_URL.downcase), status: 302
        end
        if params[:model_name] == "designer" && !can_access_designer_details
          redirect_to '/', alert: "You are not an allow to access designer page"
        end
        if params[:model_name] == "tailor" && ['super_admin'].exclude?(get_signed_in_role)
          redirect_to '/', alert: "You are not an allow to access tailor page"
        end
        if ['order', 'designer_order', 'design', 'image', 'account', 'user', 'additional_payment', 'line_item', 'coupon','currency_convert', 'ticket','return'].include?(params[:model_name]) && ['super_admin','marketing','senior_marketing','operations','support'].exclude?(current_account.role.try(:name))
          redirect_to '/', alert: "You are not an allow to access"
        end
        if params[:model_name] == 'public_system_constant' && params[:action] == 'edit' && PUBLIC_CONSTANT_EDIT_EMAIL.split(",").exclude?(current_account.email)
          redirect_to '/'
        end
        if params[:model_name] == 'order' && params[:_method] == 'put' && params[:order] && params[:order][:state] == 'sane'
          order  = Order.where(id: params[:id]).first
          if order && order.state != 'sane'
            flash[:error] =  "You are not allow to change the order state to sane."
            redirect_to "/admin/order/"
          end
        end
      elsif current_account.designer? && (request.url.downcase.include? DESKTOP_SITE_URL.downcase) && (DESKTOP_SITE_URL.downcase != SELLER_URL.downcase )
        redirect_to (request.url.downcase).sub(DESKTOP_SITE_URL.downcase,SELLER_URL.downcase), status: 302
      end
    elsif params[:account].present? && params[:account][:accountable_type] == 'designer' && (request.url.downcase.include? DESKTOP_SITE_URL.downcase) && (DESKTOP_SITE_URL.downcase != SELLER_URL.downcase)
      redirect_to (request.url.downcase).sub(DESKTOP_SITE_URL.downcase,SELLER_URL.downcase), status: 302
    end
  end

  def can_access_designer_details
    ['super_admin'].include?(get_signed_in_role) ||
    (
      params[:source_abstract_model] == 'designer_shipper' &&
      params[:current_action] == 'create' &&
      ['operations'].include?(get_signed_in_role) &&
      Ability.new(current_account).can?(:read, Designer)
    )
  end

  def get_signed_in_role
    @role_name ||= current_account.role.try(:name)
  end


  def current_cart
    @current_cart ||= Cart.all_rel.where(used: false).find_by_id(session[:cart_id]) if session[:cart_id].to_i > 0
    # This cond. check for user previous cart after login
    if (@current_cart.blank? ? account_signed_in? : (account_signed_in? && @current_cart.total_items <= 0))
      @current_cart = set_from_previous_cart
    end
    # This cond. assign guest cart to user if present, after login
    unless @current_cart.blank?
      set_user_for_cart(@current_cart)
      @current_cart.update_attribute(:email, current_account.try(:email))
      current_pe_subscriber.update_cart(@current_cart) if current_pe_subscriber
      # clear already ordered items and create duplicates for them
      refresh_ordered_items_in_cart
    end
    if @current_cart.blank?
      cart = Cart.create(used: false, hash1: SecureRandom.hex(16))

      session[:cart_id] = cart.id
      cart
    else
      @current_cart
    end
  end

  def set_cart
    # This is @cart
    # @cart knows future is uncertain
    # @cart is lazy
    # @cart makes promise
    # @cart procrastinate
    # @cart is smart
    # be like @cart
    # lazyness is key to success
    @cart = promise{ current_cart }
    @cart_recommendations_designs = promise{ @cart.recommendations(@conversion_rate, @actual_country) }
  end

  def set_feed_index
    @notification_index = TimelineEvent.first.try(:id)
    if @notification_index && session[:my_current_feed_index].blank?
      session[:my_current_feed_index] = TimelineEvent.first.id
    end
  end

  def get_lineage(kind)
    category = Category.find_by_namei(kind)
    a = Array.new
    until category.root?
      a << category.name
      category = category.parent
    end
    a << category.name
    return a
  end

  def get_facebook_friends_female
    user = current_account.user
    @friends = user.friends.select('first_name, uid').where(:gender => 'female')

    if @friends.blank?
      friends = current_graph.get_connections("me", "friends", :fields => "first_name,last_name,gender")
      friends.each do |friend|
        Friend.create(:first_name => friend['first_name'],
                       :last_name => friend['last_name'],
                       :gender => friend['gender'],
                       :uid => friend['id'],
                       :user_id => user.id)
      end
      @friends = user.friends.select('first_name, uid').where(:gender => 'female')
    end
    @friends
  end

  def get_facebook_friends_on_mirraw
    user = current_account.user
    _friends = user.friends.select('uid').pluck(:uid)

    if _friends.blank?
      friends = current_graph.get_connections("me", "friends", :fields => "first_name,last_name,gender")
      friends.each do |friend|
        Friend.create(:first_name => friend['first_name'],
                       :last_name => friend['last_name'],
                       :gender => friend['gender'],
                       :uid => friend['id'],
                       :user_id => user.id)
      end
      _friends = user.friends.select('uid').pluck(:uid)
    end
    @accounts = Account.where(:provider => "facebook", :uid => _friends)
    @friends_on_mirraw = @accounts.collect! {|a| a.accountable }
  end

  def set_currency(country_code)
    countries_currencies = CurrencyConvert.currency_convert_memcached
    country_details      = countries_currencies.select{|c| c.country_code == country_code}.first if country_code != '--'
    country_details      = countries_currencies.select{|c| c.country_code == 'US'}.first if country_details.blank?
    session[:country]    = {
                             :name => country_details.country,
                             :symbol => country_details.symbol,
                             :rate => country_details.rate,
                             :country_code => country_details.country_code,
                             :hex_symbol => country_details.hex_symbol
                           }
  end

  def create_date(date_params)
    year = date_params[:year].to_i
    month = date_params[:month].to_i
    day = date_params[:day].to_i
    begin
      date = Date.new(year,month,day)
    rescue
      date = Date.new(year,month).end_of_month
    end
  end

  def get_date(by = 'current')
    # current_period - Start of current month to current date
    # month - Month diff from current date
    if params[:f_start_date].present? and params[:f_end_date].present?
      start_date = Date.parse params[:f_start_date]
      end_date = Date.parse params[:f_end_date]
    elsif params[:start_date].present? and params[:end_date].present?
      start_date = create_date params[:start_date]
      end_date = create_date params[:end_date]
    else
      end_date = Date.today
      case by
      when 'current'
        start_date = Date.today.beginning_of_month
      when 'month'
        start_date = 1.month.ago
      end
    end
    [start_date, end_date]
  rescue ArgumentError
    [Date.today.beginning_of_month, Date.today]
  end

  def report_file_aws(html_string, filename, email)
    csv_string = TableCSV.new
    csv_string.attach(html_string)
    file_data = csv_string.content
    UploadReport.export_csv_data(file_data, filename, email)
  end

  def mobile_redirect
    return if request.fullpath.start_with?('/ahoy/messages')

    # if session[:cart_id] == -1 && current_account.blank? && browser.mobile? && browser.android? && !browser.tablet?
    if session[:cart_id].to_i <= 0  && current_account.blank? && (browser.device.mobile? || browser.device.tablet?)
      status = false

      # Here we are checking weather the request.fullpath match with any of the mobile route pattarns
      # If yes user is being redirected to mobile site
      if request.fullpath =~ MOBILE_PATTERN && !(request.fullpath =~ DESKTOP_PATTERN)
        request.headers['Vary'] = 'User-Agent'
        redirect_to (request.url).sub(request.base_url, ENV.fetch('MIRRAW_MOBILE_DOMAIN')),
          status: 302
      end
    end
  end

  # This method set previous cart after login if cart in empty
  def set_from_previous_cart
    cart = current_account.unused_carts.last
    if !cart.blank? && cart.id != -1
      session[:cart_id] = cart.id
      Cart.all_rel.find_by_id(session[:cart_id])
    end
  end

  # This method set user for logged in user
  def set_user_for_cart(cart)
    if account_signed_in?
      unless current_account.blank? && cart.user.present?
        cart.user = current_account.accountable
        cart.save
      end
    end
  end

  def set_subscription_image
    if cookies[:user_subscribed].blank?
      @subscription_banner_img = promise{Rails.cache.fetch("subscription_banner_#{@country_code}", :expires_in => 24.hours) {
              banner = NewsletterBanner.where("app_source = 'desktop' OR app_source = 'universal'").mirraw.live.country(@country_code).first
              url_value = banner.present? ? banner : 'https://deqjvj8xmhubl.cloudfront.net/assets/sub7.png'
            }}
    end
  end

  def set_static_menu
    @static_menu = promise{
      Menu.menu_column_and_item_by_hide_appsource_country(request.headers['HTTP_CLOUDFRONT_VIEWER_COUNTRY'] || request.headers['HTTP_COUNTRY'] || session[:country][:country_code]).all
    }
  end

  def set_unbxd_cookie
    if cookies[:'unbxd.userId'].blank?
      if account_signed_in?
        if current_account.unbxd_user_id.blank?
          cookies[:'unbxd.userId'] = "uid-#{Time.now.to_i}-#{rand(100000)}"
          current_account.update_attributes(unbxd_user_id: cookies[:'unbxd.userId'])
          cookies[:'unbxd.visit'] = 'first_time'
        else
          cookies[:'unbxd.userId'] = current_account.unbxd_user_id
          cookies[:'unbxd.visit'] = 'repeat'
        end
      else
        cookies[:'unbxd.userId'] = "uid-#{Time.now.to_i}-#{rand(100000)}"
        cookies[:'unbxd.visit'] = 'first_time'
      end
    end
    @top_searches_unbxd = promise{ RequestStore.cache_fetch(:top_searches_unbxd_data, :expires_in => 6.hours) {
      # begin
      #   url = ENV['TOP_SEARCH_UNBXD_URL'] || 'http://trending-queries.unbxdapi.com/trending/site/mirraw_com-u1447848356766'
      #   response = HTTParty.get(url)
      #   top_query = response.body.present? ? JSON.parse(response.body, quirks_mode: true) : [{'query'=>'jewellery'}, {'query'=>'sarees'}, {'query'=>'lehengas'}]
      #   search_value = top_query.is_a?(Hash) ? [{'query'=>'jewellery'}, {'query'=>'sarees'}, {'query'=>'lehengas'}] : top_query
      # rescue
        [{'query'=>'jewellery'}, {'query'=>'sarees'}, {'query'=>'lehengas'}]
      #end
    } }
  end

  def set_dynamic_cookie
    if session[:cart_total_items].to_i > 0 || account_signed_in? || @theme_tester.current_theme != 'black_theme'
      cookies[:dynamic_user] = 't'
    else
      cookies.delete :dynamic_user
    end
    if account_signed_in?
      cookies[:signed_in] = 1
    else
      cookies.delete :signed_in
    end
  end

  def ensure_cart_not_empty
    redirect_to root_url, :notice => "Your cart is empty!" if current_cart.line_items.empty?
  end

  def set_testing_environment
    experiments = session[:experiment].try(:deep_symbolize_keys) || {}
    @tester = AbTesting::Tester.new(@country_code, get_user_experiment_id, experiments[:all], experiments[:current_experiment])
  end

  def set_theme
    preffered_theme = ['development', 'staging', 'production'].include?Rails.env ? 'red_theme' : (params[:theme] || 'black_theme')
    @theme_tester = AbTesting::ThemeTester.new(preffered_theme)
    theme_value = gon.theme = @theme_tester.current_theme
    cookies[:theme] = { value: theme_value, expires: (Time.current + 1.year) }
  end

  def get_user_experiment_id
    session[:user_exp_id] ||= rand(1..100)
  end

  def refresh_ordered_items_in_cart
    unless @already_refreshed
      @current_cart.refresh_ordered_items!
      @already_refreshed = true
    end
  end

  def current_pe_subscriber=(pe_subscriber)
    @current_pe_subscriber = begin
      session[:pe_subscriber_id] = pe_subscriber.id
      cookies[:pe_subscribed] = {value: 't', expires: 1.year.from_now} # may used by gtm
      pe_subscriber
    end
  end

  def current_pe_subscriber
    @current_pe_subscriber ||= if session[:pe_subscriber_id].present?
      PushEngageSubscriber.find_by_id(session[:pe_subscriber_id])
    end
  end

end
