class DesignerCampaignParticipationsController < ApplicationController
    def create
        @seller_campaign = SellerCampaign.find params[:seller_campaign_id]
        @designer = Designer.find params[:designer_id]
        @designer_campaign_participation = DesignerCampaignParticipation.create(seller_campaign: @seller_campaign, designer: @designer)
        redirect_to all_campaign_path(@designer)
    end

    def cancel
        @designer_campaign_participations = DesignerCampaignParticipation.find params[:id]
        @designer = Designer.find @designer_campaign_participations.designer_id
        if @designer_campaign_participations.present?
            @designer_campaign_participations.destroy
        else
            message = 'Cannot find Campaign'
        end
        redirect_to all_campaign_path(@designer), notice: message
    end
end
  