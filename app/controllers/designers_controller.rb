class DesignersController < ApplicationController
  before_filter :authenticate_account!, :except => [:index, :show, :edit_designer_invoice,:designer_detail_for_online_sales, :all_campaign]
  load_and_authorize_resource except: [:edit_designer_invoice ,:designer_detail_for_online_sales], unless: :is_app_request?
  before_filter :ensure_current_designer_url, :only => :show
  before_filter :rectify_fb_posting, unless: :is_app_request?
  caches_action :index, expires_in: 48.hours

  layout 'seller', only: [:additional_discount,:claim_page, :bulk_dispatch, :show, :manage_boutique, :oos_bestseller_designs, :pickup, :view_arrange_pickup, :prepare_manifest, :upload_manifest, :edit, :update ,:account_health, :historical_data, :fb_publish_new, :manage_pages, :payout_invoices, :upload_invoices, :review_report, :rank, :bulk_upload, :failed_designs, :failed_images, :feedback, :all_feedback, :order_quality_report, :design_review_report, :ads, :all_campaign]

  # GET /designers
  # GET /designers.json
  def index
    @integration_status = "new"
    @designers = Designer.select('name, id, cached_slug').where(state_machine: ['approved','review']).where("name is NOT NULL").order('name ASC')

    respond_to do |format|
      format.html # index.html.erb
      # format.json { render json: @designers }
    end
  end

  # GET /designers/1
  # GET /designers/1.json
  def show
    @designer = Designer.find(params[:id])

    @seo = SeoList.where(:label => @designer.cached_slug).first

    @vacation_mode_on = @designer.vacation_mode_on?

    @coupons = @designer.coupons.live.advertise

    if !@designer.inactive_designer_state?
      if params[:sort] == "unpublished"
        @designs = @designer.designs.unpublished.includes([:images]).paginate(:page => params[:page], :per_page => 60)
      elsif params[:sort] == "bstslr"
        @designs = @designer.designs.published.retail.includes([:images]).order('designs.sell_count DESC').paginate(:page => params[:page], :per_page => 60)
      else
        @designs = @designer.designs.published.retail.includes([:images]).order('id DESC').paginate(:page => params[:page], :per_page => 60)
      end
    end

    #Pulling Designer Default Master Addons Values
    columns = 'addon_type_values.id as atv_id, addon_types.id as at_id, addon_type_values.name as atv_name, addon_types.name as at_name,master_addons.price as price,
    addon_type_values.position as atv_position, categories.name as category_name, master_addons.prod_time as prod_time'
    @master_addons = Designer.joins(:master_addons => [:addon_type, :addon_type_value, :category]).where(:id => @designer.id).select(columns).where(:allow_addon => true).group_by(&:at_name)

    designer_additional_discount_variables

    #Pulling All OptionTypes With Category
    columns = 'option_type_values.*, option_types.name as ot_name, categories.id as cat_id'
    @option_types = OptionTypeValue.joins(option_type: :category).select(columns).order('option_type_values.position ASC').group_by(&:ot_name)

    @sortVal = params[:sort]
    @followers = @designer.followers

    unless @designer.name?
      respond_to do |format|
        format.html { redirect_to edit_designer_path(@designer), notice: 'Please setup your profile.' }
      end
    else
      @design = Design.new
      @design.variants.build
      @design.retail = true

      # Temp hack
      if request.xhr?
        respond_to do |format|
          format.js
        end
      else
        respond_to do |format|
          format.html
        end
      end
    end
  end

  # GET /designers/new
  # GET /designers/new.json
  def new
    @designer = Designer.new
    @designer.account = Account.new

    respond_to do |format|
      format.html # new.html.erb
      format.json { render json: @designer }
    end
  end

  # GET /designers/1/edit
  def edit
    @designer = Designer.find(params[:id])
    @disable_fields = [:account_holder_name,:account_number,:bank_name,:ifsc_code,:branch,:cancelled_cheque].collect{|d_attribute| d_attribute if @designer.try(d_attribute).present?}.compact
    unless @designer.account.terms_of_service?
      redirect_to vendor_agreement_path(@designer) if current_account.designer?
    end
  end

  # POST /designers
  # POST /designers.json
  def create
    @designer = Designer.new(params[:designer])
    respond_to do |format|
      if @designer.save
        b = Shipper.find_by_name("Bluedart")
        f = Shipper.find_by_name("Fedex")
        @designer.shippers = [b, f]
        format.html { redirect_to @designer, notice: 'Designer was successfully created.' }
        format.json { render json: @designer, status: :created, location: @designer }
      else
        format.html { render action: "new" }
        format.json { render json: @designer.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /designers/1
  # PUT /designers/1.json
  def update
    @designer = Designer.find(params[:id])
    @disable_fields = params[:disable_fields].to_s.split(" ")
    designer_params_hash = designer_params
    designer_params_hash = designer_params_hash.except(*designer_params_hash.select{|k,v| v.blank? }.keys)
    except_values = current_account.designer? ? [*@disable_fields, 'state_machine'] : [*@disable_fields, 'state_machine', 'phone', 'alt_phone']
    designer_hash = designer_params_hash.except(*except_values)
    respond_to do |format|
      if @designer.update_attributes(designer_hash)
        if designer_params[:state_machine].present? && ['accounts_admin'].include?(current_account.role.try(:name)) &&
        (e = @designer.state_machine_transitions.find{|transition| transition.to == designer_params[:state_machine]}).present?
          @designer.fire_state_machine_event(e.event)
        end
        format.html { redirect_to @designer, notice: 'Your profile was successfully updated.' }
        format.json { render json: {status: 200} }
      else
        format.html { render action: "edit" }
        format.json { render json: @designer.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /designers/1
  # DELETE /designers/1.json
  def destroy
    @designer = Designer.find(params[:id])
    @designer.destroy

    respond_to do |format|
      format.html { redirect_to designers_url }
      format.json { head :ok }
    end
  end

  def all_campaign
    @designer = Designer.find(params[:id])
    if params[:start_date].present? && params[:end_date].present?
      start_date = Date.parse(params[:start_date]).beginning_of_day
      end_date = Date.parse(params[:end_date]).end_of_day
      @seller_campaigns = SellerCampaign.where('start_date >= ? AND end_date <= ?', start_date, end_date).paginate(page: params[:page], per_page: 6)
    else
      @seller_campaigns = SellerCampaign.where('end_date >= ?', Date.today.end_of_day).paginate(page: params[:page], per_page: 6)
    end
  end

  def account_health
    @metric_values = @designer.current_metric_values.designer_visible.preload(:metric_definition).group_by(&:name).as_json
    respond_to do |format|
      format.html { render 'designers/seller_metrics/account_health' }
      format.json { head :ok }
    end
  end

  def historical_data
    if  stale?(last_modified: Date.today.beginning_of_day)
      visible_clause = current_account.admin? ? {visible_to_admin: true} : {visible_to_designer: true}
      if params[:metric_definition_id].present?
        @metric_definitions = MetricDefinition.where(id: params[:metric_definition_id]).where(visible_clause)
      else
        @metric_definitions = MetricDefinition.where(visible_clause)
      end
      if @metric_definitions.present?
        @metric_values = promise {@metric_definitions.first.metric_values.where(actor_id: @designer.id,actor_type: 'Designer').where(generated_on: (@metric_definitions.first.duration || 30).days.ago.to_date..Date.today).select('generated_on,value').order(:generated_on).map{|metric_value| [metric_value.generated_on,metric_value.value]}.to_h}
      end

      respond_to do |format|
        format.html { render 'designers/seller_metrics/historical_data' }
        format.json { render json:  @metric_values }
      end
    end
  end

  def bulk_dispatch
    redirect_to designer_url(@designer), alert: 'Your bulk dispatch access is temporarily disabled. <NAME_EMAIL> to know more.' and return unless @designer.bulk_dispatch
    dates = Date.parse(params['daterange_selector'].split('-')[0]).beginning_of_day..Date.parse(params['daterange_selector'].split('-')[1]).end_of_day rescue 3.months.ago.beginning_of_day..Date.today.end_of_day
    @state = params[:options_for_filter] || 'pending'
    if @state == 'dispatched'
      condition = if params[:tracking_num].present?
                    {tracking_num: params['tracking_num']}
                  elsif params[:bulk_shipment_id].present?
                    {id: params['bulk_shipment_id']}
                  else
                    nil
                  end
      @shipments = Shipment.joins(:bulk_designer_orders).where(bulk_designer_orders: {designer_id: @designer.id}).where(condition).where(order_id: nil, designer_order_id: nil).where(created_at: dates).group(:id).preload(bulk_designer_orders: [:payment_order, line_items: [[design: :images], line_item_addons: :addon_type_value]]).paginate(page: params[:page], per_page: 10)
    else
      @designer_orders = @designer.designer_orders.where(ship_to: 'mirraw').where(bulk_shipment_id: nil).where(created_at: dates).where(state: @state).preload(:delivery_nps_info,:order, line_items: [[design: :images], line_item_addons: :addon_type_value]).paginate(page: params[:page], per_page: 15)
    end
  end

  def get_domestic_shipper_names
    if (des_orders = DesignerOrder.where(id: params[:dos_ids])).present?
      shipper_names = []
      clickpost_serviceable = []
      des_orders.collect do |dos|
        shipper_names << dos.get_serviceable_shipper_names
        clickpost_serviceable << dos.clickpost_serviceable
      end
      dispatch_through_clickpost = clickpost_serviceable.all?{|x| x == true}
      shipper_names = shipper_names.inject(:&)
      shippers,rapid = Shipper::ALL_SHIPPERS.slice(*shipper_names),false
      shippers,rapid = if shippers.keys.include?('RAPID DELIVERY')
        [Shipper::ALL_SHIPPERS.slice('RAPID DELIVERY'),true]
      elsif (names = (shippers.keys & DOMESTIC_PREPAID_COURIER_AUTOMATION['mirraw'].to_a.map(&:upcase))).present?
        [shippers.slice(*names),true]
      else
        [shippers,rapid]
      end
      if shippers.present?
        invoice_total = LineItem.joins(:designer_order).where(designer_order_id: params[:dos_ids]).sum('line_items.vendor_selling_price * line_items.quantity * (100 - designer_orders.transaction_rate)/100.0').to_f
      end
      render json: shippers.present? && invoice_total <= (des_orders.first.designer.state == SHIPPER_STATE ? 100000 : 50000) ? {shippers: shippers, rapid: rapid, encoded_dos_id: Base64.urlsafe_encode64(params[:dos_ids].join('-')), clickpost_serviceable: dispatch_through_clickpost} : {error: 'Please change order combination. No Common Shippers Available.'}
    else
      render json: {error: 'No Common Shippers Available. Please change ordere combination'}
    end
  end

  def feedback
    rating = @designer.reviews.approved.product
    @survey_question_hash = SurveyQuestion.designer_text_hash
    @reviews = rating.nps.comments.preload(:order, :survey_answers, [design: :images], :designer).limit(20).order('reviews.id desc').group_by(&:order)
    @rating = {}
    [30,90,365,nil].each do |no_of_days|
      @rating[no_of_days] = (no_of_days ? rating.where{created_at > no_of_days.days.ago} : rating).rating_group_text
    end
  end

  def all_feedback
    @survey_question_hash = SurveyQuestion.designer_text_hash
    @reviews_data = @designer.reviews.approved.product.nps.comments.preload(:order, :survey_answers,[design: :images], :designer).order('reviews.id desc').paginate(page: params[:page],per_page: 50)
    @reviews = @reviews_data.group_by(&:order)
  end

  def order_quality_report
    @all_event_names = ScopeEvent.where(visible_to_designer: true).pluck(:name)
    @headers = params[:event_names] || @all_event_names
    date_range = params['daterange_selector'].present? ? Date.parse(params['daterange_selector'].split('-')[0]).beginning_of_day..Date.parse(params['daterange_selector'].split('-')[1]).end_of_day : 3.months.ago.beginning_of_day..Time.current
    if params[:commit] == 'Email Report'
      DesignerOrder.sidekiq_delay
                   .download_quality_report(
                     @designer.id,
                     date_range,
                     @headers,
                     current_account.email
                   )
      flash[:notice] = 'File will be emailed to you shortly.'
      redirect_to (request.env["HTTP_REFERER"] || designers_order_quality_report_path(@designer)) and return
    end
    defective_dos_ids = DesignerOrder.where(designer_id: @designer.id).where('order_id is not null and state <> ?','new').joins(:scope_events).where(created_at: date_range).where(scope_events: {visible_to_designer: true}).uniq.pluck(:id)
    @all_orders       = DesignerOrder.where(id: defective_dos_ids).preload(:payment_order).eager_load(:scope_events).where(created_at: date_range).paginate(page: params[:page], per_page: 30)
    respond_to do |format|
      format.html { render 'designers/seller_metrics/order_quality_report' }
      format.json { @metric_values }
    end
  end

  def set_vacation_mode
    # @designer = Designer.find(params[:designer_id])
    if params[:commit] == 'End My Vacation Now'
      flash[:notice]  = 'Vacation mode removed successfully.'
      DesignersController.sidekiq_delay(queue: 'low')
                         .state_change(@designer.id, 'review')
    else
      begin
        @designer.vacation_start_date = Date.parse(params[:vacation_start_date]).beginning_of_day
        @designer.vacation_end_date = Date.parse(params[:vacation_end_date]).end_of_day
        @designer.vacation_message = params[:vacation_message]
        if @designer.save(validate: false)
          set_vacation_state(@designer)
        else
          flash[:alert] = @designer.errors.messages.values.join(',')
        end
        flash[:notice]  = 'Vacation mode applied successfully. Please wait for some time for your status on dashboard to change to Vacation.'
      rescue => e
        flash[:alert] = e.message
      end
    end
    redirect_to :back
  end

  def set_inactive_mode
    if params[:commit] == 'Mark account active'
      flash[:notice]  = 'Your acccount has been marked as active successfully.'
      @designer.dissatisfied
    elsif params[:commit] == 'Mark account inactive'
      flash[:notice] = 'Your acccount is marked as inactive. Your products will not be visible on panel.'
      @designer.to_inactive
    end
    redirect_to :back
  end

  def set_vacation_state(designer)
    DesignersController
      .sidekiq_delay_until(designer.vacation_start_date, queue: 'low')
      .state_change(designer.id, 'vacation')
    DesignersController
      .sidekiq_delay_until(designer.vacation_end_date.end_of_day - 1.hour, queue: 'low' )
      .state_change(designer.id, designer.state_machine)
  end

  def self.state_change(designer_id, state)
    designer = Designer.find_by_id(designer_id)
    if designer.present? && designer.state_machine != state
      case state.to_sym
        when :banned
          designer.banned_1
        when :approved
          designer.satisfied
        when :review
          designer.dissatisfied
        when :vacation
          designer.on_vacation if designer.vacation_start_date.present? && designer.vacation_end_date.present?
        when :inactive
          designer.to_inactive
      end
      designer.save!(validate: false)
    end
  end

  def manage_boutique
    @designs = @designer.designs.includes([:images,:variants])
    if params["search"].present?
      params["search"].gsub!(/\=|\>|\</,' ')
      @designs = @designs.search_for(params["search"]).paginate(:page => params[:page])
    else
      @sort = params[:sort].presence || (is_app_request? ? 'all' : 'published')
      case @sort
        when "unpublished"
          @designs = @designs.unpublished.paginate(:page => params[:page], :per_page => 30)
        when 'banned'
          @designs = @designs.banned.paginate(:page => params[:page], :per_page => 30)
        when 'published'
          @designs = @designs.published.order('created_at DESC').paginate(:page => params[:page], :per_page => 30)
        when 'reject'
          @designs = @designs.reject.order('created_at DESC').paginate(:page => params[:page], :per_page => 30)
        when 'review'
          @designs = @designs.review.order('created_at DESC').paginate(:page => params[:page], :per_page => 30)
        when 'all'
          @designs = @designs.order('created_at DESC').paginate(:page => params[:page], :per_page => 30)
        else
          @designs = @designs.where(:state => @sort).order('created_at DESC').paginate(:page => params[:page], :per_page => 20)
      end
    end
    @stock_updates = StockUpdate.where(designer_id: @designer.id).order('id desc')
    @stock_updates = @stock_updates.where(account_id: @designer.account.id) if current_account.designer?
    @stock_updates = @stock_updates.paginate(page: params[:page], per_page: 15)
    respond_to do |format|
      format.html
      format.json
    end
  end

  def oos_bestseller_designs
    @designs = @designer.get_oos_bestseller_designs
    pagination_hash = {:page => params[:page], :per_page => 30}
    if params["search"].present?
      params["search"].gsub!(/\=|\>|\</,' ')
      @designs = @designs.search_for(params["search"]).paginate(pagination_hash)
    else
      @sort = params[:sort].presence || 'all'
      @designs = if params[:sort]
                  @designs.send(:"#{@sort}").order('created_at DESC').paginate(pagination_hash)
                else
                  @designs.order('created_at DESC').paginate(pagination_hash)
                end
    end
  end

  def claim_page
    redirect_to designer_url(@designer), alert: 'Your claim page access is temporarily disabled. <NAME_EMAIL> to know more.' and return if @designer.inactive_designer_state?
    @claim_products = DesignerIssue.joins(:order).includes(:designer,design: [:images]).where("claim_mark = ? and designer_issues.state=? and orders.state=?",true,'pending','sane').paginate(page: params[:page], per_page: 20)
    @claim_requests = ClaimRequest.preload(design: [:designer,:images]).where(designer_issue_id: @claim_products.map(&:id),designer_id: @designer.id).group_by(&:designer_issue_id)
  end

  def claim_form
    design = Design.find_by_id(params[:design_id])
    designer_id = current_account.accountable_id
    if design.present?
      designer = design.designer
      if designer.id == designer_id
        designer_issue = DesignerIssue.find_by_id(params[:designer_issue])
        if designer_issue.claim_requests.pluck(:design_id).include?(design.id)
          render :json => {:error => 'Claim Request Present For This Design !'}
        elsif design.images.count == 0
          render :json => {:error => 'You cannot claim request for a design without uploading images !'}
        else
          claim_req=designer_issue.claim_requests.create(design_id: design.id,designer_id: designer.id)
          render :json => {message: 'Claim request successfully generated.',design_title: design.title,design_code: design.design_code,cached_slug: design.cached_slug,designer_slug: designer.cached_slug}
        end
      else
        render :json => {:error => 'Enter from a design which you have uploaded !'}
      end
    else
      render :json => {:error => 'Enter a valid design number !'}
    end
  end

  def get_bulk_upload_ids
    @designer_ids = Designer.find_by_id(params[:designer_id]).designer_batch_ids.sort
    if @designer_ids.present?
      render :json => {:status => 'ok', :designer_ids => @designer_ids }
    else
      render :json => {:status => 'error', :errors => 'No Batches Found'}
    end
  end

  def additional_discount
    @designer = Designer.find(params[:id])
    redirect_to designer_url(@designer), alert: 'No Additional Discount Facility for Transfer Based Model for now.' and return if @designer.is_transfer_model?
    if request.post?
      begin
        @designer.apply_additional_discount(params[:additional_discount].to_i,params[:start_date].to_s,params[:end_date].to_s)
        flash[:notice] = "Your additional discount is now effective. It may take some time to reflect"
      rescue => error
        redirect_to :back, flash: {error: error.message}
      end
    end
    tomorrow = Date.tomorrow
    @start_date = @designer.additional_discount_start_date.present? ? @designer.additional_discount_start_date.strftime("%A, %d %B, %Y") : tomorrow.strftime("%A, %d %B, %Y")
    @end_date = @designer.additional_discount_end_date.present? ? @designer.additional_discount_end_date.strftime("%A, %d %B, %Y") : tomorrow.strftime("%A, %d %B, %Y")
    @current_discount_percent = @designer.vendor_additional_discount_percent.to_i
    @design_data = (@designer.designs.where('discount_percent > 5').first || @designer.designs.first || Design.first).generate_additional_discount_data
    @drop_down_data = VENDOR_ADDITIONAL_DISCOUNT_DROPDOWN_LIST.split(",").map{|text| [ (/\(.*\)/.match(text) || text).to_s.strip,text.to_i] }
  end

  def download_csv
    delayed_file_name = Hash.new("#{params[:state]} Inventory")
    delayed_file_name.merge!({
      'published' => 'Published Inventory',
      'unpublished' => 'Unpublished Inventory'
    })
    request = {
      name: delayed_file_name[params[:state]],
      method: 'inventory_csv',
      params: [params[:state]],
      file_type: 'csv',
      object: {@designer.class.to_s => @designer.id, "is_object?" => true},
      account: {@designer.account.class.to_s => @designer.account.id, "is_object?" => true}
    }
    render json: DelayedFile.create_delayed_file(request)
  end

  def bulk_upload
    redirect_to designer_url(@designer), alert: "Your upload access is temporarily disabled. <NAME_EMAIL> to know more." if @designer.inactive_designer_state? || @designer.state_machine == "review_pending" || !@designer.upload_panel?
    if request.post?
      error = ''
      begin
        xlsx = Roo::Spreadsheet.open(params[:file].tempfile, extension: :xlsx)
        xlsx.default_sheet = xlsx.sheets[1]
      rescue => e
        error = e
      end
      version=(params[:version]||:Saree).to_sym
      if xlsx.try(:first_row) && error.blank?
        format = if BulkUpload.meta_data[version][:extra][:amazon_url].present? && xlsx.try(:first) === BulkUpload.meta_data[version][:amazon_header]
                  'amazon'
                elsif params[:home_decor_types].present? && xlsx.try(:first) === BulkUpload.meta_data[:HomeDecorVariant][:amazon_header]
                  version = :HomeDecorVariant
                  'amazon'
                elsif xlsx.try(:first) === BulkUpload.meta_data[version][:header]
                  'mirraw'
                end
      end

      # check for Valid Xl
      if format.present?
        # @designer = Designer.find(params[:designer_id])
        if Rails.env.test?
          batch_url = params[:test_url]
        else
           directory = Fog::Storage.new({
            provider: 'AWS',
            aws_access_key_id: ENV.fetch('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key: ENV.fetch('AWS_SECRET_ACCESS_KEY'),
            region: ENV.fetch('AWS_REGION')
          }).directories.new(key: ENV.fetch('S3_BUCKET'))

          filename  = "designer_bulk_uploads/#{@designer.id}/#{Time.now.strftime("%m_%d_%H_%M") + SecureRandom.hex(2) +'.xlsx'}"
          # upload that file
          file = directory.files.create(
            :key    => filename,
            :body   => params[:file].read,
            :public => true
          )
          batch_url = file.public_url
        end
        @designer_batch = DesignerBatch.new
        @designer_batch.designer_id = @designer.id
        @designer_batch.filename = batch_url
        @designer_batch.version=version
        @designer_batch.save
        # @designer.sidekiq_delay(queue: 'low')
        #          .version_bulk_upload_designs(
        #            batch_url,
        #            @designer_batch,
        #            format,
        #            current_account
        #          )
        SidekiqDelayGenericJob.set({queue: 'low'})
                              .perform_async(
                                @designer.class.to_s, 
                                @designer.id,
                                "version_bulk_upload_designs",
                                batch_url,
                                {@designer_batch.class.to_s => @designer_batch.id },
                                format,
                                {current_account.class.to_s => current_account.id}
                              )
        redirect_to designers_bulk_upload_path, :notice => 'Bulk update is scheduled and shall reflect in 30 mins.'
      else
        redirect_to designers_bulk_upload_path, :alert => "File processing failed #{error}"
      end
    end

    @version_bulk_upload=[]
    BulkUpload.meta_data.each do |version,value|
      if value[:extra].present? && ((url=value[:extra][:url]).present? || value[:extra][:amazon_url].present?)
        amazon_format = value[:extra][:amazon_url]
        @version_bulk_upload << {name: version,url: url,new_url: amazon_format}
      end
    end
    gon.home_decor = DesignBulk::HOMEDECOR
  end

def uploading_failed_designs
    row = []
    row << params['design_code']
    row << params['title']
    row << params['category']
    row << params['package_details']
    row << params['weight_in_gms']
    row << params['fabric_of_saree']
    row << params['saree_color']
    row << params['length_of_saree_in_metres']
    row << params['width_of_saree_in_inches']
    row << params['work']
    row << params['type_name']
    row << params['blouse_availability']
    row << params['fabric_of_blouse']
    row << params['blouse_color']
    row << params['size_of_blouse_in_cms']
    row << params['blouse_as_shown_in_the_image']
    row << params['blouse_work']
    row << params['petticoat_availability']
    row << params['size_of_petticoat_metres']
    row << params['color_of_petticoat']
    row << params['fabric_of_petticoat']
    row << params['image']
    row << params['image1']
    row << params['image2']
    row << params['image3']
    row << params['image4']
    row << params['tag_list']
    row << params['quantity']
    row << params['price']
    row << params['discount_percent']
    row << params['occasion']
    row << params['look']
    row << params['saree_border']
    row << params['pallu_style']
    row << params['region']
    row << params['pattern']
    row << params['embellish']
    row << params['celebrity']
    row << params['description']
    row << params['product_type']
    @failed_design_id = params['design_id'].to_i
    # @designer = Designer.find params[:designer_id]
    @designer_batch = DesignerBatch.find params[:failed_batch_id]
    # @designer.sidekiq_delay(queue: 'low')
    #          .save_single_record(row, @designer_batch, @failed_design_id)
    SidekiqDelayGenericJob.set({queue: 'low'}).perform_async(@designer.class.to_s, 
                                                            @designer.id, 
                                                            "save_single_record", 
                                                            row, 
                                                            {@designer_batch.class.to_s => @designer_batch.id}, 
                                                            @failed_design_id )
    redirect_to failed_designs_path
  end

  def uploading_version_failed_designs
    # to do complete make over
    row=[]
    params[:value].each do |key,value|
      row[key.to_i]=value 
    end
    designer = @designer
    designer_batch = DesignerBatch.find params[:failed_batch_id]
    failed_design_id = params['design_id'].to_i
    failed_design = designer_batch.version_failed_designs.where(id: failed_design_id).first
    if designer.id == designer_batch.designer_id && failed_design.present?
      version=designer_batch.version.to_sym
      version_method=version.to_s.underscore
      data={}
      BulkUpload.meta_data[version][:categories_designs].to_a.each do |category|
        data[category] = Category.names_to_ids(category)
      end
      data[:collection] = designer.designer_collections_mapper
      data = data.merge(Property.get_property_value_mapping(BulkUpload.meta_data[version][:designs_property_values].to_a))
      data = data.merge(OptionType.get_option_type_values(BulkUpload.meta_data[version][:variants].to_a))
      design=DesignBulk.new(designer_id:designer.id,designer_batch_id:designer_batch.id)
      design.set_transfer_price_based_on_version(row,version) if designer.is_transfer_model?
      variant_codes = version_method == 'bag' ? BagUpload.bag(design, row, data) : BulkUpload.send(version_method, design, row, data)
      error = []
      error += design.errors.full_messages if design.errors.present?
      error += design.errors.full_messages unless design.valid?
      error << 'Variant SKU Code is already taken' if design.variants.present? && design.is_variant_design_code_uniq?(variant_codes)
      if designer.is_transfer_model? && design.variants.present?
        discount_value = (100 - design.discount_percent).to_f/100
        design.variants.each do |variant|
          variant.transfer_price = design.transfer_price
          variant.price = ((design.transfer_price.to_f / (1 - (design.designer.transfer_model_rate/100.0) * (1 + IGST/100.0)))/discount_value).to_i
        end
      end
      design.quantity = design.variants.map(&:quantity).compact.sum if design.variants.present?
      unless error.present?
        Design.transaction do
          DesignBulk.import [design],validate: false,recursive: true
          designer_batch.decrement(:failed, 1)
          designer_batch.increment(:passed, 1)
          designer_batch.save
          SidekiqDelayGenericJob.perform_async(design.class.to_s, design.id, "post_process")
          failed_design.delete
        end
      else
        failed_design.update_attributes(error: error,row: row)
      end
    end
    redirect_to failed_designs_path << "?failed_batch_id=#{designer_batch.id}"
  end

  def failed_designs
    redirect_to root_url unless @designer.upload_panel?
    @ids = @designer.designer_batches.order('created_at DESC').pluck(:id)
    design_state = DESIGN_STATES.split(',') - ['review','reject','processing']

    @batch_id = params['failed_batch_id'].presence || @ids.try(:first)
    if (@failed_batch = DesignerBatch.find_by_id @batch_id).present?
      if (version_failed_design=@failed_batch.version).present?
        @version_header=BulkUpload.meta_data[version_failed_design.to_sym][:header]
        @skip_columns  =BulkUpload.meta_data[version_failed_design.to_sym][:skip_columns] || []
        @failed_designs = VersionFailedDesign.where(designer_batch_id: @batch_id).paginate(page: params[:page], per_page: 40)
      else
        @failed_designs = FailedDesign.where(designer_batch_id: @batch_id).paginate(page: params[:page], per_page: 40)
      end
      @design_approved = @failed_batch.designs.where('state IN (?)', design_state).count
      @design_review = @failed_batch.designs.where(state: ['review','processing']).count
      @design_rejected = @failed_batch.designs.where(state: "reject").count
      @failed_images = DelayImage.joins(:design).where(designs: {designer_batch_id: @failed_batch.id}).includes(:design).limit(100)
    end
  end

  def delete_processing_skus
    designer_batch = DesignerBatch.find_by_id(params[:batch_ids].to_i)
    if designer_batch.present?
      if designer_batch.designer_id != params[:designer_id].to_i
        render json: { error: 'Unauthorized access: Designer batch does not belong to this designer' } and return
      end
      
      if designer_batch.designs.processing.exists? || 
         designer_batch.failed_designs.exists? || 
         designer_batch.version_failed_designs.exists?
        lock_key = "lock:delete_designs:#{params[:batch_ids].to_i}"
        if Redis.current.get(lock_key)
          render json: { notice: 'Please wait as the deletion for this batch is currently under process'}
        else
          DeleteProcessingSkusJob.perform_async(params[:batch_ids], Account.current_account.email)
          render json: { notice: "Processing skus for the designer batch #{designer_batch.id} will be deleted shortly" }
        end
      else
        render json: { notice: 'No processing SKUs or failed designs found for this batch' }
      end
    else
      render json: { notice: 'Designer batch not found' }
    end
  end

  def failed_images
    # @designer = Designer.find(params[:designer_id])
    redirect_to root_url unless @designer.upload_panel?
    @failed_images = @designer.delay_images.includes(:design).order('updated_at desc').paginate(page: params[:page], per_page: 40)
  end

  def update_failed_images
    designer = @designer
    batch = designer.designer_batches.where(id: params["failed_batch_id"]).first
    image = designer.delay_images.where(id: params["image_id"]).first
    if image.present?
      image.update_attributes(url: params["url"].gsub(/dl=0/, 'dl=1'),message: nil)
      # image.sidekiq_delay.run
      SidekiqDelayGenericJob.perform_async(image.class.to_s, image.id, "run")
    end
    redirect_to failed_designs_path(designer)
  end

  def upload_csv
    begin
      # @designer = Designer.find(params[:designer_id])
      authorize! :manage, @designer

      connection = Fog::Storage.new({
        :provider                 => 'AWS',
        aws_access_key_id: ENV['AWS_ACCESS_KEY_ID'],
        aws_secret_access_key: ENV['AWS_SECRET_ACCESS_KEY'],
        :region => "ap-southeast-1"
      })

      # First, a place to contain the glorious details
      directory = connection.directories.new(
        :key    => "mirraw-test"
      )

      filename  = "stock_update/#{@designer.id}/#{Time.now.strftime("%m_%d_%H_%M") + SecureRandom.hex(2) +'.csv'}"
      # upload that file
      file =  directory.files.create(
        :key    => filename,
        :body   => params[:file].read,
        :public => true
      )
      stock_batch = StockUpdate.new
      stock_batch.designer_id = @designer.id
      stock_batch.inventory_file_name = file.public_url
      stock_batch.account_id = current_account.id
      stock_batch.save
      can_edit_price = current_account.can_edit_price?
      # @designer.sidekiq_delay
      #          .stock_update(
      #            stock_batch,filename,
      #            current_account,
      #            can_edit_price
      #          )
      SidekiqDelayGenericJob.perform_async(@designer.class.to_s,
                                            @designer.id,
                                            "stock_update",
                                            {stock_batch.class.to_s => stock_batch.id},
                                            filename,
                                            {current_account.class.to_s => current_account.id},
                                            can_edit_price
                                            )
      redirect_to :back, :notice => 'Stock update is scheduled and shall reflect in 30 mins.'
    rescue NoMethodError
      error = params[:file].blank? ? 'CSV file not uploaded' : ''
      redirect_to request.referer.present? ? :back : designer_path, flash: {error: error}
    rescue => error
      redirect_to :back, flash: {error: error.message}
    end
  end

  def addon_settings
    # @designer = Designer.find(params[:designer_id])
    categories = @designer.categories
    @addon_types_list = Array.new
    #Getting Addon Types & Addon Values For each category the designer is related
    #putting them into an array accordingly
    categories.each do |category|
      master_cat = category.search_master
      addon_types = Array.new
      if master_cat.present?
        master_cat.addon_types.each do |at|
          if (addon_type_values = at.addon_type_values.where(:design_id => nil)).count > 0
            addon_types << {:type_name => at.name,:type_id => at.id,:type_values => addon_type_values}
          end
        end
        @addon_types_list << {:category_name => master_cat.name,:category_id => master_cat.id,:addons => addon_types}
      end
    end
    @addon_types_list.uniq!
  end

  def set_addons_settings
    # @designer = Designer.find(params[:designer_id])
    if params[:category_id].present?
      category_id = params[:category_id].to_i
      if params[:addon_types].present?
        addon_types = params[:addon_types]
        addon_types.each do |key, at|
          addon_type_value_id = at[:addon_type_value_id].to_i
          price = at[:price].to_i
          addon_type_id = at[:addon_type_id].to_i
          prod_time = at[:prod_time].to_i

          master_addon = MasterAddon.where(:addon_type_id => addon_type_id,
          :addon_type_value_id => addon_type_value_id,:category_id => category_id,
          :designer_id => @designer.id).first_or_create
          master_addon.update_attributes(:price => price, :prod_time => prod_time)
        end
      end
    end
    redirect_to :back, :notice => 'Addons Settings Saved'
  end

  def discontinue_designs
    redirect_to :back, notice: 'Please select atleast one product !' and return unless params[:design_ids].present?
    ids = params[:design_ids]
    if params[:commit] == 'Delete'
      @designer.designs.where({id: params[:design_ids]}).update_all({state: 'delete_by_mirraw', deleted_on: Time.now})
    elsif params[:commit] == 'Reject'
      @designer.designs.where({id: params[:design_ids]}).update_all({state: 'reject', reject_on: Time.now})
    elsif params[:commit] == 'InStock'
      invalid_designs = []
      if @designer.review_pending?
        invalid_designs.push({id: 'All designs', error: 'Vendor is not approved !'})
        ids = []
      else
        @designer.designs.where({id: params[:design_ids]}).each do |des|
          des.state = 'in_stock'
          if !des.valid?
            invalid_designs.push({id: des.id, error: des.errors.full_messages})
          elsif des.quantity <= 0
            invalid_designs.push({id: des.id, error: 'No Available Stock !'})
          end
        end
        ids = params[:design_ids].map(&:to_i) - invalid_designs.collect(&:first).collect(&:last)
      end
      @designer.designs.where({id: ids}).update_all({state: 'in_stock', last_in_stock: Time.now, last_in_stock_by: current_account.accountable_type}) if ids.present?
      DesignerMailer.sidekiq_delay
                    .sendout_design_error_notification(
                      current_account.email,
                      invalid_designs,
                      'Few designs could not be moved to in_stock state'
                    ) if invalid_designs.present?
    elsif params[:commit] == 'OutOfStock'
      @designer.designs.where({id: params[:design_ids]}).update_all({state: 'seller_out_of_stock', last_seller_out_of_stock: Time.now, quantity: 0})
    end

    DesignersController.sidekiq_delay
                       .reindex_discontinued_designs(
                         params[:designer_id],
                         ids, params[:commit]
                       ) if ids.present?

    redirect_to :back
  end

  def product_analytics
    # @designer = Designer.find(params[:designer_id])
    @designs = @designer.designs.where('sell_count > 5')
    @designs_return = @designer.designs.where('sell_count > 20').where('return_count > 0')
    @designs = @designs.where('designs.created_at > ?', params[:range].to_i.days.ago) if params[:range].present? && params[:range].to_i > 0
    if params[:type].present? && params[:type] == 'low_stock'
      @designs = @designs.where('designs.state = ? AND designs.quantity < ?', 'in_stock', 2).paginate(:page => params[:page])
    elsif params[:type].present? && params[:type] == 'oos'
      @designs = @designs.where('designs.state IN (?)', ['sold_out', 'seller_out_of_stock', 'blocked']).paginate(:page => params[:page])
    elsif params[:type].present? && params[:type] == 'buyer_return'
      @designs = @designs_return.where('(((return_count*1.0)/sell_count)*100) > 10').paginate(:page => params[:page])
      @design_type = 'return'   
    end

    if params[:type] == 'low_stock' && params[:range] == '-1'
      @message = "All time best sellers that currently have low stock count. You should try to increase the stock count of above products so that you don't lose sales when few quantities are sold out."
    elsif params[:type] == 'low_stock' && params[:range].to_i > 0
      @message = "Products that were selling well in the last #{params[:range]}  days and have low stock count. You should try to increase the stock count of above products so that you don't lose sales when few quantities are sold out."
    elsif params[:type] == 'oos' && params[:range] == '-1'
      @message = "All time best sellers that are currently out of stock. You should definitely be investing good amount of time and effort to bring best sellers back in stock. This will give you best ROI."
    elsif params[:type] == 'oos' && params[:range] == '90'
      @message = "Products that sold well in last 3 months and are currently out of stock. You should definitely be investing good amount of time and effort to bring best sellers back in stock. This will give you best ROI."
    elsif params[:type] == 'buyer_return' && params[:range] == '-1'
      @message = "The best strategy for online business is competitive price and amazing quality. If your returns are high, it will damage not just your brand but also your revenue and marketing. At Mirraw, we value quality and customer satisfaction above everything else."
    end
  end


  def self.reindex_discontinued_designs(designer_id, design_ids, state, notes = nil)
    @designs = Design.where(id: design_ids)
    @designs = @designs.where(designer_id: designer_id) if designer_id.present?
    @designs.each do |design|
      design.notes = "#{design.notes.to_s}. #{notes}." if notes.present?
      if state == 'InStock'
        designer_catalog = Designer::IN_CATALOG[design.designer.in_catalog]
        if (var = design.check_for_catalog_remove(designer_catalog))
          # design.sidekiq_delay.send("#{var}_catalog_remove")
          SidekiqDelayGenericJob.perform_async(design.class.to_s, design.id, "#{var}_catalog_remove")
        end
      end
      design.save
    end
    if state == 'InStock'
      cnt = DesignCluster.where(design_id: design_ids).update_all(state_multiplier: 1)
    elsif %w(outofstock delete reject).include?(state.try(:downcase))
      cnt = DesignCluster.where(design_id: design_ids).update_all(state_multiplier: 0)
    end
    DesignCluster.recalculate_winners_in_bulk(design_ids) if cnt.to_i > 0
  end

  def manage_pages
    if current_account.token?
      pages = current_graph.get_connections(current_account.uid, "accounts")
      @pages = Hash.new

      pages.each do |page|
        @pages[page['name']] = page['id']
      end
    end
  end

  def toggle_fb_autopost
    designer = current_account.designer
    unless designer
      redirect_to designer_path(params[:id]),flash:{error: 'Only designer can modify facebook autopost'}
      return
    end
    if params[:autopost] == true
      designer.update_attribute(:automated_facebook_posting,true) unless designer.automated_facebook_posting == true
      if designer.can_autopost? && graph_has_permissions(designer,'manage_pages','publish_pages')
        redirect_to designer,notice: 'Facebook automated posting is activated'
      else
        redirect_to account_omniauth_authorize_path(:facebook, scope: "manage_pages,publish_pages")
      end
    elsif params[:autopost] == false
      designer.update_attribute(:automated_facebook_posting,false) unless designer.automated_facebook_posting == false
      redirect_to designer,notice: 'Facebook automated posting is deactivated'
    end
  end

  def facebook_page_unlink
    designer = current_account.designer
    designer.fb_page_name = ''
    designer.fb_page_id = ''
    designer.fb_page_token = ''
    designer.fb_page_link = ''
    designer.save
    redirect_to :back, :notice => 'Facebook Page Unlinked Now.'
  end

  def set_facebook_page
    page = current_graph.get_object(params[:page_id], :fields => "name, access_token, link")
    designer = current_account.designer
    designer.fb_page_name = page["name"]
    designer.fb_page_id = page["id"]
    designer.fb_page_token = page["access_token"]
    designer.fb_page_link = page["link"]
    designer.save
    unless designer.can_autopost?
      redirect_to designer, :notice => "#{page["name"]} Was Linked Successfully."
    else
      redirect_to designer, :notice => "Facebook automated posting is activated for #{page["name"]}."
    end
  end

  def fb_publish_new
    unless @designer.facebook_page_linked?
      redirect_to manage_pages_path(@designer), flash: {error: 'You need to link your facebook page before you can schedule albums.'}
    end
  end

  def fb_publish_create
    designer = current_account.designer
    links = params[:links].split(',')

    run_at = DateTime.new(Time.now.year, params[:date][:month].to_i, params[:date][:day].to_i, params[:date][:hour].to_i, params[:date][:minute].to_i)

    if params[:date][:hour].present?
      session[:fb_page_hour] = params[:date][:hour].to_i
    end

    if params[:links].blank? || params[:name].blank? || params[:description].blank?
      flash[:alert] = "One or More Parameter is missing."
      redirect_to :back
      return
    end

    if Time.zone.local_to_utc(run_at) < DateTime.current.utc
      flash[:alert] = "You cannot schedule Post for the time in the past"
      redirect_to :back
      return
    end

    DesignersController.sidekiq_delay_until(Time.zone.local_to_utc(run_at))
                       .post_to_facebook(
                         links, designer.fb_page_id, designer.fb_page_token,
                         params[:name], params[:description]
                       )
    redirect_to designer_fb_publish_new_path(designer), :notice => "Done!"
  end

  def self.post_to_facebook(links, page_id, access_token, name, description)
    page_graph = Koala::Facebook::API.new(access_token)
    albuminfo = page_graph.put_object(page_id, 'albums', :name => name, :description => description)
    album_id = albuminfo["id"]

    page_graph.batch do |batch_api|
      links.each do |link|
        /.*\/designers\/(.*)\/designs\/(.*)/.match(link.strip)
        designer_id = $1
        design_id = $2
        if designer_id && design_id
          designer = Designer.find(designer_id)
          design = designer.designs.includes([:images]).find(design_id) if designer
          batch_api.put_picture(design.master_image.photo.url(:original), { "message" =>link}, album_id)
        end
      end
    end
  end

  def ensure_current_designer_url
     redirect_to @designer, :status => :moved_permanently unless @designer.friendly_id_status.best?
  end

  def pickup
   # Removed The Content of this page Because it was only fedex specific domestic pick up Only UI is Visible  
  end

  def view_arrange_pickup
    @optional_pickups = OptionalPickup.includes(shipment: [:shipper,:order]).where(designer_id: @designer.id).where('pickup_date > ?', DateTime.yesterday)
  end

  def prepare_manifest
    @shippers = Shipper.joins(:designer_shippers).where(designer_shippers: {designer_id: @designer.id})

    if (shipper_id = params[:shipper_id]).present?
      @designer_orders = @designer.designer_orders.preload(:order,line_items: [design: :images]).includes(:shipment).
                        where(state: 'pickedup',shipment: {
                          shipment_state: 'processing', shipment_type: ['COD','PREPAID'], shipper_id: shipper_id}
                        ).group('shipments.id, designer_orders.id')
    end
    search_string = "ManifestFiles/designers/#{Date.today.strftime('%Y/%b/%d')}/#{@designer.id}"
    @manifest = AWS::S3.new.buckets[ENV['S3_BUCKET']].objects.with_prefix("#{search_string}").collect(&:key)
  end

  def upload_manifest
    if request.post?
      if params[:manifest].present? && (['image/png','image/jpg','image/jpeg','application/pdf'].include? params[:manifest].content_type)
        filename  = "ManifestFiles/designers/#{Date.today.strftime('%Y/%b/%d')}/#{@designer.id}/#{@designer.name}_#{params[:shipper_name].try(:downcase)}_#{Time.now.strftime("%m-%d-%H-%M")}#{File.extname(params[:manifest].original_filename)}"
        file = AwsOperations.create_aws_file(filename,params[:manifest],false)
        redirect_to :back, notice: 'Manifest File is uploaded successfully'
      else
        redirect_to :back, flash:{error: 'Please select image / pdf manifest to be uploaded.'}
      end
    end
  end

  def generate_manifest
    if params[:order_numbers].present?
      order_numbers = params[:order_numbers].collect{
        |key, value| value if value.present?
      }
      if @designer.present? && (shipper_id = params[:shipper_id]).present?
        @designer_orders = @designer.designer_orders.joins(:order).preload(:order,shipment: :shipper).
        where(orders: {number: order_numbers}).uniq.to_a
      end
    end
    if @designer_orders.present? && params[:shipper_id].present?
      file_name = "manifest-#{Time.zone.now.to_date}-#{params[:shipper_id]}.pdf"
      respond_to do |format|
        format.html
        format.pdf do
          render pdf: file_name,
            template:'designers/generate_manifest.html.haml'
        end
      end
    else
      redirect_to :back
    end
  end

  def add_master_addons
    # @designer = Designer.find(params[:designer_id])
    if @designer.blank?
      flash[:alert] = 'Designer not found'
    else
      @designer.check_and_create_master_addon
      flash[:notice] = "Master Addons added for Designer #{@designer.name}"
    end
    redirect_to :back
  end
  
  def payout_invoices
    model = params[:purchase].present? ? 'PurchaseReport' : 'CommissionInvoice'
    create_clause = nil
    if params[:payout_year].present? && params[:payout_month].present?
      @payout_date = Date.parse(params[:payout_year] + '-' + params[:payout_month]+ '-' + '01')
      create_clause= {created_at: (@payout_date.beginning_of_day..@payout_date.end_of_month.end_of_day)}
    end
    if params[:payout].present?
      select_clause = 'DISTINCT payout_version, paid_date, payout_amount, paid, payout_summary_url, utr_number'
      w_clause = 'payout_type = ? AND payout_summary_url is not null', (params[:purchase].present? ? 'International' : 'Domestic')
      @payout = @designer.payout_managements.select(select_clause).where(w_clause).where(create_clause).order(paid_date: :desc).paginate(page: params[:payout_page], per_page: 10)
    else
      if params[:purchase].present?
        @commission_invoices = Object.const_get(model).where(designer_id: @designer.id).where(create_clause).where(is_valid_report: 'true').order('id desc').paginate(:per_page => 20, :page => params[:report_page])
        @adjustments = AdjustmentReport.where(designer_id: @designer.id).where(create_clause).where(is_valid_report: 'true').where(report_type: 'int').order('id desc').paginate(per_page: 20, page: params[:adj_page])
      else
        @commission_invoices = Object.const_get(model).where(designer_id: @designer.id).where(create_clause).order('id desc').paginate(:per_page => 20, :page => params[:report_page])
        @adjustments = AdjustmentReport.where(designer_id: @designer.id).where(create_clause).where(is_valid_report: 'true').where(report_type: 'dom').order('id desc').paginate(per_page: 10, page: params[:adj_page])
      end
      @show_commission = current_account.admin? || (current_account.designer? && SHOW_COMMISSION_REPORT_TO_DESIGNER == 'true')
    end
    @date_year,@date_month = (2015..Date.today.year).to_a,[]
    (1..12).each {|m| @date_month << [Date::MONTHNAMES[m], m]}
  end

  def upload_invoices
    @integration_status = 'new'
    if request.post?
      if params[:invoice_file].present? && params[:invoice_file].size <= 5.megabytes
        designer_invoice = @designer.designer_invoices.build(invoice_file: params[:invoice_file], from_date: params[:invoice_start_date],to_date: params[:invoice_end_date])
        designer_invoice.save!
        redirect_to :back, notice: "Your Invoice will be Uploaded shortly"
      else
        redirect_to :back, flash: { error: "Invoice Not Uploaded : Please Upload File of Size Less Than 5 MB"}
      end  
    else
      @invoices = DesignerInvoice.latest_invoice.where(designer_id: @designer.id).preload(:payment_order).paginate(page: params[:page], per_page: 50)
    end
  end

  def bulk_update_commission_rate
    if params[:type][:csv_file].present?
      data , type ={},params[:type][:csv_file]
      CSV.foreach(params[:csv_file].path,headers: :true) do |row|
        data[row[0].to_i]=row[1].to_f if (row[0].to_i && row[1].to_i) > 0
      end
      if data.blank?
        notice="No Active Designers found"
      else
        type=='tp_rate' ? Designer.sidekiq_delay(queue: 'critical').update_tp_model_rate(data,current_account.email) : Designer.update_commission_model_rate(data,type,current_account.email)
        notice="Updated commission rate report will be mailed to you shortly."
      end
    else 
      notice="Select Commission Type"
    end
    redirect_to admin_payout_management_path,notice: notice
  end

  def review_report
    @integration_status = 'new'
    @start_date = params[:start_date].present? ? Date.parse(params[:start_date]) : Date.today - 30.day
    @end_date = params[:end_date].present? ? Date.parse(params[:end_date]) : Date.today
    @quality_products = @designer.quality_of_designs
    @result = @designer.get_reviews_of_design(@start_date,@end_date)
    @reviews = @designer.reviews.where(assured: 'designer', approved: true)
    @designs = Design.where(id: @result['design_ids']).preload(:images).order('total_review desc').paginate(page: params[:page], per_page: 10)
  end

  def rank
    @integration_status = 'new'
    @all_designers = Designer.ranking.paginate(page: params[:page], per_page: 50)
  end

  def edit_credit_note 
    @edit_credit_note = true
    @show_credit_note_pdf = true
    begin
      order_id, designer_order_id = Base64.urlsafe_decode64(params[:edit_credit_note_link]).include?('/') ? Base64.urlsafe_decode64(params[:edit_credit_note_link]).split('/') : ['',Base64.urlsafe_decode64(params[:edit_credit_note_link]).split('-').reject(&:blank?)]
      if (order_id.match(/^\d+$/) && designer_order_id.match(/^\d+$/)) || (@designer = DesignerOrder.where(id: designer_order_id.first).first.try(:designer)).present?
        @order = Order.find_by_id(order_id) if order_id.present?
        @designer_order = DesignerOrder.where(id: designer_order_id).first
        if @designer.present?
          @line_items = LineItem.where(designer_order_id: designer_order_id).sane_items.preload(:order, design: [:categories,property_values: :property],variant: :option_type_values)
          credit_note_nos = DesignerOrder.where(id: designer_order_id).pluck(:credit_note_number).reject(&:blank?)
        else
          @designer_order = DesignerOrder.find_by_id(designer_order_id)
          @designer = @designer_order.designer
          credit_note_nos = @designer_order.credit_note_number
          render :inline => '<h4> Something went wrong please try again with proper link</h4>' and return if @order.blank? || @designer_order.blank?
        end
        if credit_note_nos.blank?
          # @invoice_number = @designer.get_invoice_number
          # DesignerOrder.where(id: designer_order_id).update_all(invoice_number: @invoice_number)
        elsif (@credit_note_nos = params[:credit_note_number]).present?
          DesignerOrder.where(id: designer_order_id).update_all(credit_note_number: @credit_note_nos)
          @edit_credit_note = false
          @credit_note_nos = nil if @order.present?
          render :layout => false, :pdf => (@order.present? ? @order.number : @credit_note_nos), :template => 'designer_orders/show.html' and return
        elsif @order.blank?
          @credit_note_nos = credit_note_nos.first
        end
        render 'designer_orders/show', layout: false
      else
        redirect_to :inline =>'<h4> Something went wrong please try again with proper link</h4>' and return
      end
    rescue ArgumentError => e
      render :inline =>'<h4> Something went wrong please try again with proper link</h4>'
    end
  end

  def edit_designer_invoice
    @edit_invoice = true
    @show_invoice_pdf = true
    begin
      order_id, designer_order_id = Base64.urlsafe_decode64(params[:edit_invoice_link]).include?('/') ? Base64.urlsafe_decode64(params[:edit_invoice_link]).split('/') : ['',Base64.urlsafe_decode64(params[:edit_invoice_link]).split('-').reject(&:blank?)]
      if (order_id.match(/^\d+$/) && designer_order_id.match(/^\d+$/)) || (@designer = DesignerOrder.where(id: designer_order_id.first).first.try(:designer)).present?
        @order = Order.find_by_id(order_id) if order_id.present?
        @designer_order = DesignerOrder.where(id: designer_order_id).first
        if @designer.present?
          @line_items = LineItem.where(designer_order_id: designer_order_id).sane_items.preload(:order, design: [:categories,property_values: :property],variant: :option_type_values)
          inv_numbers = DesignerOrder.where(id: designer_order_id).pluck(:invoice_number).reject(&:blank?)
        else
          @designer_order = DesignerOrder.find_by_id(designer_order_id)
          @designer = @designer_order.designer
          inv_numbers = @designer_order.invoice_number
          if inv_numbers.present? && @designer_order.ship_to == 'mirraw' && params[:invoice_number].present? && inv_numbers != params[:invoice_number] && @designer_order.completed?
            @designer_order.update_columns(state: 'dispatched', invoice_state: 'not_uploaded')
            @designer_order.designer_invoice.destroy if @designer_order.designer_invoice.present?
          end
          render :inline => '<h4> Something went wrong please try again with proper link</h4>' and return if @order.blank? || @designer_order.blank?
        end
        if inv_numbers.blank?
          @invoice_number = @designer.get_invoice_number
          DesignerOrder.where(id: designer_order_id).update_all(invoice_number: @invoice_number)
        elsif (@invoice_number = params[:invoice_number]).present?
          DesignerOrder.where(id: designer_order_id).update_all(invoice_number: @invoice_number)
          @edit_invoice = false
          @invoice_number = nil if @order.present?
          render :layout => false, :pdf => (@order.present? ? @order.number : @invoice_number), :template => 'designer_orders/show.html' and return
        elsif @order.blank?
          @invoice_number = inv_numbers.first
        end
        render 'designer_orders/show', layout: false
      else
        redirect_to :inline =>'<h4> Something went wrong please try again with proper link</h4>' and return
      end
    rescue ArgumentError => e
      render :inline =>'<h4> Something went wrong please try again with proper link</h4>'
    end
  end

  def designers_update
    @designer = Designer.find(params[:designer_id])
    if @designer.update_attributes(designer_params)
      flash[:success] = "Designer Updated!!"
      redirect_to admin_designer_path(@designer)
    else
      render 'admin/designers_edit'
    end
  end

  def design_review_report
    if request.xhr?
      if (@designer.claimed_review_count < MAX_REVIEW_COUNT) &&  (@review = @designer.reviews.find_by(id: params[:review_id])) && !@designer.inactive_designer_state? && @review.rating < 4
        @review.update_column(:assured, 'designer')
        @designer.increment!(:claimed_review_count)
      end
    else
      redirect_to '/404' and return unless @design = @designer.designs.find_by(id: params[:design_id])
      @start_date = (params[:start_date].try(:to_date) || 9.month.ago).beginning_of_day
      @end_date = (params[:end_date].try(:to_date)|| Date.today).end_of_day
      filter = {
        approved: "approved = FALSE and assured = 'admin'",
        rejected: "approved = TRUE and  assured = 'admin'",
        under_review:   "approved=TRUE and assured= 'designer'",
        new: "approved=TRUE and coalesce(assured,'') not in ('designer', 'admin')"
      }
      @reviews = @design.reviews.where(created_at: @start_date..@end_date).preload([design: :images], :designer).where(filter[params[:status].try(:to_sym)]).order(created_at: :desc).paginate(page: params[:page], per_page: 10)
    end
  end

  def designer_detail_for_online_sales
    unless params[:token].present? && @designer = Designer.monetize_designer(params[:token])
      render nothing: true ,status: 404
    end
  end

  def ads

  end

  def change_owner
    @designer.update_column(:owner_id, params[:owner_id])
    render nothing: true
  rescue => e
    render json: {error: e.message}
  end

  private

  def designer_params
    params.require(:designer).permit(Designer.accessible_attributes)
  end

  def rectify_fb_posting
    if session['facebook_permission_checked_at'] != Date.today && (@designer.present? || params[:designer_id].present?)
      designer = @designer || Designer.find(params[:designer_id])
      if(current_account.try(:designer?).present? && current_account.accountable_id == designer.try(:id) &&
        designer.try(:can_post?) && !graph_has_permissions(designer,'manage_pages','publish_pages'))
        Designer.where(id: designer.id).update_all(fb_page_name: nil,fb_page_id: nil,fb_page_token: nil)
        flash.now[:error] = 'Facebook posting disabled. Please authorize manage_pages, publish_pages permission'
        flash[:error] = 'Facebook posting disabled. Please authorize manage_pages, publish_pages permission'
      end
      session['facebook_permission_checked_at'] = Date.today
    end
  rescue Exception => e
    session['facebook_oauth_callback_redirect'] = request.fullpath
    raise e if e.class == Koala::Facebook::AuthenticationError
  end

  def graph_has_permissions(designer,*permission_lists)
    if designer.present? && (token = designer.fb_page_token).present? && (uid = designer.account.try(:uid)).present?
      Koala::Facebook::API.new(token).get_connections(uid,'permissions').each do |permission|
        permission_lists.delete(permission['permission']) if permission['status'] == 'granted'
        return true unless permission_lists.present?
      end
    end
    false
  end

end
