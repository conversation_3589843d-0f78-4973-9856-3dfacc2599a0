class MirrawAdmin::DesignersController < MirrawAdmin::BaseController
    def upload_designer_discount

    end

    def update_designer_discount
        csv_file = params[:csv_file]
        filename = "designer_update-#{Time.now.strftime("%m-%d-%Y")}-#{current_account.email}-#{csv_file.original_filename}"
      if csv_file.present? && csv_file.content_type == 'text/csv'
        begin
          directories = AwsOperations.get_directory(bucket: 'ticket-images-new', new_connection: true)
          fog_object = directories.files.create(
            key: filename,
            body: csv_file.read,
            public: true
          )
          csv_file_path = fog_object.public_url
          SidekiqDelayClassSpecificGenericJob.perform_async("UpdateDesignerService","run",{csv_file_path: csv_file_path,skip_object_creation: true})
          flash[:success] = "CSV file Uploaded successfully for designers. It will take some time to reflect the changes"
          redirect_to upload_designer_discount_mirraw_admin_designers_path
        rescue => exception
          flash[:notice] = "Please upload a valid CSV file."
          redirect_to upload_designer_discount_mirraw_admin_designers_path
        end
      else
        flash[:notice] = "Please upload a valid CSV file."
        redirect_to upload_designer_discount_mirraw_admin_designers_path
      end
    end


    def dynamic_qc_panel
      @designers_info  = Designer.live_designers
      if params[:designer]
        @designers_info = @designers_info.search_by_id_or_name(params[:designer])
      end
      
      if params[:qc_filter].present? && params[:qc_filter]!='All'
        @designers_info = @designers_info.search_by_qc_rate(params[:qc_filter])
      end

      @designers_info = @designers_info.order('dynamic_qc_rate DESC NULLS LAST, name ASC').paginate(page: params[:page], per_page: 25)
    end

    def update_dynamic_qc_rate
      designer_ids = params[:designer_ids]
      invalid_designer_ids = nil
      # If designer_ids are not valid comma separated ids and qc rate is invalid, show invalid format on panel
      unless Util.valid_comma_separated_ids?(designer_ids) && (qc_rate = params[:qc_rate].try(:to_f))
        flash[:alert] = "Invalid format! Please enter ids separated by commas."
        invalid_designer_ids = designer_ids
      else
        designer_ids = designer_ids.split(/\W+/).uniq.map(&:to_i)
        valid_range_ids = designer_ids.select do |id|
          Util.is_valid_integer?(id)
        end
        out_of_range_ids = designer_ids - valid_range_ids
        valid_designers = Designer.where(id: valid_range_ids)
        updated_ids = valid_designers.pluck(:id)
        not_updated_ids = valid_range_ids - updated_ids + out_of_range_ids

        valid_designers.update_all(dynamic_qc_rate: qc_rate)

        flash[:notice] = updated_ids.present? ? "Updated QC rate for IDs: #{updated_ids.join(', ')}." : ""
        flash[:notice] += "Could not update QC rate for Invalid IDs: #{not_updated_ids.join(', ')}" if not_updated_ids.present?
      end
      redirect_to dynamic_qc_panel_mirraw_admin_designers_path(invalid_designer_ids: invalid_designer_ids) and return
    end

    def upload_designer_reviews
    end

    def update_designer_reviews
      designer_ids = params[:designer_ids]
      enable_reviews = params[:enable_reviews]

      if designer_ids.blank?
        flash[:notice] = "Please enter designer IDs."
        redirect_to upload_designer_reviews_mirraw_admin_designers_path and return
      end

      if enable_reviews.blank?
        flash[:notice] = "Please select enable reviews option."
        redirect_to upload_designer_reviews_mirraw_admin_designers_path and return
      end

      begin
        # Process the designer IDs and enable_reviews setting via Sidekiq
        SidekiqDelayClassSpecificGenericJob.perform_async(
          "DesignerReviewsUploadService",
          "process_designer_reviews",
          {
            designer_ids: designer_ids,
            enable_reviews: enable_reviews == 'true',
            current_account_email: current_account.email,
            skip_object_creation: true
          }
        )

        flash[:success] = "Designer reviews settings update has been queued. It will take some time to reflect the changes."
        redirect_to upload_designer_reviews_mirraw_admin_designers_path
      rescue => exception
        flash[:notice] = "Error processing request: #{exception.message}"
        redirect_to upload_designer_reviews_mirraw_admin_designers_path
      end
    end
end