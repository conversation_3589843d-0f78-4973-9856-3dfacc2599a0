class MirrawAdmin::ReportsController < MirrawAdmin::BaseController
    def best_sellers
        best_seller = BestsellerService.new(params[:start_date],params[:end_date])
        @distinct_designable_types = best_seller.get_designable_type
        @state = best_seller.get_design_states
        @data = best_seller.get_best_sellers_with_durations
        @data = best_seller.get_best_sellers_with_durations(params[:geo]) if  params[:geo].present?
        @data = @data.select { |item| params[:design_state_dropdown].include?(item[10]) } if params[:design_state_dropdown].present?
        @data = @data.select { |item| item[3] == params[:designable_type] } if params[:designable_type].present?
        @data = @data.select { |item| item[0] == params[:designer_id].to_i } if params[:designer_id].present?
        @data = @data.select { |item| item[1].downcase == params[:designer_name].downcase } if params[:designer_name].present?
        @data = @data.select { |item| item[2] == params[:design_id].to_i } if params[:design_id].present?
        @data = @data.first(params[:top].to_i) if params[:top].present?
        if params[:download_report].present?
            csv_data = best_seller.get_bestsellers_report(@data,current_account)
            send_data csv_data, filename: "best_sellers_report_#{Time.now}.csv", type: "text/csv"
        end
        @total_count = @data.length
        @data = @data.paginate(page: params[:page], per_page: 50)
    end

end
