class ReviewsController < ApplicationController
  before_filter :set_review, only: [:edit]
  before_filter :set_designer, only:[:new,:edit,:update,:create,:index]
  before_filter :set_design, only:[:new,:edit,:update,:create,:index]
  after_filter :rating_cache_clear, only: [:create, :update]
  before_filter :authenticate_account!, only:  :design_review
  respond_to :html, :js
  layout 'admin', only: :design_review
  def index
    @integration_status = 'new'
    if @design.present?
      reviews = @design.reviews.approved.order("(cast(extract(epoch from (now() + interval '7' day)) as float) - cast(extract(epoch from updated_at) as float)) + ((5 - rating) * 100) asc")
      # reviews = Review.approved.where(design_id: @design.id).order('updated_at DESC')
      @reviews_list =  reviews.where('review != ?', '').paginate(:page => params[:page], :per_page => 10)
      @star_percentage,@star_rating,@total_review = Review.reivew_percentage(reviews)
      if account_signed_in? && current_account.user?
        @review = Review.find_by_user_id_and_design_id(current_account.accountable_id,@design.id)
      end
    else
      redirect_to root_path,notice: 'No design found'
    end
  end

  def show
  end

  def new
    # Check if designer has reviews enabled
    if @designer.present? && @designer.enable_reviews == false
      respond_to do |format|
        format.html { redirect_to designer_design_path(@designer.cached_slug, @design.cached_slug), notice: 'Reviews are not available for this designer\'s products' }
        format.json { render json: { error: 'Reviews not enabled' }, status: :forbidden }
      end
      return
    end

    # Calls edit if user has already reviewed the product
    # Else create is run
    @review = Review.new
    if account_signed_in? && current_account.user? && (user = current_account.user).present?
      if (user_review = user.reviews.where(design_id: @design.id).first).present?
        @review = user_review
      end
      @order_present = user.can_review?(@design.id)
      respond_with(@review)
    else
      respond_to do |format|
        format.html { redirect_to new_account_session_path,notice: 'You must login as a user before adding a review' }
        format.json { render json: @review.errors, status: :unprocessable_entity }
      end
    end
  end

  def edit
    # Check if designer has reviews enabled
    if @designer.present? && @designer.enable_reviews == false
      respond_to do |format|
        format.html { redirect_to designer_design_path(@designer.cached_slug, @design.cached_slug), notice: 'Reviews are not available for this designer\'s products' }
        format.json { render json: { error: 'Reviews not enabled' }, status: :forbidden }
      end
      return
    end

    @order_present = true #so that existing user who have reviews can edit their reviews.
    if !account_signed_in? || (account_signed_in? && !current_account.user?)
      respond_to do |format|
        format.html { redirect_to new_account_session_path,notice: 'You must login as a user before adding a review' }
        format.json { render json: @review.errors, status: :unprocessable_entity }
      end
    end

  end

  def create
    # Check if designer has reviews enabled
    if @designer.present? && @designer.enable_reviews == false
      respond_to do |format|
        format.html { redirect_to designer_design_path(@designer.cached_slug, @design.cached_slug), notice: 'Reviews are not available for this designer\'s products' }
        format.json { render json: { error: 'Reviews not enabled' }, status: :forbidden }
      end
      return
    end

    @review = Review.new(params[:review])
    @review.design_id = @design.id
    @review.designer_id = @designer.id
    @review.user_id = current_account.accountable_id
    @review.approved = true

    if account_signed_in? && current_account.user?
      if @review.rating.present? && @review.review.present? && @review.review.length > 10
        @review.save
        @design.update_reviews
        @designer.update_reviews
        redirect_to designer_design_path(@designer.cached_slug,@design.cached_slug)
      else
        respond_to do |format|
          notice = @review.review.present? ? 'Minimum rating is one star.' : 'Please write a review in more than 30 characters'
          format.html {  redirect_to new_designer_design_review_path(@designer.cached_slug,@design.cached_slug),notice: notice}
          format.json { render json: @review.errors, status: :unprocessable_entity }
        end
      end
    else
      respond_to do |format|
        format.html { redirect_to designer_design_path(@designer.cached_slug,@design.cached_slug),notice: 'You must be logged in as a user to add a review' }
        format.json { render json: @review.errors, status: :unprocessable_entity }
      end
    end
  end

  def update
    # Check if designer has reviews enabled
    if @designer.present? && @designer.enable_reviews == false
      respond_to do |format|
        format.html { redirect_to designer_design_path(@designer.cached_slug, @design.cached_slug), notice: 'Reviews are not available for this designer\'s products' }
        format.json { render json: { error: 'Reviews not enabled' }, status: :forbidden }
      end
      return
    end

    @review = Review.find_by_user_id_and_design_id(current_account.accountable_id,@design.id)
    if @review.present? && account_signed_in? && current_account.user?
      if params[:review][:rating].present? &&  params[:review][:review].present?
        @review.design_id = @design.id
        @review.designer_id = @designer.id
        @review.user_id = current_account.accountable_id
        @review.approved = true
        @review.update_attributes(params[:review])

        @design.update_reviews
        @designer.update_reviews

        redirect_to designer_design_path(@designer.cached_slug,@design.cached_slug)
      else
        respond_to do |format|
          notice = params[:review][:review].present? ? 'Minimum rating is one star.' : 'Please write a review'
          format.html { redirect_to edit_designer_design_review_path(@designer.cached_slug,@design.cached_slug,@review.id),notice: notice }
          format.json { render json: @review.errors, status: :unprocessable_entity }
        end
      end
    else
      notice_text = @review.present? ? 'You must be logged in as a user to add a review' : 'Review not present'
      respond_to do |format|
        format.html { redirect_to designer_design_path(@designer.cached_slug,@design.cached_slug),notice: notice_text }
        format.json { render json: @review.errors, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @review.destroy
    respond_with(@review)
  end

  def site_review
    if request.post?
      response ={error: 'something went wrong'}
      if params[:review].present? && params[:star].present?
        if current_account.present? && (@review = Review.where(user_id: current_account.try(:accountable_id), site_review: true).first).present?
          @review.review = params[:review] unless params[:review].blank?
          @review.rating = params[:star]
          @review.approved = true
        else
          @review = Review.new(review: params[:review], rating: params[:star], site_review: true)
          unless can? :manage, @review
            @review.user_id = current_account.try(:accountable_id)
          else
            @review.system_user = true
          end
        end
        @review.assured = nil
        @review.save
        response = {success: true}
      elsif (review = Review.where(id: params[:review_id]).first).present?
        params[:resolved] == 'true' ? review.update_column(:assured, 'Resolved') : review.update_column(:approved, false)
        response = {success: true}
      end
      render json:  response
    else
      @integration_status = 'new'
      @kind='review_page'
      @seo = SeoList.where(label: @kind).first
      @site_reviews = Review.where('rating > 1').preload(:user).site.approved.comments.order("(cast(extract(epoch from (now() + interval '7' day)) as float) - cast(extract(epoch from updated_at) as float)) + ((5 - rating) * 100) asc").paginate(:page => params[:site_page], :per_page => 10, :total_entries => 100)
      @reviews = Review.where('rating > 1').includes(:user, [design: :images],:designer).product.approved.comments.order("(cast(extract(epoch from (now() + interval '7' day)) as float) - cast(extract(epoch from updated_at) as float)) + ((5 - rating) * 100) asc").paginate(page: params[:page], :per_page => 10, :total_entries => 100)
      @best_reviewed_designs = Review.includes(:user, [design: :images],:designer).product.approved.comments.joins(:design).where('rating > 4 and designs.total_review > 20').order("(cast(extract(epoch from (now() + interval '7' day)) as float) - cast(extract(epoch from reviews.updated_at) as float)) + ((5 - rating) * 100) asc").paginate(:page => params[:reviewed_best], :per_page => 10, :total_entries => 100)
      if (user_id = current_account.try(:accountable_id)).present? && !current_account.admin?
        @review = Review.site.where(user_id: user_id).first
      end
    end
  end

  def design_review
    redirect_to '/404' unless FEEDBACK_REVIEW_ACCESSIBLE_EMAIL_ID.include?(current_account.email)
    if request.xhr?
      if (review = Review.where(id: params[:review_id]).first) && review.designer_id.present?
        message = "Your Request to remove feedback on product #{review.design_id} has been "
        if params[:review_action] == 'accept'
          review.update_column(:approved, false)
          review.design.update_reviews
          review.designer.update_reviews
          SidekiqDelayGenericJob.set(queue: 'low').perform_async(review.class.to_s, review.id, "update_vendor_odr")
          #review.sidekiq_delay(queue: 'low').update_vendor_odr
          message += 'Approved'
        else params[:review_action] == 'reject'
          message += 'Rejected'
        end
        review.update_column(:assured,'admin')
        send_message(message, review.designer.account.id)
        review.designer.decrement!(:claimed_review_count) if review.designer.claimed_review_count > 0
        render js: "$('##{review.id}').toggle(300, function(){$(this).remove();});" and return
      end
    else
      @reviews = Review.where(assured: 'designer', approved: true).preload([design: :images], :designer).paginate(page: params[:page], per_page: 20)
    end
  end

  def remove
    if (review = Review.find_by_id(params[:id])).present?
      review.update_attributes(approved: false, notes: 'mirraw_review')
      review.design.update_reviews
      review.designer.update_reviews
      render json: {status: 200}
    else
      render json: {status: 500}
    end
  end

  def reviews_by_filter
    if (['top_rated','negative','most_recent'].include?(params[:filter]) && params[:design_id].present?)
      if stale? etag: params, last_modified: Time.at(params[:last_updated].to_i)
        @reviews_list = Review.includes(:user).send(params[:filter]).approved.comments.where(design_id: params[:design_id]).paginate(page: params[:page] || 1, per_page: 10)
        respond_to do |format|
          format.js { render file: '/reviews/reviews_by_filter.js', content_type: 'text/javascript'}
        end
      end
    else
      respond_to do |format|
        format.js { render file: '/reviews/reviews_by_filter.js', content_type: 'text/javascript'}
      end
    end
  end

  private
    def set_designer
      @designer= Designer.find_by_cached_slug(params[:designer_id])
    end

    def set_design
      @design= Design.find_by_cached_slug(params[:design_id])
    end

    def set_review
      @review = Review.find_by_id(params[:id])
    end

    def rating_cache_clear
      Rails.cache.delete("design_#{set_design.id}:#{@symbol}_rating")
    end

    def send_message(message, account_id)
      Message.create(title:'Feedback Review', description: message , account_id: account_id, priority: 1)
    end
end
