class ShipmentsController < ApplicationController

  before_filter :authenticate_account!, except: :update_reverse_pickup_status
  before_filter :check_admin_account, only: [:index,:recent,:add_payment_details]
  before_filter :is_valid_user, only: [:index]
  layout 'admin', only: [:create_dhl_ecom_pickups,:add_payment_details, 'index', :ndr_update_panel]

  def label
    begin
      s = Shipment.find(params[:shipment_id])
      if s.label.present?
        render :json => {:label => s.label}
      else
        render :json => {:error => 'label not generated'}
      end
    rescue
      render :json => {:error => 'Shipment not found'}
    end
  end

  def invoice
    begin
      s = Shipment.find(params[:shipment_id])
      if s.invoice.present?
        render :json => {:invoice => s.invoice}
      else
        render :json => {:error => 'Invoice not generated'}
      end
    rescue
      render :json => {:error => 'Shipment not found'}
    end
  end

  def jewellery_invoice
    begin
      s = Shipment.find(params[:shipment_id])
      if s.jewellery_invoice.present?
        render :json => {:invoice => s.jewellery_invoice}
      else
        render :json => {:error => 'Jewellery_Invoice not generated'}
      end
    rescue
      render :json => {:error => 'Shipment not found'}
    end
  end

  def is_valid_user
    unless ADMIN_PANEL_ACCESS["shipment_url_access"].to_a.include?(current_account.email)
      redirect_to root_path, notice: 'You are not authorized to access this page.'
    end
  end

  def index
    begin
      @s = Shipment.preload([:order, :line_items => [:design => [:images, :designer]]]).find_by_id(params[:shipment_id])  
      ## If order is international and order contains jewelery then generate invoice
      ## Invoice declares the value of the jewellery in an order
      @jwellery_invoice = false
      if @s.order.geo == "international"
        for line_item in @s.line_items
          if( line_item.designable_type == 'Jewellery')
            @jwellery_invoice = true
          end
        end
      end
      if ( @jwellery_invoice  == true && @s.jewellery_invoice_file_name.blank?)
        ShipmentDelivery::Invoice.generate_jewellery_invoice(params[:shipment_id])
      end
    rescue
      redirect_to request.referer.present? ? :back : root_url,:notice => 'Shipment not found'
    end 
  end

  def update_reverse_pickup_status
    if request.headers['HTTP_WEBHOOK_KEY'] != ENV['CLICKPOST_WEBHOOK_KEY']
      render json: { errors: 'Webhook Key Mismatch' }, status: 401 and return
    elsif params['waybill'].present?
      shipment = Shipment.joins(:shipper).where(number: params['waybill']).where(shippers: { clickpost_shipper_id: params['cp_id'] }).first
      if shipment.present?
        if valid_state_transition?(shipment, params['status'])
          shipment.update_clickpost_shipment_state(params.deep_symbolize_keys)
          render json: { msg: "SUCCESS" }, status: 200 and return
        else
          render json: { errors: "Invalid state transition from #{shipment.shipment_state}" }, status: 422 and return
        end
      elsif (rtv_shipment = RtvShipment.where(number: params['waybill']).first).present?
        SidekiqDelayGenericJob.perform_async(rtv_shipment.class.to_s, rtv_shipment.id, "update_clickpost_state", params.deep_symbolize_keys.merge(sidekiq_request_params: true))
        render json: { msg: "SUCCESS" }, status: 200 and return
      end
    end
    render json: { errors: "AWB number not found" }, status: 404
  end
  
  def valid_state_transition?(shipment, status)
    case shipment.shipment_state
    when 'delivered'
      return false if status.downcase == 'out for delivery'
    end
    true
  end
  

  def self.generate_bulk_invoice(items, shipment_id, designer)
    items = items.map{|item| item.with_indifferent_access}
    shipment = Shipment.find_by_id shipment_id
    all_dos = DesignerOrder.where(bulk_shipment_id: shipment.id)
    warehouse_address_ids  = all_dos.collect(&:warehouse_address_id).uniq
    _,_,_,_,_,shipping_pincode,_,_ = DesignerOrder.get_warehouse_billing_address(warehouse_address_ids)
    value_of_goods = items.sum{|i| i[:total_price]}.round(2)
    e_way_bill_status = value_of_goods >= 50000 && (items.collect{|i| i[:designable_type].to_s.downcase}.to_set - ['jewellery']).any?
    bill_data = {
      'Reason for Transportation' => 'Export',
      'GSTIN of Recipient' => 'URD',
      'Place of Delivery' => shipping_pincode,
      'Invoice or Challan Date' => Time.current.strftime('%d/%m/%y'),
      'HSN Code' => items.collect{|i| i[:hsn_code].to_s[0..3]}.join(","),
      'Transport Document Number' => shipment.number,
      'Vehicle Number' => ''
    } if e_way_bill_status

    invoice_data = Shipment.calculate_domestic_invoice(nil, items, false, nil, designer)
    all_dos.each do |dos|
      dos.update_column(:gst_tax, invoice_data[:all_dos_tax][dos.id].to_i)
    end
    pdf_content =  ActionController::Base.new().render_to_string(
      :template => '/shipments/invoice_designer',
      :layout   => false,
      :locals   => {:@order => nil, :@items => items, :@shipment => shipment, mirraw_domestic: false, invoice_number: shipment.invoice_number, invoice_data: invoice_data, designer: designer, domestic_sor: false}
    )
    if e_way_bill_status
      bill_data['Invoice or Challan Date'] = shipment.created_at.strftime("%d/ %m/ %Y")
      bill_data['Invoice or Challan Number'] = shipment.invoice_number
      bill_data['Reason for Transportation'] =  'Supply'
      bill_data.merge!('GSTIN of Recipient' => MIRRAW_GST_NUMBER, 'Place of Delivery' => shipping_pincode )
      bill_data['Value of Goods'] = "\u20B9 #{value_of_goods}"
      pdf_content += ShipmentsController.generate_e_waybill(bill_data)
    end
    invoice = WickedPdf.new.pdf_from_string(pdf_content, encoding: "UTF-8")
    shipment.add_invoice(invoice)
    shipment.save!
  end

  def self.generate_invoice(items, order_number, shipment_id, shipping_charges = 0, total = 0, discount = 0,invoice_data: {})
    @order  = Order.where(:number => order_number).first
    no_igst_couriers = IGST_PAYMENT_COMMERCIAL.split(',')
    @shipment = Shipment.find shipment_id
    date,currency_code = '','INR'
    shipper_name = @shipment.shipper.name.try(:downcase)
    new_number, international, market_rate = true, false, 1
    dhl_csb = shipper_name == 'dhl' && !@shipment.csb_used
    items = items.map{|item| item.with_indifferent_access}
    commercial_status = COMMERCIAL_FOR_AUTOMATION == 'true' && shipper_name != 'delhivery'
    if @shipment.designer_order_id.present?
      total = items.sum{|i| i[:total_price]}
    elsif commercial_status
      commercial, paypal_rate, currency_code = @order.get_commercial_values(false,shipper_name)
      market_rate = ((cc = CurrencyConvert.where{(symbol == currency_code) | (iso_code == currency_code)}.first).exchange_rate.presence || cc.market_rate)
    end
    value_of_goods = (total* market_rate).round(2)
    e_way_bill_status = value_of_goods >= 50000 && (items.collect{|i| i[:designable_type].to_s.downcase}.to_set - ['jewellery']).any?

    bill_data = {
      'Reason for Transportation' => 'Export',
      'GSTIN of Recipient' => 'URD',
      'Place of Delivery' => @order.pincode,
      'Invoice or Challan Date' => Time.current.strftime('%d/%m/%y'),
      'HSN Code' => items.collect{|i| i[:hsn_code].to_s[0..3]}.join(","),
      'Transport Document Number' => @shipment.number,
      'Vehicle Number' => ''
    } if e_way_bill_status
    if (designer_order = @shipment.designer_order).present?
      _, _, _,_, _, shipping_pincode, _, _ = DesignerOrder.get_warehouse_billing_address(designer_order.warehouse_address_id)
      new_number = false
      invoice_data = Shipment.calculate_domestic_invoice(@order, items, false, designer_order) if invoice_data.blank?
      tcs_applicable = DesignerOrder.is_tcs_applicable(designer_order, @order)
      tds_applicable = DesignerOrder.is_tds_applicable(designer_order, @order)
      tcs_value = tcs_applicable ? (invoice_data[:total_taxable_value]* 0.5/100.0).round(2) : 0
      tds_value = tds_applicable ? (invoice_data[:total_taxable_value]* 0.1/100.0).round(2) : 0
      gst_tax = designer_order.get_tcs_tds_and_gst_value[0]
      tcs_tax = designer_order.get_tcs_tds_and_gst_value[1]
      tds_tax = designer_order.get_tcs_tds_and_gst_value[2]
      designer_order.update_columns(gst_tax: gst_tax, tcs_tax: tcs_tax, tds_tax: tds_tax)
      pdf_content =  ActionController::Base.new().render_to_string(
        :template => '/shipments/invoice_designer',
        :layout   => false,
        :locals   => {:@order => @order, :@items => items, :@shipment => @shipment, mirraw_domestic: false, invoice_data: invoice_data, domestic_sor: designer_order.domestic_sor?(@order)}
      )
      if e_way_bill_status
        bill_data['Invoice or Challan Date'] = (designer_order.pickup.presence || designer_order.completed_at.presence || designer_order.created_at).strftime("%d/ %m/ %Y")
        bill_data['Invoice or Challan Number'] = designer_order.invoice_number
        bill_data['Reason for Transportation'] =  'Supply'
        bill_data.merge!('GSTIN of Recipient' => MIRRAW_GST_NUMBER, 'Place of Delivery' => shipping_pincode)  if (designer_order.ship_to.blank? && @order.international?) || (designer_order.ship_to == 'mirraw')
      end
    else
      international= true
      irn_number, irn_barcode, error_msg = nil, nil, nil
      if DISABLE_ADMIN_FUCTIONALITY['irn_feature'] && ([market_rate.to_f, currency_code.to_s] & [1.0, 'INR']).blank?
        begin
          OrderMailer.report_mailer("IRN Request #{@order.number} / #{@shipment.id}","Please find below items details below: <br>Market Rate: #{market_rate.to_f}<br> Currency Code: #{currency_code}<br><br>#{items}",{'to_email'=> [DEPARTMENT_HEAD_EMAILS['accounts']],'from_email_with_name'=>'<EMAIL>'},{}).deliver
          generate_irn = GenerateIrnNumber.new(@order, items, @shipment, market_rate.to_f,currency_code)
          response = generate_irn.generate_forward_irn
          if response[:error] == false
            irn_number, irn_barcode = response[:irn_number], response[:irn_barcode]
            @shipment.update_columns(irn_number: irn_number, gst_barcode: irn_barcode)
          else
            error_msg  = response[:error_msg]
          end
        rescue => e
          error_msg = "#{e.message} ===> #{e.backtrace}"
        end
        if error_msg.present?
          OrderMailer.report_mailer("Error While Creating IRN Invoice #{@order.number}","Failed to generate IRN number due to below reason <br>#{error_msg}",{'to_email'=> [DEPARTMENT_HEAD_EMAILS['accounts']],'from_email_with_name'=>'<EMAIL>'},{}).deliver
          ExceptionNotify.sidekiq_delay.notify_exceptions('IRN NUMBER Generation Error',error_msg, { params: "Order: #{@order.number} ====> Shipment: #{@shipment.id} ====> items: #{items}" })
        end
      end
      if commercial_status
        shipment_detail = {}
        shipment_detail['currency_code']= currency_code
        shipment_detail['shipper_name'] = shipper_name
        shipment_detail['awb_number']   = @shipment.number
        shipment_detail['total_price']  = total.round(2)
        shipment_detail['dhl_csb']  = dhl_csb
        @order.other_details['invoice_paypal_rate'] = paypal_rate
      else
        commercial = false
      end
      date = @order.get_invoice_number
      new_number = false if @order.order_notification["invoice_#{date}"].present?

      bill_data['Invoice or Challan Number'] = date if e_way_bill_status
      if (SHIPPER_LIST_INTERNATIONAL.map(&:downcase).include? shipper_name.downcase) && commercial
        Shipment.generate_invoice_inr(@order, @shipment, shipment_detail, items, date, market_rate)
        pdf_content =  ActionController::Base.new().render_to_string(
          :template => '/shipments/commercial_invoice',
          :layout   => false,
          :locals   => {:@order => @order, :@items => items, :@shipment_detail => shipment_detail, :@date => date, :@market_rate => market_rate, no_igst_couriers: no_igst_couriers, :@irn_number => irn_number, :@gst_barcode => irn_barcode}
        )
      elsif shipper_name == 'delhivery'
        invoice_data = Shipment.calculate_domestic_invoice(@order, items, true)
        pdf_content =  ActionController::Base.new().render_to_string(
        :template => '/shipments/invoice_designer',
        :layout   => false,
        :locals   => {:@order => @order, :@items => items, :@shipment => @shipment, :@market_rate => 1, mirraw_domestic: true, invoice_number: date, invoice_data: invoice_data, domestic_sor: false}
        )
        bill_data['Reason for Transportation'] =  'Supply' if e_way_bill_status
      else
          pdf_content =  ActionController::Base.new().render_to_string(
          :template => '/shipments/invoice',
          :layout   => false,
          :locals   => {:@order => @order, :@items => items, :@date => date}
        )
      end
      pdf_content += ActionController::Base.new().render_to_string(
        :template => '/shipments/annexure.html.haml',
        :layout   => false,
        :locals   => {invoice_date: @shipment.created_at, invoice_number: date, delivery_term: (@order.shipping.to_i > 0 ? 'C&F' : 'FOB')}
      ) if ['dhl','skynet'].include?(shipper_name) && !@shipment.csb_used?
      @shipment.invoice_data['fob_value']         = total.round(2)
      @shipment.invoice_data['packaging_charges'] = shipping_charges
      @shipment.invoice_data['currency_code']     = currency_code
      @shipment.invoice_number                    = date
      @shipment.exchange_rate                     = market_rate
      @shipment.shipment_type                     = 'COD' if @order.international? && @order.cod?
    end
    if e_way_bill_status
      bill_data['Value of Goods'] = "\u20B9 #{value_of_goods} " + ("/ #{total} #{currency_code}" unless currency_code == 'INR').to_s
      pdf_content += ShipmentsController.generate_e_waybill(bill_data)
    end
    invoice = WickedPdf.new.pdf_from_string(pdf_content, encoding: "UTF-8")
    @shipment.add_invoice(invoice)
    if @shipment.save!
      if international
        li_invoices = []
        items.each do |item|
          s_item = ShipmentInvoiceItem.where(line_item_id: item[:line_item_id],description: item[:name],shipment_id: @shipment.id).first_or_create
          s_item.update_attributes(hsn_code: item[:hsn_code],rate: item[:price],discount: item[:item_discount],discounted_rate: item[:item_discounted_price],taxable_value: item[:taxable_value],gst_rate: item[:gst_rate],gst_tax: item[:gst_tax],total_amount: item[:total_price],product_type: item[:designable_type],currency: currency_code,gst_paid: dhl_csb || no_igst_couriers.exclude?(shipper_name),quantity: item[:quantity],weight: item[:weight])
        end
      end
      if @order.order_notification.has_key?("invoice_#{date}")
        @order.order_notification["invoice_#{date}"] = @shipment.invoice.url
        @order.save
        new_number = false if @order.check_if_all_items_dispatched? && (shipper_name == 'delhivery' || commercial)
      end
      if !@order.events.find_by_note_type("Fedex Invoice Upload Order").nil?
        awb_number = @shipment.number
        order_id = @order.id
        shipment_id = @shipment.id
        FedexUploadDocumentsJob.set(queue: 'critical').perform_in(5.minutes.from_now, order_id, awb_number, shipment_id, false)
      end
      
      # if new_number
      #   unless commercial
      #     commercial, paypal_rate, currency_code = @order.get_commercial_values(true,shipper_name)
      #     shipment_detail = {}
      #     shipment_detail['shipping_cost']= shipping.round(2)
      #     shipment_detail['currency_code']= currency_code
      #     shipment_detail['gift']         = gift_wrap_price
      #   end
      #   shipment_detail['awb_number']=@shipment.number
      #   @order.generate_order_invoice(shipment_detail,date,paypal_rate)
      # end
    end
  end

  def self.generate_e_waybill(bill_data)
    ActionController::Base.new().render_to_string(
        template: '/shipments/_e_way_bill.html.haml',
        layout: false,
        locals: {bill_data: bill_data},
        encoding: 'UTF-8'
      )
  end

  def recent
    no_of_shipments = params[:limit].present? && params[:limit].to_i > 0 ? params[:limit] : 100
    s_select  = 'shipments.*, orders.number as order_number'
    @shipments = Shipment.select(s_select).joins(:order).order('shipments.id DESC').limit(no_of_shipments)
    @shipments = @shipments.where('shipments.designer_order_id IS NULL') if params[:all].blank?
  end

  def add_payment_details
    @integration_status = 'new'
    @shippers=Shipper.select('id,name').where(enabled:true, international: true)
    @active_best_shippers = Shipper.select('distinct shippers.id,name').joins(:shipper_fuel_prices)
    if params[:commit] == 'Download'
      if params['select_items'].present? && params[:type] == 'Bulk Commercial Invoice'
        ShipmentMailer.sidekiq_delay(queue: 'high')
                      .mail_invoices_in_bulk(
                        params[:type], nil, current_account.email,
                        params[:start_date], params[:end_date],
                        params[:select_items]
                      )
      else
        # Shipment.sidekiq_delay(queue: 'high').download_invoices_in_bulk(current_account,Date.parse(params[:start_date]).beginning_of_day,Date.parse(params[:end_date]).end_of_day,params[:type],params[:shipper])
        SidekiqDelayGenericJob.set({queue: 'high'}).perform_async("Shipment", 
                                                                  nil, 
                                                                  "download_invoices_in_bulk", 
                                                                  {current_account.class.to_s => current_account.id},
                                                                  Date.parse(params[:start_date]).beginning_of_day,
                                                                  Date.parse(params[:end_date]).end_of_day,
                                                                  params[:type],
                                                                  params[:shipper]
                                                                )
      end
      redirect_to :back, :notice => "#{params[:type]} will be mailed to you shortly."
    elsif params[:commit] == 'Submit'
      @all_invoices = []
      shipper_id = params[:shipper] == 'All' ? nil : "shipments.shipper_id = #{params[:shipper]}"
      orders = Order.joins(:shipments).where(shipper_id).where('shipments.designer_order_id is null and shipments.created_at >= ? and shipments.created_at <= ?',Date.parse(params[:start_date]).beginning_of_day,Date.parse(params[:end_date]).end_of_day).uniq
      orders.uniq.each do |order|
        if order.order_notification.present? && (key=order.order_notification.keys.find{|k| k.to_s.include? 'invoice'}).present?
          @all_invoices << {key => order.order_notification[key],'number'=> order.number}
        end
      end
    end
  end

  def calculate_best_shipper
    notice = 'No surcharge percent provided !'
    if params[:surcharge_percent].to_f > 0
      operation = params[:select_operation] == 'Increase' ? '+' : '-'
      if params[:base_charge_percent].to_f > 0
        update_query = "base_charge = base_charge * (100 #{operation} #{params[:base_charge_percent].to_f})/100.0, surcharge = (base_charge * (100 #{operation} #{params[:base_charge_percent].to_f})/100.0) * (100 + #{params[:surcharge_percent].to_f})/100.0"
      else
        update_query = "surcharge = base_charge * (100 + #{params[:surcharge_percent].to_f})/100.0"
      end
      ShipperFuelPrice.where(shipper_id: params[:shipper_id]).update_all(update_query)
      ShipperFuelPrice.sidekiq_delay(queue: 'critical')
                      .recalculate_best_shipper_country_wise
      notice = 'Fuel Charge updates is scheduled and will reflect changes in some time.'
    end
    redirect_to shipments_add_payment_details_path, notice: notice
  end

  def update_payment_details
    begin
      connection = Fog::Storage.new({
        :provider                 => 'AWS',
        :aws_access_key_id        => ENV['AWS_ACCESS_KEY_ID'],
        :aws_secret_access_key    => ENV['AWS_SECRET_ACCESS_KEY'],
        :region => "ap-southeast-1"
      })
    
      # First, a place to contain the glorious details
      directory = connection.directories.new(
        :key    => Shipment.mirraw_bucket
      )

      filename  = Shipment.payment_file_dir + "#{Time.now.strftime("%m_%d_%H_%M") + SecureRandom.hex(6) +'.csv'}"

      # upload that file
      file =  directory.files.create(
        :key    => filename,
        :body   => params[:csv_file].read,
        :public => true
      )

      Shipment.sidekiq_delay.payments_update(filename)      
      redirect_to :back, :notice => 'Shipment update is scheduled'
    rescue NoMethodError => error
      error = params[:csv_file].blank? ? 'CSV file not uploaded' : error.message
      redirect_to :back, :notice => error
    rescue => error
      redirect_to :back, :notice => error.message
    end
  end

  def self.generate_tofrom_label(shipment)
    pdf_content =  ActionController::Base.new().render_to_string(
      :template => '/shipments/tofrom_label',
      :layout   => false,
      :locals   => {:@shipment => shipment}
    )
    tofrom_label = WickedPdf.new.pdf_from_string(pdf_content)
    shipment.add_tofrom_label(tofrom_label)
    shipment.save!
  end

  def view_crc
    redirect_to root_url,notice: 'You are not authorized to view this page' and return unless  %w(accounts accounts_admin super_admin admin).include?(current_account.role.name)
    @integration_status = 'new'
    @approved_versions = CrcVersion.where(status: true).where('created_at::date between ? and ?',6.month.ago,Date.today)
    @crc_versions = CrcVersion.where('created_at::date between ? and ? ',2.month.ago.to_date,Date.today.end_of_month)
    if request.post?
      filename  = "crc_version/#{current_account.id}/#{Time.now.strftime("%m_%d_%H_%M") + SecureRandom.hex(3) +'.csv'}"
      AwsOperations.create_aws_file(filename,params[:file],true)
      CrcVersion.create(url: AwsOperations.get_aws_file_path(filename),uploaded_by_id: current_account.id)
    end
  end

  def approve_crc
    notice = 'Thanks! You Have Approved This CRC'
    if current_account.present? && crc_version = CrcVersion.where(id: params[:id]).first
      notice = 'Thanks! CRC will be updated soon' if crc_version.approver_1_id.present?
      crc_version.update_approver(current_account.id)
    end
    redirect_to shipments_view_crc_path,notice: 'Thanks! You Have Approved This CRC'
  end

  def create_for_order
    @integration_status = 'new'
    if params[:number].present? && params[:shipper].present?
      if params[:items].present? && params[:packer_id].present? && params[:weight].present? && params[:packaging].present? && params[:service].present?
        req_attr = parse_form_create_for_order(params)
        req_attr[:invoicer_id] = current_account.id

        # Variables for AJAX CALLS
        @job_id = gon.delayed_job_id = (Shipment.sidekiq_delay.ups_create_international(params[:number], req_attr)).id
        gon.delayed_job_type = 'shipment'
        gon.order_id = order_id = Order.find_by_number(params[:number]).id

        @notice = 'Creating shipments please wait'
      end
      @line_items = LineItem.joins(designer_order: :order).dispatchable.where(shipment_id: nil, received: 'Y', orders: {number: params[:number]}).where('designer_orders.state <> ?','canceled')
      @services = Shipment.ups_services
      @packaging_type = Shipment.ups_packaging_type
      @order = Order.find_by_number(params[:number])
      redirect_to order_order_detail_path(@order.number), notice: 'Cannot create shipment for blacklist user' if @order[:other_details].try(:[], 'blacklist_user') || @order.blacklist_user_order
    end
  end

  def delayed_job
    response = {error: false, error_text: '', complete: false}
    id, order_id = params[:id].to_i, params[:order_id].to_i
    if id == 0 || order_id == 0
      response[:error] = true
      response[:error_text] = 'params blank | missing'
    else
      dj = Delayed::Job.find_by_id(id)
      order = Order.find_by_id(order_id)
      if dj.blank? && order.present?
        response[:complete] = true
        if order.fedex_shipment_error.present?
          response[:shipment_error] = true
          response[:shipment_error_text] = order.fedex_shipment_error
        else
          response[:shipment_error] = false
          response[:shipment_id] = order.shipments.last.id
        end
      end
    end
    render json: response
  end

  def courier_reconciliation
    begin
      if params[:commit] == 'Download Current Rates' && current_account.role.try(:name) == 'accounts_admin'
        rates        = CurrencyConvert.select('distinct(symbol),exchange_rate').order('exchange_rate,symbol')
        column_names = %w(symbol exchange_rate)
        exchange_rate_file = CSV.generate(headers: true) do |csv|
          csv << ['Currency'] + column_names
          rates.each do |rate|
            csv << [CurrencyConvert::SYMBOL_NAME[rate.symbol]] + rate.attributes.values_at(*column_names)
          end
        end
        send_data exchange_rate_file,filename: "Current_exchange_Rates_#{Date.today.strftime('%d_%b_%Y')}.csv" and return
      elsif params[:commit] == 'Update Rates' && current_account.role.try(:name) == 'accounts_admin' && params[:csv_file_exchange_rate].present?
        original_filename = params[:csv_file_exchange_rate].original_filename.gsub(' ','_')
        filename  = Shipment.payment_file_dir+"exchange_rates/Rates_#{Date.today.strftime('%d_%b_%Y')}_#{original_filename}"
        file_text = File.read(params[:csv_file_exchange_rate].path)
        file_text = StringModify.string_utf8_clean_without_space(file_text) unless file_text.is_utf8?
        CurrencyConvert.sidekiq_delay(queue: 'critical')
                       .update_exchange_rates(filename,file_text)
        redirect_to :back, notice: "Rates will be Updated in few minutes !"
      else
        original_filename = params[:csv_file_courier_reconciliation].original_filename.gsub(' ','_')
        filename  = Shipment.payment_file_dir+"reconciliation/"+ SecureRandom.hex(6)+"_#{original_filename}"
        AwsOperations.create_aws_file(filename,params[:csv_file_courier_reconciliation])
        Shipment.sidekiq_delay
                .perform_courier_reconciliation(
                  filename,original_filename,
                  current_account.email
                )
        redirect_to :back, :notice => "Shipment update is scheduled , Dispute file will be mailed to #{current_account.email}"
      end
    rescue NoMethodError => error
      error = params[:csv_file_courier_reconciliation].blank? ? 'CSV file not uploaded' : error.message
      redirect_to :back, :notice => error
    rescue => error
      redirect_to :back, :notice => error.message
    end
  end

  def create_dhl_ecom_pickups
    dhl_ecom_shipper = Shipper.where('lower(name) = ?','dhl ecom').first
    select_shipment_clause = 'shipments.id,shipments.price,shipments.number,shipments.weight,label_file_name,label_updated_at,shipments.created_at,order_id, label_file_size'
    @shipments = Shipment.forward.select(select_shipment_clause).where('shipment_state <> ? AND shipper_id = ? AND label_file_name IS NOT NULL', 'processing_abandoned',dhl_ecom_shipper.id).where('dhl_ecom_pickup_id is null')
    select_pickup_clause = "dhl_ecom_pickups.id,dhl_ecom_pickups.created_at,handover_id,string_agg(shipments.number, ',') as numbers,handover_note_file_name,handover_note_updated_at, handover_note_file_size,pickup_label_file_name,pickup_label_updated_at, pickup_label_file_size, pickup_status,pickup_details"
    @dhl_ecom_pickups = DhlEcomPickup.select(select_pickup_clause).joins(:shipments).where('dhl_ecom_pickups.created_at > ?',1.month.ago).group('dhl_ecom_pickups.id').paginate(page:params[:page],per_page:20)
    @rate = CurrencyConvert.where('lower(symbol) = ? AND lower(country) = ?','usd','united states').first
    if request.post?
      begin
        shipment_numbers = params[:shipment]
        dhl_ecom_urls = Mirraw::Application.config.dhl_ecom
        token_response = HTTParty.get("#{dhl_ecom_urls[:token_url]}?clientId=#{dhl_ecom_urls[:credentials]['client_id']}&password=#{dhl_ecom_urls[:credentials]['password']}&returnFormat=json")
        if token_response['accessTokenResponse']['responseStatus']['code'] == '100000'
          token = token_response['accessTokenResponse']['token']
          if params[:close_out].present?
            header = {
              messageType: "CLOSEOUT",
              accessToken: token,
              messageDateTime: DateTime.now.strftime("%Y-%m-%dT%H:%M:%S%Z"),
              messageVersion: "1.3",
              messageLanguage: "en"
            }

            shipment_items = []
            shipment_numbers.each do |num|
              shipment_items << {
                shipmentID: num,
                bagID: nil
              }
            end

            body = {
              customerAccountId: nil,
              pickupAccountId: dhl_ecom_urls[:credentials]['pickup_account_id'],
              soldToAccountId: dhl_ecom_urls[:credentials]['sold_to_account_id'],
              handoverID: nil,
              generateHandover: 'Y',
              handoverMethod: 2,
              shipmentItems: shipment_items
            }

            closeout_request = {
              closeOutRequest: {
                hdr: header,
                bd: body
              }
            }

            closeout_response = HTTParty.post(dhl_ecom_urls[:closeout_url],body: closeout_request.to_json, headers: {'Content-Type' => 'application/json'})
            closeout_resp = closeout_response['closeOutResponse']['bd']
            if closeout_resp['responseStatus']['code'] == '200' && closeout_resp['responseStatus']['message'] == 'SUCCESS'
              dhl_ecom_pickup = DhlEcomPickup.create(handover_id: closeout_resp['handoverID'],pickup_status:'pending',pickup_details:{total_bags: params[:bags_count],bags_weight:params[:bags_total_weight],total_value:params[:total_value],currency:params[:currency]})
              Shipment.where(number:shipment_numbers).update_all(dhl_ecom_pickup_id:dhl_ecom_pickup.id)
              SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(dhl_ecom_pickup.class.to_s, dhl_ecom_pickup.id, "add_handover_note", Base64.decode64(closeout_resp['handoverNote']))
              #dhl_ecom_pickup.sidekiq_delay(queue: 'critical')
              #               .add_handover_note(
              #                 Base64.decode64(closeout_resp['handoverNote'])
              #               )              
              # PackageManagement.sidekiq_delay(queue:'low').mail_dhl_ecom_out_scan_sheet(shipment_numbers, current_account, dhl_ecom_shipper.id)
              SidekiqDelayGenericJob.set({queue:'low'}).perform_async("PackageManagement", nil, "mail_dhl_ecom_out_scan_sheet", shipment_numbers, {current_account.class.to_s => current_account.id}, dhl_ecom_shipper.id)
              redirect_to (request.env["HTTP_REFERER"] || dhl_ecom_pickup_path), notice:'Close Out Successful' and return
            else
              closeout_errors = []
              closeout_resp['shipmentItems'].each{|a| closeout_errors << a['responseStatus']['messageDetails'].collect(&:values)}
              raise "Something went wrong. #{closeout_errors.flatten.join('. ')}"
            end
          elsif params[:create_pickup].present?
            header = {
              messageType: "PICKUP",
              messageDateTime: DateTime.now.strftime("%Y-%m-%dT%H:%M:%S%Z"),
              accessToken: token,
              messageVersion: "1.2",
              messageLanguage: "en"
            }
            order_numbers =  params[:order_numbers].split(',').map{|s| s.split('_')[0].gsub('INAIX','')}
            order = Order.where(number: order_numbers).first
            company_name, _, shipping_address_1,shipping_address_2,shipping_city, shipping_pincode,_,_ = order.get_warehouse_shipping_address
            from_address = "#{shipping_address_1},#{shipping_address_2}"
            address_array = Order.multi_line_address(50, from_address)
            shipper_address = {
              name: company_name,
              addressLine1: address_array[0],
              addressLine2: address_array[1],
              addressLine3: address_array[2],
              city: shipping_city,
              country: "IN",
              state: "27",
              postalCode: shipping_pincode
            }

            bags = {
              quantity: params[:bags_count].to_i,
              totalWeight: params[:bags_total_weight].to_f,
              totalValue: params[:total_value].to_f,
              currency: params[:currency]
            }

            handoverItems = []
            handoverItems << {
              handoverID: params[:handover_id],
              generateHandover: 'N',
              pickupDate: nil,
              shipperDetails: shipper_address,
              bags: bags
            } 

            pickup_body = {
              customerAccountId: nil,
              pickupAccountId: dhl_ecom_urls[:credentials]['pickup_account_id'],
              soldToAccountId: dhl_ecom_urls[:credentials]['sold_to_account_id'],
              handoverItems: handoverItems
            }

            pickup_request = {
              pickupRequest: {
                hdr: header,
                bd: pickup_body
              }
            }

            pickup_response = HTTParty.post(dhl_ecom_urls[:pickup_url],body:pickup_request.to_json,headers: {'Content-Type' => 'application/json'})
            pickup_resp = pickup_response['pickupResponse']['bd']['handoverItems'][0]
            if pickup_resp['responseStatus']['code'] == '200' && pickup_resp['responseStatus']['message'] == 'SUCCESS'
              DhlEcomPickup.find_by_id(params[:pickup_id]).update_column(:pickup_status,'Successful')
              redirect_to (request.env["HTTP_REFERER"] || dhl_ecom_pickup_path), notice:'Pickup Successful' and return
            else
              raise "pickup not created. #{pickup_resp['responseStatus']['messageDetails'].collect(&:values).join('. ')}"
            end
          end
        else
          raise "Token Expired During Execution.Please Try Again."
        end
      rescue => error
        redirect_to (request.env["HTTP_REFERER"] || dhl_ecom_pickup_path), notice: error.message
      end
    end    
  end

  def get_dhl_ecom_pickup_label
    if request.post?
      begin
        dhl_ecom_pickup = DhlEcomPickup.find_by_id(params[:id])
        dhl_ecom_urls = Mirraw::Application.config.dhl_ecom
        token_response = HTTParty.get("#{dhl_ecom_urls[:token_url]}?clientId=#{dhl_ecom_urls[:credentials]['client_id']}&password=#{dhl_ecom_urls[:credentials]['password']}&returnFormat=json")
        if token_response['accessTokenResponse']['responseStatus']['code'] == '100000'
          token = token_response['accessTokenResponse']['token']
          header = {
            messageType: "PICKUPLABEL",
            messageDateTime: DateTime.now.strftime("%Y-%m-%dT%H:%M:%S%Z"),
            accessToken: token,
            messageVersion: "1.2",
            messageLanguage: "en"
          }
          pickup_body = {
            customerAccountId: nil,
            pickupAccountId: dhl_ecom_urls[:credentials]['pickup_account_id'],
            soldToAccountId: dhl_ecom_urls[:credentials]['sold_to_account_id'],
            handoverItems: [{handoverID: params[:handover_id]}]
          }

          pickup_label_request = {
            pickupLabelRequest: {
              hdr: header,
              bd: pickup_body
            }
          }
          pickup_label_response = HTTParty.post(dhl_ecom_urls[:pickup_label_url],body:pickup_label_request.to_json,headers: {'Content-Type' => 'application/json'})
          pickup_label_resp = pickup_label_response['pickupLabelResponse']['bd']['handoverItems'][0]
        
          if pickup_label_resp['responseStatus']['code'] == '200' && pickup_label_resp['responseStatus']['message'] == 'SUCCESS' && dhl_ecom_pickup.present?
            pickup_label  = Base64.decode64(pickup_label_resp['bags']['bagLabel']) 
            SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(dhl_ecom_pickup.class.to_s, dhl_ecom_pickup.id, "add_pickup_label", pickup_label) if pickup_label.present?
            #dhl_ecom_pickup.sidekiq_delay(queue: 'critical')
            #               .add_pickup_label(pickup_label) if pickup_label.present?
            response = {success:"pickup request successful."}
            render json: response
          else
            raise "Pickup Label Request Failed. #{pickup_label_resp['responseStatus']['messageDetails'].collect(&:values).join('. ')}"
          end
        else
          raise 'Token Expired while in Execution'
        end
      rescue => error
        render json: {error: error.message}
      end
    end
  end

  def reverse_shipment
    @shipment  = Shipment.where(id: params[:shipment_id]).first
    line_item_ids = @shipment.line_item_ids
    @shipment.abandoned!
    LineItem.bulk_add_into_scan('LineItem', line_item_ids, 'Shipment Reversed', current_account.id)
    render json: {notice: 'Reversed Successfully'}
  rescue => e
    render json: {error: e.message}
  end

  def ndr_update_panel
    @shippers = Shipper.where.not(clickpost_shipper_id: nil).pluck(:name, :id)
    if request.post?
      response_msg = 'Invalid Details Provided.'
      if params[:ndr_update_file].present? && params[:shipper_select].present?
        filename  = 'shipments/ndr_updation/' + "#{Time.now.strftime("%m_%d_%H_%M") + SecureRandom.hex(6) +'.csv'}"
        AwsOperations.create_aws_file(filename, params[:ndr_update_file])      
        response_msg = 'NDR Updation is in progres. Report of the same will be mailed to you.'
        clickpost_ndr_object = NDRClickPostAutomation.new(aws_file: filename, shipper_id: params[:shipper_select], account: current_account)
        clickpost_ndr_object.sidekiq_delay.post_ndr_awb_details_to_clickpost
      end
      redirect_to ndr_update_panel_path, notice: response_msg  
    end
  end

  private
  def parse_form_create_for_order(params)
    req_attr = {quantity: 0, amount: 0, ids: [], invoice_items: [], weight: params[:weight],
     service_code: params[:service], packaging_type: params[:packaging], packer_id: params[:packer_id]
    }
    params[:items].each do |index, item|
      if item[:selected].present?
        invoice_item = {quantity: item[:quantity].to_i, price: item[:price].to_i, name: item[:name].gsub('-',' ').camelize}        
        invoice_item[:total_price] = invoice_item[:price] * invoice_item[:quantity]

        req_attr[:ids] << item[:id]
        req_attr[:quantity] += invoice_item[:quantity].to_i
        req_attr[:amount] += invoice_item[:total_price]
        req_attr[:invoice_items] << invoice_item
      end
    end
    req_attr
  end
end
