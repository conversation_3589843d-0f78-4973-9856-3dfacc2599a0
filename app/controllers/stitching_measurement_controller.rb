require 'size_chart_mapping'

class StitchingMeasurementController < ApplicationController
  layout 'admin', only: [:add_measurement_mapping]
  MAX_MIN_W_H = MeasurementInfo.select('MAX(weight) as max_weight,MIN(weight) as min_weight, MAX(height) as max_height,MIN(height) as min_height')[0]
  def create
    mapped_stitching_measurements = SizeChartMapping.new(params[:type_of_suit], stitching_measurement_params[:chest_size]).()
    if params[:type_of_suit] == 'Lehenga'
      saree_measurements = SizeChartMapping.new('Saree', stitching_measurement_params[:chest_size]).()

      mapped_stitching_measurements = mapped_stitching_measurements.slice(:chest_size, :waist_size, :hip_size, :code)
      saree_measurements = saree_measurements.slice(:length, :under_bust, :size_around_arm_hole, :shoulder_size)

      mapped_stitching_measurements = mapped_stitching_measurements.merge(saree_measurements)
    end
    
    stitching_measurement_attributes = stitching_measurement_params.merge(mapped_stitching_measurements) { |key, old_value, new_value| old_value.presence || new_value }
    stitching_measurement_attributes["style_no"] = "As per image" if stitching_measurement_attributes["style_no"] == "0"
    stitching_measurement_attributes["back_style_no"] = "As per image/Recommend by stylist" if stitching_measurement_attributes["back_style_no"] == "0"

    stitching_measurement = StitchingMeasurement.new(stitching_measurement_attributes)
    stitching_measurement.user_review['user_measurement_experience'] = params[:user_measurement_experience]
    order = Order.where(id: stitching_measurement.order_id).first
    stitching_measurement.measurement_group = 'user'
    if order.present?
      all_line_items = order.line_items.sane_items.preload(:stitching_measurements).where(stitching_required: 'Y').to_a
      line_item = all_line_items.find{|item| item.design_id == stitching_measurement.design_id}
    end
    if params[:source] != 'admin'
      status_order, show_addons = false, false
      if line_item.present?
        stitching_measurement_count = StitchingMeasurement.where(line_item_id: line_item.id).count
        stitching_measurement.line_item_id = line_item.id
        if stitching_measurement_count < line_item.quantity
          stitching_measurement.user_id = current_account.try(:user).try(:id)
          stitching_measurement.user_review['used_measurement_tag'] = params[:selected_measurement][:mes_tag] if params[:selected_measurement][:mes_tag].present?
          stitching_measurement.user_review['used_review_id'] = params[:selected_measurement][:review_id] if params[:selected_measurement][:review_id].present?
          if !order.stylist_id.present? && order.sane?            
            order.assign_it_to_stylist(all_line_items.count, line_item.design.designable_type.downcase, 'custom')            
          end
          stitching_measurement.stylist_id = order.stylist_id
          stitching_measurement.add_padding_cancan(line_item)
          stitching_measurement.save 
          stitching_measurement.update_order_state(params[:measurement_repeat],params[:similar_products])
          status_order = true
          SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "send_stitching_info_mail_to_user")
          #order.sidekiq_delay.send_stitching_info_mail_to_user
        end
      end
      if SWITCH_ON_CUSTOM_PAID_ADDON && params[:stitching_measurement][:product_designable_type].to_s == 'lehenga_choli' && line_item.stitching_measurements.count >= line_item.quantity
        addon_notes = line_item.line_item_addons.pluck(:notes).map(&:downcase).join(',')
        show_addons = (addon_notes.exclude?('cancan') || addon_notes.exclude?('padded'))
      end
      render json: {status_order: status_order, show_addons: show_addons}
    elsif stitching_measurement.save
      if !order.stylist.present? && order.sane? && line_item.present?
        order.assign_it_to_stylist(all_line_items.count, line_item.design.designable_type.downcase, 'custom')        
      end
      stitching_measurement.stylist_id = order.stylist_id
      stitching_measurement.add_padding_cancan(line_item)
      stitching_measurement.save
      SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "send_stitching_info_mail_to_user")
      #order.sidekiq_delay.send_stitching_info_mail_to_user
      render json: {head: :no_content}
    else
      render json: {head: :no_content , :message => "stitching measurements not created"}
    end
    if !stitching_measurement.new_record?
      if params[:use_previous_measurement].present? && params[:use_previous_measurement] != 'Create New' && StitchingMeasurement.exists?(id: params[:use_previous_measurement],state: 'approved')
        stitching_measurement.add_notes('Approved Using System Measurements',false)
        stitching_measurement.user_review['system_approved'] = 'true'
        stitching_measurement.measurement_approved!
      end
      stitching_measurement.create_stylist_measurement
      StitchingMeasurement.sidekiq_delay
                          .generate_pdf(
                            stitching_measurement.line_item_id,
                            'stylist'
                          ) if params[:source] != 'admin'
    end
  end

  def update
    stitching_measurement = StitchingMeasurement.where(id: params[:stitching_measurement][:id]).first
    order = Order.where(id: stitching_measurement.order_id).first
    respond_to do |format|
      if stitching_measurement.update_attributes(stitching_measurement_params)
        format.html { redirect_to order_order_detail_path(order.number), notice: 'measurement successfully updated.' }
        format.json { head :no_content }
      else
        format.html { redirect_to order_order_detail_path(order.number), notice: 'measurement cannot be updated.' }
        format.json { render json: stitching_measurement.errors }
        ExceptionNotify.sidekiq_delay.notify_exceptions("Stitching Measurement Update Errors",stitching_measurement.errors, {params: params})
      end
    end
  end

  def add_custom_addons
    if (line_item_addon = LineItemAddon.where(id: params[:item_addon_id]).first).present?
      line_item, order = line_item_addon.line_item, line_item_addon.line_item.order
      total, additional_payment_data, notes = 0, {items: {}}, []
      symbol = @symbol || session[:country][:symbol]
      country_code = @country_code || session[:country][:country_code]
      AddonOptionValue.includes(:addon_option_type).where(:id => params[:addon_option_value_ids]).each_with_index do |addon_option_value, index|
        amount = addon_option_value.addon_option_type.effective_price(line_item.design) * line_item.quantity
        total += amount
        amount = CurrencyConvert.to_currency(symbol, amount, country_code).round(2)
        notes.push("#{addon_option_value.p_name} : #{symbol} #{amount}")
        additional_payment_data[:items].merge!({
          "amount_#{index+1}" => amount,
          "item_name_#{index + 1}" => addon_option_value.p_name,
          "item_number_#{index + 1}" => addon_option_value.id,
          "quantity_#{index + 1}" => 1
        })
      end
      if total > 0
        additional_payment_data[:return_url] = stitching_form_url(order_number: order.number,design_id: line_item.design_id,product_designable_type: 'lehenga_choli')
        additional_payment = line_item_addon.build_additional_payment(order_id: order.id,payment_state: 'new',total: total, currency_rate_market_value: CurrencyConvert.countries_marketrate[country_code], notes: notes.join(','), charge_type: 'custom_addon', currency_code: symbol, currency_rate: @conversion_rate, country_code: country_code)
        if country_code == 'IN'
          additional_payment.paid_currency_code = 'INR'
          additional_payment.paid_currency_rate= 1
        else
          additional_payment.paid_currency_rate = additional_payment.get_paypal_rate
          additional_payment.paid_currency_code = !CURRENCY_CONVERT_TO_USD.keys.include?(country_code) && Order::PAYPAL_ALLOWED_CURRENCIES.include?(symbol) ? symbol : 'USD'
        end
        line_item_addon.save!
        render json: {redirect_url: order.paypal_payments_standard_url(order_url(order.number), symbol, country_code, additional_payment.id, additional_payment_data)}
      else
        render json: {error: 'Please select Addon Options !'}
      end
    else
      render json: {error: 'Something Went Wrong !'}
    end
  end

  def measurement_data
    @selected_stitching_measurement = StitchingMeasurement.unscoped.where(user_id: params[:user_id],id: params[:sm_id]).first
    @partial_name = params[:product_designable_type].to_s == 'lehenga_choli' ? 'lehenga_form' : 'salwarkameez_form'
    @quantity = params[:quantity].to_i
    @design = Design.find_by_id(params[:design_id])
    order = Order.find_by_id(params[:order_id])
    @stitching_measurements =  StitchingMeasurement.where(order_id: params[:order_id], design_id: params[:design_id]).where(product_designable_type: get_designable_from_params)
    @similar_bought_products = order.line_items.preload(design: :designer).joins(:design,line_item_addons: :addon_type_value).joins('left outer join stitching_measurements on stitching_measurements.line_item_id = line_items.id').where('lower(stitching_required) = ? AND lower(designs.designable_type) = ? AND line_items.design_id <> ?','y',@design.designable_type.downcase,params[:design_id]).where('lower(addon_type_values.name) IN (?)',['custom stitching','custom blouse stitching']).where('stitching_measurements.id is null')
    gon.styles_mapping = @styles_mapping = JSON.parse(params[:styles_mapping]) if params[:product_designable_type] == 'lehenga_choli'    
    gon.product_mapping = @product_mapping = JSON.parse(params[:product_mapping])
    render file: '/stitching_measurement/measurement_form_update.js', content_type: 'text/javascript'
  end

  def measurement_info
    height = (params[:height].to_f * 10).to_i
    weight = [MAX_MIN_W_H.max_weight.to_i, MAX_MIN_W_H.min_weight.to_i, params[:weight].to_i].sort[1]
    height = [MAX_MIN_W_H.max_height.to_i, MAX_MIN_W_H.min_height.to_i, height].sort[1]
    height = MAX_MIN_W_H.max_height.to_i if params[:height].to_i == 5 && params[:height].length == 4
    design_type = params[:product_designable_type].to_s.strip == 'kurti' ? 'anarkali' : params[:product_designable_type].to_s.strip
    measurement_info = MeasurementInfo.where(weight: weight).where(height: height,product_designable_type: design_type).first.as_json    
    if (design = Design.preload(:designable).find_by_id(params[:design_id])).present?
      measurement_info.merge!(design.add_validations)
    elsif params[:sample_form] == 'true'
      measurement_info.merge!(max_chest_size: (ENABLE_PLUS_SIZE['enable'] ? CUSTOM_STITCHING_BUST_SIZE_CONSTRAINTS[(design_type == 'anarkali' ? 'salwarkameez' : 'saree')] : ENABLE_PLUS_SIZE['max_size']))
    end
    render json: measurement_info
  end

  def standard_measurement_info
    errors=false
    error_text= ''
    standard_measurements= SIZE_CHART_TABLE[params[:current_size]]
    if !standard_measurements.present?
      errors = true
      error_text= 'Measurements Not Found'
    else
      std_sizes = get_measurement_hash(standard_measurements, params[:current_size])
    end
    render json: {std_sizes: std_sizes, errors: errors, error_message: error_text }
  end

  def stitching_info_mailer
    order = Order.where(id: params[:order_id]).first
    order.add_notes_without_callback("Stitching Info Mail sent",'stitching',current_account)
    OrderMailer.sidekiq_delay.send_stitching_info_label(order.id)
    respond_to do |format|
      format.html { redirect_to order_order_detail_path(order.number), notice: 'Mail was successfully sent' }
    end
  end

  def stitching_form_mailer
    order = Order.where(number: params[:order_number]).first
    SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "send_stitching_measurement_mail")
    #order.sidekiq_delay.send_stitching_measurement_mail(current_account)
    respond_to do |format|
      format.html { redirect_to order_order_detail_path(order.number), notice: 'Form was successfully sent' }
    end
  end

  def stitching_form
    @integration_status='new'

    parameters = Struct.new('Params', :order, :design) do
      include ActiveModel::Validations

      validates :order, presence: true, length: { is: 10 }
      validates :design, presence: true, numericality: { only_integer: true }, length: { maximum: 40 }
    end.new(params[:order_number], params[:design_id])

    order = promise { Order.preload(:line_items).find_by_number(params[:order_number]) }
    @design = promise { order.designs.where(id: params[:design_id]).first }
    @line_item = promise { order.line_items.select{ |li| li.design_id == @design.id }.first }

    return redirect_to root_url, notice: 'Page not found' unless parameters.valid? && order.present? && @design.present? && @line_item.present?
 
    @order_id = order.id
    @order_number = order.number
    if current_account.present? && current_account.user.present? && current_account.user.id != order.user_id
      redirect_to root_url,notice: 'You are not authorized to access this page' and return
    end
    @user_id = order.user_id
    @quantity = gon.quantity =  @line_item.quantity
    gon.current_account = current_account.present?
    gon.sample_stitching_form = false
    @type_of_suit = gon.type_of_suit = @design.designable_type.to_s
    @stitching_measurements =  StitchingMeasurement.where(order_id: order.id, design_id: @design.id)
    addon_notes = @line_item.line_item_addons.collect(&:notes).map(&:downcase).join(',')
    if SWITCH_ON_CUSTOM_PAID_ADDON && params[:product_designable_type].to_s == 'lehenga_choli' && (addon_notes.exclude?('cancan') || addon_notes.exclude?('padded')) ############### check if can be paid through domestic paypal ########
      condition = []
      condition << "lower(addon_option_types.name) like '%cancan%'" if addon_notes.exclude?('cancan')
      condition << "lower(addon_option_types.name) like '%padded%'" if addon_notes.exclude?('padded')
      @addon_options = @line_item.line_item_addons.first.addon_type_value.addon_option_types.where(option_type: 'checkbox', published: true).where(condition.join(' OR ')).preload(:addon_option_values)
      gon.addon_option_names = @addon_options.collect(&:name)
    end
    gon.styles_mapping = @styles_mapping = get_styles_mapping(@design.id) if params[:product_designable_type] == 'lehenga_choli'
    design_type = params[:product_designable_type].to_s.strip == 'kurti' ? 'anarkali' : params[:product_designable_type].to_s.strip
    gon.product_id = @design.id
    @plus_size_product = @line_item.is_product_plus_size? if @line_item.present? && @design.present? && @design.designable_type.to_s.downcase == 'saree'
    gon.product_mapping = @product_mapping = MeasurementInfo.where(design_id: @design.id, product_designable_type: design_type).map{|info| [(info.product_designable_type == 'lehenga_choli' ? info.design_id.to_s + '-' + info.chest_size.to_s : info.design_id.to_s), [info.chest_size.to_s.split('-').map(&:to_i), info.length.to_s.split('-').map(&:to_i), info.sleeves.to_s.split('-').map(&:to_i), info.bottom_length.to_s.split('-').map(&:to_i)]]}.to_h
    if !@plus_size_product
      gon.bust_values = if @product_mapping.present? && !@product_mapping.key?(@design.id.to_s)
        gon.should_change_as_per_bust_size = true
        @product_mapping.map{|k,v| v[0].first}
      else
        gon.should_change_as_per_bust_size = false
        SIZE_CHART_TABLE.keys.select do |key|
          key.start_with?(@type_of_suit)
        end.map do |key|
          key[/\d+\z/].to_i
        end
      end
      gon.bust_values.to_a.select!{|val| @design.check_if_plus_size_serviceable(val) }
    end
    gon.max_plus_size = PLUS_SIZE['MAX_PLUS_SIZE']['blouse'].to_i if @plus_size_product && !PLUS_SIZE['MAX_PLUS_SIZE'].blank? 
    @similar_bought_products = order.line_items.preload(design: :designer).joins(:design,line_item_addons: :addon_type_value).joins('left outer join stitching_measurements on stitching_measurements.line_item_id = line_items.id').where('lower(stitching_required) = ? AND lower(designs.designable_type) = ? AND line_items.design_id <> ?','y',gon.type_of_suit.downcase,@design.id).where('lower(addon_type_values.name) IN (?)',['custom stitching','custom blouse stitching']).where('stitching_measurements.id is null')
    @user_measurements = StitchingMeasurement.where(user_id: order.user_id).where('measurement_name is not null AND height is not null AND height <> ?','0').where(product_designable_type: get_designable_from_params).order('created_at desc').limit(20).collect{|sm| [sm.measurement_name,sm.id]}
    @user_measurements.unshift(['Create New','Create New'])
  end

  def sample_stitching_forms
    if ['admin', 'development', 'staging', 'test'].include?(Rails.env) && params[:product_designable_type].present? && params[:type_of_suit].present?
      @integration_status='new'
      @quantity = 1
      gon.current_account = current_account.present?
      gon.sample_stitching_form = true
      @type_of_suit = gon.type_of_suit = params[:type_of_suit]
      @stitching_measurements = []
      @similar_bought_products = []
      @user_measurements = [['Create New','Create New']]
      gon.styles_mapping = @styles_mapping = get_styles_mapping(-1) if params[:product_designable_type] == 'lehenga_choli'
      render action: 'stitching_form'
    else
      redirect_to root_url, notice: 'Page Not Found'
    end
  end

  def add_measurement_mapping
    @neckline_general_mappings = MeasurementInfo.where(product_designable_type: nil, design_id: nil).order('id')
    design_where_clause = params[:design_id_search].present? ? "design_id = #{params[:design_id_search]}" : ''
    @product_neckline_mappings = MeasurementInfo.where('design_id IS NOT NULL AND product_designable_type IS NULL').where(design_where_clause).paginate(page: params[:page], per_page: 10)
    design_mapping_where_clause = params[:design_search].present? ? "design_id = #{params[:design_search]}" : ''
    @product_mapping = MeasurementInfo.select("design_id, Array_agg((product_designable_type || '/' || chest_size || '/' || length || '/' || sleeves || '/' || coalesce(bottom_length, '') || '/' || id || '/' || updated_at)) as mapping_data").where('design_id is not null and product_designable_type is not null').where(design_mapping_where_clause).group(:design_id).paginate(page: params[:mapping_page], per_page: 10)
  end

  def update_measurement_info
    if params[:design_id].present? && !params[:create_product_mapping].present?
      mes_info = MeasurementInfo.where(design_id: params[:design_id], product_designable_type: nil).first_or_initialize
      mes_info.front_styles = params[:front_styles]
      mes_info.back_styles = params[:back_styles]
      mes_info.hook = params[:hook].join(',')
      mes_info.sleeves = params[:sleeves].join(',')
      mes_info.save
      redirect_to add_measurement_mapping_path, notice: "Product Neckline Mapping for #{mes_info.design_id} successfully created."
    elsif params[:create_product_mapping].present?
      w_check = params[:form_type] == 'lehenga_choli' ? {chest_size: params[:min_chest_size]} : {}
      mes_info = MeasurementInfo.where(design_id: params[:design_id], product_designable_type: params[:form_type]).where(w_check).first_or_initialize
      is_new_mapping = mes_info.new_record?
      mes_info.chest_size = [params[:min_chest_size], params[:max_chest_size].presence].compact.map(&:to_i).sort.join('-')
      mes_info.length = [params[:min_length], params[:max_length].presence].compact.map(&:to_i).sort.join('-')
      mes_info.sleeves = [params[:min_sleeve], params[:max_sleeve].presence].compact.map(&:to_i).sort.join('-')
      mes_info.bottom_length = [params[:min_bottom_length].presence, params[:max_bottom_length].presence].compact.map(&:to_i).sort.join('-').presence
      mes_info.save
      redirect_to add_measurement_mapping_path, notice: "Product Mapping for #{mes_info.design_id} Successfully #{is_new_mapping ? 'Created' : 'Updated'}."
    else
      if params[:delete].present?
        response = {error: true, message: 'Could Not Delete. Please try again.'}
        response = {message: 'Successfully Deleted.'} if MeasurementInfo.find_by_id(params[:id]).try(:destroy)
      else
        response = {error: true, message: 'Could Not Update. Please try again.'}
        if params[:id] && params[:front_styles]
          MeasurementInfo.find(params[:id]).update_attributes(front_styles: params[:front_styles], back_styles: params[:back_styles], hook: params[:hooks], sleeves: params[:sleeves])
          response = {message: 'Successfully Updated.', timestamp: Time.current.strftime('%d %b %Y %I:%M %p')}
        end
      end
      render json: response
    end
  end

  private
  def get_measurement_hash(standard_measurements, current_size)
    if current_size.match(/^Saree/)
      std_sizes = {'shoulder_size' => standard_measurements[9],'size_around_arm_hole' => standard_measurements[8],'chest_size' =>standard_measurements[0],'under_bust'=> standard_measurements[7],'length'=> standard_measurements[3], 'front_neck'=> standard_measurements[4],'back_neck'=> standard_measurements[5],'code' => standard_measurements[10] }
    elsif current_size.match(/^Salwar/)
      std_sizes = {'shoulder_size' => standard_measurements[7],'size_around_arm_hole' => standard_measurements[8],'chest_size' => standard_measurements[3],'waist_size' => standard_measurements[5],'hip_size' => standard_measurements[6], 'sleeves_around' => standard_measurements[9],'code' => standard_measurements[10]}
    elsif current_size.match(/^Lehenga/)
      saree_state = current_size.gsub 'Lehenga','Saree'
      saree_measurements = SIZE_CHART_TABLE[saree_state]
      std_sizes = {'chest_size'=>standard_measurements[1],'waist_size' => standard_measurements[3], 'shoulder_size' => saree_measurements[9],'size_around_arm_hole' => saree_measurements[8],'hip_size' => standard_measurements[4],'under_bust'=> saree_measurements[7],'length'=> saree_measurements[3],'front_neck'=> 'As per the image','back_neck'=> 'As per the image','code' => standard_measurements[5] }
    end
  end

  def get_designable_from_params
    return params[:product_designable_type] == 'anarkali' ? %w(anarkali anarkali_pant kameez_pant kameez_salwar kameez_chudidar kameez_with_bottom_as_per_image anarkali_with_bottom_as_per_image) : (@design.designable_type == 'Lehenga' ? 'lehenga_choli' : (['Kurti','Islamic'].include?(@design.designable_type) ? @design.designable_type.try(:downcase) : 'blouse'))
  end

  def get_styles_mapping(design_id)
    all_styles_mapping = MeasurementInfo.select('front_styles, back_styles, hook, sleeves, design_id').where('product_designable_type IS NULL AND (design_id IS NULL OR design_id = ?)', design_id).map{|i| i.design_id.present? ? [i.design_id, [i.front_styles, i.back_styles, i.hook, i.sleeves]] : [i.front_styles.to_s, [i.back_styles.to_s, i.hook.to_s, i.sleeves.to_s]]}.to_h    
    if all_styles_mapping.key?(design_id)
      design_specific_mapping = all_styles_mapping.delete(design_id).to_a.map{|i| i.to_s.split(',')}
      all_styles_mapping.slice!(*design_specific_mapping[0])
      all_styles_mapping.keys.each do |front_style|
        (0..2).to_a.each do |index|
          all_styles_mapping[front_style][index] = (all_styles_mapping[front_style][index].split(',') & design_specific_mapping[index+1]).join(',')
        end
      end
    end
    all_styles_mapping
  end

  def stitching_measurement_params
    params.require(:stitching_measurement).permit(:measurement_name,:design_id, :line_item_id, :order_id, :size_around_ankle, :shoulder_size, :product_designable_type,
                  :length, :embroidary, :chest_size, :sleeves_length, :sleeves_around, :front_neck, :back_neck,
                  :size_around_thigh, :bottom_length, :size_around_arm_hole, :waist_size, :hip_size, :size_around_knee,
                  :measurement_locked, :padded, :style_no, :hook, :weight, :height, :under_bust, :stitching_label_url, :measurement_info_done_by,
                  :stitching_notes,:shoulder_to_apex,:measurement_type, :denim_waist_size, :age, :accuracy_info, :source_info, :back_style_no, :code)
  end
end
