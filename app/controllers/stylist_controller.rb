class StylistController < ApplicationController
  before_filter :authenticate_account!
  layout 'admin'

  def index
    @start_date, @end_date = get_date('month')
    preload_array = [line_item: [:stylist_events, :open_communication_topics]]
    is_stylist_admin = STYLIST_PANEL_ADMIN_ACCESS.include?(current_account.email)    
    if current_account.present? && is_stylist_admin || (current_account.try(:role).try(:name) == 'stylist' && (@stylist = current_account.stylist).present?)
      if params[:measurement_state].present?
        @stylist = Stylist.get_stylists.find{|sty| sty.id == params[:stylist_id].to_i} if is_stylist_admin && @stylist.blank?
        if (is_non_approved_filter = (['approved but tailor assign pending', 'approved'].exclude?(params[:measurement_state].try(:downcase))))
          preload_array = [line_item: [:stylist_events, :measurement_approved_process_date, :latest_stitching_handover_scan]]        
        end
        s_clause = 'stitching_measurements.id,orders.stylist_id,orders.confirmed_at,orders.created_at,orders.number,orders.country,stitching_measurements.state,stitching_measurements.order_id,stitching_measurements.design_id,stitching_measurements.product_designable_type, line_items.received_on, issue_status, qc_status, line_item_id'
        state = params[:measurement_state] == 'Approved But Tailor Assign Pending' ? 'approved' : params[:measurement_state].parameterize('_')
        w_days_passed = 'orders.confirmed_at <= ?',(params[:days].to_i).days.ago
        w_system_approved = ''
        w_system_approved = 'user_review ? :key',{key:'system_approved'} if state == 'approved' && params[:system_approved].present?
        w_approved_but_tailor_pending = ''
        w_approved_but_tailor_pending = "line_items.stitching_sent_on is null and (line_items.issue_status is null or line_items.issue_status = 'N') and line_items.received_on is not null" if params[:measurement_state] == 'Approved But Tailor Assign Pending'
        stylist_id = params[:stylist_id].present? ? params[:stylist_id] : current_account.stylist.id
        @measurements = StitchingMeasurement.select(s_clause).preload(preload_array).joins(:order,line_item: :designer_order).where('line_items.status IS NULL and line_items.stitching_done_on is null').where('stitching_measurements.state = ? AND designer_orders.state NOT IN (?) AND orders.state NOT IN (?)',state,['canceled','vendor_canceled'], ['cancel', 'dispatched']).where('orders.created_at BETWEEN ? AND ?',@start_date.beginning_of_day,@end_date.end_of_day).where(w_approved_but_tailor_pending).where(w_system_approved).where(w_days_passed).where('orders.stylist_id = ?', stylist_id).where.not('stitching_measurements.product_designable_type = ?','fnp').reorder('').sort_by{|sm| (is_non_approved_filter ? sm.line_item.get_sort_date(sm.confirmed_at, 'Measurement Approved') : sm.confirmed_at)}.group_by{|sm| [sm.order_id,sm.design_id]}
        @no_measurement_designs = LineItem.includes(:designer_order,:stitching_measurements).where(designer_orders: { order_id: @measurements.keys.map{|a| a[0]}}).where.not( designer_orders: { state: ['canceled','vendor_canceled']}).where('status IS NULL AND stitching_required = ?','Y').where('stitching_measurements.line_item_id IS NULL').group_by{|item| item.designer_order.order_id} if @measurements.present?
        if params[:measurement_state].try(:downcase) == 'phone call' 
          all_present_country = @measurements.values.collect{|a| a.collect{|b| b.country}}.flatten.uniq.map(&:downcase)
          @country_with_time_zone = Country.select('name,time_zone').where('lower(name) IN (?)',all_present_country).group_by{|d| d.name.downcase}
        end
      end
    else
      redirect_to root_path, notice:'You are not authorized to access this page.'
    end
  end

  def manage_stylist
    if request.get? && current_account.admin? && ['admin', 'accounts_admin', 'operations'].include?(current_account.try(:role).try(:name).try(:downcase))
      @stylists = Stylist.all.order('id').paginate(page: params[:page], per_page: 15)
    elsif request.post?
      response = {error: 'Stylist not found.'}
      if (stylist = Stylist.find_by_id(params[:id]))
        begin
          stylist.assign_attributes(stylist_params)
          stylist.save!
          response = {success: 'Successfully Updated.'}
        rescue => error
          response = {error: error.message}
        end
      end
      render json: response
    else
      redirect_to root_path, notice:'You are not authorized to access this page.'
    end
  end

  def review_measurements
    if current_account.present? && current_account.try(:role).try(:name) == 'stylist' && (@stylist = current_account.stylist).present?
      if params[:order].present? && params[:design].present?
        @stitching_mes = StitchingMeasurement.joins(:line_item).where('order_id = ? AND stitching_measurements.design_id = ? AND line_items.status IS NULL',params[:order].to_i,params[:design].to_i)        
        @all_approved = @stitching_mes.all?{|s| s.approved?}
        @previous_suggestion = @stitching_mes.order(:updated_at ).select{|st| st.suggested_measurements.present?}.first
        @suggested_by_mes = @stitching_mes.select{|st| (ids = st.suggested_measurements['suggested_ids']).present? && (ids & params[:mes_ids].map(&:to_i)).present?}.first        
        @stitching_mes = @stitching_mes.where(id:params[:mes_ids]) if params[:mes_ids].present?
        @rejected_mes = @stitching_mes.where('state = ? AND suggested_measurements <> ? AND (reject_mail_sent_at IS NULL OR reject_mail_sent_at < ?)','rejected',{}.to_yaml,2.days.ago)
        reviews_id = @stitching_mes.collect(&:user_review).map{|x| x['used_review_id']}
        @reviews = Review.where('id IN (?) AND review IS NOT NULL',reviews_id)
        @stitching_mes = @stitching_mes.preload([order: [tailoring_infos: :item]], :design, [line_item: [:designer_order, line_item_addons: :addon_type_value]])
      end
    else
      redirect_to root_path, notice:'Your are not authorized to access this page.'
    end
  end

  def get_attributes_data
    @selected_attributes = params[:measurements]
    @selected_attributes -= ['suggested_ids']
    @filtered_ids = params[:filtered_ids]
    @suggested_measurements = params[:values] if params[:values].present?
    @stitch_mes = StitchingMeasurement.find_by_id(params[:id])
    @suggestion_id = params[:suggestion_id] if params[:suggestion_id].present?
    respond_to do |format|
      format.js
    end
  end

  def reject_measurement
    if params[:reject_mes].present?
      rejected_attributes = params[:attributes].strip.split(" ")
      stitching_mes = StitchingMeasurement.find_by_id(params[:id])
      suggestion_mes = StitchingMeasurement.find_by_id(params[:suggestion_id]) if params[:suggestion_id].present?
      stitching_mes.suggested_measurements = {}
      stitching_mes.measurement_rejected
      if suggestion_mes.present? && suggestion_mes.rejected?
        ids = Array.new
        ids = suggestion_mes.suggested_measurements['suggested_ids'] if suggestion_mes.suggested_measurements['suggested_ids'].present?
        ids << params[:id].to_i 
        ids.uniq!
        suggestion_mes.suggested_measurements.store('suggested_ids',ids)
        suggestion_mes.save
      else
        total_attributes = rejected_attributes.length - 1
        (0..total_attributes).each do |number|
          suggested_value = params["suggested_value_#{number}".to_sym]
          stitching_mes.suggested_measurements.store(rejected_attributes[number],suggested_value)
        end
        stitching_mes.suggested_measurements.store('user_note',params[:note]) if params[:note].present?
        stitching_mes.reject_mail_sent_at = nil if stitching_mes.reject_mail_sent_at.present?
        stitching_mes.save  
      end
      stitching_mes.add_notes('Measurement Rejected with suggestions',true,current_account)
      notice = 'Measurement Rejected with Suggestions.'
    end
    if params[:send_reject_mail].present?
      rejected_mes_ids = params[:rejected_mes_ids].strip.split(" ")
      OrderMailer.sidekiq_delay
                 .send_measurement_rejected_with_suggestions_mail_to_user(
                   rejected_mes_ids
                 )
      StitchingMeasurement.where(id:rejected_mes_ids).update_all("reject_mail_count = reject_mail_count + 1, reject_mail_sent_at = '#{DateTime.now}'")
      notice = 'Measurement Rejected With Suggestions Mail Sent To User.'
    end
    filtered_mes_ids = params[:filtered_mes_ids].strip.split(" ")
    redirect_to review_measurements_path(order:params[:order],design:params[:design],mes_ids:filtered_mes_ids), notice: notice
  end

  def approve_measurement
    stitch_mes = StitchingMeasurement.find_by_id(params[:measurement_id])
    suggested_ids = stitch_mes.suggested_measurements['suggested_ids']
    if params[:phone] == 'true'
      notice = 'Phone Call Arrangement is scheduled'
      Stylist.update_suggested_measurements(suggested_ids,current_account,stitch_mes,true)
      redirect_to root_path, notice: notice
    else
      if params[:approve].present?
        stitch_mes.user_review.delete('system_approved')
        stitch_mes.measurement_approved   
        notice = 'Measurement Approved Successfully'
        stitch_mes.add_notes('Measurement Approved',true,current_account)
        filtered_mes_ids = params[:filtered_mes_ids].split(" ")
        redirect_to review_measurements_path(order:params[:order],design:params[:design],mes_ids:filtered_mes_ids), notice: notice
      else
        notice = 'Measurement Updated Successfully'
        attribute_hash = {}
        stitch_mes.suggested_measurements.each do |key,value|
          next if ['suggested_ids','user_note'].include?(key)
          attribute_hash[key.to_sym] = value
        end
        Stylist.update_suggested_measurements(suggested_ids,current_account,stitch_mes,false,attribute_hash)
        redirect_to root_path, notice: notice
      end
    end
  end

  def hold_measurement
    filtered_mes_ids = params[:filtered_mes_ids].to_s.split(" ")
    notice = 'Measurement Could Not Be Found.'
    if request.post? && (stitch_mes = StitchingMeasurement.find(params[:measurement_id])).present?
      stitch_mes.move_to_hold
      stitch_mes.add_notes("Hold Measurement : #{params[:hold_reason]}", true, current_account)
      notice = 'Successfully Moved To Hold.'
    end
    redirect_to review_measurements_path(order:params[:order],design:params[:design],mes_ids:filtered_mes_ids), notice: notice
  end

  def reassignment_page
    if current_account.try(:admin?) && current_account.role.try(:name) != 'stylist' 
      if request.get?
        @stylists = Stylist.where(available: true).map{|a| [[a.name,a.stylist_group].compact.join(' | '), a.id]}
      elsif request.post?
        if params[:assign_type] == 'single_assign'
          order = Order.where('number = ? AND state NOT IN (?)',params[:order_number],['cancel','dispatched']).first
          if order.present?
            redirect_with_notice("#{order.number} is already assigned to given stylist", 'reassignment_page') and return if (new_stylist_id = params[:select_single_stylist].to_i) == (old_stylist_id = order.stylist_id)
            new_stylist_name = Stylist.single_reassign(order, old_stylist_id, new_stylist_id, current_account)
            redirect_with_notice("#{order.number} reassigned to #{new_stylist_name}", 'reassignment_page')
          else
            redirect_with_notice('Order not exists or in wrong state.', 'reassignment_page')
          end
        elsif params[:assign_type] == 'bulk_assign' && (csv_data = CSV.read(params[:csv_file].path)).length <= 500 && (stylists = params[:select_stylist].try(&:compact)).present?
          stylist_names = JSON.parse(params[:stylist_names])
          order_numbers = csv_data.collect{|data| data[0]}.compact.uniq.map(&:upcase)
          Stylist.sidekiq_delay
                 .bulk_reassign(
                   stylists.map(&:to_i),
                   order_numbers,
                   current_account.email,
                   stylist_names
                 )
          redirect_with_notice("Stylist Reassignment is scheduled. Reassignment Report will be mailed to #{current_account.email} shortly.", 'reassignment_page')
        else
          notice = (params[:assign_type] && params[:select_stylist].present? ? 'Please upload CSV File with maximum of 500 records.' : 'Please Select At Least 1 Stylist for Reassignment.')
          redirect_with_notice(notice, 'reassignment_page')
        end
      end
    else
      redirect_with_notice('You are not authorized to access this page.', 'full_image', 'store')
    end
  end

  def daily_track_report
    @stylists = Stylist.get_stylists.map{|sty| [sty.id, sty.name]}.to_h
    @stylist_track_data = Stylist.get_current_track_report
    respond_to do |format|
      format.html
      format.csv {
        headers["Content-Disposition"] = "attachment; filename=\"Stylist Track Report #{Time.current.strftime('%d-%b-%Y %I:%M %p')}\""
        render text: get_stylist_track_csv(@stylist_track_data, @stylists) 
      }
    end
  end

private 
  def get_stylist_track_csv(track_data, stylist_details)
    CSV.generate do |csv|
      metric_names = ['Slot1', 'Slot2', 'to_be_received', 'approved', 'rejected', 'hold', 'phone_call', 'Reassigned', 'Alteration', 'Received', 'total_worked_count', 'Planned', 'total_pending']
      csv << (['Stylist'] + metric_names.map(&:titleize))
      track_data.keys.each do |sty_id|
        csv << ([stylist_details[sty_id]] + track_data[sty_id].values_at(*metric_names))
      end
    end
  end

  def redirect_with_notice(notice, action, controller = nil)
    flash[:notice] = notice
    redirect_to ({action: action, controller: controller}.reject{|k,v| v.nil?})
  end

  def stylist_params
    params.permit(:orders_count, :vacation_start_date, :vacation_end_date, :vacation_message, :stylist_group)
  end

end

