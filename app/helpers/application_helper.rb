module ApplicationHelper

  def page_title(text)
    content_for :title, text
  end  

  def nav_link(link_text, link_path)
    class_name = current_page?(link_path) ? 'active' : ''

    content_tag(:li, :class => class_name) do
      link_to link_text, link_path
    end
  end

  def env_development?
    Rails.env.development?
  end

  def meta_tag(tag, text)
    content_for :"#{tag}", text
  end

  def yield_meta_tag(tag, default_text='')
    content_for?(:"#{tag}") ? content_for(:"#{tag}") : default_text
  end

  def check_for_landings
    (params[:controller]  == "pages" && (params[:action] == "landing" || params[:action] == "contact"))
  end

  def check_for_collection_noindex
    (params[:controller] == "store" && params[:action] == "collection" && ( %w(wedding-sarees wedding-salwars wedding-lehengas wedding-jewellery).include?(params[:collection])))
  end

  RATING_DISABLE = {
    'global_on' => 1,
    'designer_collections' => {
      'show' => {
        'rating_ui' => 1,
      }
    },
    'designs' => {
      'show' => {
        'rating_ui' => 0,
        'rating' => 0,
        'review_text' => 0,
        'designer_rating_ui' => 1,
        'designer_rating' => 0,
        'designer_rating_text' => 0
      }
    },
    'dynamic_landing_pages' => {
      'show' => {
        'rating_ui' => 1
      }
    },
    'store' => {
      'catalog2' => {
        'rating_ui' => 0
      },
      'collection' => {
        'rating_ui' => 1
      },
      'tags1' => {
        'rating_ui' => 1
      },
      'search1' => {
        'rating_ui' => 1
      }
    },
    'pages' => {
      'eid' => {
        'rating_ui' => 1
      }
    },
    'errors' => {
      'error_404' => {
        'rating_ui' => 1
      }
    }
  }.freeze

  COLOR = {
       :black    => "#000000",
       :blue     => "#4169e1",
       :green    => "#00FF00",       
       :maroon   => "#800000",
       :purple   => "#800080",
       :grey     => "#C0C0C0",       
       :red      => "#FF0000",
       :pink     => "#FF00FF",       
       :orange   => "#FF8C00",
       :yellow   => "#FFFF00",
       :white    => "#FFFFFF"       
    }
  
  SORT_TYPE = {
    'Handpicked' => 'default',
    'Popularity' => 'bstslr',
    'Price - Low to High' => 'l2h',
    'Discounts' => 'discount',
    'Newest First' => 'new',
    'Trending' => 'trending',
  }

  SORT_TYPE.merge!({'Top Rated' => 'top_rated'}) if RATING_DISABLE['global_on'].to_i == 0
  SORT_TYPE.merge!({'Price - High to Low' => 'h2l', 'Most Ordered' => 'trending-designs'}) if Rails.env.admin?

  SORT_TYPE_REVERSE = {
    'discount' => 'Highest discounts',
    'default' => 'Handpicked',
    'h2l' => 'High to Low - price',
    'l2h' => 'Low to High - price',
    'new' => 'Newness'
  }

  STATES = State.joins(:country).where("countries.name": "India").pluck("states.name").sort

  COUNTRIES = [
               'India',
               'Afghanistan',
               'Albania',
               'Algeria',
               'American Samoa',
               'Andorra',
               'Angola',
               'Anguilla',
               'Antigua And Barbuda',
               'Argentina',
               'Armenia',
               'Aruba',
               'Australia',
               'Austria',
               'Azerbaijan',
               'Bahamas',
               'Bahrain',
               'Bangladesh',
               'Barbados',
               'Belarus',
               'Belgium',
               'Belize',
               'Benin',
               'Bermuda',
               'Bhutan',
               'Bolivia',
               'Bosnia and Herzegovina',
               'Botswana',
               'Brazil',
               'British Virgin Islands',
               'Brunei',
               'Bulgaria',
               'Burkina Faso',
               'Burma',
               'Burundi',
               'Cambodia',
               'Cameroon',
               'Canada',
               'Cape Verde',
               'Cayman Islands',
               'Central African Republic',
               'Chad',
               'Chile',
               'China',
               'Christmas Island',
               'Colombia',
               'Comoros',
               'Cook Islands',
               'Costa Rica',
               'Cote Ivoire',
               'Croatia',
               'Cuba',
               'Cyprus',
               'Czech Republic',
               'Denmark',
               'Djibouti',
               'Dominica',
               'Dominican Republic',
               'East Timor',
               'Ecuador',
               'Egypt',
               'El Salvador',
               'Equatorial Guinea',
               'Eritrea',
               'Estonia',
               'Ethiopia',
               'Falkland Islands',
               'Faroe Islands',
               'Fiji',
               'Finland',
               'France',
               'French Guiana',
               'French Polynesia',
               'Gabon',
               'Gambia, The',
               'Georgia',
               'Germany',
               'Ghana',
               'Gibraltar',
               'Greece',
               'Greenland',
               'Grenada',
               'Guadeloupe',
               'Guam',
               'Guatemala',
               'Guinea',
               'Guinea-Bissau',
               'Guyana',
               'Haiti',
               'Honduras',
               'Hungary',
               'Iceland',
               'Indonesia',
               'Iran',
               'Iraq',
               'Ireland',
               'Israel',
               'Italy',
               'Jamaica',
               'Japan',
               'Jordan',
               'Kazakhstan',
               'Kenya',
               'Kiribati',
               'Korea - North',
               'Korea - South',
               'Kuwait',
               'Kyrgyzstan',
               'Laos',
               'Latvia',
               'Lebanon',
               'Lesotho',
               'Liberia',
               'Libya',
               'Liechtenstein',
               'Lithuania',
               'Luxembourg',
               'Macau',
               'Macedonia',
               'Madagascar',
               'Malawi',
               'Malaysia',
               'Maldives',
               'Mali',
               'Malta',
               'Marshall Islands',
               'Martinique',
               'Mauritania',
               'Mauritius',
               'Mayotte',
               'Mexico',
               'Micronesia - Federated States of',
               'Moldova',
               'Monaco',
               'Mongolia',
               'Montserrat',
               'Morocco',
               'Mozambique',
               'Namibia',
               'Nauru',
               'Nepal',
               'Netherlands',
               'Netherlands Antilles',
               'New Caledonia',
               'New Zealand',
               'Nicaragua',
               'Niger',
               'Nigeria',
               'Niue',
               'Norfolk Island',
               'Northern Mariana Islands',
               'Norway',
               'Oman',
               'Pakistan',
               'Palau',
               'Panama',
               'Papua new Guinea',
               'Paraguay',
               'Peru',
               'Philippines',
               'Pitcairn Island',
               'Poland',
               'Portugal',
               'Puerto Rico',
               'Qatar',
               'Reunion',
               'Romania',
               'Rwanda',
               'Saint Helena',
               'Saint Kitts and Nevis',
               'Saint Lucia',
               'Saint Vincent and the Grenadines',
               'Samoa',
               'San Marino',
               'Sao Tome and Principe',
               'Saudi Arabia',
               'Senegal',
               'Serbia and Montenegro',
               'Seychelles',
               'Sierra Leone',
               'Singapore',
               'Slovakia',
               'Slovenia',
               'Solomon Islands',
               'Somalia',
               'South Africa',
               'Spain',
               'Sri Lanka',
               'Sudan',
               'Suriname',
               'Swaziland',
               'Sweden',
               'Switzerland',
               'Syria',
               'Taiwan',
               'Tajikistan',
               'Tanzania',
               'Thailand',
               'Togo',
               'Tokelau',
               'Tonga',
               'Trinidad And Tobago',
               'Tunisia',
               'Turkey',
               'Turkmenistan',
               'Turks and Caicos Islands',
               'Tuvalu',
               'U.S. Virgin Islands',
               'Uganda',
               'Ukraine',
               'United Arab Emirates',
               'United Kingdom',
               'United States',
               'Uruguay',
               'Uzbekistan',
               'Vanuatu',
               'Vatican City - Holy See',
               'Venezuela',
               'Vietnam',
               'West Bank',
               'Western Sahara',
               'Yemen',
               'Yugoslavia - Federal Repubic Of',
               'Zambia',
               'Zimbabwe'
                ]

  def bootstrap_class_for flash_type
    case flash_type
      when 'success'
        "alert-success"
      when 'error'
        "alert-error"
      when 'alert'
        "alert-danger"
      when 'notice'
        "alert-info"
      else
        flash_type.to_s
    end
  end            
             
             
  def ApplicationHelper.categories
    a = {}   
    Category.roots.each do |node|
      node.leaves.each do |leaf|
        name = leaf.name      
      
        parent = leaf.parent
        name += ' < ' + parent.name

        unless parent.root?
          grandparent = parent.parent
          name += ' < ' + grandparent.name
        end
        a[name] = leaf.id
      end   
    end
  	
  	# include roots
  	Category.roots.each do |node|
	    if node.leaves.empty?
        name = node.name
        a[name] = node.id
      end
	  end
  	return a
  end
  
  def rand_range(min, max)
    min + rand(max - min + 1)
  end
  
  def title(page_title, options={})
    content_for(:title, page_title.to_s)
  end
  
  def description(page_desc, options={})
    content_for(:description, page_desc.to_s)
  end
  
  def keywords(page_keywords, options={})
    content_for(:keywords, page_keywords.to_s)
  end

  def og_title(og_title_desc, options={})
    content_for(:og_title, og_title_desc.to_s)
  end
  
  def og_image(og_image_desc, options={})
    content_for(:og_image, og_image_desc.to_s)
  end
  
  def og_url(og_url_desc, options={})
    content_for(:og_url, og_url_desc.to_s)
  end
         
  def og_type(og_type_desc, options={})
    content_for(:og_type, og_type_desc.to_s)
  end

  def get_menu_headers
    # Only one sub level
    menu = Hash.new
    @categories = Category.roots.sort_by { |root| root.order || 100 }
    @categories.each do |root|
      menu[root.name] = root.children.map {|l| l.name }
    end
    menu
  end
  
  def get_menu_title(title)
    return title if title.downcase.exclude?('below') || title.downcase.include?('size')
    hex_symbol = instance_variable_get(:@hex_symbol)
    digits = title.gsub(/\D/, "")
    return title if digits.blank?
    title.gsub(/( \W|\d)/, "") + get_price_in_currency_with_symbol(digits.to_i,false,hex_symbol)
  end

  def get_category_childrens(name)
    c = Category.find_by_namei(name)
    c.children.collect! {|l| l.name }
  end
  
  
  def color_lookup_r(val)
    COLOR.each do |key, key_val|
      if key_val == val
        return key
      end
    end
    nil
  end
  
  def designer_list
     #Rails.cache.fetch('designer_list', :expires_in => 24.hours) { Designer.graded.published.limit(44) }
     Designer.graded.published.limit(44)
  end
    
  def is_live?
    true
  end
  
  def facebook_like
    content_tag :iframe, nil, :allowTransparency => "true", :frameborder => "0", :scrolling => "no", :src => "//www.facebook.com/plugins/like.php?href=#{CGI::escape(request.base_url + request.path)}&send=false&layout=button_count&width=90&show_faces=false&action=like&colorscheme=light&font=verdana&height=21", :style => "border:none; overflow:visible; width:90px; height:21px;"
  end

  def facebook_like_url(url)
    content_tag :iframe, nil, :allowTransparency => "true", :frameborder => "0", :scrolling => "no", :src => "//www.facebook.com/plugins/like.php?href=#{CGI::escape(url)}&send=false&layout=button_count&width=90&show_faces=false&action=like&colorscheme=dark&font=verdana&height=21", :style => "border:none; overflow:visible; width:90px; height:21px;"
  end


  def facebook_fan_page_like
    content_tag :iframe, nil, :allowTransparency => "true", :frameborder => "0", :scrolling => "no", :src => "//www.facebook.com/plugins/like.php?app_id=***************&href=https%3A%2F%2Fwww.facebook.com%2FMirrawDesigns&send=false&layout=button_count&width=90&show_faces=false&action=like&colorscheme=light&font&height=21", :style => "border:none; overflow:visible; width:90px; height:21px;"
  end

  def get_data_by_class(object)
    case object.class.name
    when 'Account'
      object.name
    when 'Time'
      object.strftime('%e %b %Y')
    else
      object
    end
  end

  def get_menu_from_kind(kind)
    c = Category.find_by_namei(kind)
    if c.present?
      if c.leaf?
        parent = c.parent
        childrens = parent.children.sort_by { |child| child.order || 100 }
        childrens = childrens.collect! { |l| l.name }
      else
        parent = c
        childrens = c.children.sort_by { |child| child.order || 100 }
        childrens = childrens.collect! { |l| l.name }
      end
      h = Hash.new
      h['parent'] = parent.name
      h['childrens'] = childrens
      h['query'] = kind
      h     
    end
  end
  
  def has_kids?(kind)
    c = Category.find_by_namei(kind)
    !c.leaf?
  end
  
  def get_currencies
    CurrencyConvert.order('Country ASC')
  end
  
  def get_price_in_currency_coupons(price)
    conversion_rate = instance_variable_get(:@conversion_rate)
    price_in_currency = (price/conversion_rate).ceil    
    return price_in_currency
  end

  def schema_symbol
    @symbol == 'Rs' ?  'INR' : @symbol
  end

  # to be used in mailers
  def get_actual_price_in_currency_with_symbol(price, conversion_rate, currency_symbol)
    conversion_rate = conversion_rate || instance_variable_get(:@conversion_rate) || 1
    return "#{currency_symbol} #{price}" if conversion_rate.to_f == 1
    "#{currency_symbol} #{(price.to_f/conversion_rate).round(2)}"
  end

  def get_actual_price_in_currency(price, conversion_rate)
    conversion_rate = conversion_rate || instance_variable_get(:@conversion_rate) || 1
    return price if conversion_rate.to_f == 1
    (price/conversion_rate).round(2)
  end

  def get_price_with_symbol(price,currency_symbol)
    return "#{currency_symbol} #{price.ceil}" if ['INR', 'RS'].include?(currency_symbol.upcase)
    "#{currency_symbol} #{(price.to_f).round(2)}"
  end

  def get_price_in_currency(price)
    conversion_rate = instance_variable_get(:@conversion_rate) || 1
    return price.ceil if ['INR', 'Rs'].include?(@symbol)
    return (price/conversion_rate).round(2)
  end

  def get_symbol_from(hex_code)
    codes = hex_code.split(', ')
    symbol = ''
    codes.each do |code|
      symbol += [code.hex].pack('U')
    end
    symbol
  end

  def get_price_in_currency_with_symbol(price,pre_calc_price=nil, hex_code = nil)
    currency_symbol = instance_variable_get(:@symbol)
    if pre_calc_price.present?
      price_in_currency_with_symbol = currency_symbol == "Rs" ? currency_symbol +' '+price.ceil.to_s : currency_symbol +' '+price.round(2).to_s
    elsif hex_code.present?
      price_in_currency_with_symbol = hex_code == "&#x20B9;" ? "#{get_symbol_from(hex_code)} #{price.ceil}" : "#{get_symbol_from(hex_code)} #{get_price_in_currency(price).round(1)}"
    else
      price_in_currency_with_symbol = currency_symbol == "Rs" ? currency_symbol +' '+price.ceil.to_s : currency_symbol +' '+get_price_in_currency(price).round(2).to_s
    end
    price_in_currency_with_symbol
  end

  def get_price_in_currency_for_roles(price)
    if (instance_variable_get(:@view_flag) && !current_account.is_ops_support_user?) || @order.currency_code.blank? || @order.currency_rate.blank?
      return  'Rs.' + ' ' + price.to_s
    else
      return get_actual_price_in_currency_with_symbol(price.to_i, @order.currency_rate, @order.currency_code).to_s
    end
  end

  def get_price_in_currency_for_scaling(price,country_code)
    conversion_rate = CONVERSION_RATES[country_code]
    conversion_rate = 1 if conversion_rate.blank?
    price_in_currency = (price/conversion_rate).round(2)
    return price_in_currency
  end

  def get_price_in_currency_with_symbol_for_scaling(price,country_code)
    @symbol_hash||=CurrencyConvert.countries_symbol
    currency_symbol = @symbol_hash[country_code]
    currency_symbol = "INR" if currency_symbol.blank?
    return currency_symbol +' '+get_price_in_currency_for_scaling(price,country_code).round(2).to_s
  end

  def get_sub_total_in_currency(price,quantity)
    value = get_price_in_currency(price) * quantity
  end
  
  def get_item_total_price_in_currency_from_cart
    total = 0
    cart = instance_variable_get(:@cart)
      for stores in cart.designer_stores do
        for item in stores[:items] do
          total += get_sub_total_in_currency(item.price,item.quantity)
          line_item_addons = item.line_item_addons
          line_item_addons.each do |addon|
            total += get_sub_total_in_currency(addon.snapshot_price,item.quantity)
          end
        end
      end
    total
  end
  
  def get_item_total_price_in_currency_from_order(skip_addons = false)
    total = 0
    order = instance_variable_get(:@order)
    cod_check_order = order.present? && order.cod? && order.currency_rate_market_value.present? && order.currency_code.present? && [order.currency_code].exclude?(@symbol)
    total += get_item_total_price_without_addons_in_currency_from_order(order, cod_check_order)
    total += get_item_addons_price_in_currency_from_order unless skip_addons
    total.round(2)
  end

  def get_item_total_price_without_addons_in_currency_from_order(order, cod_check_order)
    total = 0
    order.designer_orders.each do |designer_order|
      designer_order.line_items.each do |item|
        item_price = cod_check_order ? order.international_cod_price(item.price) : item.price
        total += (get_actual_price_in_currency(item_price, order.currency_rate) * item.quantity)
      end
    end
    total.round(2)
  end

  def get_item_addons_price_in_currency_from_order
    total = 0
    order = instance_variable_get(:@order)
    cod_check_order = order.present? && order.cod? && order.currency_rate_market_value.present? && order.currency_code.present? && [order.currency_code].exclude?(@symbol)
    order.designer_orders.each do |designer_order|
      unless designer_order.state == 'canceled'
        designer_order.line_items.not_canceled.each do |item|
          item.line_item_addons.each do |addon|
            addon_snapshot_price = cod_check_order ? order.international_cod_price(addon.snapshot_price) : addon.snapshot_price
            total += (get_actual_price_in_currency(addon_snapshot_price, order.currency_rate) * item.quantity)
          end
        end
      end
    end
    total.round(2)
  end

  def utm_icn_data
    session.to_hash.symbolize_keys!.slice(:utm_source,:utm_medium,:utm_campaign,:utm_term,:utm_content,:icn_term).merge({ip_address: request.remote_ip})
  end

  def cart_total_items
    total_items = @cart.try(:total_items).to_i
    prev_count = session[:cart_total_items].to_i
    session[:cart_total_items] = total_items
    if prev_count == 0 || total_items == 0
      if total_items > 0 || account_signed_in?
        cookies[:dynamic_user] = 't'
      else
        cookies.delete :dynamic_user
      end
    end
    total_items
  end

  def get_approx_shipping_cost(design, country)
    weight = design.approx_weight
    shipping_cost = Country.shipping_cost_for(weight, country)
    additional_cost = Country.shipping_cost_for(weight*2, country)
    single_item_value = get_price_in_currency_with_symbol(shipping_cost)
    additional_value = get_price_in_currency_with_symbol((additional_cost - shipping_cost))
    return [single_item_value, additional_value]
  end
  
  def coupon_text(coupon,exclude_min_amount=false)
    symbol = instance_variable_get(:@symbol)
    if !exclude_min_amount && (coupon.min_amount.present? && coupon.min_amount > 10)
      if coupon.coupon_type == "POFF"
        coupon_text = coupon.percent_off.to_s + '% off on ' + symbol +' '+ get_price_in_currency_coupons(coupon.min_amount).to_s
      else
        coupon_text = get_price_in_currency_with_symbol(coupon.flat_off) + ' off on ' + symbol +' '+ get_price_in_currency_coupons(coupon.min_amount).to_s
      end
    else
      if coupon.coupon_type == "POFF"
        coupon_text =  exclude_min_amount ? "To Get Your #{coupon.percent_off.to_s} % OFF" : coupon.percent_off.to_s + '% off'
      elsif coupon.coupon_type == 'STITOFF'
        coupon_text = 'To Get Free Stitching'
      else
        coupon_text = exclude_min_amount ? "#{get_price_in_currency_with_symbol(coupon.flat_off)} OFF" : symbol +' '+ get_price_in_currency_coupons(coupon.min_amount).to_s + ' flat off '
      end
    end
  end

  def coupon_text_short(coupon)
    symbol = instance_variable_get(:@symbol)
    if coupon.min_amount > 10
      if coupon.coupon_type == "POFF"
        coupon_text = "Extra " + coupon.percent_off.to_s + '% off on ' + symbol +' '+ get_price_in_currency_coupons(coupon.min_amount).to_s
      else
        coupon_text = get_price_in_currency_with_symbol(coupon.flat_off) + ' off on ' + symbol +' '+ get_price_in_currency_coupons(coupon.min_amount).to_s
      end
    else
      if coupon.coupon_type == "POFF"
        coupon_text = "Extra " + coupon.percent_off.to_s + '% off on this product.'
      else
        coupon_text = symbol +' '+ get_price_in_currency_coupons(coupon.min_amount).to_s + ' flat off '
      end
    end
  end

  def coupon_text_short_boutique(coupon, designer)
    symbol = instance_variable_get(:@symbol)
    if coupon.min_amount > 10
      if coupon.coupon_type == "POFF"
        coupon_text = "Extra " + coupon.percent_off.to_s + '% off on ' + symbol +' '+ get_price_in_currency_coupons(coupon.min_amount).to_s
      else
        coupon_text = get_price_in_currency_with_symbol(coupon.flat_off) + ' off on ' + symbol +' '+ get_price_in_currency_coupons(coupon.min_amount).to_s
      end
    else
      if coupon.coupon_type == "POFF"
        coupon_text = "Extra " + coupon.percent_off.to_s + "% off on all products by #{designer.name}."
      else
        coupon_text = symbol +' '+ get_price_in_currency_coupons(coupon.min_amount).to_s + ' flat off '
      end
    end
  end

  def get_addons(line_item_id)
    addons = LineItemAddon.where(:line_item_id => line_item_id)
    items = Array.new
    addons.each do |addon|
      if addon && addon.addon_type_value
        items << {:name => addon.addon_type_value.name, description: addon.addon_type_value.description, :price => addon.snapshot_price, :prod_time => addon.addon_type_value.prod_time, :notes => addon.notes, :id => addon.id}
      end
    end
    if items.count > 0
      return items
    else
      return false
    end
  end

  def critical_pages?
    (controller_name == 'store' && ['catalog2', 'tags1', 'search1', 'full_image'].include?(action_name)) || (controller_name == 'designs' && action_name == 'show') ||
    (controller_name == 'wishlists' && action_name == 'index')
  end

  def subscription_pages?
    (controller_name == 'store' && ['collection', 'catalog2', 'landing', 'tags1', 'search1', 'full_image'].include?(action_name)) ||
      (controller_name == 'designs' && action_name == 'show') || (controller_name == 'dynamic_landing_pages' && action_name == 'show')
  end

  def is_search_result_page?
    controller_name == 'store' && action_name == 'search1'
  end

  def percentage(num,denom,round=false)
    percent = num.to_f/denom.to_f * 100
    if round == false then percent else percent = percent.round(2) end
  end

  def country_or_currency_indian?
    (['india', 'n/a'].include?(@actual_country.downcase)) ||  (['inr', 'rs'].include?(@symbol.downcase))
  end

  def country_or_currency_eligble_for_multiline_address?
    (['india', 'uae'].include?(@actual_country.downcase)) ||  (['inr', 'rs', 'aed'].include?(@symbol.downcase))
  end

  def cod_available_for_country?
    ENABLE_COD_COUNTRIES.include?(Country.country_name(@country_code).downcase)
  end


  def rts_available_country?
    RTS_ALLOWED_COUNTRIES.include?(@actual_country.try(:downcase))
  end

  def unbxd_activated?
    ENV['UNBXD_ACTIVATED'] == 'true'
  end

  def mirraw_contact_number(current_currency=nil, current_country=nil)
    symbol = current_currency.present? ? current_currency : @symbol
    country_code = current_country.present? ? current_country : @country_code
    if ['inr', 'rs'].include?(symbol.try(:downcase))
      return MIRRAW_CONTACT_INFO
    elsif symbol.try(:downcase) == 'cad'
      return '+15-817-054-535'
    elsif country_code.to_s.downcase == 'gb'
      return '+441-214-614-192'
    else
      return '+1-949-464-5941'
    end
  end

  def get_order_retry_url(app_source, order_number)
    app_source = ((app_source = app_source.split('-')).length == 2) ? app_source[0] : app_source[0..1].join('-') if app_source.present?
    case app_source
    when 'Android', 'iOS'
      'https://bnc.lt/retry-order'
    when 'Android-Jewellery'
      'https://ixys.app.link/retry-order'
    when 'Android-Gifts'
      'https://kl0g.app.link/retry-order'
    when 'Android-Sarees'
      'https://ebh9.app.link/retry-order'
    when 'Android-Bags'
      'https://zga2.app.link/retry-order'
    when 'Android-Blouses'
      'https://dk9z.app.link/retry-order'
    when 'Android-Kurtis'
      'https://xeb7.app.link/retry-order'
    when 'Android-Lehengas'
      'https://yqzd.app.link/retry-order'
    when 'Android-Men'
      'https://9bt2.app.link/retry-order'
    when 'Android-Salwar_Kameez'
      'https://6wnx.app.link/retry-order'
    when 'Android-Earrings'
      'https://rqss.app.link/retry-order'
    when 'Android-Anarkali'
      'https://x3a7.app.link/retry-order'
    else
      order_retry_url(id: order_number, utm_campaign: :order_retry)
    end
  end

  def app_ranking_batchwise(app_id)
    line_chart app_rank_batchwise_path(app_id: app_id), height: '500px', library: {
      title: 'App Ranks',
      vAxis: {
        allowDecimals: false,
        title: 'Rank',
        direction: -1,
        ticks: [0,5,10,20,30,40,50]
      },
      hAxis: {
         direction: -1, 
         title: 'Batch'
      }
    }
  end

  def vendor_perf_charts(url,designer,metric_definition,id=nil,designers=nil)
    library_settings = {hAxis: {title: 'Date'}}
    if metric_definition.present?
      library_settings[:title] = "#{metric_definition.name.try(:titleize)} over #{metric_definition.duration} days"
      library_settings[:vAxis] = {title: metric_definition.name.to_s.titleize,baseline: metric_definition.threshold_value.to_f,baselineColor: 'red'}
      line_chart Rails.application.routes.url_helpers.send(url,designer,metric_definition.id,format: :json), library: library_settings
    else
      library_settings[:title] = "Order Defect Rate over 30 days"
      library_settings[:vAxis] = {title: 'Order Defect Rate'}
      line_chart Rails.application.routes.url_helpers.send(url,metric_id: id,designers: designers,format: :json),id: 'vendor_odr_report', library: library_settings
    end
  end

  def get_gift_wrap_totals_hash
    gift_wrap_totals = {amount: 0}
    unless GIFT_WRAP_PRICE.to_f < 0
      gift_wrap_price = get_price_in_currency(GIFT_WRAP_PRICE.to_f)
      gift_wrap_totals = {amount: gift_wrap_price, pre_calc_price: true, class: 'gift_wrap', title: 'Gift Wrap Charges'}
    end
    return gift_wrap_totals[:amount], gift_wrap_totals
  end

  def get_current_role
    @current_role ||= current_account.present? ? current_account.role.try(:name) : 'User'
  end

  def get_dashboard_link_details
    @dashboard_links ||= DASHBOARD_NAVIGATIONS      
  end

  def webp_picture_tag(pclip_object, options={})
    style_name = options.delete(:p_style)
    style_type = pclip_object.content_type || 'image/jpeg'
    if WEBP_CONFIGURATION['switch'] && style_name.present?
      sup_time = WEBP_CONFIGURATION[pclip_object.instance.class.to_s]
      if sup_time.nil? || (sup_time.is_a?(Time) && pclip_object.instance.created_at > sup_time)
        webp_style_name = pclip_object.styles.keys.find{|style| style == "#{style_name}_webp".to_sym}
      end
    end
    if options[:lazy].is_a?(Hash)
      lazy = options.delete(:lazy)
      lazy = {p_holder: asset_path('11.png'), class: 'lazy', source_attr: {src: 'data-original', srcset: 'data-srcset'}}.deep_merge!(lazy.to_h)
      options[lazy[:source_attr][:src]] = pclip_object.url(style_name)
      if webp_style_name.present?
        content_tag :picture, class: lazy[:class] do
          content_tag(:source, '', srcset: lazy[:p_holder], type: 'image/webp', lazy[:source_attr][:srcset] => pclip_object.url(webp_style_name)) +
          content_tag(:source, '', srcset: lazy[:p_holder], type: style_type, lazy[:source_attr][:srcset] => pclip_object.url(style_name)) +
          image_tag(lazy[:p_holder], options)
        end
      else
        options[:class] = [options[:class], lazy[:class]].compact.join(' ')
        image_tag(lazy[:p_holder],  options)
      end
    else
      if webp_style_name.present?
        content_tag :picture do
          content_tag(:source, '', srcset: pclip_object.url(webp_style_name), type: 'image/webp') +
          content_tag(:source, '', srcset: pclip_object.url(style_name), type: style_type) +
          image_tag(pclip_object.url(style_name), options)
        end
      else
        image_tag(pclip_object.url(style_name), options)
      end
    end
  end

  def get_design_price_or_range(design, effective_price)
    if Design::VARIANT_PRICE_RANGE_ENABLE && (variants = design.variants).present?
      max_price = variants.select{|v| v.quantity.to_i > 0}.max_by(&:price).try(:effective_price)
      if max_price.present? && effective_price != max_price
        return "#{get_price_in_currency_with_symbol(effective_price)} - #{get_price_in_currency_with_symbol(max_price)}"
      end
    end
    return get_price_in_currency_with_symbol(effective_price)
  end

  def show_wishlist?
    !current_account || current_account.user?
  end

  # Helper method to determine if hierarchical menu should be used
  def use_hierarchical_menu?
    # TEMPORARY: Force enable for testing
    result = true  # Change this back to the original logic later
    Rails.logger.debug "=== HIERARCHICAL MENU TOGGLE ==="
    Rails.logger.debug "use_hierarchical_menu?: #{result}"
    Rails.logger.debug "Rails.env.development?: #{Rails.env.development?}"
    Rails.logger.debug "params[:hierarchical_menu]: #{params[:hierarchical_menu]}"
    result
  end

  # Helper method to get menu display partial
  def menu_display_partial
    partial = use_hierarchical_menu? ? 'layouts/hierarchical_menu' : 'layouts/static_menu'
    Rails.logger.debug "Using menu partial: #{partial}"
    partial
  end

  def onlinesale_data_object(products, order = nil)
    objectParam = Hash.new(0)
    products = Array.wrap(products)
    is_design = products.first.is_a?(Design)
    objectParam[:products] = products.map do |product|
      design = is_design ? product : product.design
      object = {
        clientId: design[:designer_id],
        skuId: design[:id],
        productName: design[:title],
        productPrice: design[:price],
        currency: 'INR',
        category: design.categories.try(:first).try(:name) || 'others',
        imageUrl: design.master_image.try(:photo).try(:url)
      }
      unless is_design
        (price = product.try(:variant).try(:[],:price).try(:nonzero?).presence) && (object[:productPrice] = price)
        object[:quantity] = product.quantity
        objectParam[:totalAmount] = (objectParam[:totalAmount] + (object[:productPrice].to_f * object[:quantity].to_f)).round(2)
      end
      object
    end
    unless is_design
      objectParam[:currency] = 'INR'
      if order.is_a? Order
        objectParam[:paymentMethod] = order.pay_type
        objectParam[:orderId] = order.number
      end
    end
    objectParam
  end

  def self.include(base)
    puts "#{self} -> #{base}"
    include RatingHelper
  end
end