module Designers<PERSON>elper

  def get_return(return_count, sell_count)
        return_per = (((return_count*1.0)/sell_count)*100).round(2)
     return return_per
  end

  def get_payout_notes(date)
  	payout_notes = DesignerOrder.unscoped.select("designer_payout_notes").where(designer_id: @designer.id).where(designer_payout_status: "paid").where("designer_payout_date BETWEEN ? AND  ?", date.beginning_of_month, date.end_of_month)
  end

  def get_standard_images(type = :salwar_kameez)
    Design::HEIGHT_RANGE_FOR_STANDARD_ADDON[type].values.map do |img|
      image_tag "#{img}.jpg", id:"std-#{img}", style: 'display:none;', height: 200 #dont change order of attributes else js won't work
    end.join('')
  end

  def has_high_rate?(designer, rate_type:, consequence:)
    (@has_high_rate ||= {})["#{rate_type}-#{consequence}-#{designer.id}"] ||= designer.metric_values.where({
      generated_on: 1.day.ago.to_date,
      metric_definition_id: DESIGNER_METRIC_CONFIG[rate_type][consequence]["metric_definition_id"]
    }).last.try(:value).try(:>, DESIGNER_METRIC_CONFIG[rate_type][consequence]["thresholds"]["metric_value"])
  end

  def view_payout_invoice(commission_invoice)
    commission_invoice.commission >=0 ? 'Download Invoice' : 'Download Credit Note'
  end

  def invoice_type_class(commission_invoice)
    commission_invoice.commission >=0 ?  "info" : "danger"
  end
end
