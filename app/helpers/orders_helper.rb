require 'nokogiri'
require 'open-uri'

module OrdersHelper

  def qc_not_required(line_item)
    if line_item.design.sell_count == 0
      return false
    elsif (line_item.design.return_count >= 10 && (line_item.design.return_count*100)/line_item.design.sell_count <= 5)
      return true
    elsif (line_item.design.return_count < 10 && (line_item.design.return_count*100)/line_item.design.sell_count <= 1)
      return true
    end
    return false
  end

  def stitching_status(line_item)
    status = line_item.stitching_required == 'Y' ? "STITCHING REQUIRED" : ""
  end

  def get_shipment_status(d)
    tracking_url = d.tracking
    
    return "Shipment Delivered" if d.delivered_at.present? || d.completed?
    
    # Don't fetch tracking status every single time. We cache it for an hour
    if tracking_url.present? && tracking_url.starts_with?("http://")
      if Rails.cache.fetch(tracking_url).present?
        Rails.cache.fetch(tracking_url)
      else  
        Rails.cache.fetch(tracking_url, :expires_in => 1.hour) do
          begin
            doc = Nokogiri::HTML(open(tracking_url, 'User-Agent' => 'ruby'))
            delivered_at = doc.css('.ResultsTableCell1')[9].try(:text).try(:strip)
            if delivered_at.present? 
              d.delivered_at = DateTime.strptime(delivered_at, "%m/%d/%Y") 
              d.save!
            end
            doc.css('#ContentPlaceHolder1_lblCurrentStatus').children.text 
          rescue
            "Status Unknown"
          end  
        end  
      end
    else
      "Tracking # Missing"
    end      
  end

  def get_courier_invoice_url(shipper_name, number)
    if ['dhl', 'dhl_ecom', 'skynet', 'fedex', 'delhivery', 'atlantic', 'xindus', 'xpressbees_ups'].include?(shipper_name)
      courier_url = "#{root_url}orders/#{shipper_name}_invoice/#{number}"
    elsif ['ups','aramex'].include?(shipper_name)
      courier_url = shipper_name == 'ups' ? shipments_create_for_order_url(number: number, shipper: 'ups') : aramex_international_invoice_url(number)
    end
  end

  def get_orders_measurements_state_wise_count(all_mes)
    count = Hash.new(0)
    all_mes.group_by(&:state).each{|state, mes| (state == 'hold' ? count['processing'] += mes.size : count[state] = mes.size)}
    count
  end

  def get_orders_measurements_suggestions(all_mes)
    all_mes.to_a.collect do |sm|
      suggestions = (sm.suggested_measurements.presence || all_mes.find{|s| s.suggested_measurements['suggested_ids'].to_a.include?(sm.id)}.try(:suggested_measurements))
      [sm.id, suggestions] if suggestions
    end.compact.to_h
  end

  def check_if_bucket_scan_needed(item)
    ENABLE_UNPACKING_BUCKET_PROCESS == 'true' && ( @order.international? || item.designer_order.ship_to == 'mirraw') && (!get_bucket_response(item)[:error] && @bucket_check_res[:bucket_codes].present?)
  end

  def get_bucket_response(item)
    @bucket_check_res = item.do_warehouse_bucket_check(@order)
  end

  def luxe_order(order)
    order.line_items.any? {|line_item| line_item.designer.designer_type == "Tier 1 Designer"}
  end

  def trigger_ga4_purchase_event(order)
    return false unless ['followup', 'sane', 'new'].include?(order.state)
    return false if Rails.env.admin?
    date_time = if order.sane?
                  (order.confirmed_at + 2.minutes)
                elsif order.new? && order.cod? 
                  (order.created_at + 5.minutes)
                else
                  (order.created_at + 5.minutes)
                end
    return DateTime.now <= date_time              
  end

  def is_stitching_done?(order)
    ship_to_mirraw_dos = order.designer_orders.select do|dso|
      dso.ship_to == 'mirraw'
    end
    status_array = ship_to_mirraw_dos.flat_map do |dos|
      dos.line_items.map do |item|
        item.received== 'Y' && (item.stitching_required.nil? || (item.stitching_required == 'Y' && item.stitching_done == 'Y'))
      end
    end
    return status_array.include?(true)
  end


  def ready_for_deliver_condition(item_data)
    return true  if (item_data.stitching_required.nil?) || (item_data.stitching_required == 'Y' && item_data.stitching_done == 'Y')
  end

  def display_shipping_label?(order)
    (order.best_shipper.present? && order.best_shipper.downcase == 'atlantic' && order.shipments.without_designer_order.present?)
  end

  def get_flag_position(country_code)
    flag_positions = {'IN' => -2413,'US' => -5221,'CA' => -834,'UK' => -1775}
    flag_positions[country_code]
  end
end
