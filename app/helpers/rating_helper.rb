module RatingHelper

  def action_path_merge(params, key, value)
    params.select{|k,v| k.to_s[/controller|action/]}.merge({key => value})
  end

  def rating_activated?(args={})
    if current_account.present? && current_account.admin?
      return true
    end

    # Check global rating settings first
    return false if args.size == 0 && ApplicationHelper::RATING_DISABLE['global_on'].to_i != 0

    # Check specific controller/action/view settings
    begin
      return false if ApplicationHelper::RATING_DISABLE[args['controller']][args['action']][args['view']].to_i != 0
    rescue
      # If no specific setting found, continue with designer check
    end

    # Check if designer has reviews enabled (only for design-related pages)
    if args['controller'] == 'designs' && defined?(@designer) && @designer.present?
      return @designer.enable_reviews != false
    end

    true
  end
end