class SendMailsForStitchingMeasurementReminderJob
    include Sidekiq::Worker
    sidekiq_options retry: false, queue: :rake
    def perform
        Order.unscoped.joins(:line_items)
        .joins("LEFT OUTER JOIN stitching_measurements ON stitching_measurements.line_item_id = line_items.id AND stitching_measurements.measurement_group = 'stylist'")
        .select(orders: [:id, :billing_phone, :country_code, :number, :confirmed_at])
        .select("COUNT(DISTINCT stitching_measurements.id) as sm_count")
        .select("(SELECT SUM(CASE WHEN li.stitching_required = 'Y' THEN li.quantity ELSE 0 END) FROM line_items li INNER JOIN designer_orders dor ON li.designer_order_id = dor.id WHERE dor.order_id = orders.id) as li_count")
        .where.not(designer_orders: {state: ['canceled', 'vendor_canceled', 'buyer_returned', 'rto', 'dispatched']})
        .where(orders: {state: ['sane', 'partial_dispatch', 'ready_for_dispatch'], confirmed_at: 1.month.ago..6.hours.ago})
        .group('orders.id')
        .find_each(batch_size: 200) do |order|
            if order['li_count'].to_i > order['sm_count'].to_i && order.stitching_addon?(true, false)
                OrderMailer.sidekiq_delay.stitching_measurement(order.id, true)
            end
        end
    end
end
