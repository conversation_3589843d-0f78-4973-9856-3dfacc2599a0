class SendMailsForStitchingMeasurementReminderJob
    include Sidekiq::Worker
    sidekiq_options retry: false, queue: :rake
    def perform
        Order.unscoped.joins(:line_items).joins{stitching_measurements.outer}.select(orders: [:id,:billing_phone,:country_code,:number,:confirmed_at]).select("count(stitching_measurements.id) as sm_count, sum(case when line_items.stitching_required = 'Y' then line_items.quantity else 0 end)/count(line_items.id) as li_count").
        where("orders.other_details @> hstore(:key, :value)",key: 'stitching_order',value: 'true').where(line_items: {status: nil}).where.not(designer_orders: {state: ['cancel','vendor_canceled','buyer_returned','rto']}).where(orders: {state: 'sane', confirmed_at: 2.days.ago.beginning_of_day..2.days.ago.end_of_day}).group('orders.id').find_each(batch_size: 200) do |order|
            if order['li_count'].to_i > order['sm_count'].to_i && order.stitching_addon?(true, true)
                OrderMailer.sidekiq_delay.stitching_measurement(order.id)
                order.send_confirmation_sms_international('stitching_form')
            end
        end
    end
end
