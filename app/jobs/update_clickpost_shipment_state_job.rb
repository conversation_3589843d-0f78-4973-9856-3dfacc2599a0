class UpdateClickpostShipmentStateJob
    include Sidekiq::Worker
    sidekiq_options retry: false
    def perform
        shipments = Shipment.joins(:order,:shipper).where('orders.country=?','India').where('shipments.designer_order_id is NULL').where('shipments.shipment_state NOT IN (?)',['delivered']).where('shipments.created_at>=?', 20.day.ago)
        Shipment.sidekiq_delay(queue: 'default').clickpost_shipment_state_track(shipments.collect(&:number))
    end
end