class Ability
  include CanCan::Ability

  def initialize(account) 
    account ||= Account.new # guest user (not logged in)
      if account.role? :super_admin
        can :manage, :all
        can :access, :rails_admin
        can :dashboard, :all
      elsif account.role? :admin
        can :manage, :all
        # cannot :access, :rails_admin
        # cannot :dashboard, :all
      elsif account.role? :designer
        can :read, :all
        can :manage, Ticket
        cannot :read, VendorPromotion
        cannot :read, Order
        cannot :read, DynamicPrice
        cannot :read, FacebookPost
        cannot :read, FbPage
        can :create, Order
        cannot :read, Buyer
        cannot :read, DesignerOrder 
        cannot :read, Designer
        can :warehouse_order_csv_report, WarehouseOrder
        cannot :coupon_dashboard, Coupon
        can :manage, Design do |design|
          design.try(:designer).try(:account) == account
        end
        can :read, DynamicSizeChart
        can :manage, DynamicSizeChart do |size_chart|
          size_chart.new_record? || size_chart.try(:designer).try(:account) == account
        end
        can :manage, DesignerOrder do |designer_order|
          designer_order.try(:designer).try(:account) == account
        end 
        can :manage, DesignerCollection do |designer_collection|
          designer_collection.try(:designer).try(:account) == account
        end
        can :manage, Designer do |designer|
          designer.try(:account) == account
        end
        can :publish, Design do |design|
          design.try(:designer).try(:account) == account
        end
        cannot :publish, Designer
        cannot :rank, Designer
        # cannot :read, DelayedJobAdmin
        can :create, Coupon
        can :read, Coupon
        can :read, DesignerIssue do |di|
          di.designer.try(:account) == account
        end
        can :designer_report, Designer do |designer|
          designer.try(:account) == account
        end
        can :defer_pickup, Designer do |designer|
          designer.try(:account) == account
        end
      elsif account.role? :listing
        # Should include anything person uploading design should have
        can :manage, Designer
        can :manage, Design
        can :manage, VendorAddon
        can :manage, Saree
        can :manage, Jewellery
        can :manage, SalwarKameez
        can :manage, Bag
        can :manage, Other
        can :manage, Lehenga
        can :manage, AddonTypeValue
        can :manage, AddonType
        can :manage, Property
        can :manage, PropertyValue
        can :manage, AddonOptionType
        can :manage, AddonOptionValue
        can :manage, Variant
        can :manage, OptionType
        can :manage, OptionTypeValue
        can :manage, Image
        can :manage, DynamicSizeChart
        # can :create, DelayedJobAdmin
        cannot :read, DesignerOrder
        cannot :read, Order
        cannot :read, Admin
        cannot :read, User
        cannot :read, VendorPromotion
        can :duplicate_inventory, Admin
        can :add_designs_to_category, Admin

      elsif account.role? :remote_vendor_team
        alias_action :warehouse_orders, to: :read
        can :designer_reports, Admin
        can :designers, Admin
        can :designers_show, Admin
        can :manage_boutique, Designer
        can :oos_bestseller_designs, Designer
        can :vendor_performance, Admin
        can :read, Order
        can :read, DesignerOrder
        can :read, Account
        can :read, Designer
        can :manage, Designer
        can :manage, DynamicSizeChart
        can :manage, DynamicTemplate
        can :read, Design
        can :update, Design
        can :read, Image
        can :manage, Design do |design|
          design.try(:designer).try(:owner_id) == account.id
        end
        can :manage, DesignerOrder do |designer_order|
          designer_order.try(:designer).try(:owner_id) == account.id
        end
        can :manage, DesignerCollection do |designer_collection|
          designer_collection.try(:designer).try(:owner_id) == account.id
        end
        can :manage, Designer do |designer|
          designer.try(:owner_id) == account.id
        end

      elsif account.role? :vendor_team
        can :access, :rails_admin
        can :dashboard, :all
        can :read, Account
        can :manage, DynamicLandingPage
        # can :update, Account
        can :manage, TagSlider
        can :read, Designer
        can :manage, Designer
        can :manage, VendorAddon
        can :manage, DynamicSizeChart
        can :manage, DynamicTemplate
        can :manage, Coupon
        can :manage,FilterNotifierScheduler
        can :manage,FilterGroup
        can :read, Order
        can :read, DesignerOrder
        can :read, Design
        can :new, Design
        can :create, Design
        can :update, Design
        can :read, Image
        can :update, Image
        can :read, SellerFollowup
        can :read, DesignerBatch
        can :manage, Board
        can :manage, Announcement
        can :manage, Category
        can :manage, CategoryAlbum
        can :manage, CategoryBanner
        can :manage, CategoryConnection
        can :manage, CategoryNewArrival
        can :manage, DesignsVideoListing
        can :manage, Landing
        can :manage, NewsletterBanner
        can :manage, Property
        can :manage, PropertyValue
        can :manage, Tab
        can :manage, TagSlider
        can :manage, StoryCollection
        # can :export, SellerFollowup
        can :designer_reports, Admin
        can :designs_under_review, Admin
        can :add_designs_to_collection, Admin
        can :add_designs_to_collection_create, Admin
        can :enable_design_video, Admin
        can :add_designs_to_featured_products, Admin
        can :order_detail, Order
        can :manage_boutique, Designer
        can :oos_bestseller_designs, Designer
        can :designers_show, Admin
        can :designers, Admin
        can :vendor_performance, Admin
        can :catalog_update, Admin
        can :manage, VendorPromotion
        can :review_designer_batches, Admin
        can :download_bulk_edit, Admin
        can :view_for_bulk_edit, Admin
        can :duplicate_inventory, Admin
        can :schedule_for_dedup, Admin
        can :add_designs_to_category, Admin
        can :manage, Return
        can :add_notes, Order
        if account.has_readonly_return_access?
          can :show, Return
          can :order_returns, Return
        end
        if ['<EMAIL>','<EMAIL>','<EMAIL>'].include?(account.try(:email))
          can :manage, BannerSlider
          can :manage, Frontpage
          can :manage, Block
          can :manage, Board
          can :manage, NewsletterBanner
          can :manage, Tab
        end


      elsif account.role? :senior_vendor_team
        alias_action :warehouse_orders, to: :read
        can :access, :rails_admin
        can :dashboard, :all
        can :read, Account
        can :manage, DynamicLandingPage
        # can :update, Account
        can :read, Designer
        can :manage, Designer
        can :manage, VendorAddon
        can :manage, Coupon
        can :manage, DynamicSizeChart
        can :manage, DynamicTemplate
        can :manage,FilterNotifierScheduler
        can :manage,FilterGroup
        can :read, Order
        can :read, DesignerOrder
        can :manage, Return
        if account.is_super_senior_vendor_team?
          can :manage, DesignerOrder
        end
        can :read, Design
        can :new, Design
        can :create, Design
        can :update, Design
        can :read, Image
        can :update, Image
        can :read, SellerFollowup
        can :read, DesignerBatch
        can :manage, Board
        can :manage, Announcement
        can :manage, Category
        can :manage, CategoryAlbum
        can :manage, CategoryBanner
        can :manage, Block
        can :manage, Frontpage
        can :manage, CategoryConnection
        can :manage, CategoryNewArrival
        can :manage, DesignsVideoListing
        can :manage, Landing
        can :manage, NewsletterBanner
        can :manage, Property
        can :manage, PropertyValue
        can :manage, Tab
        can :manage, TagSlider
        can :manage, StoryCollection
        # can :export, SellerFollowup
        can :designer_reports, Admin
        can :designs_under_review, Admin
        can :add_designs_to_collection, Admin
        can :add_designs_to_collection_create, Admin
        can :enable_design_video, Admin
        can :add_designs_to_category, Admin
        can :add_designs_to_featured_products, Admin
        can :order_detail, Order
        can :manage_boutique, Designer
        can :oos_bestseller_designs, Designer
        can :category_stats, Admin
        can :category_subreports, Admin
        can :designers, Admin
        can :designers_show, Admin
        can :designers_edit, Admin
        can :designers_update, Admin
        can :manage, BannerSlider
        can :manage, Frontpage
        can :vendor_performance, Admin
        can :catalog_update, Admin
        can :manage, VendorPromotion
        can :review_designer_batches, Admin
        can :download_bulk_edit, Admin
        can :view_for_bulk_edit, Admin
        can :duplicate_inventory, Admin
        can :schedule_for_dedup, Admin
        can :manage, WarehouseOrder
        can :manage, TableauReport
        can :manage, PackageManagement
        can :manage, Ticket
        can :add_notes, Order

      elsif account.role? :senior_marketing
        # TODO: permissions to be implemented once the permission sheet get ready.
        can :access, :rails_admin
        can :dashboard, :all
        can :read, Order
        #can :export, Order
        can :read, Designer
        can :read, Design
        can :update, Design
        can :manage, Coupon
        can :manage, FacebookPost
        can :manage, BannerSlider
        can :manage, CurrencyConvert
        can :manage, DynamicSizeChart
        can :manage, DynamicTemplate
        can :manage, FilterNotifierScheduler
        can :manage, FilterGroup
        can :manage, Block
        can :manage, Board
        can :manage, CategoryBanner
        can :manage, NewsletterBanner
        can :manage, CategoryConnection
        can :manage, FashionUpdate
        can :manage, DynamicLandingPage
        can :manage, Landing
        can :manage, FbPage
        can :manage, CategoryAlbum
        can :read, LineItem
        can :read, Category
        can :update, Category
        can :manage, Country
        can :manage, Frontpage
        can :manage, TagSlider
        can :manage, StoryCollection
        can :manage, AutomatedNotificationAudience
        can :manage, Collection
        can :manage, OfferPanel
        can :stats_calculations, Admin
        can :stats, Admin
        can :stats_summary1, Admin
        can :campaign_stats, Admin
        can :manage, PromotionPipeLine
        can :manage, DynamicPrice
        can :category_stats, Admin
        can :category_subreports, Admin
        can :upload_newsletter_image, Admin
        can :upload_newsletter_image_create, Admin
        can :read, AddonTypeValue
        can :update, AddonTypeValue
        can :manage, NavBlock
        can :manage, NavTab
        can :manage, Widget
        can :localytics_push, Admin
        can :push_data_to_localytics, Admin
        cannot :read, VendorPromotion
        can :duplicate_inventory, Admin
        can :add_designs_to_collection, Admin
        can :add_designs_to_collection_create, Admin
        can :enable_design_video, Admin
        can :account_health, Designer
        can :historical_data, Designer
        can :feedback, Designer
        can :all_feedback, Designer
        can :review_report, Designer
        #can :order_quality_report, Designer
        can :designer_report, Admin
        can :manage, Tab
        can :manage, PaymentOption

      elsif account.role? :marketing
        # TODO: permissions to be implemented once the permission sheet get ready.
        can :access, :rails_admin
        can :dashboard, :all
        # can :read, Order
        # can :export, Order
        can :read, Designer
        can :read, Design
        can :read, FbPage
        can :manage, FbPage, admin: { account: {id: account.id}}
        can :update, Design
        can :read, Coupon
        can :read, LineItem
        can :manage, FacebookPost
        can :manage, BannerSlider
        can :manage, DynamicSizeChart
        can :manage, CurrencyConvert
        can :manage, Block
        can :manage, Board
        can :manage, CategoryBanner
        can :manage, NewsletterBanner
        can :manage, CategoryConnection
        can :manage, DynamicLandingPage
        can :manage, Landing
        can :manage, Category
        can :manage, Country
        can :manage, Frontpage
        can :manage, TagSlider
        can :manage, StoryCollection
        can :manage, AutomatedNotificationAudience
        can :manage, Collection
        can :manage, CategoryAlbum
        can :manage, OfferPanel
        can :stats_calculations, Admin
        can :stats, Admin
        can :stats_summary1, Admin
        can :campaign_stats, Admin
        can :manage, PromotionPipeLine
        can :manage, DynamicPrice
        can :category_stats, Admin
        can :category_subreports, Admin
        can :manage, NavBlock
        can :manage, NavTab
        can :manage, Widget
        can :localytics_push, Admin
        can :push_data_to_localytics, Admin
        cannot :read, VendorPromotion
        can :duplicate_inventory, Admin
        can :add_designs_to_collection, Admin
        can :add_designs_to_collection_create, Admin
        can :enable_design_video, Admin
        can :account_health, Designer
        can :historical_data, Designer
        can :feedback, Designer
        can :all_feedback, Designer
        can :review_report, Designer
        #can :order_quality_report, Designer
        can :designer_report, Admin
        can :manage, Tab
        can :manage, PaymentOption

      elsif account.role? :junior_marketing
        can :access, :rails_admin
        can :dashboard, :all
        can :read, Order
        can :manage, BannerSlider
        can :manage, Block
        can :manage, Board
        can :manage, CategoryBanner
        can :manage, NewsletterBanner
        can :manage, CategoryConnection
        can :manage, Landing
        can :manage, Frontpage
        can :manage, TagSlider
        can :manage, StoryCollection
        can :manage, AutomatedNotificationAudience
        can :manage, FashionUpdate
        can :duplicate_inventory, Admin
        can :manage, Tab
        can :manage, DynamicLandingPage
        can :manage, Coupon

      elsif account.role? :business_analyst
        can :access, :rails_admin
        can :dashboard, :all
        can :read, Order
        can :read, Designer
        can :read, Design
        can :read, FbPage
        can :manage, FbPage, admin: { account: {id: account.id}}
        can :update, Design
        can :read, Coupon
        can :read, LineItem
        can :manage, FacebookPost
        can :manage, BannerSlider
        can :manage, DynamicSizeChart
        can :manage, CurrencyConvert
        can :manage, Block
        can :manage, Board
        can :manage, RackAudit
        can :manage, CategoryBanner
        can :manage, NewsletterBanner
        can :manage, CategoryConnection
        can :manage, DynamicLandingPage
        can :manage, Landing
        can :manage, Category
        can :manage, Country
        can :manage, Frontpage
        can :manage, TagSlider
        can :manage, StoryCollection
        can :manage, AutomatedNotificationAudience
        can :manage, Collection
        can :manage, CategoryAlbum
        can :manage, OfferPanel
        can :stats_calculations, Admin
        can :stats, Admin
        can :stats_summary1, Admin
        can :campaign_stats, Admin
        can :manage, PromotionPipeLine
        can :manage, DynamicPrice
        can :category_stats, Admin
        can :category_subreports, Admin
        can :manage, NavBlock
        can :manage, NavTab
        can :manage, Widget
        can :localytics_push, Admin
        can :push_data_to_localytics, Admin
        can :order_detail, Order
        cannot :read, VendorPromotion
        can :duplicate_inventory, Admin
        can :manage, GiftCard
        can :ready_for_dispatch, Admin
        can :partial_dispatch_orders, Admin
        can :manage, TableauReport
        can :tableau_page, TableauReportsController
        can :manage, WarehouseOrder
        can :manage, Tab
        can :manage, DesignerOwner

      elsif account.role? :senior_seo
        can :access, :rails_admin
        can :dashboard, :all
        can :manage, FashionUpdate
        can :read, Designer
        can :read, Category
        can :update, Category
        can :manage, SeoList
        can :manage, SitemapItem
        can :manage, Menu
        can :manage, MenuColumn
        can :manage, MenuItem
        can :manage, PopularLink
        can :manage, FAQ
        can :manage, Horoscope
        can :manage, RedirectRule
        can :add_designs_to_category, Admin
        cannot :read, VendorPromotion

      elsif account.role? :seo
        can :access, :rails_admin
        can :dashboard, :all
        can :read, Category
        can :read, SeoList
        can :manage, SeoList
        can :manage, LuxeSeoList
        can :read, FAQ
        can :read, PopularLink
        cannot :read, VendorPromotion
        
      elsif account.role? :luxe_seo
        can :access, :rails_admin
        can :dashboard, :all
        can :manage, LuxeSeoList
        can :read, Category

    elsif account.role? :senior_development
        # TODO: permissions to be implemented once the permission sheet get ready.
        can :access, :rails_admin
        can :dashboard, :all
        can :read, Order
        can :manage, DesignerOrder
        can :read, LineItem
        can :manage, CurrencyConvert
        can :read, Design
        can :manage, Feed
        can :manage, PublicSystemConstant
        # can :export, Order
        can :update, Design
        can :read, Designer
        can :read, Category
        can :update, Category
        can :manage, BannerSlider
        can :manage, Block
        can :manage, Board
        can :manage, CategoryBanner
        can :manage, NewsletterBanner
        can :manage, CategoryConnection
        can :manage, Landing
        can :manage, Frontpage
        can :manage, TagSlider
        can :manage, StoryCollection
        can :manage, AutomatedNotificationAudience
        can :manage, FashionUpdate
        can :order_detail, Order
        can :manage, TableauReport


      elsif account.role? :development
        # TODO: permissions to be implemented once the permission sheet get ready.
        # can :access, :rails_admin
        # can :dashboard, :all
        can :read, Order
        can :manage, DesignerOrder
        can :read, LineItem
        can :manage, CurrencyConvert
        can :read, Design
        can :manage, Feed
        can :manage, PublicSystemConstant
        # can :export, Order
        can :update, Design
        can :read, Designer
        can :read, Category
        can :update, Category
        can :manage, BannerSlider
        can :manage, Block
        can :manage, Board
        can :manage, CategoryBanner
        can :manage, NewsletterBanner
        can :manage, CategoryConnection
        can :manage, Landing
        can :manage, Frontpage
        can :manage, TagSlider
        can :manage, StoryCollection
        can :manage, AutomatedNotificationAudience
        can :manage, FashionUpdate
        can :order_detail, Order


      elsif account.role? :stitching
        can :manage, Order
        can :read, DesignerOrder
        can :update, DesignerOrder
        can :read, LineItem
        can :update, LineItem
        can :read, Design
        can :read, Designer
        can :manage, Ticket
        can :manage, TailoringInfo
        can :create_issue_line_item,Admin
        can :manage, PackageManagement
        cannot :read, VendorPromotion
        can :send_email, Admin

      elsif account.role? :stylist
        can :manage, Order
        can :read, DesignerOrder
        can :add_designs_to_collection, Admin
        can :add_designs_to_collection_create, Admin
        can :enable_design_video, Admin
        can :update, DesignerOrder
        can :read, LineItem
        can :update, LineItem
        can :read, Design
        can :read, Designer
        can :manage, Ticket
        can :manage, TailoringInfo
        cannot :add_tailoring_measurement, TailoringInfo
        can :create_issue_line_item,Admin
        can :send_email, Admin
        can :manage, PackageManagement
        can :manage, StitchingMeasurement
        can :manage, Stylist
        can :addons_for_order, Admin if account.is_super_stylist_team?
        cannot :read, VendorPromotion

      elsif account.role? :categorystylist
        can :manage, Order
        can :read, DesignerOrder
        can :update, DesignerOrder
        can :read, LineItem
        can :update, LineItem
        can :read, Design
        can :read, Designer
        can :manage, Ticket
        can :manage, TailoringInfo
        can :create_issue_line_item,Admin
        can :manage, PackageManagement
        can :manage, StitchingMeasurement
        can :manage, Stylist
        cannot :read, VendorPromotion
        can :add_designs_to_collection, Admin
        can :add_designs_to_collection_create, Admin
        can :enable_design_video, Admin

      elsif account.role? :dispatch
        #can :access, :rails_admin
        #can :dashboard, :all
        can :read, Order
        can :manage, Order
        can :read, DesignerOrder
        can :manage, DesignerOrder
        can :manage, Pickup
        can :read, Design
        can :read, Designer
        can :manage, RackAudit
        can :ready_for_dispatch, Admin
        can :partial_dispatch_orders, Admin
        can :create_issue_line_item,Admin
        can :manage, RackList
        can :manage, PackageManagement
        cannot :mark_stitching_required, PackageManagement
        cannot :remove_stitching_required, PackageManagement
        cannot :read, VendorPromotion
        can [:received_tailoring_checking, :send_for_tailoring, :add_tailoring_measurement, :fnp_bulk_assign], TailoringInfo
        can :manage, WarehouseOrder
        can :tailoring_info_page, Admin

      elsif account.role? :operations
        can :access, :rails_admin
        can :dashboard, :all
        can :read, Order
        can :manage, Order
        can :read, DesignerOrder
        can :manage, DesignerOrder
        can :read, Adjustment
        can :read, DesignerShipper
        can :update, DesignerShipper
        can :create, DesignerShipper
        can :read, Coupon
        can :manage, Courier
        can :manage, RackAudit
        can :manage, DesignerIssue
        can :manage, Shipment
        can :manage, Shipper
        can :manage, Pickup
        can :read, Design
        can :manage, Ticket
        can :manage, Return
        can :read, Designer
        can :rtv_pending, Admin
        can :pending_orders, Admin
        can :dispatched_from_vendors, PackageManagementController
        can :not_unpacked_orders, PackageManagementController
        can :report, PackageManagementController
        can :pending_quality_check, PackageManagementController
        can :mark_stitching_done, PackageManagementController
        can :ready_for_dispatch, Admin
        can :partial_dispatch_orders, Admin
        can :designer_reports, Admin
        can :sent_stitching_list, Admin
        can :stitching_items, Admin
        can :oos, Admin
        can :tailoring_info_page, Admin
        can :view_claim_requests, Admin
        can :create_claim_design, Admin
        can :create_issue_line_item,Admin
        can :stitching_done_orders, Admin
        can :stitching_pending_orders, Admin
        can :best_zone_stitching_call, Order
        can :mark_ready_for_dispatch, Admin
        can :create_designer_issue, Admin
        can :manage, OptionalPickup
        can :manage, RackList
        can :manage, PackageManagement
        can :manage, WarehouseOrder
        cannot :read, VendorPromotion
        can :send_email, Admin
        can :duplicate_inventory, Admin
        can :manage, TailoringInfo
        can :manage_boutique, Designer
        can :oos_bestseller_designs, Designer
        can :invalid_address, Admin
        can :vendor_performance, Admin
        can :manage, TableauReport
        can :account_health, Designer
        if account.has_lane_access?
          can :manage, Lane
        end
        can :manage, Blacklist

      elsif account.role? :sales
        #can :access, :rails_admin
        #can :dashboard, :all
        if account.is_super_sales_team?
          can :new_case, Return
          can :create, Return
          can :show, Return
          can :index, Return
          can :order_returns, Return
          can :get_account_no_length, Return
          can :update, Return
          can :update_return_state, Return
          can :upload_return_sheet, Return
          can :paypal_automated_returns, Return
          can :send_return_mail, Return
          can :extend_coupon, Return
          can :get_line_items_to_return, Return
          can :transfer_to_wallet, Return
          can :user_wallet, Return
          can :remove_line_item, Return
        end
        can :read, Order
        can :manage, Order
        can :read, Cart
        can :read, Coupon
        can :read, CurrencyConvert
        can :read, Design
        can :read, Designer
        can :canceled, Admin
        can :tags_stats, Admin
        can :manage, Ticket
        can :sales_pending, Admin
        can :bulk_product_list_new, Admin
        can :bulk_product_list_create, Admin
        can :send_email, Admin
        cannot :read, VendorPromotion
        can :add_designs_to_collection, Admin
        can :add_designs_to_collection_create, Admin
        can :enable_design_video, Admin


      elsif account.role? :support
        if account.is_super_support_team?
          can :access, :rails_admin
          can :dashboard, :all
          can :manage, SupportText
          can :manage, HelpCenterHeader
          can :manage, Order
          can :manage, Return
          can :manage, Ticket
          can :manage, Coupon
        end
        can :read, Order
        can :order_detail, Order
        can :read, Coupon
        can :update, Coupon
        can :read, CurrencyConvert
        can :read, Design
        can :read, Designer
        can :canceled, Admin
        can :tags_stats, Admin
        can :sales_pending, Admin
        can :new_case, Return
        can :create, Return
        can :show, Return
        can :index, Return
        can :order_returns, Return
        can :create_designer_issue, Admin
        can :create_coupon_order, Admin
        can :get_account_no_length, Return
        can :update, Return
        can :update_return_state, Return
        can :upload_return_sheet, Return
        can :paypal_automated_returns, Return
        can :send_return_mail, Return
        can :extend_coupon, Return
        can :get_line_items_to_return, Return
        can :transfer_to_wallet, Return
        can :user_wallet, Return
        can :remove_line_item, Return
        can :manage, DiscountLineItem
        cannot :read, VendorPromotion
        can :mark_stitching_required, PackageManagement
        can :remove_stitching_required, PackageManagement
        can :send_email, Admin
        can :addons_for_order, Admin
        can :add_addon_from_admin, Admin
        can :coupon_dashboard, Coupon
        can :invalid_address, Admin
        can :show, DesignerOrder

      elsif account.role? :outsourced_support
        can :read, Order
        can :order_detail, Order
        can :read, Coupon
        can :update, Coupon
        can :read, CurrencyConvert
        can :read, Design
        can :read, Designer
        can :canceled, Admin
        can :tags_stats, Admin
        can :sales_pending, Admin
        can :new_case, Return
        can :create, Return
        can :show, Return
        can :index, Return
        can :order_returns, Return
        can :create_designer_issue, Admin
        can :create_coupon_order, Admin
        can :get_account_no_length, Return
        can :update, Return
        cannot :update_return_state, Return
        can :upload_return_sheet, Return
        can :paypal_automated_returns, Return
        can :send_return_mail, Return
        can :extend_coupon, Return
        can :get_line_items_to_return, Return
        can :transfer_to_wallet, Return
        can :user_wallet, Return
        can :remove_line_item, Return
        can :manage, DiscountLineItem
        cannot :read, VendorPromotion
        can :mark_stitching_required, PackageManagement
        can :remove_stitching_required, PackageManagement
        can :send_email, Admin
        can :addons_for_order, Admin
        can :add_addon_from_admin, Admin
        can :coupon_dashboard, Coupon
        can :invalid_address, Admin
        can :show, DesignerOrder
        can :manage, Ticket
        can :send_email, Admin
        can :add_notes, Order

      elsif account.role? :category         
        # TODO: permissions to be implemented once the permission sheet get ready.
      elsif account.role? :accounts_admin
        can :manage, :all
        cannot :access, :rails_admin
        cannot :dashboard, :all
        can :manage, VendorPromotion
        can :manage, ShipperFuelPrice
        can :manage, ShipmentCharge
        can :calculate_best_shipper, Shipment
        can :designers, Admin
        can :designers_show, Admin
        can :designers_edit, Admin
        can :designers_update, Designer
      elsif account.role? :accounts
        can :manage, :all
        can :designer_invoices, Admin
        can :read, Designer
        can :manage, DynamicTemplate
        can :manage,FilterNotifierScheduler
        can :manage,FilterGroup
        can :manage, Ticket
        can :export_orders, DesignerOrder
        can :manage, VendorPromotion
        cannot :access, :rails_admin
        cannot :dashboard, :all
      else
        can :read, :all
        cannot :read, FbPage
        cannot :read, VendorPromotion
        cannot :read, DynamicSizeChart
        cannot :read, Buyer   
        cannot :read, Order
        cannot :read, Design
        cannot :read, DesignerOrder
        cannot :read, FacebookPost
        cannot :read, DynamicLandingPage
        cannot :read, Ticket
        cannot :rank, Designer
        can :create, Order
        can :show, Order
        can :read, Design
        can :autopost, Order
        can :res, Order
        can :read, Design, :published => true
        # cannot :read, DelayedJobAdmin
        if account.role? :user
            cannot :read, User
            can :manage, User do |user|
                user.try(:account) == account
            end
            can :manage, Return
            cannot :index, Return
            cannot :new_case, Return
            cannot :coupon_dashboard, Coupon
            can :manage, Wishlist do |wishlist|
              wishlist.user.try(:account) == account
            end
            can :create, Wishlist
        end
      end
      if account.accountable_type == 'Admin'
        cannot :history, :all
        can :dashboard, DesignerOwner do |designer_owner|
            designer_owner.id == account.accountable_id ||
            account.role?(:super_admin) ||
            account.is_exclusive_senior_vendor_team?
        end
        can :manage, DesignerOrderPanelsController
      end

    # Define abilities for the passed in user here. For example:
    #
    #   user ||= User.new # guest user (not logged in)
    #   if user.admin?
    #     can :manage, :all
    #   else
    #     can :read, :all
    #   end
    #
    # The first argument to `can` is the action you are giving the user permission to do.
    # If you pass :manage it will apply to every action. Other common actions here are
    # :read, :create, :update and :destroy.
    #
    # The second argument is the resource the user can perform the action on. If you pass
    # :all it will apply to every resource. Otherwise pass a Ruby class of the resource.
    #
    # The third argument is an optional hash of conditions to further filter the objects.
    # For example, here the user can only update published articles.
    #
    #   can :update, Article, :published => true
    #
    # See the wiki for details: https://github.com/ryanb/cancan/wiki/Defining-Abilities
  end
end
