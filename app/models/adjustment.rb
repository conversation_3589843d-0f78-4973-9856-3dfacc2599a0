class Adjustment < ActiveRecord::Base
  belongs_to :designer
  belongs_to :order
  belongs_to :designer_order
  belongs_to :payment_order, -> {select('orders.id,orders.country,orders.buyer_state,orders.number,orders.created_at,orders.name,orders.confirmed_at,orders.actual_country_code')}, class_name: 'Order', foreign_key: 'order_id'
  attr_accessible :amount, :notes, :status, :designer_id, :order_id, :order, :designer_order_id
  belongs_to :return_designer_order
  extend DynamicTemplateMethod
  dynamic_template_fields :amount, :status, :created_at, :updated_at, :payout_date

  def self.generate_adjustments_report(ids = [], payout_type, email)
    irn_alert_email_array = ['<EMAIL>','<EMAIL>', DEPARTMENT_HEAD_EMAILS['accounts']]
    irn_error_hash, irn_request = '',''
    adj_type = payout_type == 'Domestic' ? 'dom': 'int'
    igst,sgst,cgst = (IGST.to_f/100), (SGST.to_f/100), (CGST.to_f/100)
    invoice_ids,invoice_date = [], Date.yesterday.strftime('%d/%m/%Y')
    vendor_adjustments = Adjustment.joins(order: :designer_orders).where('designer_orders.designer_id = adjustments.designer_id').where(id: ids).select('adjustments.designer_id,array_agg(distinct adjustments.id) as adj_id').where("amount is not null and adjustments.return_designer_order_id is null and (adjustments.notes like 'RTV Cost%' OR adjustments.notes like 'OOS Cost%' OR lower(adjustments.notes) like 'shipping charge%' OR lower(adjustments.notes) like 'shipping cost%' OR adjustments.notes like 'Critical Cost%' OR adjustments.notes like 'QC Failed Cost%' OR lower(adjustments.notes) like '%reverse%' OR adjustments.notes like '%photoshoot%') and lower(adjustments.notes) not like '%gst%' and lower(adjustments.notes) not like '%refund for%' and lower(adjustments.notes) not like '%buyer return for%'").group(:designer_id)
    vendor_adjustments.each_slice(100) do |adj|
      adjustments = Adjustment.select('*,amount * -1 as amount,lower(notes) as notes').where(id: adj.collect(&:adj_id).flatten.uniq).preload(:payment_order,:designer).group_by(&:designer_id)
      adjustments.each do |designer_id,adjs|
        total_adjustment = {rtv_cost: 0, oos_cost: 0, ship_cost: 0, qc_cost: 0, critical_cost: 0, other: 0, photoshoot_cost: 0}
        filepath = '/tmp/int_adjustment_report.csv'
        CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
          csv << ['Adjustment Mirraw Id','Invoice Date','Adjustment Creation Date','Reference Number','Order Confirmation Date','Taxable Value Total','GST Amount Total','Total Invoice Value','Notes','Adjustment Paid status']
          adjs.each do |adj|
            if adj.notes.include?('rtv cost')
              total_adjustment[:rtv_cost] += adj.amount
            elsif adj.notes.include?('oos cost')
              total_adjustment[:oos_cost] += adj.amount
            elsif adj.notes.include?('shipping charge') || adj.notes.include?('shipping cost')
              total_adjustment[:ship_cost] += adj.amount
            elsif adj.notes.include?('critical cost')
              total_adjustment[:critical_cost] += adj.amount
            elsif adj.notes.include?('qc failed cost')
              total_adjustment[:qc_cost] += adj.amount
            elsif adj.notes.include?('reverse')
              total_adjustment[:other] += adj.amount
            elsif adj.notes.include?('photoshoot')
              total_adjustment[:photoshoot_cost] += adj.amount  
            end
            gst = (adj.amount/(1+igst)).round(2)
            csv << [adj.id,invoice_date,adj.created_at.strftime('%d/%m/%Y'),adj.payment_order.number,adj.payment_order.confirmed_at.try(:strftime,'%d/%m/%Y'),gst,(adj.amount - gst).round(2),adj.amount,adj.notes,adj.status]
          end
        end
        adjustment_file = payout_type.to_s + 'AdjustmentReport/' + designer_id.to_s + '/' + Date.today.strftime('%b%Y').to_s + 'adjustment.csv'
        AwsOperations.create_aws_file(adjustment_file, File.read(filepath), false)
        download_url = AwsOperations.get_aws_file_path(adjustment_file)
        total = total_adjustment.values.compact.sum
        des = adjs.first.designer
        if (des.business_state || des.state).try(:upcase) == SHIPPER_STATE.upcase
          taxable_value = (total/(1 + cgst + sgst)).round(2)
          cgst_tax, sgst_tax, igst_tax = (taxable_value * cgst).round(2), (taxable_value * sgst).round(2), 0
        else
          taxable_value = (total/(1 + igst)).round(2)
          cgst_tax, sgst_tax, igst_tax = 0, 0, (taxable_value * igst).round(2)
        end
        filter_hash = {designer_id: designer_id, total: total, taxable_value: taxable_value, igst: igst_tax, cgst: cgst_tax, sgst: sgst_tax, adjustment_report_url: download_url, report_type: adj_type}
        return if AdjustmentReport.where(filter_hash).present?
        adjustment_invoice = AdjustmentReport.create(filter_hash)
        invoice_ids << adjustment_invoice.id
        total_adjustment.each {|key,val| total_adjustment[key] = (val/(1+igst)).round(2)}
        irn_number, irn_barcode, error_msg, items = nil, nil, nil, []
        if total > 0
          year = Time.current.strftime("%y")
          year = %w(01 02 03).include?(Time.current.strftime("%m")) ? "#{year.to_i-1}" : "#{year.to_i}"
          inv_number = "A#{designer_id}/#{year}/#{adjustment_invoice.id}"
          filename  = designer_id.to_s + "_" + inv_number + ".pdf"
          if DISABLE_ADMIN_FUCTIONALITY['irn_feature'] && des.try(:gst_no).present?
            items =[{:name=>"Other Services", :quantity=>1, :total_price=> taxable_value.round(2), :igst=> igst_tax.round(2), :cgst=> cgst_tax.round(2), :sgst=> sgst_tax.round(2), :hsn_code=>"999799", :gst_rate=>18.00}]
            begin
              irn_request  += "Adjustment Report ID : #{adjustment_invoice.id}<br>Designer: #{des.id}<br>Items: #{items}<br><br>"
              generate_irn = GenerateIrnNumber.new(Order.new, items, adjustment_invoice, 1.0, 'INR', des, inv_number)
              response = generate_irn.generate_forward_irn
              if response[:error] == false
                irn_number, irn_barcode = response[:irn_number], response[:irn_barcode]
                adjustment_invoice.update_columns(irn_number: irn_number, irn_barcode: irn_barcode)
              else
                error_msg  = response[:error_msg]
              end
            rescue => e
              error_msg = "#{e.message} ===> #{e.backtrace}"
            end
            irn_error_hash += "Adjustment Report ID: #{adjustment_invoice.id}<br>Invoice Number: #{inv_number}<br>Items: #{items}<br>Reason: #{error_msg}<br><br>" if error_msg.present?
          end
          pdf_content =  ActionController::Base.new().render_to_string(
            :template => 'designers/adjustment_invoice.html.haml',
            :layout   => false ,
            :locals   => {designer: des, adjustment_invoice: adjustment_invoice, adjustment_values: total_adjustment, inv_number: inv_number, inv_date: invoice_date, irn_number: irn_number, irn_barcode: irn_barcode}
          )
          payout_invoice = WickedPdf.new.pdf_from_string(pdf_content)
          AwsOperations.create_aws_file(filename, payout_invoice, false)
          download_url = AwsOperations.get_aws_file_path(filename)
          adjustment_invoice.adjustment_invoice_url = download_url
          adjustment_invoice.invoice_number         = inv_number
          adjustment_invoice.save
        end
      end
    end
    OrderMailer.report_mailer("Adjustments IRN Request #{Date.today.strftime('%b%Y')}","<br>#{irn_request}",{'to_email'=> irn_alert_email_array.first,'from_email_with_name'=>'<EMAIL>'},{}).deliver
    OrderMailer.report_mailer("Error While Creating Adjustments IRN Invoice","Failed to generate IRN number due to below reason <br>#{irn_error_hash}",{'to_email'=> irn_alert_email_array,'from_email_with_name'=>'<EMAIL>'},{}).deliver if irn_error_hash.present? 
    AdjustmentReport.sidekiq_delay_until(15.minutes.from_now, queue: 'low')
                    .mail_invoice_and_report_links(
                      invoice_ids,
                      irn_alert_email_array.third
                    )
  end
end