class BoostedDesign < ActiveRecord::Base
  belongs_to :design
  belongs_to :designer
  belongs_to :category
  
  validates :design_id, presence: true
  validates :designer_id, presence: true
  validates :boost_fee, numericality: { greater_than_or_equal_to: 0 }

  validate :no_overlapping_boosts

  before_create :set_boost_time

  scope :active, -> {
    where(state: 'active')
    .where("boost_end_time >= ?", Time.current)
  }

  # Upcomming boost with in next day
  scope :upcoming_day, -> {
    where(state: 'scheduled')
      .where(boost_start_time: Time.current.change(hour: 12, min: 30)..Time.current.tomorrow.change(hour: 13, min: 0))
  }

  scope :scheduled, -> {
    where(state: 'scheduled')
  }

  scope :error, -> {
    where(state: 'error')
  }
  
  scope :within_period, ->(start_time, end_time) {
    where("(boost_start_time >= ? AND boost_start_time < ?) OR
           (boost_end_time > ? AND boost_end_time <= ?) OR
           (boost_start_time <= ? AND boost_end_time >= ?)",
          start_time, end_time,  
          start_time, end_time,  
          start_time, end_time) 
  }

  state_machine :state, initial: :scheduled do
    event :activate do
      transition scheduled: :active
    end
  
    event :expire do
      transition active: :expired
    end
  
    event :cancel do
      transition scheduled: :canceled
    end

    event :boost_error do
      transition any => :error
    end
  end
  
  def set_boost_time
    duration = (designer && designer.designer_boost_config && designer.designer_boost_config.duration).to_i
    return unless duration > 0

    current_time = Time.current
    target_time = current_time.hour < 12 || (current_time.hour == 12 && current_time.min < 50) ? 
                  current_time.change(hour: 12, min: 50, sec: 0) : 
                  current_time.tomorrow.change(hour: 12, min: 50, sec: 0)
    
    self.boost_start_time ||= target_time
    self.boost_end_time ||= boost_start_time + duration.hours - 30.minutes
  end
    
  private

  def no_overlapping_boosts
    overlapping_boosts = BoostedDesign.where(design_id: design_id)
                                     .where("boost_start_time < ? AND boost_end_time > ?", boost_end_time, boost_start_time)
    overlapping_boosts = overlapping_boosts.where.not(id: id) if persisted? # Exclude self during updates
    if overlapping_boosts.exists?
      errors.add(:base, "This design is already boosted during the selected time period.")
    end
  end


end
