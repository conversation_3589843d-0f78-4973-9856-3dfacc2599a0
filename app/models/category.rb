# == Schema Information
#
# Table name: categories
#
#  id                 :integer         not null, primary key
#  name               :string(255)
#  parent_id          :integer
#  lft                :integer
#  rgt                :integer
#  created_at         :datetime
#  updated_at         :datetime
#  title              :text
#  description        :text
#  photo_file_name    :string(255)
#  photo_content_type :string(255)
#  photo_file_size    :integer
#  photo_updated_at   :datetime
#  url                :text
#  order              :integer
#

class Category < ActiveRecord::Base
  acts_as_nested_set
  has_and_belongs_to_many :designs
  has_one  :design_node , as: :dimension
  has_and_belongs_to_many :designers
  has_and_belongs_to_many :properties
  has_and_belongs_to_many :popular_links
  has_and_belongs_to_many :faqs, class_name: "FAQ"
  has_many :addon_types
  has_many :master_addons
  has_many :option_types
  has_one  :categories_option_type
  has_one  :option_type, through: :categories_option_type
  has_many :category_banners
  has_many :category_new_arrivals
  has_many :category_connections, :foreign_key => :source_category_id
  has_many :categories, :through => :category_connections, :source => :referral_category
  has_many :categories_property_values
  has_many :category_album
  has_many :boosted_designs
  has_many :dynamic_size_charts
  has_one :default_size_chart, -> { where(designer_id: nil, default: true) }, class_name: 'DynamicSizeChart'
  has_many :grading_tags, as: :grading_taggable
  has_one :google_analytics_data,as: :metric
  has_many :time_series_datasets,as: :metric
  before_move :update_category_option_type
  after_move :update_hsn_code
  before_save :update_google_and_mirraw_product_categories

  serialize :breadcrumb_path, Hash
  include UpdateUnbxd
  include SidekiqHandleAsynchronous

  has_attached_file :photo, :styles => {:thumbnail => "70x70"}
  validates :super_child_label, presence: true, if: :super_child?
  validates :photo, presence: true, if: :super_child?
  validates_attachment_size :photo, :less_than => 50.kilobytes
  validates_attachment_content_type :photo, :content_type => /image/

  before_post_process :randomize_file_name
  before_save :update_triggers
  after_create :update_category_option_type
  after_save :send_to_unbxd
  before_destroy :send_to_unbxd

  PRICE_MATCH_GUARANTEE_CATEGORIES = promise do
    required_category_ids = []
    Category.where(id: SystemConstant.get('PRICE_MATCH_CATEGORY')).each do |category|
      required_category_ids.push(*category.cached_self_and_descendants_id)
    end
    required_category_ids.uniq
  end

  def self.find_by_namei(name)
    key = 'find_by_namei_' + name
    RequestStore.cache_fetch(key) do
      where([ "lower(name) = ?", name.downcase ]).take
    end
  end

  def self.getid(name)
    find_by_namei(name).try(:id)
  end

  def self.names_to_ids(name)
    Rails.cache.fetch("category_names_to_ids_#{name.hash}", expires_in: 1.day) do
      hash = {}
      parent_category = Category.find_by_namei(name.to_s.tr('_', '-'))
      if parent_category.present?
        parent_category.self_and_descendants.select('id,name').each do |category|
          hash[category.name.downcase.underscore.to_sym] = category.id
        end
      end
      hash
    end
  end


  def lineage
    full_name = self.name
    unless self.root?
      parent = self.parent
      full_name += ' < ' + parent.name
    end
    if self.try(:app_name) == 'luxe'
      full_name << " *"
    end
    full_name
  end

  def self.dynamic_size_chart_enums
    @dynamic_size_chart_enums ||=  {none: 0,active: 1, inherit: 2}.freeze
  end

  def self.category_type_enum
    @type_of_categories ||= {primary: 0, secondary: 1, ternary: 2}.freeze
  end

  def dynamic_size_chart_state_enum
    Category.dynamic_size_chart_enums
  end

  dynamic_size_chart_enums.each do |enum, value|
    define_singleton_method("dynamic_size_chart_#{enum}")do
      where(dynamic_size_chart_state: value)
    end
    define_method("dynamic_size_chart_#{enum}?") do
      dynamic_size_chart_state == value
    end
    define_method("dynamic_size_chart_#{enum}!") do
      update_column :dynamic_size_chart_state,value
    end
  end

  def dynamic_size_chart_category
    if dynamic_size_chart_active?
      self
    elsif dynamic_size_chart_inherit?
      ancestors.reverse.each do |category|
        if category.dynamic_size_chart_active?
          return category
        elsif !category.dynamic_size_chart_inherit?
          return nil
        end
      end
    end
  end

  def eligible_for_boost?
    category.advertise && category.boosted_designs.active.count < category.advertise_positions
  end
  
  # Cached version for better performance
  def cached_dynamic_size_chart_category
    RequestStore.cache_fetch("category_dynamic_size_chart_#{id}", expires_in: 6.hours) do
      dynamic_size_chart_category
    end
  end

  def cached_self_and_ancestors_name
    RequestStore.cache_fetch("category_self_and_ancestors_name_#{id}") do
      self_and_ancestors.pluck(:name)
    end
  end

  def get_menu_list_by_count(name)
    Rails.cache.fetch("category_menu_list_#{name}_h2l",expires_in: 6.hours) do
      Design.joins(:categories).where(categories:{id: self.self_and_descendants.pluck(:id)}).order('count(designs.id) desc').group('categories.id').count(:id)
    end
  end

  def get_menu_list_by_name(name)
    Rails.cache.fetch("category_menu_list_#{name}_alpha",expires_in: 6.hours) do
      Design.joins(:categories).where(categories:{id: self.self_and_descendants.pluck(:id)}).order('categories.name').group('categories.id').count(:id)
    end
  end

  def self.get_name_to_ids
    Rails.cache.fetch('category_get_all_names_to_ids',expires_in: 24.hours) do
      Category.select('id,p_name').map{|c|[c.id,c.p_name]}.to_h
    end
  end

  def cached_self_and_ancestors_id
    RequestStore.cache_fetch("category_self_and_ancestors_id_#{id}") do
      self_and_ancestors.pluck(:id)
    end
  end

  def cached_self_and_descendants_id
    RequestStore.cache_fetch("category_self_and_descendants_id_#{id}") do
      self_and_descendants.pluck(:id)
    end
  end

  def self.getids(name)
    key = 'getids_' + name
    RequestStore.cache_fetch(key, :expires_in => 24.hours) do
      if (category = Category.find_by_namei(name)).present?
        category.self_and_descendants.pluck(:id)
      else
        []
      end
    end
  end

  def search_master
    master_cat = self
    if master?
      return master_cat
    elsif master_cat.parent_id != nil
      while(true)
        master_cat = Category.find(master_cat.parent_id)
        if master? or master_cat.parent_id == nil
          break
        end
      end
    end
    return master_cat
  end

  def master?
    self.is_master == true
  end

  def self.designer_visible
    Category.where(:hide => false)
  end

  def self.designer_category_list designer_id
    list = {}
    designer = Designer.find(designer_id)
    s_clause = "categories.id, REPLACE(LOWER(categories.name),' ','_') as name"
    Category.select(s_clause).each {|c| list[c.name] = c.id}
    list
  end

  def design_scores
    DesignScore.joins(design: :categories).where('categories.id' => Category.names_to_ids(self.name).values)
  end

  def self.design_node
    category_ids = all.collect(&:cached_self_and_ancestors_id).flatten.uniq
    DesignNode.where(dimension_type: 'Category',dimension_id: category_ids)
  end

  def self.design_count
    child_ids = {}
    select('name,id').each do |category|
      child_ids[category.id] = Category.unscoped.names_to_ids(category.name).values
    end
    total_counts = unscoped.where(id: child_ids.values.flatten.uniq).joins(:designs).group('categories.id').count('designs.id')
    Hash[child_ids.collect{|key,values| [key,total_counts.slice(*values).sum(&:last)]}]
  end

  def get_category_top_sellers(count)
    geo = Design.country_code.try(:downcase) == 'in' ? 'domestic' : 'international'
    Rails.cache.fetch("category_top_sellers_#{self.id}_#{count}_#{geo}",expires_in: 6.hour) do
      if (designs = Bestseller.bestseller_designs(count, false).preload(:images, :designer).joins(:categories).where(categories: {id: cached_self_and_descendants_id}).group(:id)).length > 4
        designs.to_a
      else
        Design.search do
          with(:state, 'in_stock')
          with(:category_ids, self.cached_self_and_descendants_id)
          without(:in_catalog_one, 3)
          without(:in_catalog_one, 0)
          with(:average_rating, 4...5)
          with(:cluster_winner, 1)
          order_by(:sell_count, :desc)
          Sunspot.config.pagination.default_per_page = count
        end.results
      end
    end
  end

  def fetch_connected_category_names
    if parent_id.present?
      categories = CategoryConnection.select('categories.name as referral_name, category_connections.source_category_id as source_id').joins(:referral_category).where(source_category_id: [id, parent_id]).group_by(&:source_id)
      (categories[id] || categories[parent_id]).to_a.collect(&:referral_name)
    else
      self.categories.pluck(:name)
    end
  end
  def send_to_unbxd
    items = self.designs
    ids = items.pluck(:id)
    update_unbxd ids
  end

  handle_asynchronously :send_to_unbxd, priority: 8

  def update_hsn_code
    self.update_column(:hsn_code, ancestors.where('hsn_code is not null').last.try(:hsn_code)) if hsn_code.blank?
  end

  def update_category_option_type
    if option_type = parent.option_type
      categories_option_types.build(option_type: option_type)
    end
  end

  handle_asynchronously :update_category_option_type, priority: 8

  def url_path
    kind_name = name.downcase
    RedirectRule.kind_to_route[kind_name] || "/store/#{kind_name}"
  end

  def luxe_url_path
    kind_name = name.downcase
    RedirectRule.kind_to_route[kind_name] || "/#{kind_name}"
  end

  private
    def randomize_file_name
      extension = File.extname(photo_file_name).downcase
      self.photo.instance_write(:file_name, "#{SecureRandom.hex(16)}#{extension}")
    end

    def update_triggers
      if self.name.present? && (self.name_changed? || self.p_name.blank?)
        self.p_name = self.name.gsub('-',' ').split.map(&:capitalize).join(' ')
      end
      title.strip! if title.present?
    end

    def update_google_and_mirraw_product_categories
      if google_product_category.nil?
        self.google_product_category = ancestors.where('google_product_category is not null').last.try(:google_product_category)
      end
      if mirraw_product_category.nil?
        self.mirraw_product_category = ancestors.where('mirraw_product_category is not null').last.try(:mirraw_product_category)
      end
    end
end
