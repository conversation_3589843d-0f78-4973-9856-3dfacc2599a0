# == Schema Information
#
#    Table name: category_banners
#
#    id                   : integer     not NULL, Primary Key
#    name                 : string
#    category_id          : integer     not NULL, Foreign Key
#    photo_file_name      : string
#    photo_content_type   : string
#    photo_file_size      : integer
#    photo_updated_at     : datetime
#    start_date           : date
#    end_date             : date
#    country              : string
#


class CategoryBanner < ActiveRecord::Base
  #attr_accessible :category_id, :country, :end_date, :photo, :name, :start_date
  belongs_to :category
  has_attached_file :photo, 
    styles: {
      main: "945x200",
      main_webp: ["945x200", :webp]
    },
    convert_options:{
      main: '+profile -strip -sampling-factor 4:2:0 -quality 80 -interlace Plane -background white -flatten +matte -gravity center',
      main_webp: "-quality 80 -define webp:method=6"
    }
  scope :desktop_source, -> {where("app_source LIKE ?",'%desktop%')}
  validates_attachment_content_type :photo, :content_type => /image/
  validates_attachment_size :photo, :less_than => 300.kilobytes
  process_in_background :photo

  def self.category_present(category_id)
    CategoryBanner.where("category_id = ?", category_id)
  end

  def self.live
    CategoryBanner.where('start_date <= ?', Time.now).where('end_date >= ?', Time.now)
  end

  def self.country(country)
    wildcard_search = "%#{country}%" if country.present?
    CategoryBanner.where("country ILIKE :search OR country IS NULL OR country=''", search: wildcard_search)
  end

  def self.category_banner_for(country, category=nil)
    if category.present?
      CategoryBanner.desktop_source.live.country(country).
      joins('left join categories on category_banners.category_id = categories.id').
      where('(categories.lft <= ? and categories.rgt >= ?) or categories.id is null', category.lft, category.rgt).
      order('categories.lft desc nulls last').first
    else
      CategoryBanner.desktop_source.live.country(country).where('category_id is null').first
    end
  end
end
