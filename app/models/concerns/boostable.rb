module Boostable
  extend ActiveSupport::Concern

  def currently_boosted?
    boosted_designs.active.exists?
  end

  def currently_enqueued_for_boost?
    boosted_designs.scheduled.exists?
  end
  
  def eligible_for_boost?
    if !designer.eligible_for_boost?
      errors.add(:base, "Vendor Not Eligible For Boost")
    elsif !self.in_stock?
      errors.add(:base, "Product is Not in Stock")
    elsif currently_boosted?
      errors.add(:base, "Product Already Boosted")
    elsif currently_enqueued_for_boost?
      errors.add(:base, "Product is already enqueued for boost")
    elsif boostable_category.blank?
      errors.add(:base, "Category or Parent Category not eligible for boost")
    end
    errors.empty?
  end

  def boostable_category
    categories.each do |category|
      return category if category_eligible_for_boost?(category)
  
      parent_boostable = find_boostable_parent_category(category.parent)
      return parent_boostable if parent_boostable.present?
    end
  
    nil # No eligible category or parent category found
  end
  
  def category_eligible_for_boost?(category)
    return false unless category.advertise
    category.boosted_designs.upcoming_day.count < category.advertise_positions
  end
  
  def find_boostable_parent_category(category)
    return nil unless category
  
    return category if category_eligible_for_boost?(category)
  
    # Recursively go up the chain only if the current parent isn't marked for advertise
    find_boostable_parent_category(category.parent) unless category.advertise
  end
  
end