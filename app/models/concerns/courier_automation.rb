module CourierAutomation
  def generate_dhl_shipment_request(shipper_dhl, input_data, invoice_items, items_price_total, reference, currency_code, paypal_rate, item_ids, country_code, shipping_charges)
    items_weight_total = input_data[:total_weight]
    selected_shipment_depth = input_data[:shipment_depth]
    selected_shipment_width = input_data[:shipment_width]
    selected_shipment_height = input_data[:shipment_height]
    @use_cbs = input_data[:use_cbs] == 'on'
    @ddp = input_data[:delivery_duty_paid] ? true :false

    selected_content_description = input_data[:ship_contents].tr("&",",") || 'Refer to invoice'
    total_gst_tax = 0.0

    total_quantity = 0
    invoice_items.each_with_index do |item,index| total_quantity += item[:quantity].to_i end

    meis_scheme,fob_value,names,discounts,quantities,manufacture_country_code,manufacture_country_name,hs_codes,commodity_type,serial_num, umos, cesss,igsts, t_values, line_item_weights,sku_number,jewellery_type_lineitem = '','','','','','','','','','','','','','','','',''

    invoice_items.each_with_index do |item,index|
      jewellery_type_lineitem += "#{index == 0 ? '' : ','}#{item[:designable_type] == 'Jewellery'? "Artificial Jewellery" : "0"}"
      sku_number += "#{index == 0 ? '' : ','}#{item[:sku_number]}"
      commodity_type += (item[:meis_scheme] ? "#{index == 0 ? '' : ','}Customized Fashion Garments" : "#{index == 0 ? '' : ','}Others")
      meis_scheme += item[:meis_scheme] ? "#{index == 0 ? '' : ','}1" : "#{index == 0 ? '' : ','}0"
      qty = DISABLE_ADMIN_FUCTIONALITY['new_dhl_api_quantity'] ? item[:quantity].to_s : '1'
      fob_value   += "#{index == 0 ? '' : ','}#{item[:total_price]}"
      names       += "#{index == 0 ? '' : ','}#{qty}#{item[:name].tr(',','')}"
      quantities  += "#{index == 0 ? '' : ','}#{qty}"
      manufacture_country_name += "#{index == 0 ? '' : ','}INDIA"
      manufacture_country_code += "#{index == 0 ? '' : ','}IN"
      discounts += "#{index == 0 ? '' : ','}0"
      hs_codes    += "#{index == 0 ? '' : ','}#{item[:hsn_code]}"
      serial_num  += "#{index == 0 ? '' : ','}#{index+1}"
      umos        += "#{index == 0 ? '' : ','}PCS"
      cesss       += "#{index == 0 ? '' : ','}0"
      igsts       += "#{index == 0 ? '' : ','}0"
      t_values    += "#{index == 0 ? '' : ','}#{item[:total_price]}"
      line_item_weights += "#{index == 0 ? '' : ','}#{(items_weight_total.to_f*item[:quantity].to_i/total_quantity).round(2)}"
      total_gst_tax+= item[:gst_tax]
    end
    company_name, shipping_telephone, shipping_address_1,shipping_address_2,shipping_city, shipping_pincode, shipping_state, shipping_state_code = self.get_warehouse_shipping_address
    from_address = "#{shipping_address_1},#{shipping_address_2}"
    address_array = Order.multi_line_address(34, from_address)
    shipper = {

      "shipperName" => "MR. ANUP NAIR",
      "shipperCompName" => company_name,
      "shipperAddress1" => address_array[0],
      "shipperAddress2" => address_array[1],
      "shipperAddress3" => address_array[2],
      "shipperCity" => shipping_city,
      "shipperPhoneNumber" => shipping_telephone,
      "shipperPostalCode" => shipping_pincode,
      "shipperCountryName" => "INDIA" ,
      "shipperCountryCode" => "IN",
      "shipperRef" => reference
    }
    # exporter_details = {
    #   "exporter_CompanyName" => company_name,
    #   "exporter_AddressLine1" => address_array[0],
    #   "exporter_AddressLine2" => address_array[1],
    #   "exporter_AddressLine3" => address_array[2],
    #   "exporter_City" => shipping_city,
    #   "exporter_Division" => shipping_state,
    #   "exporter_DivisionCode" => shipping_state_code,
    #   "exporter_PostalCode" => shipping_pincode,
    #   "exporter_CountryCode" => "IN",
    #   "exporter_CountryName" => "India",
    #   "exporter_PersonName" => "MR. ANUP NAIR",
    #   "exporter_PhoneNumber" => shipping_telephone,
    #   "exporter_Email" => "<EMAIL>",
    #   "exporter_MobilePhoneNumber" => shipping_telephone
    # }
    invoice_number = get_invoice_number
    invoice_date   = other_details.try(:[],'invoice_date').presence || Date.today.to_s
    self.other_details['total_gst_tax'] = total_gst_tax.round(2) unless order_notification[invoice_number].present?

    state_code_countries = 'US', 'IN', 'CA', 'AE'
    order_street = self.street.tr("\n"," ").tr("\r"," ").gsub("&","and").strip
    street_address = multi_line_address([(order_street.length * 0.7).to_i,35].min, order_street)
    recipient_name = multi_line_address(35, name.gsub("&","and").strip)[0]
    recipient = {
      "consigneeName" => recipient_name,
      "consigneeCompName" => recipient_name,
      "consigneePh" => phone,
      "consigneeAddLine1" => street_address[0] ,
      "consigneeAddLine2" => street_address[1] || city || "" ,
      "consigneeAddLine3" => street_address[2] || "" ,
      "consigneeCity" => city,
      "consigneeDivCode" => (state_code_countries.include?(country_code) ? state_code : buyer_state ) ,
      "postalCode" => pincode,
      "consigneeCountryCode" => country_code,
      "consigneeCountryName" => country
    }
    if items_price_total.zero?
      commercial_r,paypal_r,currency_c = self.get_commercial_values
      declared_value = (self.discount + self.shipping_charges + self.wallet_discount(self.currency_rate)) / paypal_r.round(2)
    else
      declared_value = items_price_total
    end
    packages = {
      "Placeofsupply" => shipping_city,
      "dateofsupply" => invoice_date,
      "shipperstatecode" => "27",
      "shipperstateName" => shipping_state.upcase,
      "dutiableDeclaredvalue" =>  '%.2f' % declared_value,
      "dutiableDeclaredCurrency" => currency_code ,
      # "dutyAccNumber" => '' , not present in new method PostShipment_CSBV
      "shipNumberOfPieces" => 1 ,
      "shipCurrencyCode" => currency_code ,
      "shipPieceWt" => items_weight_total ,
      "shipPieceDepth" => selected_shipment_depth,
      "shipPieceWidth" => selected_shipment_width,
      "shipPieceHeight" => selected_shipment_height ,
      "shipContents" => selected_content_description,
      "specialService"    => @ddp ? 'DD' : 'DS',
      'bankADCode' => self.get_ad_code
    }
    
    payment_txn_id = self.payment_gateway.present? && self.payment_gateway.downcase.eql?("stripe") && self.payment_gateway_transaction.present? ? self.payment_gateway_transaction.transaction_id : self.paypal_txn_id

    order_details = {
      "paymentorTxnID" => "#{payment_txn_id}",
      "orderNumber" => "#{self.number}",
      "orderDate" => "#{self.created_at.strftime("%Y-%m-%d")}",
      "sKU_Number" => sku_number,
      "jewelleryType" => jewellery_type_lineitem
    }

    invoice_details = {
      'useDHLInvoice'          => 'Y',
      'manufactureCountryCode' => manufacture_country_code,
      'weight'                 => line_item_weights,
      'commodityCode'          => hs_codes 
    }
    
    if @use_cbs
      packages.merge!({
      'manufactureCountryName' => manufacture_country_name,
      'discount'               => discounts,
      'usingecommerce'    => '1',
      'isUnderMEISScheme' => meis_scheme,
      'iECNo'             => IEC_NUMBER,
      'termsOfTrade'      => @ddp ? 'DDP' : 'DAP',
      'serialNumber'      => serial_num,
      'fOBValue'          => fob_value,
      'description'       => names,
      'qty'               => quantities,
      'hSCode'            => hs_codes,
      'insuredAmount'     => 0,
      'commodityType'     => commodity_type,
      'gSTIN'             => MIRRAW_GST_NUMBER,
      'gSTInvNo'          => invoice_number,
      'gSTInvNoDate'      => invoice_date,
      'nonGSTInvNo'       => '',
      'nonGSTInvDate'     => '',
      'totalIGST'         => '',
      'isUsingIGST'       => 'No',
      'usingBondorUT'     => 'Yes',
      'isIndemnityClauseRead'=> 'Yes',
      'consigneeEmail'    => email,
      'invoiceRatePerUnit' => fob_value,
      'shipPieceUOM'      => umos,
      'shipPieceCESS'     => cesss,
      'shipPieceIGST'     => igsts,
      'shipPieceIGSTPercentage' => '',
      'shipPieceTaxableValue' => t_values
      
      })
    end

    quote_request = {
      "shipperPostCode" => shipping_pincode,
      "receiverCountryCode" => country_code,
      "postCode" => pincode,
      "declaredCurrency" => "INR",
      "declaredValue" => '%.2f' % (items_price_total * paypal_rate).to_i,
      "pieces" => 1,
      "shipPieceWt" => items_weight_total,
      "shipPieceDepth" => selected_shipment_depth,
      "shipPieceWidth" => selected_shipment_width,
      "shipPieceHeight" => selected_shipment_height
    }
    inr_price = (items_price_total  * paypal_rate).to_i
    @shipment = get_shipment_object(shipper_dhl, inr_price, input_data, item_ids, reference, self.id, @use_cbs)
    request_hash = packages.merge(recipient).merge(shipper).merge(invoice_details).merge(order_details).deep_dup
    # using long_image_queue for api calls
    # Shipment.sidekiq_delay(queue: 'critical')
    #         .create_dhl_shipment(
    #           @use_cbs, self, @shipment, quote_request, invoice_items,
    #           shipping_charges, items_price_total, request_hash
    #         )
    SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async("Shipment", 
                                                                    nil,
                                                                    "create_dhl_shipment",
                                                                    @ddp,
                                                                    @use_cbs,
                                                                    {self.class.to_s => self.id},
                                                                    @shipment.merge(:sidekiq_request_params => true, :has_object? => true),
                                                                    quote_request.merge(:sidekiq_request_params => true),
                                                                    invoice_items,
                                                                    shipping_charges,
                                                                    items_price_total,
                                                                    request_hash.merge(:sidekiq_request_params => true)           
                                                                  )
    get_post_shipment_attempt_status(shipper_dhl.name)    
  rescue => error
    self.add_tags_skip_callback('shipment_error')
    {error: true, message: error.message}    
  end

  def recipients_array_object
    order_street = street.tr("\n"," ").tr("\r"," ").gsub("&","and").strip
    street_address = multi_line_address([(order_street.length * 0.7).to_i, (order_street.length > 30 ? 30 : 15)].min, order_street)
    destination = country_code == 'IN' ? city.upcase : ATLANTIC_COUNTRIES[country_code]
    raise 'Destination code mismatch' unless destination
    recipient = {
      ProductCode: "SPX",
      ServiceName: "SELF",
      VendorName: "TC1",
      DestinationName: destination,
      ConsigneeName: multi_line_address(30, name.gsub("&","and").strip)[0],
      ConsigneeContact: name,
      ConsigneeAdd1: street_address[0], 
      ConsigneeAdd2: street_address[1] || city  || "",
      ConsigneeCity: city.upcase,
      ConsigneeState: buyer_state.upcase,
      ConsigneePin: pincode,
      ConsigneeTelno: phone,
      ConsigneeMobile: phone,
      ConsigneeEmail: email
    }
  end


  def shipper_hash
    company_name, shipping_telephone, shipping_address_1, shipping_address_2, shipping_city, shipping_pincode, _, shipping_state_code = get_warehouse_shipping_address
    address_array = Order.multi_line_address(30, shipping_address_1)
    shipper_state = "#{shipping_address_2.split(',')[0]},#{shipping_city},#{shipping_address_2.split(',')[1]}"
    shipper={
      OriginName: 'THANA',
      ShipperContact: 'MIRRAW',
      ShipperName: company_name,
      ShipperAdd1: address_array[0],
      ShipperAdd2: address_array[1],
      ShipperCity: address_array[2] || 'Mumbai',
      ShipperState: shipper_state,
      ShipperPin: shipping_pincode,
      ShipperTelno: shipping_telephone,
      ShipperMobile: shipping_telephone
    }
  end

  def manifest_gst_details
    {
      "ManifestGstDetails" => {
        "GST_Invoice" => "0",
        "LUTIGST" => "U",
        "TotalIGST" => "0",
        "BankADCode" => "0510648",
        "BankAccount" => "**************",
        "BankIFSC" => "HDFC0000626",
        "LUTNumber" => "***************",
        "ExchangeRate" => "0",
        "Firm" => "NG",
        "NFEI" => "0",
        "PayofIGST" => "0",
        "ECommerce" => "0",
        "MEISScheme" => "0",
        "Format" => "CSB5",
        "IECNo" => "**********"
      }
    }
  end
  
  def generate_atlantic_shipment_request(shipper_atlantic, invoice_items, input_data, country_code, items_count, items_price_total, reference, paypal_rate, shipping_charges, item_ids, currency_code)
    shipment_request ={
      CustomerRefNo: reference,
      InvoiceDate: DateTime.current.strftime('%d-%m-%Y'),
      InvoiceNo: get_invoice_number,
      Pieces:  "1",
      Currency: currency_code,
      Weight: input_data[:total_weight].to_f.round(3),
      Content: input_data[:ship_contents].tr("&",","),
      ShipmentValue: "#{items_price_total}",
      DocumentType: "GSTIN (Normal)",
      DocumentNumber: MIRRAW_GST_NUMBER,
      Dox_Spx: "SPX",
      CSBType: "CSB5",
      TermofInvoice: "FOB",
    }
    request_hash = shipment_request.merge(recipients_array_object).merge(shipper_hash).merge(manifest_gst_details)

    inr_price = (items_price_total  * paypal_rate).to_i

    shipment = get_shipment_object(shipper_atlantic, inr_price, input_data, item_ids, reference, self.id)
    
    # using long_image_queue for api calls
    # Shipment.sidekiq_delay(queue: 'critical')
    #         .atlantic_shipment(self, shipment_request, shipment, invoice_items,
    #                            shipping_charges, items_price_total)
    SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async("Shipment", 
                                                                  nil,
                                                                  "atlantic_shipment",
                                                                  {self.class.to_s => self.id},
                                                                  request_hash.merge(:sidekiq_request_params => true),
                                                                  shipment.merge(:sidekiq_request_params => true, :has_object? => true),
                                                                  invoice_items,
                                                                  shipping_charges,
                                                                  items_price_total,
                                                                  )                                                                 
    get_post_shipment_attempt_status(shipper_atlantic.name)
  rescue => error
    AtlanticApi.new.notify("Atlantic Shipment Error", "Atlantic Shipment and Label Generation Failed", request_hash,"", error)
    self.add_tags_skip_callback('shipment_error')
    {error: true, message: error.message}
  end

  def generate_aramex_shipment_request(shipper_aramex, invoice_items, input_data, country_code_value, items_price_total, reference, paypal_rate, shipping_charges, item_ids, currency_code)    
    _, shipping_telephone, shipping_address_1, _,shipping_city, shipping_pincode, shipping_state, shipping_state_code = self.get_warehouse_shipping_address
    inr_price = (items_price_total * paypal_rate).to_i
    customer_details = {
      address: 
      {
        city: city,
        state: state,
        state_code: state_code,
        :pincode => pincode,
        :street => street,
        :country_code => country_code_value
      },
      :contact =>
      {
        :name => name,
        :phone_number1 => phone,
        :email => email
      }
    }

    mirraw_details = {
      :address =>
      {
        :city => shipping_city,
        :state => shipping_state,
        :state_code => shipping_state_code,
        :pincode => shipping_pincode,
        :street => shipping_address_1,
        :country_code => 'IN'
      },
      :contact =>
      {
        :name => 'Mirraw Online Services Pvt Ltd',
        :phone_number1 => shipping_telephone,
        :email => '<EMAIL>'
      }
    }
    shipment_hash = get_shipment_object(shipper_aramex, inr_price, input_data, item_ids, reference, self.id)
    # using long_image_queue for api calls
    # Shipment.sidekiq_delay(queue: 'critical')
    #         .aramex_create_international(
    #           self, shipment_hash, customer_details, mirraw_details,
    #           invoice_items, items_price_total, currency_code, shipping_charges) 
    SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async("Shipment", 
                                                                   nil,
                                                                   "aramex_create_international",
                                                                   {self.class.to_s => self.id},
                                                                   shipment_hash.merge(:sidekiq_request_params => true, :has_object? => true),
                                                                   customer_details.merge(:sidekiq_request_params => true),
                                                                   mirraw_details.merge(:sidekiq_request_params => true),
                                                                   invoice_items,
                                                                   items_price_total,
                                                                   currency_code,
                                                                   shipping_charges,
                                                                  )   
    get_post_shipment_attempt_status(shipper_aramex.name)
  rescue => error
    self.add_tags_skip_callback('shipment_error')
    {error: true, message: error.message}
  end

  def get_post_shipment_attempt_status(shipper_name)
    self.other_details["#{shipper_name.downcase}_status"] = 'processing'
    self.skip_before_filter = true
    self.save(validate: false)      
    self.remove_tags_skip_callback('shipment_error')
    {error: false, message: 'Label Generation In Process please check order detail page.'}
  end

  private

  def get_shipment_object(shipper, inr_price, input_data, item_ids, reference, order_id, is_csb=false)
    {
      shipper:         {shipper.class.to_s => shipper.id, "is_object?" => true},
      price:           inr_price,
      weight:          input_data[:total_weight].to_f.round(2),
      line_item_ids:   item_ids,
      order_id:        order_id,
      packaging_type:  input_data[:packaging_type],
      invoicer_id:     input_data[:packer_id],
      packer_id:       input_data[:packer_id],
      mirraw_reference:  reference,
      csb_used:          is_csb
    }
  end
  
end