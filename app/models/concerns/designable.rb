module Designable

  LIST = [:<PERSON><PERSON>, :Bag, :Consumable, :<PERSON>, :<PERSON>, :Other, :<PERSON><PERSON>, :<PERSON>D<PERSON><PERSON>, :<PERSON><PERSON>, :<PERSON><PERSON>, :<PERSON><PERSON><PERSON>, :MusicalInstrument, :<PERSON><PERSON><PERSON>, :Turban, :Lehenga]

  def self.included(base)
    base.class_eval do
      @designable_list
      (self.columns.map(&:name) - ['id','created_at','updated_at']).each do |column|
        define_method("#{column}_values"){self.desigable_values(column)}
        define_singleton_method("#{column}_values"){self.desigable_values(column)}
      end
      def self.desigable_values(column_name)
        Rails.cache.fetch("designable_values_for_#{column_name}", expires_in: 1.day) do
          DesignableValue.where(value_type: column_name, designable_type: self.to_s).pluck(:name)
        end
      end
      def desigable_values(column_name)
        self.class.desigable_values(column_name)
      end

      @properties = {}
      class << self
        attr_reader :properties
        def valid_properties_for **option
          option.each do |name, property|
            if property.is_a? Array
              @properties[name][:properties] ||= Array.wrap(property).to_set
            elsif property.is_a? Hash
              property[:properties] = Array.wrap(property[:properties]).to_set
              @properties[name] ||= property
            end
          end
        end
      end

      def validate_properties
        all_valid_properties = self.class.properties
        return unless all_valid_properties.is_a?(Hash) && design.present?
        design_properties = design.property_values.collect(&:property).compact.map(&:name).map(&:to_sym).to_set
        design_properties += self.class.columns.map(&:name).map(&:to_sym).select do |col|
          self.public_send(col).present?
        end.to_set
        all_valid_properties.each do |name, valid_properties|
          properties = valid_properties[:properties]
          call_back = valid_properties[:on_valid]
          if properties.subset?(design_properties) || properties.disjoint?(design_properties)
            if properties.subset?(design_properties) && call_back.present?
              call_back.is_a?(Symbol) ? send(call_back) : call_back.call(self)
            end
          else
            missing_fields = properties - design_properties
            filled_properties =  properties - missing_fields
            design.errors[:base] << "Please fill all #{name} properties : #{missing_fields.to_a.join(', ')}. else remove #{filled_properties.to_a.join(', ')} if #{name} is not available"
          end
        end
      end
    end
  end

  def get_max_bust_size
    if self.respond_to?(:max_bustsize_fit)
      max_size = self.max_bustsize_fit.to_i
      return max_size.odd? ? (max_size - 1) : max_size
    end
    0
  end

end