module Promotions
  # def is_on_global_sale?
  #   validate_between_date?(Time.zone.now)
  # end

  # def is_on_global_sale_period?
  #   validate_between_date?(self.created_at)
  # end

  def self.initialize_variables(todays_date=Time.zone.now)
    return  RequestStore.cache_fetch(:promotion_pipeline_method_variable_hash,expires_in: 1.hour) do
      promotion_pipelines = PromotionPipeLine.where{(start_date.lt todays_date) & (end_date.gt todays_date)}
      p_methods = promotion_pipelines.pluck(:methods_hash)
      p_variables = promotion_pipelines.pluck(:variables_hash)

      variables_hash = []
      p_variables.each do |p_var|
        variables_hash << JSON.parse(p_var)
      end
      pipeline_methods = []
      klass = []
      p_methods.each do |p_method|
        parsed_methods = JSON.parse(p_method)
        pipeline_methods << parsed_methods
        klass << parsed_methods.keys
      end
      klass = klass.flatten.uniq unless klass.flatten.blank?
      [variables_hash, pipeline_methods, klass]
    end
  end

  def self.list_of_classes(todays_date=Time.zone.now)
    variables_hash, pipeline_methods, klass=initialize_variables(todays_date)
    return klass
  end

  def self.get_method_list(method_hash, klass)
    p_methods = []
    method_hash.each do |p_method|
      p_methods << p_method[klass] if p_method[klass].present?
    end
    p_methods = p_methods.flatten.uniq unless p_methods.flatten.blank?
    p_methods
  end

  def self.use_old_method?(method_hash, klass_list, klass, method_name)
    if klass_list.include?(klass)
      p_methods = get_method_list(method_hash, klass)
      if p_methods.include?(method_name)
        use_old_charges = false
      else
        use_old_charges = true
      end
    else
      use_old_charges = true
    end
  end

  def self.free_shipping_at(country)
    country_code = RequestStore.fetch("country_code_#{country}".freeze) do
      Country.find_by_namei_cached(country).try(:[], :iso3166_alpha2)
    end
    promotion_pipelines = PromotionPipeLine.active_promotions.app_source('desktop')
    var_hash = if country_code.present?
                    frozen_key="free_shipping_#{country_code}".freeze
                    free_shipping = RequestStore.fetch(frozen_key) do
                      promotion_pipelines.find_by_name(frozen_key) ||  promotion_pipelines.find_by_name("free_shipping")
                    end
                    free_shipping.try(:variables_hash)
                  else
                    free_shipping = RequestStore.fetch(:free_shipping) do
                      promotion_pipelines.find_by_name("free_shipping")
                    end
                    free_shipping.try(:variables_hash)
                  end

    free_shipping_rate = 0
    free_shipping_rate = JSON.parse(var_hash)['free_shipping_rate'] if var_hash.present?
    return free_shipping_rate.to_i
  end

  def self.get_shipping_rate_promotion(conversion_rate, country_code, design = nil)
    promotion_pipelines = PromotionPipeLine.active_promotions.app_source('desktop')
    var_hash = if country_code.present?
      frozen_key="free_shipping_#{country_code}".freeze
      free_shipping = RequestStore.fetch(frozen_key) do
      promotion_pipelines.find_by_name(frozen_key) ||  promotion_pipelines.find_by_name("free_shipping")
      end
      free_shipping.try(:variables_hash)
    else
      free_shipping = RequestStore.fetch(:free_shipping) do
      promotion_pipelines.find_by_name("free_shipping")
      end
      free_shipping.try(:variables_hash)
    end
    if var_hash.present?
      if design == true 
        return (JSON.parse(var_hash)['on_design_rate'].to_i / conversion_rate).round(2)
      else
        return (JSON.parse(var_hash)['free_shipping_rate'].to_i / conversion_rate).round(2)
      end
    else
      return 0
    end
  end

  def self.free_stitching_find_by(country_code)
    if country_code.present?
      frozen_key="free_stitching_#{country_code}".freeze
      free_stitching = RequestStore.cache_fetch(frozen_key, expires_in: 1.day) do
        promotion_pipelines = PromotionPipeLine.active_promotions.app_source('desktop')
        promotion_pipelines.find_by_name(frozen_key) || promotion_pipelines.find_by_name("free_stitching") || ''
      end
    else
      free_stitching = RequestStore.cache_fetch(:free_stitching, expires_in: 1.day) do
        promotion_pipelines = PromotionPipeLine.active_promotions.app_source('desktop')
        promotion_pipelines.find_by_name("free_stitching") || ''
      end
    end
  end

  def self.free_stitching_at(country_code)
    JSON.parse( Promotions.free_stitching_find_by(country_code).try(:variables_hash) || '{}')['stitching_on'].to_i
  end

  def self.shipping_offer_on_category
    RequestStore.cache_fetch(:free_shipping_category, expires_in: 1.day) do
      promotion_pipelines = PromotionPipeLine.active_promotions.app_source('desktop')
      var_hash = promotion_pipelines.find_by_name('category_free_shipping').try(:variables_hash)
      var_hash ? JSON.parse(var_hash)['sale_on'] : ''
    end
  end

  def self.discount_offer_on_category(active_promotions)
    RequestStore.cache_fetch(:discount_category, expires_in: 1.day) do
      promotion_pipelines = active_promotions
      var_hash = promotion_pipelines.find_by_name('category_discount_sale').try(:variables_hash)
      var_hash ? JSON.parse(var_hash)['sale_on'] : ''
    end
  end

  def self.category_promotion_ids(active_promotions)
    RequestStore.cache_fetch(:promotions_categories_ids, expires_in: 1.day) do
      category_name = Promotions.discount_offer_on_category(active_promotions)
      if category_name.present?
        id_list = []
        category_names = category_name.split(',')
        RequestStore.cache_preload *(category_names.collect{|cat_name| 'getids_' + cat_name})
        category_names.each do |cat_name|
          id_list.push(*Category.getids(cat_name))
        end
        id_list
      else
        []
      end
    end
  end

  def self.additional_discount_percent(country_code=nil)
    RequestStore.cache_fetch("additional_discount_#{country_code}".freeze, expires_in: 1.day) do
      promotion_pipelines = PromotionPipeLine.active_promotions.app_source('desktop')
      country_promotion = if country_code.present?
        promotion_pipelines.find_by_name("additional_discount_#{country_code}") || promotion_pipelines.find_by_name("additional_discount")
      else
        promotion_pipelines.find_by_name("additional_discount")
      end
      country_code_list = country_promotion.present? ? country_promotion.country_code : ''
      var_hash = country_promotion.try(:variables_hash)
      discount_percent = [0]
      amount_on = [0]
      if var_hash && country_code_list.split(',').include?(country_code)
       discount_percent = JSON.parse(var_hash)['discount_percent'].split(',')
       amount_on = JSON.parse(var_hash)['amount'].split(',')
     end
     [discount_percent, amount_on]
    end
  end

  def self.discount_promotion(active_promotions,country_code=nil)
    RequestStore.cache_fetch("discount_promotion_record_#{country_code}".freeze, expires_in: 1.day) do
      promotion_pipelines = active_promotions
      if country_code.present?
        promotion_pipelines.find_by_name("global_sale_#{country_code}") || promotion_pipelines.find_by_name("global_sale") || []
      else
        promotion_pipelines.find_by_name('global_sale') || []
      end
    end
  end

  def self.category_discount_promotion(active_promotions,country_code=nil)
    RequestStore.cache_fetch("category_discount_promotion_record_#{country_code}".freeze, expires_in: 1.day) do
      promotion_pipelines = active_promotions
      if (category_name = Promotions.discount_offer_on_category(active_promotions)).present?
        if country_code.present?
          promotion_pipelines.find_by_name("category_discount_sale_#{country_code}") || promotion_pipelines.find_by_name("category_discount_sale") || []
        else
          promotion_pipelines.find_by_name('category_discount_sale') || []
        end
      else
        []
      end
    end
  end

  def self.sale_discount_on_country(active_promotions,country_code=nil)
    RequestStore.cache_fetch("sale_discount_country_#{country_code}".freeze, expires_in: 1.day) do
      country_promotion = Promotions.discount_promotion(active_promotions,country_code)
      country_promotion.present? ? country_promotion.country_code : ''
    end
  end

  def self.category_discount_on_country(active_promotions,country_code=nil)
    RequestStore.cache_fetch("category_discount_country_#{country_code}".freeze, expires_in: 1.day) do
      country_promotion = Promotions.category_discount_promotion(active_promotions,country_code)
      country_promotion.present? ? country_promotion.country_code : ''
    end
  end

  def self.free_shipping_on_country(country_code=nil)
    promotion_pipelines = PromotionPipeLine.active_promotions.app_source('desktop')
    country_promotion =   if country_code.present?
                            frozen_key="free_shipping_#{country_code}".freeze
                            RequestStore.fetch(frozen_key) do
                              promotion_pipelines.find_by_name(frozen_key) || promotion_pipelines.find_by_name("free_shipping")
                            end
                          else
                            RequestStore.fetch(:free_shipping) do
                              promotion_pipelines.find_by_name("free_shipping")
                            end
                          end
    country_promotion.present? ? country_promotion.country_code : ''
  end

  def self.free_stitching_on_country(country_code)
    country_promotion = Promotions.free_stitching_find_by(country_code)
    country_promotion.present? ? country_promotion.country_code : ''
  end

  def self.stitching_offer(country_code)
    RequestStore.cache_fetch("stitching_offer_#{country_code}", expires_in: 1.day) do
      var_hash = (stitching_offer = Promotions.get_stitching_offer_in_country(country_code)).try(:variables_hash)
      stitching_rate = 0
      stitching_on = 0
      category_names = ""
      addon_names = ""
      if stitching_offer.present? && var_hash.present?
        var_hash = JSON.parse(var_hash)
        stitching_rate = var_hash['stitching_rate']
        stitching_rate_standard = var_hash['standard_stitching']
        stitching_rate_standard_on = var_hash['standard_stitching_on']
        stitching_rate_custom = var_hash['custom_stitching']
        stitching_rate_custom_on = var_hash['custom_stitching_on']
        stitching_on = var_hash['stitching_on']
        category_names = var_hash['category_name'].downcase
        addon_names = var_hash['addon_name'].downcase
      end
      { stitching_rate: stitching_rate, stitching_rate_standard: stitching_rate_standard, stitching_rate_standard_on: stitching_rate_standard_on, stitching_rate_custom: stitching_rate_custom, stitching_rate_custom_on: stitching_rate_custom_on, stitching_on:  stitching_on.to_i, category_names: category_names.split(','), addon_names: addon_names.split(',') }
    end
  end

  def self.get_stitching_offer_in_country(country_code)
    promotion_pipelines = PromotionPipeLine.active_promotions.app_source('desktop')
    offer_name = 'stitching_offer'
    required_attrs = [:country_code, :variables_hash]
    stitching_offer = if country_code.present?
      RequestStore.cache_fetch("available_#{offer_name}_#{country_code}", expires_in: 1.day) do
        offer = promotion_pipelines.select(required_attrs).find_by_name("#{offer_name}_#{country_code}")
        offer ||= promotion_pipelines.select(required_attrs).find_by_name(offer_name)
        offer.present? && offer.country_code.present? && offer.country_code.include?(country_code) ? offer : ''
      end
    else
      RequestStore.cache_fetch("available_#{offer_name}", expires_in: 1.day) do
        promotion_pipelines.select(required_attrs).find_by_name(offer_name) || ''
      end
    end
    stitching_offer.presence
  end

  def self.stitching_offer_on?(country_code, design_type, addon_type, stitching_offer=nil)
    stitching_offer ||= Promotions.stitching_offer(country_code)
    stitching_offer[:category_names].include?(design_type.try(:downcase)) && stitching_offer[:addon_names].include?(addon_type.try(:downcase))
  end

  def self.offer_message(country_code)
    RequestStore.cache_fetch("offer_message_#{country_code}", expires_in: 1.day) do
      active_promotions = PromotionPipeLine.active_promotions
      promotion_offer = active_promotions.find_by_name("offer_message_#{country_code}") || active_promotions.find_by_name('offer_message')
      if promotion_offer.present? && promotion_offer.country_code?(country_code)
        promotion_offer.variables_hash.try{|var_hash| JSON.parse(var_hash)['message']}
      end.to_s
    end
  end

  def self.bmgnx_offer_message
    RequestStore.fetch 'bmgnx_offer_message' do
      bmgnx_hash = PromotionPipeLine.bmgnx_hash
      message = "Buy #{bmgnx_hash[:m]} Get #{bmgnx_hash[:n]}"
      message += bmgnx_hash[:x] != 100 ? " at #{bmgnx_hash[:x]} % off" : " free"
    end
  end

  def self.design_free_stitching_active?
    RequestStore.cache_fetch("desktop_design_free_stitching_active_for_#{::Design.country_code}?") do
      PromotionPipeLine.active_promotions.for_country(::Design.country_code).app_source('desktop').design_free_stitching_promotions.exists?
    end
  end

  def self.cumulative_discount_percent(*discount_percents)
    discount_percents.collect!(&:to_f)

    first_discount, second_discount = discount_percents.shift(2)

    return 0 unless first_discount
    return first_discount.round(2) unless second_discount

    cumulative_discount_percent = first_discount + (100 - first_discount) * (second_discount / 100.0)
    cumulative_discount_percent(cumulative_discount_percent, *discount_percents)
  end

  private

  # def validate_between_date?(date_object)
  #   begin
  #     return date_object.to_i.between?(GLOBAL_DISCOUNT_START_TO_I, GLOBAL_DISCOUNT_END_TO_I)
  #   rescue => e
  #     return false
  #   end
  # end
end

module Promotions::Cart
  def self.included base
    base.class_eval do
      alias_method :original_get_international_shipping_cost, :get_international_shipping_cost
      def get_international_shipping_cost(conversion_rate, country)
        @variables_hash, @pipeline_methods, @klass = Promotions.initialize_variables(Time.zone.now)
        use_old_charges = Promotions.use_old_method?(@pipeline_methods, @klass, 'carts', 'get_international_shipping_cost')
        use_old_charges = new_shipping_charges(conversion_rate, country) if use_old_charges == false
        if use_old_charges == true
          original_get_international_shipping_cost(conversion_rate, country)
        else
          if shipping_categories_available? || cart_has_bmgn_products?
            original_get_international_shipping_cost(conversion_rate, country, true)
          else
            return 0
          end
        end
      end

      def free_shipping_charges_text(country, conversion_rate, symbol, display_country=true)
        @variables_hash, @pipeline_methods, @klass = Promotions.initialize_variables(Time.zone.now)
        use_old_method = Promotions.use_old_method?(@pipeline_methods, @klass, 'carts', 'free_shipping_charges_text')
        if free_shipping_on_country?(country) && (use_old_method != true) && !((['india', 'n/a'].include?(country.downcase)) && display_country) && !shipping_categories_available?
          if new_shipping_charges(conversion_rate, country) == true
            free_shipping_rate = get_free_shipping_rate(conversion_rate, country).round(2)
            category_name = Promotions.shipping_offer_on_category
            country_message = display_country.present? ? " to #{country.upcase}" : ""
            return "Free Shipping #{country_message} on Sub Total #{category_name} above #{symbol} #{free_shipping_rate}"
          else
            return "You are eligible for free shipping. (Products included in the Buy M, Get N promotion are excluded from free shipping)"
          end
        else
          return false
        end
      end

      def free_shipping_available?(conversion_rate, country)
        available = false
        item_total = self.total_price_currency_without_addons(conversion_rate, :referral)
        item_total_exclude_shipping = self.total_price_currency_for_exclude_shipping(conversion_rate)
        amount = get_free_shipping_rate(conversion_rate, country)
        if (category_name = Promotions.shipping_offer_on_category).present?
          cat_line_items = []
          category_wise_total = 0
          self.line_items.each do |line_item|
            if (Category.getids(category_name) & line_item.design.categories.pluck(:id)).present?
              cat_line_items << line_item
            end
          end

          cat_line_items.each do |line_item|
            category_wise_total += (line_item.price * line_item.quantity)
          end
          if category_wise_total.present?
            category_wise_total_with_currency = category_wise_total.to_f / conversion_rate
            available = true if category_wise_total_with_currency >= amount
          end
        elsif shipping_categories_available? || cart_has_bmgn_products?
          available = true if amount.present? && free_shipping_on_country?(country) && item_total_exclude_shipping >= amount
        else
          available = true if amount.present? && free_shipping_on_country?(country) && item_total >= amount
        end
        available
      end

      def get_free_shipping_rate(conversion_rate, country)
        country_code = CurrencyConvert.currency_convert_memcached.find{|c| c.country.downcase == country.downcase}.country_code
        Promotions.get_shipping_rate_promotion(conversion_rate, country_code, false)
      end

      def free_shipping_on_country?(country)
        promotion_pipelines = PromotionPipeLine.active_promotions.app_source('desktop')
        shipping_country_code = country.present? ? (RequestStore.fetch("country_code_#{country}".freeze) do Country.find_by_namei_cached(country).try(:[],:iso3166_alpha2) end) : Design.country_code
        if Design.country_code == 'IN'
          false
        elsif (country_code = Promotions.free_shipping_on_country(shipping_country_code)).present?
          country_code.split(',').include?(shipping_country_code)
        elsif promotion_pipelines.where('name like ?', '%free_shipping%').pluck(:country_code).include?(shipping_country_code)
          true
        else
          false
        end
      end


      def free_stitching_on_country?(country_code = nil)
        country_code ||= Design.country_code
        if (country_code_list = Promotions.free_stitching_on_country(country_code)).present?
          country_code_list.split(',').include?(country_code)
        else
          false
        end
      end

      # to avoid stale data, pass recalucate: true
      def free_stitching?(country_code = nil, recalculate: false)
        country_code ||= Design.country_code
        @free_stitching ||= {}
        @free_stitching[country_code] = if (!recalculate && @free_stitching.has_key?(country_code))
          @free_stitching[country_code]
        else
          (free_stiching_coupon? || (free_stitching_on_country? && free_stitching_eligible?))
        end
      end

      def free_stitching_eligible?(country_code = nil)
        country_code ||= Design.country_code
        items_total_without_addons - self.bmgnx_discounts >= Promotions.free_stitching_at(country_code)
      end

      def free_stitching_text(country_code, conversion_rate, symbol)
        if free_stitching_on_country?(country_code)
          if free_stitching_eligible?
            return "You are eligible for free stitching"
          else
            free_stitching_rate = (Promotions.free_stitching_at(country_code).to_f/conversion_rate).round(2)
            return "Free Stitching on Item Total above #{symbol} #{free_stitching_rate} (*exclude Stitching Charges)"
          end
        else
          return false
        end
      end

      def qpm_disc_percent
        qpm = QuantityDiscountPromotion.current_active_promotions.select{|qpm| self.total_items >= qpm.required_quantity}.last
        qpm.present? ? qpm.discount_percent : 0
      end

      def next_qpm_msg
        qpm = QuantityDiscountPromotion.current_active_promotions.find{|qpm| qpm.required_quantity > self.total_items}
        qpm.present? ? qpm.cart_message(self.total_items) : nil
      end

      private

      def new_shipping_charges(conversion_rate, country)
        free_shipping_rate = get_free_shipping_rate(conversion_rate, country)
        if free_shipping_rate.present?
          if free_shipping_available?(conversion_rate, country)
            return 0
          else
            return true
          end
        end
      end
    end
  end
end

module Promotions::Order
  def self.included base
    base.class_eval do
      alias_method :original_get_shipping_cost, :get_shipping_cost
      def get_shipping_cost(country)
        @variables_hash, @pipeline_methods, @klass = Promotions.initialize_variables(Time.zone.now)
        use_old_charges = Promotions.use_old_method?(@pipeline_methods, @klass, 'orders', 'get_shipping_cost')
        use_old_charges = new_shipping_charges(country) if use_old_charges == false
        if coupon.present? && coupon.is_shipping? && ((self.total + self.mirraw_addon_charges + (self.refund_discount.to_f * self.currency_rate.to_f)) >= coupon.min_amount)
          return 0
        elsif use_old_charges == true
          original_get_shipping_cost
        else
          if shipping_categories_available? || order_has_bmgn_products?
            original_get_shipping_cost(true)
          else
            return 0
          end
        end
      end

      def new_shipping_charges(country)
        country_code = RequestStore.fetch("country_code_#{country}".freeze) do
          Country.find_by_namei_cached(country).try(:[],:iso3166_alpha2)
        end
        promotion_pipelines = PromotionPipeLine.active_promotions.app_source('desktop')
        var_hash = if country_code.present?
                        frozen_key="free_shipping_#{country_code}".freeze
                        free_shipping = RequestStore.fetch(frozen_key) do
                          promotion_pipelines.find_by_name(frozen_key) ||  promotion_pipelines.find_by_name("free_shipping")
                        end
                        free_shipping.try(:variables_hash)
                      else
                        free_shipping = RequestStore.fetch(:free_shipping) do
                          promotion_pipelines.find_by_name("free_shipping")
                        end
                        free_shipping.try(:variables_hash)
                      end
        start_date_value = if country_code.present?
                            promotion_pipelines.find_by_name("free_shipping_#{country_code}").try(:start_date) ||  promotion_pipelines.find_by_name("free_shipping").try(:start_date)
                          else
                            promotion_pipelines.find_by_name("free_shipping").try(:start_date)
                          end
        created_date = self.created_at || Time.zone.now
        shipping_rate = nil
        free_shipping_rate = 0
        free_shipping_rate = JSON.parse(var_hash)['free_shipping_rate'] if var_hash.present?
        shipping_rate = free_shipping_rate if shipping_rate.nil? && free_shipping_rate.present?

        if shipping_rate.present?
          if free_shipping_available?(country)  && start_date_value.present? && (created_date >= start_date_value)
            return 0
          else
            return true
          end
        end
      end

      def free_shipping_available?(country)
        available = false
        item_total = total.to_f + (refund_discount.to_f * currency_rate.to_f)
        amount = Promotions.free_shipping_at(country)
        if (category_name = Promotions.shipping_offer_on_category).present?
          cat_line_items = []
          category_wise_total = 0
          self.designer_orders.each do |designer_order|
            designer_order.line_items.not_canceled.each do |line_item|
              if (Category.getids(category_name) & line_item.design.categories.pluck(:id)).present?
                cat_line_items << line_item
              end
            end
          end
          cat_line_items.each do |line_item|
            category_wise_total += (line_item.price * line_item.quantity)
          end
          category_wise_total = 0 if category_wise_total.nil?
          if category_wise_total.present?
            available = true if category_wise_total >= amount
          end
        elsif order_has_exclude_free_shipping_item?
          item_total_exclude_shipping = self.total_price_currency_for_exclude_shipping
          available = true if amount.present? && free_shipping_on_country?(country) && item_total_exclude_shipping >= amount
        else
          available = true if item_total >= amount && self.free_shipping_on_country?(country)
        end
        available
      end
      
      def order_has_exclude_free_shipping_item?
        present = false
        self.designer_orders.each do |d|
          unless d.state == "canceled" || self.state == 'cancel'
            d.line_items.not_canceled.each do |item|
              if (item.design.categories.pluck(:id) & EXCLUDE_FREE_SHIPPING_CATEGORIES).present? || [1,2,3].include?(item.buy_get_free)
                present = true
              end
            end
          end
        end
        return present
      end
    end
  end
end

module Promotions::Design
  def self.included base
    base.class_eval do

      # Calculate global_discount based on promotion's discount percent
      #
      # == Returns:
      # Float
      #
      def global_price_percent(active_promotions,country_code=nil)
        country_code||= Design.country_code
        RequestStore.cache_fetch("global_price_percent_#{country_code}".freeze, expires_in: 1.day) do
          promotion_pipelines = active_promotions
          var_hash = if country_code.present?
                        promotion_pipelines.find_by_name("global_sale_#{country_code}").try(:variables_hash) || promotion_pipelines.find_by_name("global_sale").try(:variables_hash)
                      else
                        promotion_pipelines.find_by_name("global_sale").try(:variables_hash)
                      end
          var_hash.present? ? JSON.parse(var_hash)['global_discount_percent'].to_i : 0
        end
      end

      def category_wise_global_price_percent(active_promotions,country_code=nil)
        country_code||= Design.country_code
        RequestStore.cache_fetch("category_wise_global_price_percent_#{country_code}".freeze, expires_in: 1.day) do

          promotion_pipelines = active_promotions
          var_hash = if country_code.present?
                        promotion_pipelines.find_by_name("category_discount_sale_#{country_code}").try(:variables_hash) || promotion_pipelines.find_by_name("category_discount_sale").try(:variables_hash)
                      else
                        promotion_pipelines.find_by_name("category_discount_sale").try(:variables_hash)
                      end
          var_hash.present? ? JSON.parse(var_hash)['global_discount_percent'].to_i : 0
        end
      end

      def designer_wise_global_price_percent(active_promotions,designer_id,country_code=nil)
        country_code||= Design.country_code
        var_hash = if country_code.present?
                      active_promotions.find_by_name("designer_discount_sale_#{country_code}").try(:variables_hash) || active_promotions.find_by_name("designer_discount_sale").try(:variables_hash)
                    else
                      active_promotions.find_by_name("designer_discount_sale").try(:variables_hash)
                    end
        discount_percent = 0
        if var_hash.present?
          discount_percentages = JSON.parse(var_hash)['global_discount_percent'].split(',').map(&:to_i)
          designer_ids = JSON.parse(var_hash)['designers_on'].split(',').map(&:to_i)
          discount_hash = Hash[designer_ids.zip(discount_percentages)]
          discount_percent = discount_hash[designer_id]
        end
          return discount_percent
      end

      def global_discount_on_amount(active_promotions,country_code=nil)
        country_code||= Design.country_code
        RequestStore.cache_fetch("global_discount_on_amount_#{country_code}".freeze, expires_in: 1.day) do
          promotion_pipelines = active_promotions
          var_hash = if country_code.present?
                        promotion_pipelines.find_by_name("global_sale_#{country_code}").try(:variables_hash) || promotion_pipelines.find_by_name("global_sale").try(:variables_hash)
                      else
                        promotion_pipelines.find_by_name("global_sale").try(:variables_hash)
                      end
          var_hash.present? ? JSON.parse(var_hash)['amount_on'].to_i : 0
        end
      end

      # Calculate effective price based on global sale and discount_price
      #
      # == Returns:
      # Integer
      #
      def effective_price(return_type = RETURN_SCALED, country_code: nil)
        country_code ||= Design.country_code
        if direct_dollar_applicable?(country_code)
          return direct_dollar_promotion(country_code)
        end
        @effective_price ||= { RETURN_NORMAL => {}, RETURN_SCALED => {} }
        @effective_price[return_type][country_code] ||= begin
          price_without_scale = (self.price(RETURN_NORMAL) * (100 - effective_discount_percent(country_code)) / 100.0).to_i
          if return_type == RETURN_SCALED
            (price_without_scale * self.get_scale(country_code)).to_i
          else
            price_without_scale
          end
        end
      end

      def get_free_shipping_rate(country_code)
        conversion_rate = CurrencyConvert.currency_convert_memcached.find{|c| c.country_code == country_code}.rate
        Promotions.get_shipping_rate_promotion(conversion_rate, country_code, true)
      end

      def effective_price_for_country(country_code, return_type = RETURN_NORMAL)
        effective_price(return_type, country_code: country_code)
      end

      def direct_dollar_promotion(country_code = Design.country_code)
        promotion_pipelines = PromotionPipeLine.active_promotions.app_source('desktop')
        p_id = self.design_promotion_pipe_lines.map(&:promotion_pipe_line_id).first
        var_hash = if p_id.present?
          frozen_key="direct_dollar_promotion_id_#{p_id}".freeze
          direct_dollar_promotion = RequestStore.fetch(frozen_key) do
            promotion_pipelines.find_by_id(p_id) || {}
          end
          direct_dollar_promotion.try(:variables_hash)
        end
        if var_hash.present?
          var_hash = JSON.parse(var_hash)
          return var_hash["#{country_code}"].to_i if var_hash.keys.include?(country_code)
          0
        else
          return 0
        end
      end

      def direct_dollar_applicable?(country_code = Design.country_code)
        (self.class == Variant || self.buy_get_free == 4) && direct_dollar_promotion(country_code).between?(1, self.price)
      end

      # Calculate effective discount based on global sale and existing discount
      #
      # == Returns:
      # Integer
      #
      def effective_discount_percent(country_code = nil)
        country_code ||= Design.country_code
        if direct_dollar_applicable?(country_code)
          return (100 - ((self.effective_price / self.price.to_f) * 100).round())
        end
        @effective_discount_percent ||= {}
        @effective_discount_percent[country_code] ||= begin
          active_promotions = PromotionPipeLine.active_promotions
          discount_percent = self.discount_percent.to_f
          seller = self.is_a?(Variant) ? self.design.designer : self.designer
          global_discount_percent = if !seller.is_one_tire_designer? && discount_sale_on_amount?(active_promotions,country_code) && sale_on_country?(active_promotions, country_code)
            global_price_percent(active_promotions,country_code)
          end.to_f
          category_discount_percent = if category_discount_available?(active_promotions) && category_sale_on_country?(active_promotions, country_code)
            category_wise_global_price_percent(active_promotions,country_code)
          end.to_f
          designer_discount_percent = if designer_discount_available?(active_promotions, country_code)
            designer_wise_global_price_percent(active_promotions,seller.id,country_code)
          end.to_f
          Promotions.cumulative_discount_percent(discount_percent, global_discount_percent, category_discount_percent, designer_discount_percent, self.pair_product_discount.to_f).round
        end
      end

      #IF NO COUNTRY IS SPECIFIED IN PROMOTIONS TABLE(for an event) THIS RETURNS FALSE!
      def sale_on_country?(active_promotions,country=nil)
        if (country_code = Promotions.sale_discount_on_country(active_promotions,country)).present?
          country_code.split(',').include?(country||Design.country_code)
        else
          false
        end
      end

      def discount_sale_on_amount?(active_promotions,country_code=nil)
        self.discount_price >= global_discount_on_amount(active_promotions,country_code)
      end

      def category_sale_on_country?(active_promotions,country=nil)
        if (country_code = Promotions.category_discount_on_country(active_promotions,country)).present?
          country_code.split(',').include?(country||Design.country_code)
        else
          false
        end
      end

      def category_discount_available?(active_promotions)
        if (promotion_category_ids = Promotions.category_promotion_ids(active_promotions)).present?
          (self.category_ids & promotion_category_ids).present?
        end
      end

      def designer_discount_available?(active_promotions, country_code=nil)
        country_code ||= Design.country_code
        designer_discount_promotion = RequestStore.cache_fetch("designer_discount_promotions_#{country_code}".freeze, expires_in: 1.day) do
          if country_code.present?
            active_promotions.find_by_name("designer_discount_sale_#{country_code}") || active_promotions.find_by_name("designer_discount_sale") || []
          else
            active_promotions.find_by_name("designer_discount_sale") || []
          end
        end
        country_code_list = designer_discount_promotion.try(:country_code) || ''
        var_hash = designer_discount_promotion.try(:variables_hash)
        designer_ids = var_hash.present? ? JSON.parse(var_hash)['designers_on'] : ''
        designerid = is_a?(Variant) ? design.designer_id : designer_id
        country_code_list.split(',').include?(country_code) && designer_ids.split(',').collect(&:to_i).include?(designerid)
      end

      def free_stitching_available?
        @free_stitching_available = if @free_stitching_available.nil?
          Design.country_code != 'IN' && Promotions.design_free_stitching_active? &&
          if active_promotion_pipe_lines.loaded?
            active_promotion_pipe_lines.any? do |promotion|
              promotion.is_design_free_stitching_promotion? &&
              (promotion.country_code.blank? || promotion.country_code.include?(Design.country_code)) &&
              (promotion.app_source.blank? || promotion.app_source.include?(APP_SOURCE[0].downcase))
            end
          else
            active_promotion_pipe_lines.design_free_stitching_promotions.for_country(Design.country_code).app_source(APP_SOURCE[0].downcase).exists?
          end
        else
          @free_stitching_available
        end
      end
    end
  end
end