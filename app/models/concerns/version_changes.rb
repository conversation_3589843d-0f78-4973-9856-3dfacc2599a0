module VersionChanges
  extend ActiveSupport::Concern

  included do
    after_commit :update_version_with_changes, on: :update
  end

  private

  def update_version_with_changes
    last_version = versions.order(id: :desc).first
    return unless previous_changes.present? && last_version

    tracked_changes = previous_changes.except('updated_at').presence || previous_changes

    modifier = if Account.current_account.present?
      accountable = Account.current_account
      "#{accountable.accountable_type}:#{accountable.id}:#{accountable.email}"
    else
      self.class.to_s
    end

    current_object = PaperTrail.serializer.dump(self.attributes)

    changes_data = {
      'object'=>YAML.load(current_object),
      'changes'=>{
        tracked_changes: tracked_changes.each_with_object({}) do |(key, values), hash|
          hash[key] = { from: values[0], to: values[1] }
        end,
        modifier: modifier
      }
    }

    last_version.update_columns(
      object: YAML.dump(changes_data),
      whodunnit: modifier
    )
  end
end