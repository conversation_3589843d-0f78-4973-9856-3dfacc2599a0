# == Schema Information
#
# Table name: designs
#
#  id               :integer         not null, primary key
#  description      :text
#  title            :string(255)
#  price            :integer
#  designer_id      :integer
#  eta              :integer
#  quantity         :integer         default(1)
#  color            :string(255)
#  specification    :text
#  created_at       :datetime
#  updated_at       :datetime
#  published        :boolean         default(FALSE)
#  grade            :integer
#  discount_percent :integer
#  discount_price   :integer
#  cached_slug      :string(255)
#

class Design < ActiveRecord::Base
  extend FriendlyId
  acts_as_taggable
  acts_as_taggable_on :collections
  acts_as_taggable_on :domestic_tags, :international_tags
  acts_as_taggable_on :catalogues
  acts_as_followable
  has_many :wishlists
  has_many :revenue_per_views
  has_many :design_grading_tags
  has_many :grading_tags, through: :design_grading_tags
  include UpdateUnbxd
  include SidekiqHandleAsynchronous
  extend DynamicTemplateMethod
  dynamic_template_fields :price,:quantity,:discount_percent,:discount_price,:return_count,:total_review,:return_initiated_count,:wishlists_count,:likes_count,:state,:designable_type,:created_at,:updated_at,:last_in_stock,:last_sold_out,:last_seller_out_of_stock,:last_blocked,:last_banned,:deleted_on,:reject_on,:review_on,:last_posted_at,:ready_to_ship,:mirraw_certified,:skip_qc,:premium,:average_rating,:quality_level,:graded_rating
  include DesignerQuantity
  include LocalyticsNotification
  THRESHOLD_QTY = SystemConstant.get('THRESHOLD_QTY_DESIGN_NOTIFICATION').to_i
  THRESHOLD_AMT = SystemConstant.get('THRESHOLD_AMT_DESIGN_NOTIFICATION').to_i
  LOCALYTICS_BATCH_SIZE = SystemConstant.get('LOCALYTICS_BATCH_SIZE').to_i

  UNSTITCH_POPUP_ENABLE = ENV['UNSTITCH_POPUP_ENABLE'] == 'true'
  VARIANT_PRICE_RANGE_ENABLE = ENV['VARIANT_PRICE_RANGE_ENABLE'] == 'true'

  #do not remove promise, because of request store
  if ENV['CLUSTER_CATEGORY_WISE_IDS'].to_s == '0'
    CLUSTER_CATEGORY_WISE_IDS = 0
  else
    CLUSTER_CATEGORY_WISE_IDS = promise do
      elligible_ids = []
      Category.where(id: ENV['CLUSTER_CATEGORY_WISE_IDS'].to_s.split(',')).each do |category|
        elligible_ids.push(*category.cached_self_and_descendants_id)
      end
      elligible_ids
    end
  end
  DESIGN_KEY_SPEC_SUB_GROUP = {
    'Saree' => {'saree' => ['color','fabric'].freeze,
                'blouse' => ['color','fabric'].freeze},
    'SalwarKameez' => {'kameez' => ['color','fabric'].freeze,
                       'dupatta' => ['color','fabric'].freeze,
                       'bottom' => ['color','fabric'].freeze}.freeze,
    'Kurti' => {'kameez' => ['color','fabric'].freeze,
                'dupatta' => ['color','fabric'].freeze,
                'bottom' => ['color','fabric'].freeze}.freeze,
    'Lehenga' => {'kameez' => ['color','fabric'].freeze,
                  'dupatta' => ['color','fabric'].freeze,
                  'bottom' => ['color','fabric'].freeze}.freeze,
    'Jewellery' => {'other_data' => ['color','work'].freeze}.freeze,
    'HomeDecor' => {'other_data' => ['color','work','material'].freeze}.freeze,
    'Bag' => {'other_data' => ['color','material'].freeze}.freeze,
    'Jacket' => {'other_data' => ['color','work','material'].freeze}.freeze,
    'Turban' => {'other_data' => ['color','material'].freeze}.freeze
  }.freeze

  CREATED_AT_BUCKET_SIZE = 15.days.to_i

  STITCHING_ALLOWED_DESIGNABLE_TYPES      = ['SalwarKameez', 'Lehenga', 'Saree', 'Kurti'].freeze
  STITCHING_WITH_VARIANT_DESIGNABLE_TYPES = ['SalwarKameez', 'Kurti'].freeze
  REMOVE_CATALOG_HASH={all: {value:0,remove:true},domestic:{value: 2},international:{value: 1},no: {},banned: {value: 3}}.freeze
  HEIGHT_RANGE_FOR_STANDARD_ADDON = {
    anarkali: {default: 'on_the_ankle', 68 => 'on_the_calf', 74 => 'between_calf_and_knee'}.freeze,
    salwar_kameez: {default: 'on_the_knee', 73 => 'above_to_knee'}.freeze
  }.freeze
  VENDOR_ADDON_CATEGORY = SystemConstant.get('VENDOR_ADDON_CATEGORY')

  belongs_to :designable, :polymorphic => true
  belongs_to :design_group
  has_many :in_stock_siblings, through: :design_group
  has_many :sibling_designs, through: :design_group
  has_many :design_addons
  has_many :addon_products, through: :design_addons
  has_many :in_stock_addon_products, -> { select('designs.*, design_addons.discount_percent as addon_discount_percent') }, through: :design_addons
  has_many :reviews
  has_one :video, as: :viewable
  has_one :design_cluster
  has_many :design_performances, dependent: :destroy
  has_many :performance_metric_values, as: :performer
  has_many :design_promotion_pipe_lines
  has_many :active_promotion_pipe_lines, -> {where('? between promotion_pipe_lines.start_date and promotion_pipe_lines.end_date ', Time.zone.now)}, through: :design_promotion_pipe_lines, class_name: 'PromotionPipeLine', source: :promotion_pipe_line
  has_many :dynamic_size_charts
  has_many :design_shipper_weights

  has_shortened_urls
  # serialize :dom_grade, Hash # ActiveRecord::Coders::Hstore
  # serialize :int_grade, Hash #ActiveRecord::Coders::Hstore
  has_paper_trail
  include VersionChanges
  scoped_search :on => [:title, :id, :design_code]

  searchable(include: [:approved_reviews, :dynamic_prices, :design_grading_tags, :grading_tags, :categories,:tags,:collections, :design_promotion_pipe_lines, :performance_metric_values, design_cluster: [other_clusters: [cluster_related_design: :categories]],designer: :odr_90, property_values: :property,variants: :option_type_values]) do
    text :category_name, :boost => 5.0, :more_like_this => true  do |design|
      category_names = []
      design.categories.each do|category|
        category_names.push *category.p_name.to_s.split(' ')
      end
      category_names.uniq.join(' ')
    end
    text :title, :boost => 4.0, :more_like_this => true
    text :tag_list, :boost => 2.0, :more_like_this => true do |design|
      design.tags.collect(&:name).join(',')
    end
    text :designer_name, :boost => 1.0, :more_like_this => true do |design|
      design.designer.name if design.designer.present?
    end
    text :product_id do |design|
      design.id.to_s
    end
    float :carat do |design|
      if design.designable_type == "Jewellery" && (carat = design.designable.try(:carat).presence).present?
        carat.to_f
      end
    end
    integer :category_ids, :multiple => true do |design|
      if design.consider_cluster_attrs?
        design.design_cluster.common_category_ids || []
      else
        design.category_ids
      end
    end
    integer :category_parents, :multiple => true
    integer :cod_shipper_ids, multiple: true do
      designer.cod_shipper_ids if designer.try(:cod)
    end
    text :description, :more_like_this => true do |design|
      design.description.to_s.gsub(/[[:cntrl:]]/,' ')
    end
    text :specification, :more_like_this => true do |design|
      design.specification.to_s.gsub(/[[:cntrl:]]/,' ')
    end
    text :keywords, as: :keywords do
      boost_keyword_hash[1]
    end
    text :imp_keywords, as: :imp_keywords do
      boost_keyword_hash[2]
    end
    string :state
    integer :price
    integer :discount_price_bucket
    integer :created_at_bucket
    integer :sell_count_bucket
    integer :clicks_bucket
    integer :grade_bucket
    integer :grade_bucket_mobile
    integer :grade do |design|
      design.get_grade(:grade).to_i
    end
    integer :international_grade do |design|
      design.get_grade(:international_grade).to_i
    end
    integer :international_grade_mobile do |design|
      design.get_grade(:international_grade_mobile).to_i
      # design.international_grade_mobile.to_i
    end
    integer :grade_mobile do |design|
      design.get_grade(:grade_mobile).to_i
    end
    integer :dom_grade do |design|
      design.get_grade(:dom_grade,DOM_GRADE_VARIABLE)[DOM_GRADE_VARIABLE].to_i
    end
    integer :int_grade do |design|
      design.get_grade(:int_grade,INT_GRADE_VARIABLE)[INT_GRADE_VARIABLE].to_i
    end
    integer :category_grade do |design|
      design.get_grade(:dom_grade,DOM_CATEGORY_GRADE_VARIABLE)[DOM_CATEGORY_GRADE_VARIABLE].to_i
    end

    integer :dom_grade_mob do |design|
      design.get_grade(:dom_grade,DOM_GRADE_VARIABLE_MOBILE)[DOM_GRADE_VARIABLE_MOBILE].to_i
    end
    integer :int_grade_mob do |design|
      design.get_grade(:int_grade,INT_GRADE_VARIABLE_MOBILE)[INT_GRADE_VARIABLE_MOBILE].to_i
    end

    float :store_international_grade do |design|
      design.store_grades(:international_grade)
    end

    float :store_grade do |design|
      design.store_grades(:grade)
    end

    integer :designer_id
    integer :discount_percent
    time :created_at, trie: true do |design|
      design.cluster_common_created_at
    end
    integer :discount_price
    integer :scaled_discount_price do |design|
      #NOTE As scale for all the international countries are same
      (design.discount_price * design.get_scale('US')).to_i
    end
    integer :sell_count
    integer :in_catalog_one
    integer :average_rating do |design|
      design.average_rating.to_i.zero? && design.designer.present? ? design.designer.average_rating : design.average_rating
    end
    integer :designer_rating do |design|
      design.designer.average_rating.to_i if design.designer.present?
    end
    integer :review_count do |design|
      design.approved_reviews.size
    end
    integer :buy_get_free do |design|
      design[:buy_get_free]
    end
    float :graded_rating
    integer :property_value_ids, :multiple => true do |design|
      if design.consider_cluster_attrs?
        (design.property_value_ids.push(*design.design_cluster.common_property_value_ids)).uniq
      else
        design.property_value_ids
      end
    end

    integer :option_type_value_ids, :multiple => true do |design|
      design.get_option_type_values(true)
    end

    text :collection_list, more_like_this: true do |design|
      design.collections.collect(&:name).join(',')
    end

    text :catalogue_list, more_like_this: true do |design|
      design.catalogues.collect(&:name).join(',')
    end
    float :odr_90_day do |design|
      designer = design.designer
      designer.present? ? designer.odr_90.try(:value).to_f.round(2) : 10000
    end

    string(:cluster_id_str) { |d| d.design_cluster.present? ? d.design_cluster.cluster_id.to_s : "#{d.id}_0" }
    integer :design_cluster_score do |design|
      design.design_cluster_score
    end

    integer :cluster_winner do |design|
      design.is_winner? ? 1 : 0
    end

    boolean :premium
    integer :eta
    boolean :featured_product
    float :clicks_impression do |design|
      design.performance_metric_cibs(1).round(3)
    end
    float :trending_designs do |design|
      design.performance_metric_cibs(2).round(3)
    end

    integer :sell_count_30 do |design|
      design.metric_sales_30
    end

    dynamic_integer :graderank , separator: "_" do
      design_grading_tags.inject({}) do |hash, design_grading_tags|
        hash.merge( (design_grading_tags.grading_tag.name.to_s).to_sym => design_grading_tags.rank)
      end
    end

    # boolean :promoted do |design|
    #   design.grading_promotion.try(:promoted?) || design.designer.grading_promotion.try(:promoted?)
    # end
    # time :deal_start_time do |design|
    #   if (active_deal = design.design_promotion_pipe_lines.active_flash_deals.first).present?
    #     active_deal.start_date.to_datetime
    #   else
    #     '2017-10-05 00:00:00'.to_datetime
    #   end
    # end

    # time :deal_end_time do |design|
    #   if (active_deal = design.design_promotion_pipe_lines.active_flash_deals.first).present?
    #     active_deal.end_date.to_datetime
    #   else
    #     '2017-10-05 00:00:00'.to_datetime
    #   end
    # end

  end

  scope :published, -> {where(state: 'in_stock', in_catalog_one: [nil, 1, 2]) }
  scope :in_stock, -> {where(state: 'in_stock') }
  scope :not_in_cluster, -> {where('designs.state not in (?)',['banned','reject','review','delete_by_mirraw','delete_by_designer'])}
  scope :desc_graded, -> { order('designs.grade DESC, designs.id DESC') }
  scope :int_desc_graded, -> { order('designs.international_grade DESC, designs.id DESC') }
  scope :graded, -> { order('designs.grade ASC, designs.id DESC') }
  scope :unpublished, -> { where("designs.state = 'blocked' or designs.state = 'seller_out_of_stock' or designs.state = 'sold_out'") }
  scope :deleted, -> { where("designs.state = 'delete'") }
  scope :banned, -> { where("designs.state = 'banned'") }
  scope :oos, -> { where("designs.state = 'seller_out_of_stock' or designs.state = 'delete'") }
  scope :pubun, -> { order('designs.published ASC') }
  scope :newness, -> { order('designs.id DESC') }
  scope :retail, -> { where(retail: true) }
  scope :review, -> { where(state: 'review') }
  scope :reject, -> { where(state: 'reject') }
  scope :processing, -> { where(state: 'processing') }
  scope :featured_product, -> { where(featured_product: true) }
  scope :created_between, -> (start_date, end_date) { where("created_at >= ? AND created_at <= ?", start_date, end_date )}
  scope :less_than, -> (value){ where('price < (?)', value) if value.present? }
  scope :greater_than, -> (value){ where('price >= (?)', value) if value.present? }
  scope :color, -> (value){ where('color = (?)', value) if value.present? }
  scope :tag, ->  (value) { tagged_with(value) if value.present? }
  scope :with_brand, -> (brands) { where('designer_id IN (?)', brands) if brands.present? }
  scope :in_category, -> (value) { where('categories.id' => Category.getids(value)) if value.present? }
  scope :not_in_category, -> (value) { where('categories.id NOT IN (?)', Category.getids(value)) if value.present? }
  scope :h2l, -> { order('count(designs.id) desc') }
  scope :alphabetical, -> { order('property_values.name') }
  scope :facebook_odr_design, -> { where(designer_id: SystemConstant.get('FACEBOOK_ODR_DESIGNERS').to_s.split(',')) }
  scope :not_banned_in_catalog, -> (value) { where('in_catalog_one is null or in_catalog_one not in (?)', [0,3] << (value == :international ? 1 : 2))}
  scope :active_designer_designs, -> {where('designers.state_machine NOT IN (?)', ['banned', 'on_hold', 'inactive'])}

  friendly_id :title_and_category, use: [:slugged, :finders, :history], slug_column: :cached_slug, sequence_separator: '--'


  # def slug_candidates
  #   [
  #     title_and_category,
  #     [title_and_category, id],
  #   ]
  # end

  # def should_generate_new_friendly_id?
  #   !new_record? && (cached_slug.blank? || title_changed?)
  # end

  belongs_to :designer_batch
  has_many :designer_issues
  belongs_to :designer
  belongs_to :designer_collection
  has_many :images
  has_one  :master_img, -> { where(kind: 'master').limit(1) }, class_name: 'Image'
  has_one :grading_promotion, as: :promotable
  has_many :delay_images, dependent: :destroy
  has_many :delay_designables, dependent: :destroy
  has_many :delay_design_groups, dependent: :destroy
  has_many :line_items
  has_many :tickets
  has_many :survey_answers, through: :line_items
  has_and_belongs_to_many :categories, -> {order('category_type')} do
    def hsn_code
      # freq = collect(&:hsn_code).inject(Hash.new(0)) { |h,v| h[v] += 1; h }
      # max = freq.values.max
      # freq.select { |k, f| f == max }.keys.first
      collect(&:hsn_code).first
    end
  end
  has_many :designs_video_listing
  has_many :video_listings, through: :designs_video_listing
  has_many :categories_designs, foreign_key: :design_id, dependent: :delete_all
  has_and_belongs_to_many :property_values
  has_and_belongs_to_many :custom_color_property_values, :class_name => 'PropertyValue', :conditions => proc {"property_id in (20,45,46)"}, :uniq => true
  has_many :approved_reviews, -> (object) { where("approved = ?", true) }, class_name: 'Review'
  has_many :addon_type_values
  has_many :addon_types, through: :addon_type_values
  has_many :variants, inverse_of: :design, dependent: :destroy
  has_many :variants_with_option_type_id, -> { joins(:option_type_values_variants) }, class_name: 'Variant'
  has_many :option_type_values, through: :variants
  has_many :option_types, -> { limit(1) }, through: :option_type_values
  has_many :customized_option_type_values, :class_name => 'OptionTypeValue', through: :variants #, :limit => 5  # used while generating facebook feed
  has_many :claim_requests
  belongs_to :coupon
  has_many :follows, -> { where ("follows.followable_type = 'Design'")}, :foreign_key => 'followable_id'
  has_many :stitching_measurements
  has_many :dynamic_prices
  has_many :sized_designs
  has_many :dynamic_price_for_current_country, -> { where('country_code = ?', SWITCH_DP_TO_INTERNATIONAL ? 'US' : "#{Design.country_code || 'US'}").limit(1)}, :class_name => 'DynamicPrice' # should be used for preloading data
  has_many :dynamic_price_for_domestic, -> { where('country_code = ?', 'IN').limit(1)}, :class_name => 'DynamicPrice'
  #has_many :dynamic_price_for_current_country, -> { proc {SWITCH_DP_TO_INTERNATIONAL ? "country_code = 'US'" : "country_code = '#{Design.country_code || 'US'}'"}} , :class_name => 'DynamicPrice' #, :limit => 1 # should be used for preloading data
  has_one  :design_score
  accepts_nested_attributes_for :images, :reject_if => proc { |attrs| attrs['photo'].blank? }, :allow_destroy => true
  accepts_nested_attributes_for :categories
  accepts_nested_attributes_for :variants, reject_if: proc { |attrs| attrs['id'].blank? && (attrs['price'].to_i < MIN_PRICE_PER_PRODUCT || attrs['designer_quantity'].to_i <= 0) }
  accepts_nested_attributes_for :addon_type_values
  attr_accessible :description,:title, :designer_id, :designer_batch_id, :price, :eta, :designer_quantity, :quantity, :specification, :discount_percent, :discount_price, :cached_slug, :design_code, :weight, :state, :package_details, :gst_rate, :hsn_code, :tag_list, :min_wholesale_quantity, :min_wholesale_price, :retail, :images_attributes, :category_ids, :variants_attributes, :collection_list, :designable_attributes, :pattern, :region, :embellish, :accessories, :property_value_ids, :image_ids, :in_stock_warehouse, :reordering_percent, :ordering_quantity, :color, :transfer_price

  before_destroy :ensure_not_referenced_by_any_line_item, lambda { UnbxdItem.delete_feeds self.id }

  # provides a thread safe per request basis class level variabel using requeststore
  include RequestAccessor
  rattr_accessor :country_code
  attr_accessor :skip_before_save_callback
  attr_accessor :skip_after_save_callback
  attr_accessor :skip_flush_cache_callback
  attr_accessor :validate_category
  attr_accessor :validate_variants
  attr_accessor :validate_property_values
  attr_accessor :version
  attr_accessor :pair_product_discount
  attr_accessible :grade, :international_grade, :international_grade_mobile, :grade_mobile

  delegate :is_transfer_model?, to: :designer
  # cattr_reader :per_page
  # @@per_page = 40

  validates_inclusion_of :quantity, :in => (1..501).to_a, :on => :create
  validates_numericality_of :quantity, less_than: 2**30 , only_integer: true, message: 'maximum limit crossed'
  validates_numericality_of :transfer_price, greater_than: 0, only_integer: true, if: -> design { design.is_transfer_model? }
  validates_numericality_of :price, :greater_than_or_equal_to => MIN_PRICE_PER_PRODUCT, :less_than_or_equal_to => MAX_PRICE_PER_PRODUCT, :only_integer => true
  validates_numericality_of :actual_discount_percent
  validates_inclusion_of :actual_discount_percent, :in => (0..95).to_a, :message => 'Discount_percent should be less than to 95 percent'
  validates_numericality_of :grade, :only_integer => true, :if => Proc.new {|design| design.grade.present? }
  validates_length_of :title, :within => 3..120
  validates_format_of :package_details, :with => /\d{1,3}[\w ]+(::|$)/i, :on => :create
  validate :atleast_one_image_exists
  validate :atleast_one_category_exists
  # validate :price_lock_for_designer
  validate :has_valid_category, if: :validate_category
  validate :hsn_code_length
  validate :gst_rate_value
  validate :has_valid_variants, if: :validate_variants
  validate :has_valid_property_values, if: :validate_property_values
  #validates_associated :designable, if: :validate_property_values

  validate :design_code_exists?, if: -> design {design.design_code.present? && (design.design_code_changed? || (design.state_changed? && design.state == 'in_stock'))} #:design_code, :uniqueness => {:scope => :designer_id}, :on => :create

  before_create :initialize_fields
  before_save :update_fields_trigger, unless: :skip_before_save_callback
  # Need to optimize notification callback
  # :less_quantity_notification_android, :price_drop_notification_android,

  include DesignEmailable

  after_save :set_master_image, unless: :skip_after_save_callback
  after_commit :update_design_state, unless: :skip_after_save_callback
  # fires :upload, :on   => :create,
  #                :actor => :designer

  # after_create :add_designable, :add_saree_addons, :add_salwar_lahenga_addon
  after_create :add_designable
  # Updates each design to Unbxd
  def send_to_unbxd
    #sidekiq_delay(queue: 'low').update_unbxd_design(self) if ((self.unbxd_status != 'indexed' && self.state != 'in_stock') || (self.changes.has_key?('in_catalog_one')) || ['ready_to_indexed', 'delete_from_unbxd'].exclude?(self.unbxd_status))
  end

  def quantity=(value)
    super
    check_if_quantity_changed
  end

  def check_if_quantity_changed(changed_by = nil)
    if quantity_changed? && quantity_was <= 0 && quantity > 0 && designer_quantity > 0
      self.last_in_stock_by = changed_by || Account.current_account.try(:accountable_type) || 'System'
    end
  end

  def going_to_sold_out
    self.quantity_changed? && self.quantity_was > 0 && self.quantity <= 0
  end

  def mark_design_seller_oos
    self.skip_after_save_callback = true
    if self.unavailable!
      self.send_to_unbxd
      return true
    end
    return false
  end

  def self.update_catalog(element: nil, email: nil, catalog: nil)
    unless catalog.present?
      design_catalog_entries,filepath = Design.load_design_catalog_entries(element)
      element = filepath
      Designer::IN_CATALOG.each do |key, value|
        Design.public_send("#{value}_catalog_remove",design_catalog_entries[key])
      end
    else
      Designer.show_in_catalog(catalog, element)
    end
    DesignCatalogUpdateMailer.catalog_changed(element: element, email: email, catalog: catalog)
  end

  def self.load_design_catalog_entries(element)
    filepath = AwsOperations.get_aws_file_path(element)
    grouped_designs = Hash.new {|h,k| h[k] = [] }
    open(filepath) do |f|
      csv = CSV.new(f, headers: true)
      while row = csv.shift
        if row['Design_id'].present? && row['Catalog'].present?
          grouped_designs[row['Catalog'].downcase] << row['Design_id']
        end
      end
    end
    return grouped_designs,filepath
  end

  REMOVE_CATALOG_HASH.each do |key,hash|
    self.send :define_method,"#{key}_catalog_remove" do
      #usage: call from design object
      self.in_catalog_one=hash[:value]
      self.save!
      Sunspot.remove!(self) if hash[:remove].present?
      if [:banned, :all, :no].include?(key) && (new_design_cluster = self.design_cluster).present?
        new_design_cluster.update_column(:state_multiplier, ([:banned,:all].include?(key) ? 0 : 1))
        new_design_cluster.recalculate_winner
      end
      Bestseller.delete_entries(design_ids: [self.id],catalog: key)
    end
    self.send :define_singleton_method,"#{key}_catalog_remove" do |*design_ids|
      #usage: either use scope or pass design ids as arguments
      if design_ids.present?
        designs = Design.where(id:design_ids).select(:id)
      else
        designs = select(:id)
      end
      designs.find_in_batches do |design_batch|
        ids = design_batch.collect(&:id)
        Design.where(id: ids).update_all(in_catalog_one: hash[:value])
        if hash[:remove].present?
          Sunspot.remove!(design_batch)
        else
          Sunspot.index(design_batch)
        end
        if [:banned, :all, :no].include?(key)
          cnt = DesignCluster.where(design_id: ids).update_all(state_multiplier: ([:banned,:all].include?(key) ? 0 : 1))
          DesignCluster.recalculate_winners_in_bulk(ids) if cnt > 0
        end
        Bestseller.delete_entries(design_ids: ids, catalog: key)
      end
    end
  end

  #To check design's catalog state
  {:all => nil, :removed => 0, :domestic => 1, :international => 2, :designer_banned => 3}.each do |key,value|
    self.send :define_method,"is_#{key}?" do
      self.in_catalog_one == value
    end
  end

  PerformanceMetricValue::SALES_METRIC_DAYS.each do |days|
    metric_name = "sales_#{days}"
    send :define_method, "metric_#{metric_name}" do
      if performance_metric_values.loaded?
        performance_metric_values.find{|pmv| pmv.metric_name == metric_name}.try(:value).to_i
      else
        performance_metric_values.find_by(metric_name: metric_name).try(:value).to_i
      end
    end
  end

  PerformanceMetricValue::SALES_METRIC_DAYS.each do |num_days|
    type = PerformanceMetricValue::SALES_METRIC_TYPE
    type.each do |type|
      metric_name = "sales_#{type}_#{num_days}"
      send :define_method, "metric_#{metric_name}" do
        performance_metric_values.find_by(metric_name: metric_name, created_at: (num_days.days.ago..Date.yesterday), performer_id: self.id).try(:value).to_i
      end
    end
  end

  def self.can_edit_price(design_hash,design,can_edit)
    unless can_edit
      if ((design_hash[:price].present? && design[:price] != design_hash["price"].to_i) || (design_hash[:discount_percent].present? && design[:discount_percent] != design_hash["discount_percent"].to_i))
        return false
      elsif design_hash["variants_attributes"].present?
        variants = design_hash["variants_attributes"]
        variants.reject!{|k,v| v[:id].blank?}
        vars = design.variants
        if !variants.empty?
          variants.each do |_,var|
            v = vars.find{|variant| var[:id].to_i == variant.id}
            if v.present? && v[:price] != var[:price].to_i
              return false
            end
          end
        end
      end
    end
    return true
  end

  def ga_data(other_data={})
    {
      id: id.to_s, name: "#{id}_", category: categories.first.try(:name),
      brand: designer.cached_slug
    }.merge(other_data.compact)
  end

  def approx_weight(country_code = Design.country_code)
    if country_code.present? && (country_hash = CurrencyConvert.currency_convert_memcached.find{|c| c.country_code == country_code}).present?
      conversion_rate = country_hash.rate
      on_design_rate = self.get_free_shipping_rate(country_code)
      return 0 if on_design_rate.to_i > 0 && (self.effective_price(country_code: country_code) / conversion_rate) >= on_design_rate && ((self.categories.pluck(:id) & EXCLUDE_FREE_SHIPPING_CATEGORIES).blank? && ![1,2,3].include?(self.buy_get_free))
    end
    categories.collect(&:weight).compact.max || 0
  end

  def free_shipping_eligible?
    conversion_rate = CurrencyConvert.currency_convert_memcached.find{|c| c.country_code == Design.country_code}.rate
    on_design_rate = Promotions.get_shipping_rate_promotion(conversion_rate, Design.country_code, true).to_i
    if on_design_rate > 0 && (self.effective_price / conversion_rate).round(2) >= on_design_rate && !([1,2,3].include?(self.buy_get_free) && PromotionPipeLine.bmgnx_hash.present?)
      return true
    end
  end

  # grade_geo = grade || international_grade
  def store_grades(grade_geo)
    if (['Saree', 'SalwarKameez', 'Lehenga', 'Kurti', 'Jewellery'].include?(self.designable_type) &&
        (multiplier = Design.max_grades).presence &&
        (multiplier = multiplier[self.designable_type][grade_geo.to_s]) > 0)
      multiplier + self.send(grade_geo).to_f
    else
      self.send(grade_geo)
    end
  end

  def consider_cluster_attrs?
    (CLUSTER_CATEGORY_WISE_IDS == 0 || (CLUSTER_CATEGORY_WISE_IDS & category_ids).present?) && design_cluster.present?
  end

  def cluster_common_created_at
    (consider_cluster_attrs? && design_cluster.common_created_at) || created_at
  end
  # input params - none
  # create designable for design based on design categories
  #
  # Returns object
  def add_designable
    if self.designable.blank?
      # Root categories for designables
      category_list = Hash.new
      category_list['SalwarKameez'] = 'salwar-kameez', 'dresses'
      category_list['Lehenga'] = 'bridal-lehengas', 'lehengas'
      category_list['Saree'] = 'sarees', 'bridal-sarees'
      category_list['Jewellery'] = 'jewellery', 'bridal-sets'
      category_list['Other'] = "shorts","shawls","skirts","tunics","tops","pyjamas","stole-and-dupattas","boxers","men-shoes","men-jackets","sculptures",'other-apparel','shoes','rakhi','gifts',"men-tshirts","kids","leggings","men-pyjamas","men-tshirts","leggings","men-pyjamas", "men"
      category_list['Bag'] = ['bags']
      category_list['HomeDecor'] = ['home-decor']
      category_list['Kurti'] = 'kurtas-and-kurtis', 'men-kurtas'

      # NOTE : Above list needs to be updated after the categories for Mens wear, Foot wear, Kids wear are created in live website

      design_root_categories_names = self.category_parents_name.collect{|c| c.downcase}
      category_list.each do |designable_type, category_names|
        if (design_root_categories_names & category_names).present?

          # Creates designables for the defined category
          self.designable = designable_type.constantize.create()
          break
        end
      end
      self.designable = Other.create() if self.designable.blank?
      self.save
    end
    if variants.present?
      category_id = get_plus_size_category_id
      CategoriesDesign.add_designs([id], category_id) if category_id.present?
    end
  end

  def get_plus_size_category_id
    max_size = PLUS_SIZE_CATEGORY_PARAM[designable_type][1]
    category_name = PLUS_SIZE_CATEGORY_PARAM[designable_type][0]
    get_category = false
    case designable_type
    when 'Saree'
      if (category_ids & Array.wrap(Category.getids('blouse'))).present? && is_plus_size?(max_size)
        get_category = true
      end
    when 'Kurti'
      get_category = true if is_plus_size?(max_size)
    when 'SalwarKameez'
      get_category = true if is_plus_size?(max_size)
    when 'Islamic'
      get_category = true if (category_ids & Array.wrap(Category.getids('islamic-kaftans'))).present? && is_plus_size?(max_size)
    when 'Other'
      legging_categories = Array.wrap(Category.getids('leggings')).push(*Category.getids('leggings-combo'))
      if (category_ids & legging_categories).present? && is_plus_size?(max_size)
        get_category = true
      end
    end
    Category.getid(category_name) if get_category
  end

  def is_plus_size?(plus_size)
    size_options = option_types.first.option_type_values.order(:position).select('upper(p_name) as upcase_name, position')
    if (plus_size_position = size_options.find{|option| option.upcase_name == plus_size}.try(:position)).present?
      return (size_options.select{ |option| option.position >= plus_size_position}.collect(&:upcase_name) & variants.joins(:option_type_values).where{quantity > 0}.pluck('upper(option_type_values.p_name)')).present?
    else
      return false
    end
  end

  def unstitched_variant
    variants.select{|v| v.stitched == false}.first
  end

  handle_asynchronously :add_designable, priority: 5

  def update_variant_quantity
    if self.variants.present?
      total_quantity, in_stock_by = self.variants.sum(:quantity), {}
      if (quantity == 0 && total_quantity > 0)
        SidekiqDelayGenericJob.perform_async(self.class.to_s, self.id ,"update_design_state")
        #self.sidekiq_delay.update_design_state
        in_stock_by = {last_in_stock_by: (Account.current_account.try(:accountable_type) || 'System')}
      elsif (quantity > 0 && total_quantity == 0)
        SidekiqDelayGenericJob.perform_async(self.class.to_s, self.id ,"update_design_state")
        #self.sidekiq_delay.update_design_state
      end
      self.update_columns({quantity: total_quantity}.merge!(in_stock_by))
    end
  end

  def update_design_state(account=nil)
    Account.current_account = if account.present?
      account.is_a?(Account) ? account: Account.find(account)
    else
      nil
    end
    quantity_status = self.check_quantity
    self.skip_after_save_callback = true
    if self.created_at == self.updated_at && ['review','review_pending'].include?(self.designer.state_machine)
      self.bad_designer
    elsif (self.state != 'reject' && self.state != 'on_hold' && self.state != 'seller_out_of_stock' && self.state != 'delete' && self.state != 'banned' && self.state != 'processing' && self.state != 'review') || (self.state == 'seller_out_of_stock' && quantity_status)
      if quantity_status && !self.designer.review_pending?
        self.available
      else
        # if self.pending_orders
        #   self.orders_pending_item_sold_out
        # else
          self.empty_stock
        # end
      end
    end
  end

  handle_asynchronously :update_design_state

  def design_cluster_score
    if design_cluster.present?
      # if (odr = designer.odr_90).present?
      #   odr_val = designer.odr_90.denominator_count > 50) ? (1/designer.odr_90.value) : MIRRAW_ODR
      # else
      #   odr_val = MIRRAW_ODR
      # end
      return design_cluster.score.to_i
    end
    return 0
  end

  def buy_get_free
    (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present? && (bmgnx_hash[:buy_get_free].to_i == 1) && (discount_price < bmgnx_hash[:filter].to_i) ? bmgnx_hash[:buy_get_free] : self[:buy_get_free]
  end

  def is_winner?
    design_cluster.blank? || self.id == design_cluster.winner_design_id
  end

  def get_grade(grade_column, hash_key = nil)
    if design_cluster.present?
      @high_grade_designs ||= design_cluster.other_clusters.collect(&:cluster_related_design).compact.flatten rescue []

      if hash_key.present?
        high_grade_design= @high_grade_designs.max_by{|d| d.send(grade_column)[hash_key].to_i}
      else
        high_grade_design= @high_grade_designs.max_by{|d| d.send(grade_column).to_i}
      end
      return high_grade_design.send(grade_column) if high_grade_design.present?
    end
    self.send(grade_column)
  end

  def get_colour_values
    self.property_values.to_a.keep_if{|pv| Property.get_color_properties.include?(pv.property_id)}.collect(&:name)
  end

  def created_at_bucket
    (cluster_common_created_at.to_i / CREATED_AT_BUCKET_SIZE).to_i * CREATED_AT_BUCKET_SIZE
  end

  # 2 MSB
  def discount_price_bucket
    discount_price = (self[:discount_price] || self.discount_price )
    discount_price < 256 ? 0 : discount_price & (3 <<  Math.log(discount_price,2).to_i - 1)
  end

  # 1 MSB
  def sell_count_bucket
    sell_count.to_i <= 0 ? 0 : 2**Math.log(sell_count,2).to_i
  end

  # 1 MSB
  def clicks_bucket
    clicks.to_i <= 0 ? 0 : 2**Math.log(clicks,2).to_i
  end

  # 3 MSB
  def grade_bucket
    total_grade = (2 * international_grade.to_i + grade.to_i) / 3
    total_grade < 256 ? 0 : total_grade & (7 <<  Math.log(total_grade,2).to_i - 2)
  end

  def grade_bucket_mobile
    total_grade = (2 * international_grade_mobile.to_i + grade_mobile.to_i) / 3
    total_grade < 256 ? 0 : total_grade & (7 <<  Math.log(total_grade,2).to_i - 2)
  end

  def collection_list_text
    collections.collect(&:name).join(',')
  end

  def self.make_design_live(design)
    if ['review','review_pending'].include? design.designer.state_machine
      design.bad_designer
    else
      design.available
    end
    design.save
  end

  def actual_discount_percent #do not use this. only for validation
    self[:discount_percent].to_i
  end

  def title_and_category
    if self.persisted?
      StringModify.string_utf8_clean_without_space(
        if (category_name = self.categories.first.try(:name)).present? && title.match(/#{category_name.singularize}/i).nil? && category_name.singularize.downcase != "other"
          self.title + " " + category_name.singularize
        else
          self.title
        end
      )
    end
  end

  def validate_tags
    errors.add(:tags, "too much") if tags.size > 5
  end

  def set_master_image
    # Set master image only if it hasn't been set
    if images.present? && master_image.nil?
      s = images.first
      s.kind = "master"
      s.save!
    end
  end

  def update_fields_trigger
    self[:graded_rating] = self[:total_review].to_i > 0 ? self[:average_rating].to_f ** (Math.log(self[:total_review].to_i, 6)) * self[:quality_level].to_f : 1.0
    if self[:discount_percent].blank?
      self[:discount_percent] = 0
    elsif self.is_transfer_model? && self[:discount_percent] == 0
      self[:discount_percent] = TRANSFER_MODEL_DISCOUNT
    end
    if variants.present?
      min_price_variant = variants.select{|v| v[:price].to_i >= MIN_PRICE_PER_PRODUCT && v[:quantity].to_i > 0}.min_by{|v| v[:price]} || variants.select{|v| v[:price].to_i >= MIN_PRICE_PER_PRODUCT}.min_by{|v| v[:price]}
      self[:price] = min_price_variant[:price]
      self.transfer_price = min_price_variant.transfer_price
    elsif self.is_transfer_model? && (self[:price].to_i < MIN_PRICE_PER_PRODUCT || self[:price].to_i == self.transfer_price || self.transfer_price_changed? || self.price_changed?)
      self[:price] = get_display_price_for_transfer_model
    end
    self.discount_price   = ((100 - self[:discount_percent]) * self[:price])/100
    self.international_grade = self.grade if self.international_grade.blank?
    self[:international_grade] = [self[:international_grade].to_i,2**31-1].min
    self[:grade] = [self[:grade].to_i,2**31-1].min
    if color.blank? && (color_ids = Property.get_color_properties).present? && (color_property = property_values.find{|pv| color_ids.include?(pv.property_id)}).present?
      self.color = color_property.name
    end
    # self.delay(priority: 8).flush_cache unless self.state.in?('processing','review', 'banned', 'reject') || skip_flush_cache_callback.present?
    return true
  end

  def check_quantity
    if self.variants.present?
      if self.variants_available
        return true
      end
    elsif self.quantity.present? and self.quantity > 0
      return true
    end
    return false
  end

  def actual_max_quantity
    if variants.present?
      variants.collect(&:quantity).max
    else
      self.quantity
    end
  end

  def is_stitched?
    property_values.joins(:property).where(properties: {bulk_upload_name: 'stitching'})[0].try(:name).try(:downcase) == 'stitched'
  end

  def boost_keyword_hash
    @boost_keyword_hash ||= _boost_keyword_hash
  end

  def add_saree_addons
    if self.designable_type == 'Saree'
      addon_types = []

      addon_types[0] = {:name => 'Blouse', :position => '1'}
      addon_types[1] = {:name => 'Petticoat', :position => '2'}
      addon_types[2] = {:name => 'Fall & Pico', :position => '3'}
      addon_type_ids = []
      addon_types.each do |at|
        addon_type = AddonType.where(:name => at[:name], :position => at[:position]).first_or_create
        addon_type.save
        addon_type_ids << addon_type.id
      end

      unstitched_blouse = AddonTypeValue.where(:name => 'Unstitched Blouse', :position => 1,
        :addon_type_id => addon_type_ids[0], :prod_time => 0, :visible_for => 'international',
        :payable_to => 'designer', :price => 0,:design_id => self.id, :published => true,
        :addon_type_value_group_id => AddonTypeValueGroup.find_by_name("open").id).first_or_create
      unstitched_blouse.save!

      regular_blouse_stitching = AddonTypeValue.where(:name => 'Regular Blouse Stitching', :position => 2,
       :addon_type_id => addon_type_ids[0], :prod_time => 5, :visible_for => 'international',:payable_to => 'mirraw',
       :price => 700, :design_id => self.id, :published => true,
       :addon_type_value_group_id => AddonTypeValueGroup.find_by_name("standard_stitching").id).first_or_create

      custom_blouse_stitching = AddonTypeValue.where(:name => 'Custom Blouse Stitching', :position => 3,
       :addon_type_id => addon_type_ids[0], :prod_time => 7, :visible_for => 'international',:payable_to => 'mirraw',
       :price => 800, :design_id => self.id, :published => true,
       :addon_type_value_group_id => AddonTypeValueGroup.find_by_name("custom_stitching").id).first_or_create
      custom_blouse_stitching.save!

      addon_blouse_size = AddonOptionType.where(:name => 'select blouse size', :p_name => 'Select Blouse Size', :position => 1).first_or_create
      (28..42).step(2).each_with_index do |i, index|
        regular_blouse_stitching.addon_option_values << AddonOptionValue.where(
          :name => "#{i} inch", :p_name => "#{i} inch", :addon_option_type_id => addon_blouse_size.id, :position => index
        ).first_or_create
      end

      addon_blouse_length = AddonOptionType.where(:name => 'select blouse length', :p_name => 'Select Blouse Length', :position => 2).first_or_create
      (13..24).each_with_index do |i, index|
        regular_blouse_stitching.addon_option_values << AddonOptionValue.where(
          :name => "#{i} inch", :p_name => "#{i} inch", :addon_option_type_id => addon_blouse_length.id, :position => index
        ).first_or_create
      end

      addon_blouse_sleeve = AddonOptionType.where(:name => 'select blouse sleeve', :p_name => 'Select Blouse Sleeve', :position => 3).first_or_create
      regular_blouse_stitching.addon_option_values << AddonOptionValue.where(
        :name => 'sleeveless', :p_name => "Sleeveless", :addon_option_type_id => addon_blouse_sleeve.id, :position => 0
      ).first_or_create

      (1..25).each_with_index do |i, index|
        regular_blouse_stitching.addon_option_values << AddonOptionValue.where(
          :name => "#{i} inch", :p_name => "#{i} inch", :addon_option_type_id => addon_blouse_sleeve.id, :position => index + 1
      ).first_or_create
      end
      regular_blouse_stitching.save!

      if self.category_parents_name.include?('Sarees' || 'gifts' || 'kids')
          no_fall_and_pico = AddonTypeValue.where(:name => 'No Fall and Pico', :position => 1,
           :addon_type_id => addon_type_ids[2], :prod_time => 0, :visible_for => 'international', :payable_to => 'designer',
           :price => 0, :design_id => self.id, :published => true,
            :addon_type_value_group_id => AddonTypeValueGroup.find_by_name("open").id).first_or_create
          no_fall_and_pico.save!

          fall_and_pico = AddonTypeValue.where(:name => 'Fall and Pico', :position => 2,
           :addon_type_id => addon_type_ids[2], :prod_time => 5, :visible_for => 'international', :payable_to => 'mirraw',
           :price => 100, :design_id => self.id, :published => true,
           :addon_type_value_group_id => AddonTypeValueGroup.find_by_name("standard_stitching").id).first_or_create
          fall_and_pico.save!

          unstitched_petticoat = AddonTypeValue.where(:name => 'No Petticoat', :position => 1,
           :addon_type_id => addon_type_ids[1], :prod_time => 0, :visible_for => 'international',:payable_to => 'designer',
           :price => 0,:design_id => self.id, :published => true,
            :addon_type_value_group_id => AddonTypeValueGroup.find_by_name("open").id).first_or_create
          unstitched_petticoat.save!

          petticoat_stitching = AddonTypeValue.where(:name => 'Petticoat Stitching', :position => 2,
           :addon_type_id => addon_type_ids[1], :prod_time => 5, :visible_for => 'international',:payable_to => 'mirraw',
           :price => 350,:design_id => self.id, :published => true,
           :addon_type_value_group_id => AddonTypeValueGroup.find_by_name("standard_stitching").id).first_or_create

          addon_fabric = AddonOptionType.where(:name => 'Select Fabric', :p_name => 'Select Fabric', :position => 1).first_or_create

          petticoat_stitching.addon_option_values << AddonOptionValue.where(
              :name => "Satin", :p_name => "Satin", :addon_option_type_id => addon_fabric.id, :position => 1
          ).first_or_create

          petticoat_stitching.addon_option_values << AddonOptionValue.where(
              :name => "Cotton", :p_name => "Cotton", :addon_option_type_id => addon_fabric.id, :position => 2
          ).first_or_create

          addon_around_waist = AddonOptionType.where(:name => 'Select around waist', :p_name => 'Select Around Waist', :position => 2).first_or_create
          (30..50).step(2).each_with_index do |i, index|
            petticoat_stitching.addon_option_values << AddonOptionValue.where(
              :name => "#{i} inch", :p_name => "#{i} inch", :addon_option_type_id => addon_around_waist.id, :position => index
            ).first_or_create
          end

          addon_petticoat_length = AddonOptionType.where(:name => 'Select petticoat length', :p_name => 'Select Petticoat Length', :position => 3).first_or_create
          (32..44).step(2).each_with_index do |i, index|
            petticoat_stitching.addon_option_values << AddonOptionValue.where(
              :name => "#{i} inch", :p_name => "#{i} inch", :addon_option_type_id => addon_petticoat_length.id, :position => index
            ).first_or_create
          end
          petticoat_stitching.save!
      end
    end
  end

  def weight_in_imperial
    if weight.to_i >= 454
      weight_in_lb = (weight.to_i * 0.00220462).to_d.truncate(2).to_f
      "#{weight} gms | #{weight_in_lb} lb"
    else
      weight_in_oz = (weight.to_i * 0.035274).to_d.truncate(2).to_f
      "#{weight} gms | #{weight_in_oz} oz"
    end
  end

  def weight_in_gms
    "#{weight} g"
  end

  def generate_ga_tag_hash(breadcrumbs_array=[], country_code="US")
    # discount_price = country_code != "IN" ? calculate_international_prices(country_code).last : self.effective_price
    discount_price = calculate_international_prices(country_code).last
    ga_hash = {
     event: "ga4_view_item",
     ecommerce: {
        currency: "INR",
        country_code: country_code,
        value: discount_price,
        items: [
          self.generate_design_hash(0, breadcrumbs_array, false, country_code)
        ],
        item_ids: []
      }
    }

    ga_hash
  end

  def google_ads_data
    @googe_add_hash_new = {
    "item_ids":
    [{
        "id": "#{self.id}",
        "google_business_vertical": "retail"
      }],
    "content_ids": ["#{self.id}"],
    "contents": [{"id": "#{self.id}","quantity":1}]
    }
  end

  def calculate_international_prices(country_code)
    currency_convert =  CurrencyConvert.find_by(country_code: country_code)
    country_rate = currency_convert.try(:rate) || 1
    country_marketing_rate = currency_convert.try(:market_rate) || 1
    buying_price = ((self.effective_price / country_rate).round(2) * country_marketing_rate).round(2)
    price = ((self.price / country_rate).round(2) * country_marketing_rate).round(2)
    [price, buying_price]
  end

  def generate_design_hash(index=0, breadcrumbs_array=[], index_required=true, country_code="US")
    price, discount_price = self.calculate_international_prices(country_code)
    scaled_price = discount_price
    discount_price = price - discount_price
    # buying_price = self.discount_price
    # if country_code != "IN"
      # price, discount_price = self.calculate_international_prices(country_code)
      # scaled_price = discount_price
      # buying_price = discount_price
      # discount_price = price - discount_price
    # else
      # scaled_price = self.effective_price
      # price = self.price(1)
      # discount_price = (price * (self.effective_discount_percent('IN') / 100.0)).ceil
      # buying_price = (price - discount_price).to_i
    # end

    ga_design_hash = {
      item_id: "#{self.id}",
      item_name: self.title[0..100],
      affiliation: "",
      coupon: "",
      discount: discount_price.round(2),
      item_brand: self.designer.cached_slug || "",
      item_variant: self.color || "",
      location_id: "",
      price: scaled_price.round(2),
      item_category: "",
      item_category2: "",
      item_category3: "",
      item_category4: "",
      item_category5: "",
      content_category: "#{self.categories.first.name}".titleize,
      quantity: 1
    }
    ga_design_hash[:index] = index if index_required
    if breadcrumbs_array.present?
      # Whenever Breadcrumb having more than 2 elements then it will picked up 2nd position element title
      array_index = breadcrumbs_array.length > 2 ? 2 : 1
      title = breadcrumbs_array[array_index].present? ? breadcrumbs_array[array_index].with_indifferent_access[:title] : ""
      modified_title = title.gsub("'", " ")
      item_listing_name, item_listing_id = [title, Category.find_by("LOWER(title) = '#{modified_title.try(:downcase)}'").try(:id)] if modified_title.present?
      ga_design_hash = ga_design_hash.merge({item_list_name: item_listing_name.to_s, item_list_id: item_listing_id.to_s})
      breadcrumbs_array.uniq.each_with_index do |value, index|
        if value.present?
          next if index.eql?(0)
          category_key = index.eql?(1) ? "item_category" : "item_category#{index}"
          ga_design_hash[category_key.to_sym] = value.with_indifferent_access[:title]
        end
      end
    end
    return ga_design_hash
  end

  def add_salwar_lahenga_addon
    if (self.designable_type == 'Lehenga' || self.designable_type == 'SalwarKameez' )
      addon_types = []
      if self.designable_type == 'Lehenga'
        addon_types[0] = {:name => 'Lehengas', :position => '1'}
        price_regular = 1000
        price_custome = 1200
        p_name_lahenga = 'Select Lehenga Size'
      else
        addon_types[0] = {:name => 'Salwar Kameez', :position => '1'}
        price_regular = 1200
        price_custome = 1400
        p_name_lahenga = 'Select kameez Size'
      end

      addon_type_ids = Array.new
      addon_type = AddonType.where(:name => addon_types[0][:name], :position => addon_types[0][:position]).first_or_create
      addon_type.save
      addon_type_ids << addon_type.id


        unstitched_blouse = AddonTypeValue.where(:name => 'Dress Material Only', :position => 1,
          :addon_type_id => addon_type_ids[0], :prod_time => 0, :visible_for => 'international',
          :payable_to => 'designer', :price => 0,:design_id => self.id, :published => true,
          :addon_type_value_group_id => AddonTypeValueGroup.find_by_name("open").id).first_or_create
        unstitched_blouse.save!

        regular_blouse_stitching = AddonTypeValue.where(name: 'Standard stitching sizes', position: 2,
         addon_type_id: addon_type_ids[0], prod_time: 5, visible_for: 'international',payable_to: 'mirraw',
         price: "#{price_regular}" , design_id: self.id, published: true,addon_type_value_group_id: AddonTypeValueGroup.find_by_name("standard_stitching").id).first_or_create

        custom_blouse_stitching = AddonTypeValue.where(name: 'Custom Stitching', position: 3,
         addon_type_id: addon_type_ids[0], prod_time: 7, visible_for: 'international',payable_to: 'mirraw',
         price: "#{price_custome}" , design_id: self.id, published: true,
         addon_type_value_group_id: AddonTypeValueGroup.find_by_name("custom_stitching").id).first_or_create
        custom_blouse_stitching.save!

        addon_kameez_size = AddonOptionType.where(name: 'Lehenga size', p_name: "#{p_name_lahenga}", position: 1).first_or_create
        (28..42).step(2).each_with_index do |i, index|
          regular_blouse_stitching.addon_option_values << AddonOptionValue.where(
            name: "#{i} inch", p_name: "#{i} inch", addon_option_type_id: addon_kameez_size.id, position: index
          ).first_or_create
        end

        addon_bottom_length = AddonOptionType.where(name: 'Bottom length', p_name: 'Select Bottom Length', position: 2).first_or_create
        (34..44).step(2).each_with_index do |i, index|
          regular_blouse_stitching.addon_option_values << AddonOptionValue.where(
            name: "#{i} inch", p_name: "#{i} inch", addon_option_type_id: addon_bottom_length.id, position: index
          ).first_or_create
        end

        regular_blouse_stitching.save!
    end
  end

  def master_image
    image = self.images.select{|img| img.kind == 'master'}[0]
    if images.present? && image.nil?
      image = images.first
      image.kind = "master"
      image.save!
    end
    image
  end

  def variant_images
    images.find(:all, :conditions => ['kind = ?', "variant"])
  end

  def shipping_time(country = 'india')
    mirraw_ship_time = Country.country_wise_delivery_time(country)
    if country != 'india' && self.sor_available_for_customer?#RTS_ALLOWED_COUNTRIES.include?(country)
      mirraw_ship_time
    else
      (mirraw_ship_time + self.designer_shipping_time(country)).round
    end
  end

  def warehouse_shipping_time(country = 'india', city = nil)
    city_pdd, design_eta = 0, self.eta.to_i + self.designer.vacation_days_count.to_i
    pickup_location = 'bhiwandi'
    city_pdd = DeliveryNpsInfo.get_city_based_pdd(city, [pickup_location]) if city.present? && pickup_location.present?
    if country.to_s.downcase == 'india'
      promise_days = city_pdd + self.designer.ship_time + 2 # additional two days for QC and dispatch
      promise_days += design_eta unless self.sor_available?
      promise_days += 1 if city_pdd > 0 && Time.current.advance(days: promise_days).sunday?
      return promise_days
    end
    design_eta + self.designer.ship_time + self.categories.collect(&:eta).max.to_i
  end

  def designer_shipping_time(country = 'india', city = nil)
    city_pdd, design_eta = 0, self.eta.to_i + self.designer.vacation_days_count.to_i
    pickup_location = self.sor_available? ? 'bhiwandi' : self.designer.pickup_location.try(:downcase)
    city_pdd = DeliveryNpsInfo.get_city_based_pdd(city, [pickup_location]) if city.present? && pickup_location.present?
    if country.to_s.downcase == 'india'
      promise_days = city_pdd
      promise_days += design_eta unless self.sor_available?
      promise_days += 1 if city_pdd > 0 && Time.current.advance(days: promise_days).sunday?
      return promise_days
    end
    design_eta + self.designer.ship_time + self.categories.collect(&:eta).max.to_i
  end

  def not_rts_shipping_time(country = 'india')
    (Country.country_wise_delivery_time(country) + self.designer_shipping_time(country)).round
  end

  def size_in_warehouse?(bucket = nil, quantity: 1)
    self.sized_designs.find{|sd| sd.size_bucket_id == (bucket || SizeChart::UNSTITCHED_SIZE_CHART.try(:size_bucket_id))}.try(:quantity).to_i >= quantity
  end

  def delivery_date_for_rts(country = 'india')
    Country.country_wise_delivery_time(country.try(:downcase)) #READY_TO_SHIP_DESIGNS.to_i
  end

  # ensure that there are no line items referencing this product
  def ensure_not_referenced_by_any_line_item
    if line_items.count.zero?
      return true
    else
      errors[:base] << "You have pending orders for this design. You can't delete it."
      return false
    end
  end

  def atleast_one_image_exists
    undestroyed_image_count = 0
    images.each do |i|
      undestroyed_image_count += 1 unless i.marked_for_destruction?
    end

    if undestroyed_image_count < 1
      images.each do |i|
        i.reload
      end
      errors[:base] << "You need to attach at least one image"
    end
  end

  def atleast_one_category_exists
    unless self.categories.present?
      errors[:base] << "You need to select at least one category"
    end
  end

  def can_ship_international?
    can_ship = true
    self.categories.each do |category|
      if category.international.present? && category.international == 'N'
        can_ship = false
      end
    end
    return can_ship
  end

  def wholesale?
    self.min_wholesale_price.present? && self.min_wholesale_quantity.present?
  end

  def retail?
    self.retail
  end

  def saree?
    self.designable.present? && self.designable_type == "Saree"
  end

  def consumable?
    designable_type == 'Consumable' && designable.present?
  end

  def salwar_kameez?
    self.designable.present? && self.designable_type == "SalwarKameez"
  end

  def lehenga?
    self.designable.present? && self.designable_type == "Lehenga"
  end

  def jewellery?
    self.designable.present? && self.designable_type == "Jewellery"
  end

  def bag?
    self.designable.present? && self.designable_type == "Bag"
  end

  def valid_discount_percent?
    self.discount_percent.present? && self.discount_percent > 0
  end

  def effective_price(return_type=RETURN_SCALED)
    ((self.valid_discount_percent? ? self.discount_price(return_type) : self.price(return_type))*(return_type==RETURN_SCALED ? self.get_scale : 1.0)).to_i
  end

  def create_hash_for_dynamic_price
    hash={}
    if SWITCH_DP_TO_INTERNATIONAL
      hash[:US] = self.dynamic_prices.find{|dynamic_price| dynamic_price.country_code == 'US'}.try(:scale)
    else
      self.dynamic_prices.each do |dynamic_price|
        hash[dynamic_price.country_code.to_sym] = dynamic_price.scale
      end
    end
    hash
  end

  def self.grade_designs(file_path,international=false)
    maximum_grade = SystemConstant.get('MAXIMUM_GRADE').to_i
    index_design_ids = []
    design_ids={}
    CSV.open(file_path, headers: :first_row).each do |row|
      design_ids[row[0].to_i] = maximum_grade
      if design_ids.length > 500
        Design.where(id: design_ids.keys).each do |design|
          if international
            design.update_column(:international_grade,design_ids[design.id])
            if design.state == 'in_stock'
              index_design_ids << design.id
            end
          else
            design.update_column(:grade, design_ids[design.id])
            if design.state == 'in_stock'
              index_design_ids << design.id
            end
          end
        end
        design_ids = {}
      end
      maximum_grade -= 1
    end
    time_to_run = Time.now
    sunspot_index_queue = Sunspot::IndexQueue::Entry.implementation
    to_be_imported = index_design_ids.first(10000).collect{|design_id| sunspot_index_queue.new(record_class_name: 'Design', record_id: design_id, run_at: time_to_run)}
    sunspot_index_queue.import to_be_imported, validate: false, batch_size: 500
  end

  def invoice_category_name(name = nil)
    color,work,type,material = nil
    property_values.each do |pv|
      property_name = pv.property.name.to_s.downcase
      pv_name = pv.p_name.strip
      material ||= pv_name if %w(fabric fabric_of_saree lehenga_fabric kameez_fabric top_fabric base_material).include?(property_name)
      work ||= pv_name if 'jewellery_work' == property_name
      color ||= pv_name if %w(kameez_color saree_color lehenga_color top_color color).include?(property_name)
    end
    category_names = self.category_parents_name.map(&:downcase)
    if name.present? && INVOICE_TITLE_CHANGE.include?(name.downcase)
      if designable_type == 'Saree'
        if category_names.include?('sarees')
          type = 'Saree'
        else
          type = 'Blouse'
        end
      else
        type = designable_type
      end
      if type == 'Jewellery'
        if material.present?
          return "Imitation Jewellery - #{category_names.last} - Made Of - #{material}  ( #{package_details})"
        else
          return "Imitation Jewellery - #{category_names.last} (#{package_details})"
        end
      elsif color.present? && material.present?
        return "Apparels #{color} - #{type} - Made Of - #{material} "
      elsif ['Other','Kurti','Saree', 'Islamic'].include?(type)
        return " #{package_details}"
      elsif material.present?
        return "Apparels #{type} - Made Of - #{material} "
      else
        return "Apparels #{type} - #{category_names.last} "
      end
    end
    get_default_name(material,category_names)
  end

  def generate_additional_discount_data(additional_discount=20)
    effective_discount = 100 - ((100 - self[:discount_percent])*(100-additional_discount.to_i)/100).to_i
    new_discount_price = (self[:price] * (100 - effective_discount) / 100).to_i
    hash={
      design_id: self.id,
      design_cached_slug: self.cached_slug || self.id,
      price: "Rs. #{self[:price]}",
      discount_percent: "#{self[:discount_percent]}%",
      discount_price: "Rs. #{self[:discount_price]}",
      additional_discount: "#{additional_discount}%",
      effective_discount: "#{effective_discount}%",
      new_discount_price: "Rs. #{new_discount_price}",
      new_payout: "Rs. #{(new_discount_price * (100 - self.designer.transaction_rate.to_i)/100).to_i}"
    }
  end

  # def effective_discount_percent
  #   # This Will comme in play when there is a site wide sale.
  #   # This should be same as (100 - discount_percent) * price
  #   self.valid_discount_percent? ? self.discount_percent : 0
  # end

  def return_similar_designs
    RequestStore.cache_fetch("similar_designs_#{self.id}" , expires_in: 24.hour) do
      begin
        designs = Sunspot.more_like_this(self) do
          fields :category_name, :designer_name, :title, :description, :specification
          with(:state, 'in_stock')
          with(:average_rating, 4..5)
          without(:in_catalog_one, 3)
          without(:in_catalog_one, 0)
          with(:cluster_winner, 1)
        end
        designs.results
      rescue => e
        Design.published.retail.greater_than(1000).joins(:categories).in_category('jewellery').order('created_at DESC').limit(30)
      end
    end
  end


  def self.cart_similar_designs(design_ids, sample_size)
    similar=[]
    design_id_cycle = design_ids.each.cycle
    sample_size.times do
      design = Design.find_by_id(design_id_cycle.next)
      similar << design.return_similar_designs.sample(1) if design.present?
    end
    similar
  end

  def self.similar_designs(design_ids, number)
    if design_ids.size >= number
      sample_ids = design_ids.sample(number)
      designs = sample_ids.collect {|id| Design.find(id) }
    else
      designs = design_ids.collect {|id| Design.find(id)}
      design = Design.find(design_ids.sample(1)).first
      similar = design.return_similar_designs.sample(number - design_ids.size)
      designs = designs + similar
    end
  end

  def self.featured_products_memcached
    Rails.cache.fetch(:featured_products_desktop, expires_in: 24.hours) do
      Design.search(include: [:images,:designer]) do
        with(:state, 'in_stock')
        without(:in_catalog_one, 0)
        without(:in_catalog_one, 3)
        with(:featured_product, true)
        with(:cluster_winner, 1)
      end.results
    end
  end

  def available?
    self && self.published && self.quantity > 0
  end

  def is_variant_design_code_uniq?(variant_codes)
    return self.designer_id.present? && Variant.where(design_code: variant_codes).joins(:design).where(design: {designer_id: self.designer_id}).exists?
  end

  def design_code_exists?
    if Design.where.not(state: 'reject', id: self.id).where(design_code: self.design_code).where(designer_id: self.designer_id).exists?
      errors[:base] << "Design Code already exists"
    end
  end

  def get_master_addons_category
    #Pulling master_addons for the first category that is master
    master_cat = nil
    categories = self.categories
    categories.each do |category|
      master_cat = category.search_master
      if master_cat.master?
        break
      end
    end
    columns = 'master_addons.price as price, addon_types.name as addon_type_name, addon_type_values.name as addon_type_value_name, addon_types.id as addon_type_id, addon_type_values.id as addon_type_value_id, master_addons.prod_time as prod_time'
    orderby = 'addon_types.position, addon_type_values.position ASC'
    # Assuming we are only applying addons to master  category
    master_addons = MasterAddon.joins(:addon_type, :addon_type_value).select(columns).where('master_addons.designer_id = ? and master_addons.category_id = ? and addon_type_values.published != ?',self.designer_id, master_cat.id, false).order(orderby)
    if master_addons.count > 0
      return master_addons
    else
      return false
    end
  end

  def vendor_addon_category?
    @vendor_addon_category ||= (self.category_parents_name.to_a & Design::VENDOR_ADDON_CATEGORY).present?
  end
  # Input params - current_country, currency
  #
  # Returns addon_types with addon_type_values => [addon_option_values => addon_option_type]

  def get_design_addons(current_country, currency)
    region = ['india', 'n/a'].include?(current_country.downcase) || currency.downcase == 'inr' ? 'international' : 'domestic'

    product_in_warehouse = self.sor_available? || self.designer_id == 12727

    #### Below conditions enables standard stitching for domestic market #####

    if DESIGNER_ADDON && (vendor_addon_category? || (region != 'domestic' && !(product_in_warehouse && (self.designable_type == 'Lehenga' || self.designable_type == 'SalwarKameez' || self.designable_type == 'Saree' ) ) ) )
      categories_ids = self.categories.map{|i| i.cached_self_and_ancestors_id}.flatten.uniq
      o_clause = 'addon_types.position, addon_type_values.position, addon_option_types.position, addon_option_values.position'
      addon_types = AddonType.includes(addon_type_values: [:addon_type_value_group, addon_option_values: :addon_option_type]).order(o_clause).
       where(addon_type_values: {published: true, designer_id: self.designer_id}, addon_types: {category_id: categories_ids} ).
       where('addon_type_values.quantity <> 0').uniq
    elsif variants.present? && (Design::STITCHING_ALLOWED_DESIGNABLE_TYPES.exclude?(self.designable_type) || unstitched_variant.blank?)
      []
    else
      category_id = nil
      if self.designable_type == 'Saree'
        if self.category_parents_name.include?('Sarees' || 'gifts' || 'kids')
          category_id = Category.find_by_namei('sarees').id
        else
          category_id = Category.find_by_namei('blouse').id
        end
      elsif self.designable_type == 'SalwarKameez'
        category_id = Category.find_by_namei('salwar-kameez').id
      elsif self.designable_type == 'Lehenga'
        category_id = Category.find_by_namei('lehengas').id
      elsif self.designable_type == 'Kurti'
        category_id = Category.find_by_namei('kurtas-and-kurtis').id
      end
      # Might not work in development due to lazy loading of models
      RequestStore.cache_fetch("desktop/design_addons_not_for/#{category_id}/#{region}", expires_in: 24.hours) do
        o_clause = 'addon_types.position, addon_type_values.position, addon_type_values.description, addon_option_types.position, addon_option_values.position'
        addon_types = AddonType.includes(:addon_type_values => [:addon_type_value_group,:addon_option_values => :addon_option_type]).order(o_clause).
          where(category_id: category_id).
          where(:addon_type_values => {:published => true, design_id: nil, designer_id: nil}).
          where('addon_type_values.visible_for <> ?', region).uniq.to_a
      end
    end
  end

  def size_chart
    SizeChart.active_sizes.map{|s| [s.id,s]}.to_h
  end

  def dynamic_size_chart
    chart = dynamic_size_charts.order('updated_at desc').first.presence
    return chart if chart
    dynamic_size_chart_category.try do |category|
      size_charts = category.dynamic_size_charts
      # Optimize: Use database queries instead of loading all size charts
      category.dynamic_size_charts.where(designer_id: designer_id).first ||
      category.dynamic_size_charts.where(default: true, designer_id: nil).first
    end
  end

  def design_check
    if DESIGNER_ADDON && vendor_addon_category?
      return self.designer_id
    else
      return nil
    end
  end

  def add_addons(addons)
    created_addons = Array.new
    addons.each do |addon_type_value_id, items|
      new_addon = AddonTypeValue.new(
        :addon_type_id => items[:at_id].to_i,
        :price => items['price'].to_i,
        :name => items['name'],
        :position => items['position'].to_i,
        :design_id => self.id,
        :master_id => addon_type_value_id.to_i,
        :published => false,
        :prod_time => items['prod_time'].to_i,
        :addon_type_value_group_id => items['addon_type_value_group_id'].to_i,
        :db_name => items['db_name'])

      if items['apply']=='t'
        new_addon.published = true
      end
      if items['price'].to_i == 0
        new_addon.prod_time = 0
      end
      new_addon.save!
      if items['addon_option_values_attributes'].present?
        add_addon_options(items['addon_option_values_attributes'], new_addon)
      end
      created_addons << new_addon
    end
    check_for_free_addons(created_addons)
  end

  def self.top_count_with_ties(association,tie_limit: 3, minimum_count: 3,max_limit:10,group_key: :id)
    trigger = nil
    index = 0
    self.joins(association).group(association => group_key).
    having('count(distinct designs.id) >= ?',minimum_count).order('count(distinct designs.id) desc').
    limit(max_limit).count('distinct designs.id').reject do |_,value|
      index += 1
      trigger = value if index <= tie_limit
      value != trigger
    end
  end

  def add_addon_options(addon_options, addon)
    addon_options.each do |index, addon_option_attr|
      addon_option = AddonOptionValue.new(
        :name => addon_option_attr['addon_option_value_name'],
        :p_name => addon_option_attr['addon_option_value_p_name'],
        :position => addon_option_attr['addon_option_value_position'],
        :addon_option_type_id => addon_option_attr['addon_option_type_id'].to_i,
        :addon_type_value_id => addon.id.to_i,
        :master_id => addon_option_attr['master_id'].to_i
        )
      addon_option.save!
    end
  end

  def update_addons(addons)
    updated_addons = Array.new
    addons.each do |key,addon|
      check_addon = AddonTypeValue.where(:addon_type_id => addon[:at_id].to_i, :name => addon['name'],
      :position => addon['position'].to_i, :design_id => self.id).first_or_create
      check_addon.price = addon['price']
      check_addon.published = true
      check_addon.prod_time = addon['prod_time'].to_i
      if check_addon.master_id.blank?
        check_addon.master_id = addon['atv_id']
      end
      if addon['apply'] != 't'
        check_addon.published = false
      end
      check_addon.save!
      updated_addons << check_addon
    end
    check_for_free_addons(updated_addons)
  end

  def check_for_free_addons(addons)
    create_free_for = Array.new
    addons = addons.group_by(&:addon_type_id)
    addons.each do |key, items|
      # Key state says whether or not a free addon exist in addon type category
      key_state = 0
      addons_present = false
      items.each do |item|
        if item.price == 0 and item.published == true
          key_state = 1
        end
        # Blocks from creating dummy addon when there are no addons published
        if item.published == true
          addons_present = true
        end
      end
      addon_type = AddonType.find(key)
      # Create dummy addon
      if key_state == 0 && addons_present == true
        addon_type_value = AddonTypeValue.where(:addon_type_id => addon_type.id,
        :name => 'No '+addon_type.name, :position => 0, :design_id => self.id, :price => 0,
        :prod_time => 0).first_or_create
        addon_type_value.update_attributes(:published => true)
      # Hide existing dummy addon
      elsif key_state == 1
        addon_type_value = AddonTypeValue.where(:addon_type_id => addon_type.id,
        :name => 'No '+addon_type.name, :position => 0, :design_id => self.id, :price => 0,
        :published => true, :prod_time => 0).first
        if addon_type_value.present?
          addon_type_value.update_attributes(:published => false)
        end
      end
    end

  end

  def variants_available
    self.variants.any?{|v| v.quantity? && v.quantity > 0 }
  end

  def variants_quantity
    quantity = 0
    self.variants.each do |variant|
      if variant.quantity.present? && variant.quantity > 0
        quantity += variant.quantity
      end
    end
    return quantity
  end

  def variant_freesize
    return self.variants.select{|v| v.option_type_values[0].p_name == FREESIZE and v.quantity > 0}[0]
  end

  def pending_orders
    pending_orders = Design.joins(line_items: :designer_order).where("designer_orders.state = 'new' and designs.id = ?", self.id).first
    if pending_orders.present?
      return true
    else
      return false
    end
  end

  def update_sell_count(quantity)
    self.sell_count = 0 unless self.sell_count.present?
    self.sell_count += quantity
  end

  def design_designer?(current_account)
    if current_account.present?
      self.designer.account.try(:id) == current_account.try(:id) || current_account.admin?
    else
      return false
    end
  end

  def current_state_date
    case self.state
    when 'in_stock'
      return self.last_in_stock
    when 'sold_out'
      return self.last_sold_out
    when 'blocked'
      return self.last_blocked
    when 'seller_out_of_stock'
      return self.last_seller_out_of_stock
    when 'banned'
      return self.last_banned
    when 'delete'
      return self.deleted_on
    end
  end

  def add_validations
    if designable.present? && USE_FORM_VALIDATIONS
      is_plus_size_enabled = check_if_design_available_for_plus_size
      max_allowed_chest_size = (is_plus_size_enabled ? CUSTOM_STITCHING_BUST_SIZE_CONSTRAINTS[designable_type.downcase].to_f : ENABLE_PLUS_SIZE['max_size'].to_f )
      case self.designable_type
      when 'Saree'
        {max_chest_size: max_allowed_chest_size}
      when 'SalwarKameez'
        {max_top_size: Design.regularize_value(designable.max_kameez_length), max_bottom_size: Design.regularize_value(designable.max_bottom_length,metrics: 'meters'), max_chest_size: max_allowed_chest_size}
      when 'Lehenga'
        {max_chest_size: max_allowed_chest_size, max_waist_size: Design.regularize_value(designable.max_waist_size), max_bottom_size: Design.regularize_value(designable.length),max_hip_size: Design.regularize_value(designable.max_hip_size)}
      else
        {}
      end
    else
      {}
    end
  end

  def check_if_plus_size_serviceable(size)
    return size.to_i <= designable.max_bustsize_fit.to_i if system_plus_size
    (check_if_design_available_for_plus_size || size.to_i <= ENABLE_PLUS_SIZE['max_size'])
  end

  def check_if_design_available_for_plus_size
    @plus_size_check ||= (ENABLE_PLUS_SIZE['enable'] || ENABLE_PLUS_SIZE['exceptions'].include?(id))
  end

  def system_plus_size
    @plus_size ||= (designable.respond_to?(:max_bustsize_fit) && (((ENABLE_PLUS_SIZE['max_size'].to_i.nonzero? || 42)+1)..54).include?(designable.max_bustsize_fit.to_i))
  end

  def get_eligible_size_buckets
    max_size = system_plus_size ? self.designable.get_max_bust_size : ENABLE_PLUS_SIZE['max_size']
    SizeChart.where{size <= max_size}.pluck(:size_bucket_id).push(SizeChart::UNSTITCHED_SIZE_CHART.size_bucket_id).uniq
  end

  def self.regularize_value(value, metrics: 'inches')
    if value.present?
      begin
        if value.to_i < 10
          Measurement.parse(value,'meters').convert_to(:in).quantity
        else
          Measurement.parse(value,metrics).convert_to(:in).quantity
        end
      rescue ArgumentError
        nil
      end
    end
  end

  def remove_item_quantity
    self.quantity = self.in_stock_warehouse.to_i
    if variants.none?(&:in_stock_warehouse?)
      variants.update_all(quantity: 0)
    else
      variants.map{|v| v.update_column(:quantity,v.in_stock_warehouse.to_i)}
    end
  end

  def calculate_price(price, rate)
    if price.present?
      price/=rate
      price.round(2)
    else
      price
    end
  end

  def set_currency_details(account_id)
    if (ad = ApiData.find_by_account_id(account_id)).present?
      @currency_details = CurrencyConvert.find(ad.currency_convert_id)
    else
      @currency_details = CurrencyConvert.find_by_country_code("US")
    end
  end

  def extra_params
    {
      bundleFrom: "localytics",
      design_id: "#{self.id}",
      title: self.title,
      description: self.description,
      state: self.state,
      designer_id: "#{self.designer_id}",
      designer_name: self.designer.name,
      design_image: self.master_image.photo(:small_m)[8..-1],
      stock: "#{self.quantity}",
      hex_symbol: @currency_details.hex_symbol,
      symbol: @currency_details.symbol,
      price: "#{calculate_price(self.price, @currency_details.rate)}",
      discount_price: "#{self.calculate_price(self.discount_price, @currency_details.rate)}",
      discount_percent: "#{self.effective_discount_percent}"
    }
  end

  def less_qty_wishlist_msg(account_id, img, msg, type, campaign_key = 'auto_notification')
    genuine_price_drop_wishlist?(account_id) #used to set @current_wishlist
    if @current_wishlist.present? && self.quantity > 0
      extra = self.extra_params.merge(
        type: type,
        PushImageHandler: img,
        ignore_threshold: false,
        notificationMainTitle: msg['title'],
        wishlist_id: "#{@current_wishlist.id}",
        category_name: self.categories.first.name,
        cached_slug: self.cached_slug,
        grade: "#{self.grade}",
      )
      extra.merge!(set_utm_tracking_detail(campaign_key))

      extra.merge!(
        productId: self.id,
        productTitle: self.title,
      ) if type == 'ProductDetail'

      {
        target: account_id.to_s,
        alert: msg,
        android: {extra: extra.stringify_keys}.stringify_keys
      }.stringify_keys
    end
  end

  def less_quantity_notification_wishlist(account_ids)
    app_account_ids = get_allowed_ids(account_ids)
    app_account_ids.keys.each do |app_source|
      account_ids = []
      app_account_ids[app_source].each{ |data| account_ids << data.account_id }
      if account_ids.length > 0
        msg = LOCALYTICS_MESSAGES['design']['qty_drop']['wishlist']
        campaign_key = "Limited_Stock_Notification_Wishlist"
        set_and_send_notification(account_ids, msg, campaign_key, "Quantity_Dropped_Wishlist", app_source, 'wishlist')
      end
    end
  end

  def less_qty_cart_msg(account_id, img, msg, type, campaign_key = 'auto_notification')
    genuine_price_drop_cart?(account_id) #used just to set @current_line_item
    if @current_line_item.present? && @current_line_item.quantity <= ( @current_line_item.variant.try(:quantity) || quantity )
      extra = self.extra_params.merge(
        type: type,
        PushImageHandler: img,
        ignore_threshold: true,
        notificationMainTitle: msg['title'],
        quantity: "#{@current_line_item.quantity}",
        cart_id: "#{@current_line_item.cart_id}",
        line_item_id: "#{@current_line_item.id}",
        design_add_ons: calculate_addons,
        design_variants: calculate_variants,
        snapshot_price: "#{calculate_price(@current_line_item.snapshot_price, @currency_details.rate)}",
        note: @current_line_item.note,
        total: "#{calculate_line_item_total}"
      )
      extra.merge!(set_utm_tracking_detail(campaign_key))

      {
        target: account_id.to_s,
        alert: msg,
        android: {extra: extra.stringify_keys}.stringify_keys
      }.stringify_keys
    end
  end

  def less_quantity_notification_cart(account_ids)
    app_account_ids = get_allowed_ids(account_ids)
    app_account_ids.keys.each do |app_source|
      account_ids = []
      app_account_ids[app_source].each{ |data| account_ids << data.account_id }
      if account_ids.length > 0
        msg = LOCALYTICS_MESSAGES['design']['qty_drop']['cart']
        campaign_key = "Limited_Stock_Notification_Cart"
        set_and_send_notification(account_ids, msg, campaign_key, "Quantity_Dropped_Cart", app_source, 'cart')
      end
    end
  end

  def less_quantity_notification_android(variant_id = nil)
    if NOTIFICATIONS_ENABLE == 'true'
      if variant_id.present? || (quantity_changed? && (quantity_was > quantity) && (THRESHOLD_QTY.between? quantity, quantity_was))
        user_ids = Cart.user.joins(:line_items).where("line_items.design_id" => id, "line_items.variant_id" => variant_id)
        .where('used = ? and carts.updated_at > ?', false, 30.days.ago).pluck(:user_id).uniq.compact

        self.sidekiq_elay.less_quantity_notification_cart(find_account_ids(user_ids)) if user_ids.present?  #delayed

        user_ids = Wishlist.where(design_id: self.id).wished.pluck(:user_id).compact - user_ids
        SidekiqDelayGenericJob.perform_async(design.class.to_s, design.id ,"less_quantity_notification_wishlist", find_account_ids(user_ids)) if user_ids.present?
        #self.sidekiq_delay
        #    .less_quantity_notification_wishlist(
        #      find_account_ids(user_ids)
        #    ) if user_ids.present? #delayed
      end
    end
  end

  def in_stock_notification(account_ids, campaign_key, type, notify_for)
    app_account_ids = get_allowed_ids(account_ids)
    app_account_ids.keys.each do |app_source|
      account_ids = []
      app_account_ids[app_source].each{ |data| account_ids << data.account_id }
      if account_ids.length > 0
        msg = LOCALYTICS_MESSAGES['design']['in_stock'][notify_for]
        set_and_send_notification(account_ids, msg, campaign_key, type, app_source, notify_for)
      end
    end
  end

  def in_stock_notification_android(variant_id = nil)
    if NOTIFICATIONS_ENABLE == 'true'
      user_ids = Cart.user.joins(:line_items).where("line_items.design_id" => id, "line_items.variant_id" => variant_id)
      .where('used = ? and carts.updated_at > ?', false, 90.days.ago).pluck(:user_id).uniq.compact
      SidekiqDelayGenericJob.perform_async(design.class.to_s, design.id, "in_stock_notification", find_account_ids(user_ids), 'In_Stock_Notification_Cart','Cart','cart')
      #self.sidekiq_delay.in_stock_notification(find_account_ids(user_ids),'In_Stock_Notification_Cart','Cart','cart') if user_ids.present?

      user_ids = Wishlist.where(design_id: self.id).wished.pluck(:user_id).uniq.compact - user_ids
      SidekiqDelayGenericJob.perform_async(design.class.to_s, design.id, "in_stock_notification", find_account_ids(user_ids), 'In_Stock_Notification_Wishlist','ProductDetail','wishlist') if user_ids.present?
=begin
      self.sidekiq_delay
          .in_stock_notification(
            find_account_ids(user_ids),
            'In_Stock_Notification_Wishlist',
            'ProductDetail',
            'wishlist'
          ) if user_ids.present?
=end
    end
  end

  def set_and_send_notification(account_ids, msg, campaign_key, type, app_source, method)
    img = get_notification_image
    account_ids.each_slice(LOCALYTICS_BATCH_SIZE) do |batch|
      message = []
      batch.each{ |account_id|
        self.set_currency_details(account_id)
        message << send("less_qty_#{method}_msg", account_id, img, msg, type, campaign_key)
      }
      if FCM_NOTIFICATION
        fcm_token = []
        message.each{ |msg|
          fcm_token << Account.find(msg['target'].to_i).try(:fcm_registration_token) if msg
        }
        fcm_token.compact!
        FirebaseNotification.fcm_push_notification(msg['title'], msg['body'], img, fcm_token, app_source) if fcm_token && app_source.exclude?('Designer')
      else
        LocalyticsNotification.customer_notification(message.compact, campaign_key, app_source) if app_source.include?('Android') && LOCALYTICS_NOTIFICATION == 'true'
        ClevertapNotification.modify_and_push_notification(message.compact, campaign_key, app_source.downcase) if app_source.include?('iOS') || CLEVERTAP_NOTIFICATION == 'true'
      end
    end
  end

  def get_notification_image
    self.master_image ? self.master_image.photo(:large_m) : self.images.first.photo(:large_m)
  end

  def self.individual_less_qty_notification_android_cart(account_id, design_id) #delayed
    design = Design.find_by_id(design_id)
    design.less_quantity_notification_cart(account_id)
  end

  def self.individual_less_qty_notification_android_wishlist(account_id, design_id) #delayed
    design = Design.find_by_id(design_id)
    design.less_quantity_notification_wishlist(account_id)
  end

  def genuine_price_drop_wishlist?(account_id, price = nil)
    user_id = Account.find(account_id).accountable_id
    if (@current_wishlist = Wishlist.where("user_id = ? AND design_id = ?", user_id, self.id).first).present?
      if price.present? && (@current_wishlist.country_code == @currency_details.country_code)
        current_price = calculate_price(price, @currency_details.rate)
        wl_price = calculate_price(@current_wishlist.price, @current_wishlist.conversion_rate)
        current_price < wl_price
      end
    else
      false
    end
  end

  def price_drop_wishlist_msg(account_id, diff, price, img, msg, campaign_key = 'auto_notification')
    if genuine_price_drop_wishlist?(account_id, price)
      extra = self.extra_params.merge(
        type: "Price_Dropped_Wishlist",
        PushImageHandler: img,
        ignore_threshold: false,
        notificationMainTitle: msg['title'],
        wishlist_id: "#{@current_wishlist.id}",
        category_name: self.categories.first.name,
        cached_slug: self.cached_slug,
        grade: "#{self.grade}",
        reduced_amount: "#{calculate_price(diff, @currency_details.rate)}",
        actual_price: "#{calculate_price((price+diff), @currency_details.rate)}"
      )
      extra.merge!(set_utm_tracking_detail(campaign_key))

      {
        target: account_id.to_s,
        alert: msg,
        android: {extra: extra.stringify_keys}.stringify_keys
      }.stringify_keys
    else
      nil
    end
  end

  def price_drop_notification_wishlist(account_ids, diff, price)
    app_account_ids = get_allowed_ids(account_ids)
    app_account_ids.keys.each do |app_source|
      account_ids = []
      app_account_ids[app_source].each{ |data| account_ids << data.account_id }
      if account_ids.length > 0
        img = self.master_image.photo(:large_m) #notification_image_url('price_drop.jpg')
        msg = LOCALYTICS_MESSAGES['design']['price_drop']['wishlist']
        account_ids.each_slice(LOCALYTICS_BATCH_SIZE) do |batch|
          if FCM_NOTIFICATION
            fcm_token = []
            batch.each{ |account_id|
              fcm_token << Account.find_by_id(account_id).try(:fcm_registration_token) if genuine_price_drop_wishlist?(account_id, price)
            }
            fcm_token.compact!
            FirebaseNotification.fcm_push_notification(msg['title'], msg['body'], img, fcm_token, app_source) if fcm_token && app_source.exclude?('Designer')
          else
            message = []
            campaign_key = 'Price_Dropped_Notification_Wishlist'
            batch.each{ |account_id|
              self.set_currency_details(account_id)
              message << price_drop_wishlist_msg(account_id, diff, price, img, msg, campaign_key)
            }
            LocalyticsNotification.customer_notification(message.compact, campaign_key, app_source) if app_source.include?('Android') && LOCALYTICS_NOTIFICATION == 'true'
            ClevertapNotification.modify_and_push_notification(message.compact, campaign_key, app_source.downcase) if app_source.include?('iOS') || CLEVERTAP_NOTIFICATION == 'true'
          end
        end
      end
    end
  end

  def calculate_line_item_total
    amt = calculate_price(@current_line_item.snapshot_price, @currency_details.rate)
    @current_line_item.line_item_addons.each{ |line_item_addon|
      amt += calculate_price(line_item_addon.addon_type_value.price, @currency_details.rate)
    }
    amt = (amt * @current_line_item.quantity)
  end

  def variant_params(option_type_value)
    {
      p_name: option_type_value.p_name,
      option_type: option_type_value.option_type.p_name
    }.stringify_keys
  end

  def calculate_variants
    if (variant_id = @current_line_item.variant_id).present? && @current_line_item.variant.present?
      option_type_values = []
      @current_line_item.variant.option_type_values.each{ |option_type_value|
        option_type_values << variant_params(option_type_value)
      }
      {
        variant_id: variant_id.to_s,
        option_type_values: option_type_values
      }.stringify_keys
    end
  end

  def chargeable?(line_item_addon)
    line_item_addon.snapshot_price > 0
  end

  def addon_param(line_item_addon)
    if chargeable?(line_item_addon)
      {
        name: line_item_addon.try(:addon_type_value).try(:name),
        currency_symbol: @currency_details.symbol,
        snapshot_price: "#{calculate_price(line_item_addon.addon_type_value.price, @currency_details.rate)}"
      }.stringify_keys
    end
  end

  def calculate_addons
    addons = []
    if (li_addons = @current_line_item.line_item_addons).present?
      li_addons.each{ |line_item_addon|
        addons << addon_param(line_item_addon)
      }
    end
    addons.compact
  end

  def genuine_price_drop_cart?(account_id, price = nil)
    if (@current_line_item = LineItem.joins(:cart).merge(Account.find(account_id).unused_carts).
      where('line_items.design_id' => id).
      first).present?
      if price.present? && (@current_line_item.snapshot_country_code == @currency_details.country_code)
        current_price = calculate_price(price, @currency_details.rate)
        li_price = calculate_price(@current_line_item.snapshot_price, @current_line_item.snapshot_currency_rate.to_f)
        current_price < li_price
      end
    else
      false
    end
  end

  def price_drop_cart_msg(account_id, diff, price, img, msg, campaign_key = 'auto_notification')
    if genuine_price_drop_cart?(account_id, price)
      extra = self.extra_params.merge(
        type: "Price_Dropped_Cart",
        PushImageHandler: img,
        ignore_threshold: true,
        notificationMainTitle: msg['title'],
        cart_id: "#{@current_line_item.cart_id}",
        line_item_id: "#{@current_line_item.id}",
        quantity: "#{@current_line_item.quantity}",
        design_add_ons: calculate_addons,
        design_variants: calculate_variants,
        reduced_amount: "#{calculate_price(diff, @currency_details.rate)}",
        actual_price: "#{calculate_price((price+diff), @currency_details.rate)}",
        snapshot_price: "#{calculate_price(@current_line_item.snapshot_price, @currency_details.rate)}",
        note: @current_line_item.note,
        total: "#{calculate_line_item_total}"
      )
      extra.merge!(set_utm_tracking_detail(campaign_key))

      {
        target: account_id.to_s,
        alert: msg,
        android: {extra: extra.stringify_keys}.stringify_keys
      }.stringify_keys
    else
      nil
    end
  end

  def notification_image_url(img)
    "#{ActionController::Base.helpers.image_path(img)}"
  end

  def get_allowed_ids(account_ids)
    app_versions = ALLOWED_APP_VERSIONS | ALLOWED_IOS_APP_VERSIONS[5..-1]
    ApiData.where(account_id: account_ids, app_version: app_versions).select([:account_id, :app_source]).group_by(&:app_source)
  end

  def price_drop_notification_cart(account_ids, diff, price)
    app_account_ids = get_allowed_ids(account_ids)
    app_account_ids.keys.each do |app_source|
      account_ids = []
      app_account_ids[app_source].each{ |data| account_ids << data.account_id }
      if account_ids.length > 0
        img = self.master_image.photo(:large_m) #notification_image_url('price_drop.jpg') #image to be displayed in notification panel.
        msg = LOCALYTICS_MESSAGES['design']['price_drop']['cart']
        account_ids.each_slice(LOCALYTICS_BATCH_SIZE) do |batch|
          if FCM_NOTIFICATION
            fcm_token = []
            batch.each{ |account_id|
              fcm_token << Account.find_by_id(account_id).try(:fcm_registration_token) if genuine_price_drop_cart?(account_id, price)
            }
            fcm_token.compact!
            FirebaseNotification.fcm_push_notification(msg['title'], msg['body'], img, fcm_token, app_source) if fcm_token && app_source.exclude?('Designer')
          else
            message = []
            campaign_key = 'Price_Dropped_Notification_Cart'
            batch.each{ |account_id|
              self.set_currency_details(account_id)
              message << price_drop_cart_msg(account_id, diff, price, img, msg, campaign_key)
            }
            LocalyticsNotification.customer_notification(message.compact, campaign_key, app_source) if app_source.include?('Android') && LOCALYTICS_NOTIFICATION == 'true'
            ClevertapNotification.modify_and_push_notification(message.compact, campaign_key, app_source.downcase) if app_source.include?('iOS') || CLEVERTAP_NOTIFICATION == 'true'
          end
        end
      end
    end
  end

  def find_account_ids(user_ids)
    Account.where(accountable_id: user_ids,accountable_type: 'User').pluck(:id)
  end

  def price_drop_notification_android
    if NOTIFICATIONS_ENABLE == 'true'
      if discount_price_changed? && discount_price_was && (discount_price_was > discount_price) && ((discount_price_was - discount_price) >= THRESHOLD_AMT)
        user_ids = Cart.user.joins(:line_items).where("line_items.design_id" => id)
        .where('used = ? and carts.updated_at > ?', false, 90.days.ago).pluck(:user_id).uniq.compact

        if user_ids.present?
          SidekiqDelayGenericJob.perform_async(self.class.to_s, self.id ,"price_drop_notification_cart", find_account_ids(user_ids), (self.discount_price_was - self.discount_price), self.discount_price)
          #self.sidekiq_delay.() #delayed
        end

        user_ids = Wishlist.where(design_id: self.id).wished.pluck(:user_id).uniq.compact - user_ids
        if user_ids.present?
          SidekiqDelayGenericJob.perform_async(self.class.to_s, self.id ,"price_drop_notification_wishlist", find_account_ids(user_ids), (self.discount_price_was - self.discount_price), self.discount_price)
          #self.sidekiq_delay
          #    .price_drop_notification_wishlist(,
          #) #delayed
        end
      end
    end
  end

  def self.global_sale_notification(notify_accounts, campaign_key, msg, method)
    if notify_accounts && (account_ids = notify_accounts.keys.uniq.compact).present?
      app_versions = ALLOWED_APP_VERSIONS | ALLOWED_IOS_APP_VERSIONS[5..-1]
      app_account_ids = ApiData.where(account_id: account_ids, app_version: app_versions).select([:account_id, :app_source]).group_by(&:app_source)
      app_account_ids.keys.each do |app_source|
        account_ids = app_account_ids[app_source].collect(&:account_id)
        if account_ids.length > 0
          filtered_account = notify_accounts.select {|k,v| account_ids.include? k}
          filtered_account.each_slice(LOCALYTICS_BATCH_SIZE) do |batch|
            if FCM_NOTIFICATION
              batch.each{ |account_id,values|
                img = values[:design].master_image.photo(:large_m)
                fcm_token = Account.find_by_id(account_id).try(:fcm_registration_token) if genuine_price_drop_wishlist?(account_id, values[:effective_price])
                FirebaseNotification.fcm_push_notification(msg['title'], msg['body'], img, fcm_token, app_source) if fcm_token && app_source.exclude?('Designer')
              }
            else
              message = batch.collect do |account_id,values|
                          img = values[:design].master_image.photo(:large_m)
                          values[:design].set_currency_details(account_id)
                          values[:design].send("price_drop_#{method}_msg", account_id, values[:diff], values[:effective_price], img, msg, campaign_key)
                        end
              LocalyticsNotification.customer_notification(message.compact, campaign_key, app_source) if app_source.include?('Android') && LOCALYTICS_NOTIFICATION == 'true'
              ClevertapNotification.modify_and_push_notification(message.compact, campaign_key, app_source.downcase) if app_source.include?('iOS') || CLEVERTAP_NOTIFICATION == 'true'
            end
          end
        end
      end
    end
  end

  def set_utm_tracking_detail(campaign_key)
    { utm_source: 'android_or_ios', utm_medium: 'auto_notification', utm_campaign: campaign_key }
  end

  # State Machine
  # In Stock - Quantity > 0
  # Blocked - Quantity = 0 design has X orders and quantity was X therefore X-X
  # Seller Sold Out - Quantity = 0. Seller says the item is out of stock
  # Sold Out - Quantity = 0. Current quantity gets sold out
  # Delete - When seller triggers delete

  # sold out state -> :available event -> in_stock state
  #                                           ""    -> :empty_stock event -> sold_out state
  #                                           ""    -> :orders_pending_item_sold_out event -> blocked state
  # all states Except Delete -> :available -> in stock state
  # all states -> :unavailable -> seller sold out state
  # all states -> :remove_stock -> Delete state
  # all states Except Delete -> :bad_design -> :banned

  state_machine :initial => :sold_out do
    event :bad_designer do
      transition [:sold_out, :processing, :reject] => :review
    end

    event :updated_design_details do
      transition in_stock: :review
    end

    before_transition :to => :review do |design|
      design.review_on = Time.now
    end

    event :approved do
      transition [:review, :reject] => :sold_out
    end

    event :reject_design do
      transition [:sold_out, :seller_out_of_stock, :blocked, :banned, :review, :processing, :in_stock] => :reject
    end

    before_transition :to => :reject do |design|
      design.reject_on = Time.now
    end

    event :available do
      transition [:processing, :sold_out, :seller_out_of_stock, :blocked, :banned, :review, :on_hold, :reject] => :in_stock, if: lambda {|design| !design.is_transfer_model? || (design.is_transfer_model? && design.transfer_price.present?) }
    end

    before_transition :to => :in_stock do |design|
      design.last_in_stock = Time.now
      if (design.designer.state_machine == 'vacation' && design.in_stock_warehouse.to_i == 0)
        design.designer_on_vacation
        false
      end
      # if design.design_performance.nil?
      #   DesignPerformance.create(design_id: design.id)
      # end
#      return true
    end

    event :unavailable do
      transition [:blocked, :in_stock, :sold_out] => :seller_out_of_stock
    end

    before_transition :to => :seller_out_of_stock do |design|
      design.last_seller_out_of_stock = Time.now
      design.remove_item_quantity
    end

    event :empty_stock do
      transition [:in_stock, :blocked] => :sold_out
    end

    before_transition :to => :sold_out do |design|
      design.last_sold_out = Time.now
    end

    event :delete_item_mirraw do
      transition [:sold_out, :seller_out_of_stock, :blocked, :in_stock, :banned, :review, :reject] => :delete_by_mirraw
    end

    event :delete_item_designer do
      transition [:sold_out, :seller_out_of_stock, :blocked, :in_stock, :banned, :review, :reject] => :delete_by_designer
    end

    before_transition :to => [:delete_by_designer] do |design|
      design.deleted_on = Time.now
      design.remove_item_quantity
    end

    before_transition :to => [:delete_by_mirraw] do |design|
      design.deleted_on = Time.now
      design.remove_item_quantity
    end

    event :orders_pending_item_sold_out do
      transition [:in_stock] => :blocked
    end

    before_transition :to => :blocked do |design|
      design.last_blocked = Time.now
    end

    event :bad_design do
      transition [:in_stock, :sold_out, :seller_out_of_stock, :blocked, :review] => :banned
    end

    before_transition :to => :banned do |design|
      design.last_banned = Time.now
      design.remove_item_quantity
    end

    event :designer_on_vacation do
      transition all => :on_hold
    end

    after_transition in_stock: [:sold_out, :seller_out_of_stock, :blocked, :on_hold, :banned, :reject, :delete_by_designer, :delete_by_mirraw] do |design|
      if (current_design_cluster = design.design_cluster).present?
        current_design_cluster.update_column(:state_multiplier, 0)
        current_design_cluster.recalculate_winner
      end
    end

    after_transition to: :in_stock do |design|
      # Need to optimize
      #design.in_stock_notification_android if design.quantity > THRESHOLD_QTY && design.variants.blank?
      designer = design.designer
      SidekiqDelayGenericJob.perform_async(design.class.to_s, design.id, "create_and_update_score") unless design.design_score.present?
      #design.sidekiq_delay
      #      .create_and_update_score unless design.design_score.present?
      SidekiqDelayGenericJob.perform_async(designer.class.to_s, designer.id, "update_design_count_score") if designer.present? && !designer.try(:score_flag)
      #designer.sidekiq_delay
      #        .update_design_count_score if designer.present? && !designer.try(:score_flag)
      design.update_reviews
      # design.delay.update_similar
      designer_catalog = Designer::IN_CATALOG[designer.in_catalog]
      if (var = design.check_for_catalog_remove(designer_catalog))
        design.send("#{var}_catalog_remove")
      end
      if (current_design_cluster = design.design_cluster).present?
        current_design_cluster.update_column(:state_multiplier, 1)
        current_design_cluster.recalculate_winner
      end
    end
  end

  def check_for_catalog_remove(designer_catalog)
    designer_state = REMOVE_CATALOG_HASH[designer_catalog.to_sym][:value]
    design_state = in_catalog_one
    if designer_state == design_state
      return false
    elsif (designer_state == 1 && design_state == 2) || (designer_state == 2 && design_state == 1) || (design_state == 0) || (design_state == 3)
      return 'all'
    elsif design_state.nil?
      return REMOVE_CATALOG_HASH.find{|k,v| v[:value] == designer_state}[0]
    elsif designer_state.nil?
      return REMOVE_CATALOG_HASH.find{|k,v| v[:value] == design_state}[0]
    elsif designer_state == 3
      return 'banned'
    end
    false
  end

  def category_parents
    parents = Array.new
    self.categories.each do |category|
      parents.push *category.cached_self_and_ancestors_id
    end
    if consider_cluster_attrs?
      design_cluster.other_clusters.collect(&:cluster_related_design).compact.collect(&:categories).compact.flatten.each do |cat|
        parents.push *cat.cached_self_and_ancestors_id
      end
    end
    return parents.uniq
  end

  # Input params - none
  #
  # Returns all ancestors category names for design category
  def category_parents_name
    @category_parents_name ||= begin
      parents = Array.new
      self.categories.each {|cat| parents += cat.cached_self_and_ancestors_name }
      parents.uniq
    end
  end

  def get_property_values(property_id)
    self.property_values.where(:property_id => property_id).collect(&:id)
  end

  def similar_designs
    RequestStore.cache_fetch("similar_designs_id_#{self.id}" , expires_in: 24.hour) do
      begin
        designs = Sunspot.more_like_this(self) do
          fields :category_name, :designer_name, :title, :description, :specification
          with(:state, 'in_stock')
          with(:average_rating, 4..5)
          with(:cluster_winner, 1)
        end
        designs.raw_results.collect{|rr| rr.primary_key.to_i}
      rescue Errno::ECONNREFUSED
        []
      end
    end
  end

  def self.bulk_flush_cache(design_ids= nil)
    # design_ids not passed then return ids from scope
    design_ids ||= pluck(:id)
    currency_convert_values = CurrencyConvert.currency_convert_memcached
    currency_convert_values.each do |c|
      cache_list = self.cache_items(c[:symbol],c[:country_code],design_ids)
      cache_list.each {|value| Rails.cache.delete(value)}
    end
  end

  def self.cache_items(symbol,country_code,design_ids)
    cache_list = Array.new
    design_ids.each do |id|
      id_s = cache_key + ':'+ symbol+ '_' + country_code
      cache_list.push(*[
                    id_s + '_details',
                    id_s + '_img',
                    # id_s + '_wholesale',
                    id_s + '_microdata_header',
                    # id_s + '_similar',
                    id_s + '_rating'
                 ])
    end
    cache_list
  end

  def flush_cache
    currency_convert_values = CurrencyConvert.currency_convert_memcached
    currency_convert_values.each do |c|
      cache_list = cache_items(c[:symbol],c[:country_code])
      cache_list.each {|index, value| Rails.cache.delete(value)}
    end
  end

  def cache_items(symbol,country_code)
    id_s       = cache_key + ':'+ symbol+'_'+country_code
    cache_list = {:details => id_s + '_details',
                   :img => id_s + '_img',
                   # :wsale => id_s + '_wholesale',
                   :microdata_header => id_s + '_microdata_header',
                   # :similar => id_s + '_similar',
                   :rating => id_s + '_rating'
                 }
  end

  def fetch_cache(symbol,country_code)
    cache_list  = self.cache_items(symbol,country_code)
    RequestStore.cache_preload(*cache_list.slice(:details,:img,:microdata_header,:rating).values)
    cache_items = Hash.new
    RequestStore._preload
    cache_items = {:details => RequestStore.store[cache_list[:details]],
                   :img => RequestStore.store[cache_list[:img]],
                   # :wsale => Rails.cache.fetch(cache_list[:wsale]),
                   :microdata_header => RequestStore.store[cache_list[:microdata_header]],
                   # :similar => Rails.cache.fetch(cache_list[:similar]),
                   :rating => RequestStore.store[cache_list[:rating]]}

    present     = cache_items[:details].present? && cache_items[:img].present? &&
                  cache_items[:microdata_header].present? && cache_items[:rating].present? # && cache_items[:wsale].present? && cache_items[:similar].present?

    return [cache_list, present, cache_items]
  end

  def get_option_type_values(preloaded=false)
    if preloaded
      variants.select{|variant| variant.quantity.to_i > 0 && variant.stitched == true}.map(&:option_type_value_ids).flatten
    else
      variants.select('option_type_values.id').joins(:option_type_values).where('variants.quantity > 0 and variants.stitched = ?', true).pluck('option_type_values.id')
    end
  end

  def self.addon_designs_for_category category_name
    Design.joins(:categories).in_category(category_name).joins(:addon_type_values)
  end

  def self.remove_addons design_ids, category_name
    unless design_ids.blank?
      designs = Design.where(id: design_ids)
      designs.each do |design|
        if design.addon_type_values.joins(addon_type: :category).in_category(category_name)
          design.addon_type_values.each do |addon_type_value|
            if addon_type_value.line_item_addons.present?
              addon_type_value.update_attribute(:published, false) if addon_type_value.published?
            else
              addon_type_value.addon_option_values.each do |addon_option_value|
                addon_option_value.addon_option_type.destroy if addon_option_value.addon_option_type.present?
                addon_option_value.destroy
              end
              addon_type_value.destroy
            end
          end
        else
        end
      end

      # designs.each do |design|
      #   design.flush_cache
      # end
    end
  end


  def update_reviews
    if (count = reviews.approved.count) > 0
      rating = reviews.approved.average('rating')
      domestic_rating = reviews.where(:geo => 'domestic').approved.average('rating')
      international_rating = reviews.where(:geo => 'international').approved.average('rating')
      update_column('total_review', count)
      update_column('average_rating', rating.round(2)) if rating.present?
      update_column('average_rating_domestic',domestic_rating.round(2)) if domestic_rating.present?
      update_column('average_rating_international', international_rating.round(2)) if international_rating.present?
    else
      update_column('total_review', 0)
      update_column('average_rating', 0.0)
    end
  end

  def self.update_effective_prices(symbol, response, country_code)
    unique_ids = response['Recommendations'].collect{ |design| design['uniqueId']}
    preload_array = [:dynamic_price_for_current_country, :designer]
    active_promotions = PromotionPipeLine.active_promotions
    preload_array << :categories unless Promotions.discount_offer_on_category(active_promotions).blank?
    catalog_geo = %w(inr rs).include?(symbol.downcase) ? :domestic : :international
    designs = Design.includes(preload_array).where(id: unique_ids).where(state: 'in_stock').not_banned_in_catalog(catalog_geo).order('average_rating desc')
    details_hash = designs.each_with_object({}){|design,hash| hash[design.id.to_s]= [design.effective_price,design.designer.to_param,design.to_param]}
    response['Recommendations'].delete_if{|design| !details_hash.has_key?(design['uniqueId']) || design['imageUrl'].blank? }
    response['Recommendations'].each do |design|
      design_data = details_hash[design['uniqueId']]
      design['discount_price'] = CurrencyConvert.convert_to(symbol, design_data[0] || design['discount_price'].to_i,country_code).round(2)
      design['designer_slug'] = design_data[1]
      design['design_slug'] = design_data[2]
    end
    response
  end

  def self.reivew_percentage(reviews,total)
    star_percentage = Array.new(6, 0)
    star_rating = Array.new(6, 0)
    if total.to_i > 0
      rating = reviews.group(:rating).count
      rating.each do |key,value|
        star_rating[key] = value
        star_percentage[key]=(value * 100) / total
      end
    end
    [star_percentage, star_rating]
  end

  def find_property_values(value, property_name)
    property = Property.select(:id).find_by_name property_name
    PropertyValue.where("property_id = #{property.id} and name = ?", value.downcase).try(:first)
  end


  def update_bulk_designs(fileurl)
    csv = Roo::Spreadsheet.open(fileurl, extension: :csv)
    csv.each_with_index do |row, index|
      if index > 0
        design = Design.find_by_id row[40]
        design.update_single_record(row) if design.present?
      end
    end
  end

  def get_product_type_initial
    if self.designable_type.present? && self.designable_type != 'Other'
      case self.designable_type
      when 'SalwarKameez'
        return 'SK-'
      when  'Lehenga'
        return 'L-'
      when  'Saree'
        return 'S-'
      when 'Jewellery'
        return 'J-'
      when 'Bag'
        return 'B-'
      else
        return 'O-'
      end
    else
      category_list = Hash.new
      category_list['J-'] = ['jewellery', 'bridal-sets']
      category_list['B-'] = ['bags']
      category_list['S-'] = ['sarees', 'bridal-sarees', "shorts","shawls","skirts","tunics","tops","pyjamas","stole-and-dupattas"]
      category_list['SK-'] = ['salwar-kameez', 'dresses']
      category_list['L-'] = ['bridal-lehengas', 'lehengas']
      category_list['K-'] = ['kurtas-and-kurtis', 'men-kurtas']
      design_root_categories_names = self.category_parents_name.collect{|c| c.downcase}
      category_list.each do |product_classification, category_names|
        if (design_root_categories_names & category_names).present?
          return product_classification
        end
      end
      return 'O-'
    end
  end

  def update_single_record(row)
    begin
      blouse_avail = ''
      product_type = 'saree'
      if row[11].downcase == 'yes'
        blouse_avail = "With Blouse"
      end
      Design.transaction do
        design = Design.find_by_id row[40]
        design['design_code'] = row[0]
        design['title'] = "#{row[6]} #{row[9]} #{row[5]} #{product_type} #{blouse_avail}"
        design['embellish'] = row[36]
        design['description'] = row[38]
        design['package_details'] = row[3]
        design['price'] = row[28].to_i
        design['discount_percent'] = row[29].to_i
        design['weight'] = row[4].to_i
        design['tag_list'] = row[26]
        design['pattern'] = row[35]
        design['region'] = row[34]
        design['quantity'] = row[27].to_i
        design['published'] = true
        design.sell_count = 0
        design.grade      = 0
        design.category_ids = Category.where('name ILIKE LOWER(?)',row[2].to_s).pluck(:id).first
        images = []
        image = Image.new
        image.kind = 'master'
        image.photo = row[21] if row[21].present?
        image.save
        images << image
        (22...25).each do |item|
          image = Image.new
          if (row[item].present?) && (row[item].include?'.jp')
            image.photo = row[item]
            image.save
            images << image
          end
        end
        property_values = []

        property_values << find_property_values(row[5], 'fabric_of_saree') if row[5].present?
        property_values << find_property_values(row[9], 'work') if row[9].present?
        property_values << find_property_values(row[10], 'type') if row[10].present?
        property_values << find_property_values(row[6], 'saree_color') if row[6].present?
        property_values << find_property_values(row[14], 'fabric_of_blouse') if row[14].present?
        property_values << find_property_values(row[15], 'blouse_color') if row[15].present?
        property_values << find_property_values(row[16], 'blouse_work') if row[16].present?
        property_values << find_property_values(row[19], 'petticoat_color') if row[19].present?
        property_values << find_property_values(row[20], 'fabric_of_petticoat') if row[20].present?
        property_values << find_property_values(row[37], 'celebrity') if row[37].present?
        property_values << find_property_values(row[30], 'occasion') if row[30].present?
        property_values << find_property_values(row[31], 'look') if row[31].present?
        property_values << find_property_values(row[32], 'saree_border') if row[32].present?
        property_values << find_property_values(row[33], 'pallu_style') if row[33].present?

        design.property_values = property_values.compact
        design.images = images

        d = design.designable
        d['width'] = row[8]
        d['length'] = row[7]
        d['saree_color'] = row[6]
        d['blouse_available'] = row[11]
        d['blouse_image'] = row[12]
        d['blouse_size'] = row[14]
        d['blouse_fabric'] = row[12]
        d['blouse_color'] = row[13]
        d['blouse_work'] = row[16]
        d['petticoat_available'] = row[17]
        d['petticoat_size'] = row[18]
        d['petticoat_color'] = row[19]
        d['petticoat_fabric'] = row[20]
        design.save!
      end # end of transaction
    rescue => error
      ExceptionNotify.sidekiq_delay.notify_exceptions('Bulk Edit Error','Bulk Edit for a record Failed',{ row: row.inspect,error: error.inspect})
    end
  end

  def self.to_csv
    CSV.generate do |csv|
      csv << ['design_code', 'title', 'category', 'package details', 'weight_in_gms', 'fabric_of_saree',' work',  'type',  'width_of_saree_in_inches',  'length_of_saree_in_metres', 'saree_color', 'blouse_availability', 'blouse_as_shown_in_the_image',  'size_of_blouse_in_cms', 'fabric_of_blouse',  'blouse_color',  'blouse_work', 'petticoat_availability',  'size_of_petticoat_metres',  'color_of_petticoat', 'fabric_of_petticoat', 'image', 'image1',  'image2',  'image3',  'image4',  'tag_list',  'quantity',  'price', 'discount_percent',  'occasion',  'look',  'saree_border',  'pallu_style', 'region',  'pattern', 'embellish', 'celebrity', 'description', 'product_type']

      all.each do |emp|
        csv << [design.design_id, design.name, design.dob, design.designation] # Row values of CSV
      end
    end
  end

  def stitchable_quantity
    package_details.to_s.scan(/\d+/).collect(&:to_i).max.try(:nonzero?) || 1
  end

  def create_and_update_score
    unless design_score.present?
      create_design_score
      DesignNode.where(dimension_type: 'Designer',dimension_id: designer_id).update_all(reload_count: true)
      DesignNode.where(dimension_type: 'Category',dimension_id: categories.collect(&:cached_self_and_ancestors_id).flatten.uniq).update_all(reload_count: true)
      DesignNode.where(dimension_type: 'PropertyValue',dimension_id: property_value_ids).update_all(reload_count: true)
    end
  end

  def mark_out_of_stock(variant = nil)
    if variant.present?
      variant.update_attributes(quantity: 0)
    else
      self.unavailable if can_unavailable?
    end
  end

  def available_category_ids
    Design.designable_to_category_mapping(designable_type)
  end

  def available_self_and_descendants_category_ids
    self.class.designable_self_and_descendants_category_ids(designable_type)
  end

  def get_valid_categories
    # categories = designable_type == 'Other' ?  Category.preload(:parent).leaves : Category.where(id: available_self_and_descendants_category_ids).preload(:parent).leaves
    # return categories.unshift(*categories.inject(Set.new){|r,cat| r << cat.parent }.to_a.compact)
    Rails.cache.fetch("available_categories",expires_in: 24.hours) do
      categories = Category.preload(:parent).leaves
      return categories.unshift(*categories.inject(Set.new){|r,cat| r << cat.parent }.to_a.compact)
    end
  end

  def available_category_ids?
    available_category_ids.present?
  end

  def initialize_fields
    self.premium = false
    true
  end

  def get_property_names
    @property_names ||= Property.get_property_values_list.slice(*BulkUpload.meta_data[version].try(:[],:mandatory_columns).try(:[],:designs_property_values).to_a.map(&:to_s))
  end

  def experiment_id(tester, type)
    case type
    when :grading
      if (ALLOW_AB_TEST_GRADING && designable_type.to_s.downcase == tester.attribute_response(:grading, :category))
        tester.decryptor tester.running(:current_experiments)[:grading], :grading
      else
        ''.freeze
      end
    end
  end

  def listed_since
    (Date.today - cluster_common_created_at.to_date).abs.to_i
  end

  #factorize : to distinguish between set of designs in last DESIGN_PERFORMANCE_METRIC_DURATION period and once not in that.
  def trending_calculation(factorize: false)
    if factorize
      (self.send("metric_sales_#{DESIGN_PERFORMANCE_METRIC_DURATION}").to_f / ([DESIGN_PERFORMANCE_METRIC_DURATION, self.listed_since].min)) * discount_price * designer_metric_calculation
    else
      (self.send("metric_sales_#{DESIGN_PERFORMANCE_METRIC_DURATION}").to_f * 1000 / ([DESIGN_PERFORMANCE_METRIC_DURATION, self.listed_since].min)) * discount_price * designer_metric_calculation
    end
  end

  def performance_metric_cibs(metric=1)
    case metric
    when 1
      if sell_count.to_i > 0
        if created_at > 6.months.ago
          return (trending_calculation)
        else
          return (trending_calculation(factorize: true))
        end
      end
      0.0
    when 2
      return (((sell_count.to_i) * 100 / clicks.to_f) * discount_price * designer_metric_calculation)
    else
      0.0
    end
  end

  def designer_metric_calculation
    odr = designer.try(:odr_90).try(:value).to_f
    odr > 0.08 ? 0 : 1
  end

  def get_similar_from_mongo(designer_id: nil)
    similar_image_results = IMAGE_SEARCH_DISABLED && designer_id.nil? ? [] : Rails.cache.fetch("similar_image_design_#{self.id}", :expires_in => 1.day) do
      result_set  = Rails.cache.fetch("similar_image_result_#{self.id}", :expires_in => 30.day) do
        MONOGODB_PRIMARY_COLLECTION.distinct(id) rescue [] #change to this if aarchitecture changes MONOGODB_PRIMARY_COLLECTION.find({'design_id': params[:id]}).distinct('similar')
      end
      if result_set.present?
        result_set.delete(self.id)
        result_set  = Design.includes(:designer, :images).published.where(id: result_set.uniq).where('designer_id <> ?',designer_id).active_designer_designs.order('designs.average_rating desc').to_a
      end
      result_set
    end.to_a
    if designer_id.present? && (design_ids = Rails.cache.fetch("similar_image_result_#{self.id}", :expires_in => 30.day)).present?
      Design.includes(:designer, :images).published.where(id: (design_ids.to_a - [self.id])).where('designer_id = ?',designer_id).to_a
    else
      similar_image_results
    end
  end

  def update_similar
    similar_count = if block_given?
                      yield
                    else
                      get_similar_from_mongo.length
                    end
    update_column(:similar_count, similar_count)
  end

  def cart_addon?
    CART_ADDON.to_h.values.map(&:values).flatten.include?(id)
  end

  def eta
    super.to_i <= 0 ? designer.try(:eta) : super
  end

  def get_hsn_code_gst_rate(per_price_item, hsn_code: nil)
    if hsn_code.blank?
      hsn_code = (designer.hsn_approved && gst_rate.present? && self.hsn_code.present?) ? self.hsn_code.to_s : categories.hsn_code
    end
    gst_rate = if hsn_code.match(/^6(1|2)..$/)
      per_price_item > 1000 ? 12 : 5
    elsif hsn_code.match(/^640(1|2|3|4|5)$/)
      per_price_item > 1000 ? 18 : 12
    elsif hsn_code.match(/^9404$/)
      per_price_item > 1000 ? 18 : 5
    else
      hsn_code.length <= 4 ? self.gst_rate : Shipment.get_gst_rate(hsn_code,per_price_item,1)
    end

    [hsn_code, gst_rate.to_f]
  end

  def get_design_type
    @type_of_design ||= (if designable_type == 'SalwarKameez' && (ANARKALI_CATEGORY_FOR_STANDARD_ADDON & categories.to_a.map(&:id))
      'Anarkali'
    else
      designable_type
    end)
  end

  class << self

    # calculates max grades for salwar saree lehenga kurti and jewellery for store/women page also can be used for collection page.
    def max_grades
      Rails.cache.fetch("design_max_grade",expires_in: 24.hour) do
        grades = {}
        Design.select('max(international_grade) as international_grade, max(grade) as grade, designable_type').where(designable_type: ['SalwarKameez', 'Saree', 'Lehenga', 'Kurti', 'Jewellery']).group(:designable_type).each do |design|
          grades[design.designable_type] = {'international_grade' => design.international_grade, 'grade' => design.grade}
        end
        grade_m = grades.collect{|t,v| v['grade']}.max
        i_grade_m = grades.collect{|t,v| v['international_grade']}.max
        grade_multiplier=Hash.new()
        grades.each do |type, value|
          grade_multiplier[type] = {'grade'=>  (grade_m - value['grade']),
                                    'international_grade' => (i_grade_m - value['international_grade'])}
        end
        grade_multiplier
      end
    end

    def designable_to_category_mapping(designable_type)
      RequestStore.cache_fetch("designable_#{designable_type}_to_category_mapping",expires_in: 24.hour) do
        (_designable_to_category_mapping[designable_type]||[]).collect do |category_name|
          Category.names_to_ids(category_name).values
        end.flatten.uniq
      end
    end

    def get_designable(category_name)
      hash = (_designable_to_category_mapping) || {}
      for k,v in hash
        categories = []
        v.each{|o| Category.names_to_ids(o).keys.each{|c| categories << c.to_s.tr('_','-')}}
        if categories.include?(category_name)
          return k
        end
      end
      return nil
    end

    def get_max_uploadable_categories
      account_type = Account.current_account.try(:accountable_type)
      max_uploadable_categories = MAX_UPLOADABLE_CATEGORIES["default"]
      if account_type.present? && MAX_UPLOADABLE_CATEGORIES.key?(account_type.try(:downcase))
        max_uploadable_categories = MAX_UPLOADABLE_CATEGORIES[account_type.downcase]
      end
      return max_uploadable_categories
    end

    def designable_self_and_descendants_category_ids(designable_type)
      Rails.cache.fetch("#{designable_type}_available_self_and_descendants_category_ids",expires_in: 24.hours) do
        Category.where(id: Design.designable_to_category_mapping(designable_type)).map do |category|
          category.try(:cached_self_and_descendants_id)
        end.flatten.uniq
      end
    end

    def calculate_ddr(design_ids)
      max_date = PerformanceMetricValue::SALES_METRIC_DAYS.max
      Design.joins(line_items: [:scope_events, order: :delivery_nps_info]).
      select([
        'designs.id as design_id',
        *PerformanceMetricValue::SALES_METRIC_DAYS.map do |day|
          [
            "count(DISTINCT CASE WHEN scope_events.name = 'NegativeFeedback' and delivery_nps_infos.promised_delivery_date >= (now() - interval '#{day}' DAY) THEN scope_scores.id END) AS negative_feedback_#{day}",
            "count(DISTINCT CASE WHEN delivery_nps_infos.promised_delivery_date >= (now() - interval '#{day}' DAY) then line_items.id end) AS total_line_item_#{day}",
            "count(DISTINCT CASE WHEN scope_events.name = 'PositiveFeedback' and delivery_nps_infos.promised_delivery_date >= (now() - interval '#{day}' DAY) THEN scope_scores.id END) as positive_feedback_#{day}",
           ]
        end.flatten
      ]).
      group('designs.id').
      where do
        (orders.state >> ['dispatched','complete']) &
        (delivery_nps_infos.promised_delivery_date >= max_date.days.ago)
      end.
      having do
        (designs.id >> Array.wrap(design_ids))
      end.
      map do |design|
        [design.design_id,
          PerformanceMetricValue::SALES_METRIC_DAYS.map do |day|
           [["ddr_#{day}".to_sym, (design["negative_feedback_#{day}"] / design["total_line_item_#{day}"].to_f unless design["total_line_item_#{day}"].zero?)],
           ["ratio_#{day}".to_sym, (design["negative_feedback_#{day}".to_sym] / design["positive_feedback_#{day}".to_sym].to_f if design["positive_feedback_#{day}"].to_i.nonzero? &&  design["positive_feedback_#{day}"].to_i >= design["negative_feedback_#{day}"].to_i)]]
          end.flatten(1).to_h
        ]
      end.to_h
    end



    private
    def _designable_to_category_mapping
      @designable_to_category_mapping ||={
        'Saree' => ['sarees','kids-sarees','lehenga-sarees','navratri-sarees-nine-days'],
        'SalwarKameez' => ['salwar-kameez','party-wear-gowns','indian-dresses','dresses','plus-size-salwar','indowestern'],
        'Kurti' => ['kurtas-and-kurtis','plus-size-kurtis', 'tunics', 'western-wear', 'kurta-sets'],
        'Jewellery' => ['jewellery','gemstones','bridal-sets','clutches','accessories','perfumes','wallets','eid-jewellery','women-accessories'],
        'Lehenga' => ['lehengas','navratri-lehenga-chaniya-choli','eid-lehenga', 'kids-lehenga-choli', 'plus-size-lehenga'],
        'HomeDecor' => ['home-decor','by-festivals', 'tapestries'],
        'Bag' => ['bags'],
        'Kurta' => ['men-kurtas', 'ethnic-wear', 'kurta-pajama', 'sherwani', 'indo-western-dresses', 'nehru-jacket', 'men-wedding-dresses', 'pathani-suits', 'dhoti-kurta', 'bandhgala-suit', 'men-pyjamas', 'men-islamic-clothing'],
        'Jacket' => ['indo-western-dresses','men-jackets','nehru-jacket','ethnic-jackets','sherwani','girls-jackets-coats'],
        'Consumable' => ['organic-foods','organic-tea', 'tea','essential-oils'],
        'Turban' => ['turbans'],
        'Islamic' => ['islamic-clothing'],
        'Kid' => ['kids', 'kids_pavadai_set']
      }.merge(DESIGNABLE_CATEGORY_MAPPING)
    end
  end

  def critical_eta
    eta + Designer::SLA_CONFIG[:sla_offset].to_i
  end

  def image_prcess_for_log(cat_ids)
    if (LONG_DIV_CATEGORY_IDS.to_a & cat_ids).present?
      master = self.images.where{kind == 'master'}.take
      if master && master.photo_updated_at <= LONG_DIV_RUN_DATE
        master.processed = true
        master.photo.reprocess! :long
        master.update_column(:photo_processing, false)
      end
    end
  end

  def mirraw_recommended?
    average_rating.to_f >= 3.5
  end

  def is_luxury_category?
    return true  if designer.designer_type == "Tier 1 Designer"
    luxe = Category.where(name: "luxe").first
    if luxe.present?
      child_category = luxe.self_and_descendants.pluck(:name)
      return true if (child_category & self.categories.pluck(:name)).present?
    end
    return false
  end


  private

  def almost_sold_out?
    (quantity > 0 && in_stock?) && quantity <= WISHLIST_DESIGN_THRESHOLDS['almost_sold_out'] && quantity_was > WISHLIST_DESIGN_THRESHOLDS['almost_sold_out'] && (1.day < updated_at - updated_at_was)
  end

  def _boost_keyword_hash
    boost_hash = {}
    property_values.to_a.uniq.each do |pv|
      (boost_hash[pv.property.try(:keyword_boost_level)]||=[])<< pv.p_name
    end
    boost_hash[1].uniq! if boost_hash[1].is_a? Array
    boost_hash[2].uniq! if boost_hash[2].is_a? Array
    boost_hash
  end

  def get_default_name(material,category_names = [])
    product_name = (category_names.include? 'men') ? 'Men' : 'Ladies'
    if self.designable_type == 'Jewellery'
      product_name += ' Imitation Jewellery'
      product_name += " #{self.package_details.gsub('::',',')}" if self.package_details.present? && self.package_details.length <= 50
      if material.present?
        product_name += " Made Of Artificial #{material}"
      else
        product_name += " (#{category_names.last})"
      end
    elsif material.present?
      product_name += " #{material} #{self.designable_type}"
    else
      product_name += " #{category_names.last}"
    end
    product_name.to_s
  end

  # def dynamic_size_chart_category
  #   RequestStore.cache_fetch("dynamic_size_chart_category_#{category_ids.uniq.join(',')}",expires_in: 6.hour) do
  #     dynamic_size_chart_category = nil
  #     categories.each do |category|
  #       dynamic_size_chart_category ||= category.dynamic_size_chart_category
  #     end
  #     dynamic_size_chart_category || ''
  #   end.presence
  # end
  def dynamic_size_chart_category
    # Find first category that has a dynamic size chart category
    # Each category caches its own result for better cache efficiency
    categories.each do |category|
      if (chart_category = category.cached_dynamic_size_chart_category)
        return chart_category
      end
    end
    nil
  end
  def hsn_code_length
    unless self.hsn_code.nil? || (self.hsn_code.is_a?(Integer) && self.hsn_code.to_s.split("").size == 4)
      errors[:base] << "HSN_CODE should be a 4 digit number"
    end
  end

  def gst_rate_value
    unless self.gst_rate.nil? || (self.gst_rate.is_a?(Integer) && [0,3,5,12,18,28].include?(self.gst_rate))
      errors[:base] << "GST Rate should belong to one of the following values 0,3,5,12,18,28."
    end
  end

  def sale_available?
    active_promotions = PromotionPipeLine.active_promotions
    global_price_percent_amount(active_promotions) > 0
  end

  def global_price_percent_amount(active_promotions)
    RequestStore.cache_fetch("global_price_percent_#{Design.country_code}", expires_in: 1.day) do

      promotion_pipelines = active_promotions
      var_hash = if Design.country_code.present?
                    promotion_pipelines.find_by_name("global_sale_#{country_code}").try(:variables_hash) || promotion_pipelines.find_by_name("global_sale").try(:variables_hash)
                  else
                    promotion_pipelines.find_by_name("global_sale").try(:variables_hash)
                  end
      discount = 0
      discount = JSON.parse(var_hash)['global_discount_percent'].to_i if var_hash.present?
      discount
    end
  end

  def has_valid_category
    if available_category_ids? && Account.current_account.try(:accountable_type).to_s == "Designer"
      errors[:base] << "You can choose category only for #{designable_type} product" unless (available_category_ids & category_ids).present?
    end
  end

  def has_valid_property_values
    mandatory_property_names = get_property_names.try(:keys).to_a
    filled_properties = property_values.collect(&:property).compact.flatten.collect(&:name)
    if (missing_fields = (mandatory_property_names - filled_properties)).present? #property_value_ids.count == mandatory_property_names.count
      errors[:base] << "Please fill following properties : #{missing_fields.join(', ')}"
    end
    self.designable.validate_properties if self.designable.respond_to? :validate_properties
  end

  def has_valid_variants
    if get_property_names['stitching'] && property_value_ids.include?(get_property_names['stitching']['stitched']) && variants.length == 0
      errors[:base] << "Please select value for Variants"
    end
  end

  include Priceable

end
