# == Schema Information
#
# Table name: designers
#
#  id                  :integer         not null, primary key
#  name                :string(255)
#  email               :string(255)
#  phone               :string(255)
#  alt_phone           :string(255)
#  city                :string(255)
#  state               :string(255)
#  street              :string(255)
#  country             :string(255)
#  pincode             :string(255)
#  description         :text
#  url                 :text
#  photo_file_name     :string(255)
#  photo_content_type  :string(255)
#  photo_file_size     :integer
#  photo_updated_at    :datetime
#  published           :boolean
#  policy              :text
#  transaction_rate    :integer
#  account_holder_name :string(255)
#  bank_name           :string(255)
#  branch              :string(255)
#  ifsc_code           :string(255)
#  account_number      :string(255)
#  grade               :integer
#  cached_slug         :string(255)
#  vat_no              :string(255) 
#  cst_no              :string(255)

class Designer < ActiveRecord::Base
  extend FriendlyId
  include SidekiqHandleAsynchronous
  has_paper_trail
  acts_as_followable

  scope :published, -> { where(state_machine: 'approved') }
  scope :graded, -> { order('designers.grade ASC') }
  scope :ordered, -> { order('designers.id ASC') }
  scope :banned, -> { where(state_machine: ['banned', 'on_hold', 'inactive']) }
  scope :live_designers, -> {where(state_machine: ['approved', 'review', 'vacation'])}
  scope :search_by_id_or_name, ->(query) {where("id = ? OR LOWER(name) LIKE LOWER(?)", 
  query.try(:to_i), "%#{query}%")}
  scope :search_by_qc_rate, ->(rate) {
    where(dynamic_qc_rate: rate.to_f == 0 ? [0, nil] : rate.to_f)
  }
  scope :changed_additional_discount, -> {where{(additional_discount_start_date.eq  Time.zone.now.to_date) | (additional_discount_end_date.eq  (Time.zone.now.to_date - 1.day))}}
  scope :has_additional_discount, -> { select('id,name,email,additional_discount_percent,additional_discount_start_date,additional_discount_end_date').where('additional_discount_percent is NOT NULL and additional_discount_start_date is NOT NULL and additional_discount_end_date is NOT NULL') }
  scope :active_additional_discount, -> { select('id,name,cached_slug,photo_file_name,photo_updated_at,email,additional_discount_percent,additional_discount_start_date,additional_discount_end_date').where{(additional_discount_percent.gt 0) & (additional_discount_start_date.lte Time.zone.now.to_date) & (additional_discount_end_date.gte Time.zone.now.to_date)} }
  scope :active_additional_discount_with_all, -> { where{(additional_discount_percent.gt 0) & (additional_discount_start_date.lte Time.zone.now.to_date) & (additional_discount_end_date.gte Time.zone.now.to_date)} }
  scope :can_autopost, -> { where("fb_page_id is not null and fb_page_id <> ? and fb_page_name is not null and fb_page_name <> ? and fb_page_token is not null and fb_page_token <> ? and automated_facebook_posting = ?",'','','',true) }
  scope :ranking, -> {where('score is not null').order('score desc')}
  scope :cached_slug, ->(designer_id) { find(designer_id).cached_slug }
  scope :ready_for_review, ->{where(state_machine: ['review','vacation','on_hold','banned'])}
  has_many :grading_tags, as: :grading_taggable
  has_many :boosted_designs
  has_one :designer_boost_config


  include UpdateUnbxd
  include StringModify
  # @@per_page = 5

  friendly_id :slug_candidates, use: [:slugged, :finders, :history], slug_column: :cached_slug, sequence_separator: '--'

  def slug_candidates
    [
      :name,
      [:name, :id],
    ]
  end
  has_many :designer_issues
  has_one :account, :as => :accountable
  has_one :grading_promotion, as: :promotable
  has_many :carts,as: :user
  has_many :designs, inverse_of: :designer
  has_many :variants,through: :designs
  has_many :dynamic_size_charts
  has_many :design_scores,through: :designs
  has_one  :design_node ,as: :dimension
  has_many :designer_collections
  has_many :delay_images, through: :designs
  has_many :designer_orders
  has_many :return_designer_orders
  has_many :coupons
  has_many :adjustments
  has_many :vendor_addons
  has_many :master_addons
  has_many :warehouse_orders
  has_and_belongs_to_many :categories
  has_many :design_categories, -> { where("designs.state = 'in_stock'")}, class_name: 'Category', through: :designs, source: :categories
  has_many :limited_design_categories, -> { where("designs.state = 'in_stock'").select('distinct(categories.name), category_type').limit(3) }, class_name: 'Category', through: :designs, source: :categories
  has_many :follows, -> { where( "follows.followable_type = 'Designer'") }, :foreign_key => 'followable_id'
  has_many :pickups
  has_many :designer_batches
  has_many :reviews
  has_many :commission_invoices
  has_many :purchase_reports
  has_many :designer_invoices
  has_many :optional_pickups
  has_many :payout_managements
  has_many :facebook_reactions ,as: :poster
  has_many :metric_values, as: :actor
  has_many :current_metric_values, -> { where("metric_values.generated_on = '#{Date.yesterday}'") }, class_name: 'MetricValue',as: :actor
  has_many :designer_shippers
  has_one  :odr_90, -> { where("metric_definition_id = 13 and generated_on = '#{Date.yesterday}'") }, class_name: 'MetricValue',as: :actor
  has_many :vendor_promotions
  has_many :survey_answers, through: :designs
  has_many :sla_violated_designer_orders, -> { sla_violated }, class_name: 'DesignerOrder'
  has_many :designer_campaign_participations
  has_many :seller_campaigns, through: :designer_campaign_participations
  belongs_to :owner, class_name: :DesignerOwner, foreign_key: :owner_id, inverse_of: :designer_statistics
  belongs_to :warehouse_address
  has_shortened_urls
  accepts_nested_attributes_for :account
  accepts_nested_attributes_for :designs, :reject_if => proc { attrs['title'].blank? }, :allow_destroy => true

  UPLOAD_FILES = ["photo","pan_card","cancelled_cheque","cst_certificate","vat_certificate","gst_certificate"]
  IN_CATALOG = {'international' => 'domestic', 'domestic' => 'international', 'all' => 'no', 'no' => 'banned'}.freeze
  BANK_DETAILS = ["account_number", "account_holder_name" , "bank_name", "branch", "ifsc_code", "cancelled_cheque", "pan_no", "vat_no", "cst_no", "gst_no", "pan_card", "vat_certificate", "cst_certificate", "gst_certificate"]
  DESIGNER_TYPES = PublicSystemConstant.where(name: 'DESIGNER_TYPES').first.try(:value).to_s.split(',')
  PRODUCTION_TYPES = [:made_to_order, :ready_to_ship].map{|type| [type, type.to_s.freeze] }.to_h
  SLA_CONFIG = {
    mirraw_designer_default_eta: 0, # mirraw designer default ETA is assumed 2 days. we can increase that value with this constant
    sla_critical_alert_days: 2,
    sla_offset: 10,
    vendor_default_dispatch_days: 5,
    non_stitching_preparation_days: 5,
    country_default_delivery_days: 5
  }.merge!(SystemConstant.get('SLA_CONFIG', :to_h).symbolize_keys).freeze

  NO_MONTHS = SystemConstant.get('NO_MONTHS').to_i
  enum production_type: PRODUCTION_TYPES

  paperclip_hash={:styles => {:small => "150x150>"},
  :processors => [:thumbnail, :paperclip_optimizer],
  :storage => :s3,
  :s3_credentials => AWS_ACCESS_KEYS,
  :path => ":class/:id/:basename_:style.:extension",
  :bucket => ENV['S3_BUCKET'],
  :default_url => "/processing.jpg",
  :url => ":s3_alias_url",
  s3_protocol: IMAGE_PROTOCOL,
  :s3_headers => { 'Cache-Control' => 'max-age=315576000', 'Expires' => 10.years.from_now.httpdate },
  :s3_host_alias => if Rails.env.production? || Rails.env.seller?
                      Proc.new {|a| "assets#{a.size % 6}.mirraw.com" }
                    else
                      'd1lycdyubshuoc.cloudfront.net'
                      #ENV['CLOUDFRONT_URL']
                    end
                  }

  has_attached_file :photo, paperclip_hash
  has_attached_file :cancelled_cheque, paperclip_hash
  has_attached_file :pan_card, paperclip_hash
  has_attached_file :vat_certificate, paperclip_hash
  has_attached_file :cst_certificate, paperclip_hash
  has_attached_file :gst_certificate,paperclip_hash
  has_attached_file :msme_certificate,paperclip_hash

  validates_length_of :name, :minimum => 2, :maximum => 60, :on => :update

  validates :additional_discount_percent, numericality: { only_integer: true, allow_nil: true, greater_than_or_equal_to: 0, less_than_or_equal_to: 95 }

  validates_numericality_of :pincode, :on => :update, allow_blank: true
  #validates_length_of :phone, :in => 8..13, :on => :update
  validates_numericality_of :grade, :only_integer => true, :if => Proc.new {|design| design.grade.present? }
  validates :eta, numericality: { allow_nil: true, greater_than_or_equal_to: 0, less_than_or_equal_to: 90 }
  validates :production_type,
  allow_nil: true,
  inclusion: { in: PRODUCTION_TYPES.values,
    message: "%{value} is not a valid production types: valid production types are #{PRODUCTION_TYPES.values.join(',')}"
  }

  #validates_presence_of :photo, :on => :update
  is_new_designer_proc =  Proc.new {|designer| designer.new_record? }

  # Enable for compulsory gst document upload compulsion..
  if ENABLE_GST
    validates_presence_of :gst_address,on: :update
    validates_presence_of :taxpayer_trade_name,on: :update
    validates_presence_of :gst_no, on: :update,unless: :should_validate?
    validates_attachment_presence :gst_certificate, unless: :should_validate?
  end

  validates_presence_of :email, :account
  #validates_presence_of :vat_no, :cst_no, :on => :update,unless:'should_validate?'
  validates :pan_no, length: {is: 10}, format: { with: /[a-z]{3}[cphfatblj][a-z]\d{4}[a-z]/i }, :on => :update,unless: 'should_validate?'
  #validates_presence_of :city, :state, :street, :on => :update
  #validates_attachment_presence :photo, :unless => 'should_validate?'
  validates_attachment_size :photo, :less_than => 5.megabytes, :unless => is_new_designer_proc
  validates_attachment_content_type :photo, :content_type => /image/, :unless => is_new_designer_proc

  validates_attachment_presence :cancelled_cheque, :unless => 'should_validate?'
  validates_attachment_size :cancelled_cheque, :less_than => 5.megabytes, :unless => is_new_designer_proc
  validates_attachment_content_type :cancelled_cheque, :content_type => /image/, :unless => is_new_designer_proc

  validates_attachment_presence :pan_card, :unless => 'should_validate?'
  validates_attachment_size :pan_card, :less_than => 5.megabytes, :unless => is_new_designer_proc
  validates_attachment_content_type :pan_card, :content_type => /image/, :unless => is_new_designer_proc

  # validates_attachment_presence :vat_certificate, :unless => 'should_validate?'
  # validates_attachment_size :vat_certificate, :less_than => 5.megabytes, :unless => is_new_designer_proc
  # validates_attachment_content_type :vat_certificate, :content_type => /image/, :unless => is_new_designer_proc

  # validates_attachment_presence :cst_certificate, :unless => 'should_validate?'
  # validates_attachment_size :cst_certificate, :less_than => 5.megabytes, :unless => is_new_designer_proc
  # validates_attachment_content_type :cst_certificate, :content_type => /image/, :unless => is_new_designer_proc

  validates_attachment_size :gst_certificate, less_than: 5.megabytes, unless: is_new_designer_proc
  validates_attachment_content_type :gst_certificate, content_type: /image/, unless: is_new_designer_proc

  validates :msme_no, presence: true, if: :msme_applicability?
  validates :msme_certificate, presence: true, if: :msme_applicability?
  validates_attachment_size :msme_certificate, :less_than => 5.megabytes, :unless => is_new_designer_proc
  validates_attachment_content_type :msme_certificate, :content_type => /image/, :unless => is_new_designer_proc

  validate :validate_vacation_date
  validate :gst_number_formate, on: :update, if: :gst_no_changed?
  validate :check_transfer_price_present, if: :transfer_model_rate_changed?
  validate :check_if_not_transfer_model, if: :exclude_gst_changed?

  #after_save :publish_designs_if
  before_photo_post_process :randomize_file_name_photo
  before_pan_card_post_process :randomize_file_name_pan_card
  before_vat_certificate_post_process :randomize_file_name_vat_certificate
  before_cst_certificate_post_process :randomize_file_name_cst_certificate
  before_gst_certificate_post_process :randomize_file_name_gst_certificate
  before_cancelled_cheque_post_process :randomize_file_name_cancelled_cheque

  process_in_background :photo
  process_in_background :cancelled_cheque
  process_in_background :pan_card
  process_in_background :vat_certificate
  process_in_background :cst_certificate
  process_in_background :gst_certificate

  before_save :update_triggers
  before_save :default_values
  after_commit :send_to_unbxd, on: :update

  before_destroy :send_to_unbxd
  before_create :initialize_values

  scope :has_category, lambda {|value| where('categories.id' => Category.getids(value)) if value.present? }
  scope :not_has_category, lambda {|value| where('categories.id NOT IN (?)', Category.getids(value)) if value.present? }

  # attr_protected :cached_slug
  # attr_accessible  :score_hash, :name, :email, :phone, :alt_phone, :city, :state, :street, :country, :pincode, :description, :url, :photo, :published, :policy, :transaction_rate, :account_holder_name, :bank_name, :branch, :ifsc_code, :account_number, :grade, :vacation_start_date, :vacation_end_date, :vacation_message, :fb_page_name, :fb_page_id, :fb_page_token, :fb_page_link, :last_fb_post_at, :wholesaler, :banned, :state_machine, :last_approved_on, :last_review_on, :last_banned_on, :seo_title, :business_name, :business_street, :business_city, :business_state, :business_country, :business_pincode, :pan_no, :tan_no, :allow_addon, :state_code, :mirraw_shipping, :cod, :pickup_name, :pickup_company_name, :pickup_phone, :pickup_street, :pickup_city, :pickup_state_code, :pickup_pincode, :upload_panel, :photo_processing, :p_name, :average_rating, :gati_vendor_code, :vat_no, :cst_no, :gst_no,:gst_address, :taxpayer_trade_name, :compliant, :cancelled_cheque, :pan_card, :vat_certificate, :cst_certificate, :gst_certificate, :additional_discount_percent, :additional_discount_start_date, :additional_discount_end_date, :warning_notification, :automated_facebook_posting, :approved_on,:score_flag, :score, :invoice_number, :designer_type, :zone, :inventory_processed_at, :hsn_approved
  def total_orders_excluding_warehouse(id,total_month=Designer::NO_MONTHS)
    select_clause = "
      designer_orders.state,
      CASE
      WHEN designer_orders.state = 'pending' THEN COUNT(DISTINCT CASE WHEN line_items.available_in_warehouse is not true THEN designer_orders.id END)
      WHEN designer_orders.state = 'replacement_pending' THEN COUNT(DISTINCT CASE WHEN line_items.rtv_quantity is null THEN designer_orders.id END)
      ELSE COUNT(DISTINCT designer_orders.id)
      END as total_orders
      "
    designer_orders.unscoped.joins(:line_items).where(designer_id: id, created_at: total_month.months.ago.beginning_of_day..Date.today.end_of_day).group(:state).select(select_clause).map{|orders| [orders.state, orders.total_orders]}.to_h
  end

  def should_generate_new_friendly_id?
    !new_record? && (cached_slug.blank? || name_changed?)
  end


  def eligible_for_boost?
    designer_boost_config.present? && designer_boost_config.can_boost && designer_boost_config.duration > 0
  end

  def self.get_revenue(designer)
    designer_payout = 0
    designer.designer_orders.each do |d|
      if d.designer_payout_status == 'paid'
        designer_payout = designer_payout + d.payout
      end
    end
    return(designer_payout)
  end

  def self.attr_visible
    @attr_visible ||= %w(id name cached_slug)
  end

  def self.get_transfer_model_vendors
    Rails.cache.fetch("tp_model_designers",expires_in: 1.hours) do
      Designer.where{transfer_model_rate > 0}.pluck(:id,:cached_slug).to_h
    end
  end

  def should_validate?
    self.new_record? || self.inactive_designer_state? || state_machine == 'vacation'
  end

  def can_autopost?
    %w(fb_page_name fb_page_id fb_page_token automated_facebook_posting).all?{|attr_name|  self[attr_name].present?}
  end

  def can_post?
    %w(fb_page_name fb_page_id fb_page_token).all?{|attr_name|  self[attr_name].present?}
  end

  def is_transfer_model?
    self.transfer_model_rate.to_i > 0
  end

  def is_gst_excluded?
    self.exclude_gst
  end

  def is_one_tire_designer?
    self.designer_type == "Tier 1 Designer"
  end

  def variable_gst_model=(value)
    @variable_gst_model = value
    if value && is_gst_excluded?
      self.domestic_transaction_rate = self[:transaction_rate] + self[:transaction_rate] * IGST/100.0
    else
      self.domestic_transaction_rate = nil
    end
  end

  def variable_gst_model
    @variable_gst_model
  end

  def is_variable_gst_model?
    !self[:domestic_transaction_rate].to_f.zero?
  end

  def domestic_transaction_rate
    if is_gst_excluded? && super.to_f.nonzero?
      return super
    else
      return self.transaction_rate
    end
  end

  def transaction_rate
    if is_transfer_model?
      return 0
    elsif is_gst_excluded? && !is_variable_gst_model?
      return (self[:transaction_rate] + self[:transaction_rate] * IGST/100.0)
    else
      return super
    end
  end

  def pickup_location
    self.pickup_city.presence || self.city.presence || self.business_city
  end

  def update_catalog_price
    designs.where.not(transfer_price: nil).each do |design|
      design.price = design.get_display_price_for_transfer_model
      design.save!
    end
  end

  def self.update_tp_model_rate(data,email)
    failed_designers_id,updated_designers_id,failed_designs_id={},[],[]
    Designer.where(id:data.keys).where(state_machine: ['review','approved']).preload(:designs).find_each(batch_size:100) do |designer|
      if designer.designs.size == designer.designs.select{|design| design.transfer_price.to_i>=0}.size 
        designer.transaction_rate = 0
        designer.transfer_model_rate = data[designer.id]
        designer.exclude_gst = false
        designer.variable_gst_model = false
        updated_designers_id << designer 
      else
        failed_designers_id[designer.id]="All Designs Transfer Price not set"
      end
    end
    result=Designer.import updated_designers_id,validate: false,on_duplicate_key_update: [:exclude_gst,:transaction_rate,:transfer_model_rate]
    (data.keys-result.ids.map(&:to_i)-failed_designers_id.keys).each do |id|
      failed_designers_id[id]="Inactive Vendor"
    end
    Design.where(designer_id: result.ids).where.not(transfer_price:nil).preload(:variants).find_each(batch_size:250) do |design|
      design.discount_percent = TRANSFER_MODEL_DISCOUNT
      if design.variants.present?
        design.variants.each do |variant|
        variant.transfer_price = design.transfer_price
        variant.price = variant.get_display_price_for_transfer_model
        failed_designs_id << variant.design_id unless variant.save
        end
      else
        design.price = design.get_display_price_for_transfer_model
      end
      failed_designs_id << design.id unless design.save
    end
    DesignerMailer.designers_commission_rate_update_status(data,failed_designers_id,email,failed_designs_id.uniq).deliver_now
  end

  def self.update_commission_model_rate(data,type,email)
    failed_designers_id,updated_designers_id ={},[]
    Designer.where(id: data.keys).where(state_machine: ['review','approved','inactive','on_hold']).find_each(batch_size:100) do |designer|
      if designer.transfer_model_rate.to_i==0
        designer.exclude_gst = ['gst_exclusive','variable_gst_exclusive'].include?(type)
        designer.transaction_rate=data[designer.id]
        designer.variable_gst_model = type == 'variable_gst_exclusive'
        updated_designers_id << designer
      else
        failed_designers_id[designer.id]="TP Model based Vendor"
      end
    end
    result=Designer.import updated_designers_id,validate: false,on_duplicate_key_update: [:exclude_gst,:transaction_rate,:domestic_transaction_rate,:transfer_model_rate]
    (data.keys-result.ids.map(&:to_i)-failed_designers_id.keys).each do |id|
      failed_designers_id[id]="Inactive Vendor"
    end
    if type == 'all_inclusive'
      s=SystemConstant.where(name:'EXCLUDE_VENDOR_FOR_ADJUSTMENTS').first
      s.value = (s.value + ',' + result.ids.join(',')).split(',').uniq.join(',')
      s.save!
    end
    DesignerMailer.sidekiq_delay.designers_commission_rate_update_status(data,failed_designers_id,email)
  end

  def access_token
    account.try(:token).presence || fb_page_token
  end

  def self.accessible_attributes(dummy = nil)
    ['score_hash', 'name', 'phone', 'alt_phone', 'city', 'state', 'street', 'country', 'pincode', 'description', 'url', 'photo', 'published', 'policy', 'transaction_rate', 'domestic_transaction_rate', 'exclude_gst', 'transfer_model_rate', 'account_holder_name', 'bank_name', 'branch', 'ifsc_code', 'account_number', 'grade', 'vacation_start_date', 'vacation_end_date', 'vacation_message', 'fb_page_name', 'fb_page_id', 'fb_page_token', 'fb_page_link', 'last_fb_post_at', 'wholesaler', 'banned', 'state_machine', 'last_approved_on', 'last_review_on', 'last_banned_on', 'seo_title', 'business_name', 'business_street', 'business_city', 'business_state', 'business_country', 'business_pincode', 'pan_no', 'tan_no', 'allow_addon', 'state_code', 'mirraw_shipping', 'cod', 'pickup_name', 'pickup_company_name', 'pickup_phone', 'pickup_street', 'pickup_city', 'pickup_state_code', 'pickup_pincode', 'upload_panel', 'photo_processing', 'p_name', 'average_rating', 'gati_vendor_code', 'vat_no', 'cst_no', 'gst_no','gst_address', 'taxpayer_trade_name', 'compliant', 'cancelled_cheque', 'pan_card', 'vat_certificate', 'cst_certificate', 'gst_certificate', 'additional_discount_percent', 'additional_discount_start_date', 'additional_discount_end_date', 'warning_notification', 'automated_facebook_posting', 'approved_on','score_flag', 'score', 'invoice_number', 'designer_type', 'zone', 'inventory_processed_at', 'hsn_approved', 'last_on_hold_at', 'last_inactive_at', 'eta', 'pan_card_file_name', 'gst_certificate_file_name', 'cancelled_cheque_file_name','msme_certificate','msme_no','msme_applicability']
  end

  # def self.find(input)
  #   input.to_i == 0 ? find_by_cached_slug(input) : super
  # end

  def send_to_unbxd
    items = self.designs
    ids = items.pluck(:id)
    update_unbxd ids
  end

  handle_asynchronously :send_to_unbxd, priority: 8

  def vacation_mode_on?
    vacation_start_date && vacation_end_date && (Date.today >= vacation_start_date.to_date) && (Date.today <= vacation_end_date.to_date)
  end

  def self.design_count
    joins(:designs).group('designers.id').count('designs.id')
  end

  def self.getids(slug)
    id_array =[]
    designers = Designer.where(cached_slug: slug).select('id,cached_slug')
    id_array.push(*designers.collect(&:id))
    ids = (slug - designers.collect(&:cached_slug)).collect(&:to_i).uniq
    id_array.push(*Designer.where(id: ids).pluck(:id))
    id_array.uniq
  end

  def vacation_days_count
    vacation_mode_on? ? (vacation_end_date.to_date - vacation_start_date.to_date).to_i : 0
  end

  def facebook_page_linked?
    fb_page_token?
  end

  def cod_shipper_ids
    RequestStore.fetch("cod_shippers_for_#{id}") do
      designer_shippers.joins(shipper: :couriers).
      where(designer_shippers: {cod: true, enabled: true}, shipper: {enabled: true},couriers: {pincode: pincode, cod: 'Y', pickup: true}).
      uniq.pluck('designer_shippers.shipper_id')
    end
  end

  def apply_additional_discount(discount_percent,start_date,end_date)
    if self.vendor_additional_discount_percent == 0
      self.additional_discount_percent = discount_percent
      date = Date.parse(start_date)
      raise RangeError, "Invalid start date. Please extend the date" if date < (Time.zone.now.to_date + 1.day)
      self.additional_discount_start_date = date
    end
    date = Date.parse(end_date)
    raise RangeError, "Invalid end date. Please extend the date" if date < Time.zone.now.to_date || date < self.additional_discount_start_date
    self.additional_discount_end_date = date
    if self.changed?
      if self.save
        DesignerMailer.sidekiq_delay.applied_additional_discount(self)
      else
        raise self.errors.full_messages.join(' , ')
      end
    else
      raise "No changes where done."
    end
  end

  def vendor_additional_discount_percent
    if self[:additional_discount_percent].present? && self[:additional_discount_start_date].present? && self[:additional_discount_end_date].present? && !self.is_transfer_model?
      today = Time.zone.now.to_date
      if self[:additional_discount_start_date] <= today && self[:additional_discount_end_date] >= today
        return self[:additional_discount_percent]
      end
    end
    0
  end

  def find_property_values_id(property_name_value)
    property_values_ids = []
    property_name = property_name_value.stringify_keys.keys
    property = Property.where(name: property_name).order("position(name::text in '#{property_name.join(',')}')").pluck(:id)
    property_id_value = property.zip property_name_value.values
    property_id_value.each do |property|
      property_values_ids << PropertyValue.where(property_id: property.first, name: property.last).pluck(:id)
    end 
    return property_values_ids.flatten 
  end

  def save_single_record(row,designer_batch,failed_design_id=nil)
    begin
      blouse_avail = ''
      product_type = 'saree'
      if row[11].downcase == 'yes'
        blouse_avail = "With Blouse"
      end 
      Design.transaction do
        design = Design.new.attributes
        design['design_code'] = row[0]
        design['title'] =  "#{row[6]} #{row[9]} #{row[5]} #{product_type} #{blouse_avail}"
        design['embellish'] = row[36]
        design['description'] = row[38]
        design['package_details'] = row[3]
        design['price'] = row[28].to_i
        design['discount_percent'] = row[29].to_i
        design['weight'] = row[4].to_i
        design['tag_list'] = row[26]
        design['pattern'] = row[35]
        design['region'] = row[34]
        design['quantity'] = row[27].to_i
        design['published'] = true
        image_ids = []
        image = Image.new
        image.kind = 'master'
        if row[21].present?
          if row[21].match(/dropbox/)
            image.photo = row[21].gsub(/\?dl=0/, '?dl=1')
          else
            image.photo = row[21]
          end
        end
        image.save
        image_ids << image.id

        (22...25).each do |item|
          image = Image.new
          if (row[item].present?) && (row[item].include?'.jp')
            # check for dropbox links
            if row[item].match(/dropbox/)
              image.photo = row[item].gsub(/\?dl=0/, '?dl=1')
            else
              image.photo = row[item]
            end
            image.save
            image_ids << image.id
          end
        end
        property_name_value = {fabric_of_saree: row[5], work: row[9], type: row[10], saree_color: row[6], fabric_of_blouse: row[12],blouse_color: row[13], blouse_work: row[16], petticoat_color: row[19],fabric_of_petticoat: row[20], celebrity: row[37], occasion: row[30], look: row[31], saree_border: row[32], pallu_style: row[33]} 
        property_value_ids = find_property_values_id(property_name_value)

        design_params                      = design.clone
        design_params[:category_ids]       = Category.where('name ILIKE LOWER(?)',row[2].to_s).pluck(:id).first
        design_params[:property_value_ids] = property_value_ids.compact
        design_params[:image_ids]          = image_ids

        d = Saree.new
        d['width'] = row[8]
        d['length'] = row[7]
        d['saree_color'] = row[6]
        d['blouse_available'] = row[11]
        d['blouse_image'] = row[15]
        d['blouse_size'] = row[14]
        d['blouse_fabric'] = row[12]
        d['blouse_color'] = row[13]
        d['blouse_work'] = row[16]
        d['petticoat_available'] = row[17]
        d['petticoat_size'] = row[18]
        d['petticoat_color'] = row[19]
        d['petticoat_fabric'] = row[20]

        design_params.reject!{ |key,value| value.nil? }
        build_design            = self.designs.build(design_params)
        build_design.sell_count = 0
        build_design.grade      = 0
        build_design.state      = "processing"
        build_design.designable = d if d.present?
        build_design.designer_batch_id = designer_batch.id

        unless build_design.save!
          if failed_design_id.nil?
            self.create_failed_design(row,build_design.errors,designer_batch)
            designer_batch.failed += 1
          end
        else
          designer_batch.passed += 1
          Design.sidekiq_delay_until(Time.now + 1.minute).make_design_live(build_design)
          if failed_design_id.present?
            designer_batch.failed -= 1
            FailedDesign.find(failed_design_id).destroy
          end
          designer_batch.save
        end
      end # end of transaction
    rescue => error
      if failed_design_id.nil?
        self.create_failed_design(row,error,designer_batch)
        designer_batch.failed += 1
        designer_batch.save
      end
    end # end of rescue
  end


  def bulk_upload_designs(filename, designer_batch)
    xlsx = Roo::Spreadsheet.open(filename, extension: :xlsx)
    # Selecting main sheet as default sheet.
    xlsx.default_sheet = xlsx.sheets[1]
    designer_batch.failed = 0
    designer_batch.passed = 0
    designer_batch.no_of_records = xlsx.last_row - 3
    xlsx.each_with_index do |row, index|
      if index > 1
        self.save_single_record(row, designer_batch) if row[1].present?
      end # end of IF
    end # end of row
    designer_batch.save
  end

  def version_bulk_upload_designs(filename, designer_batch, format, to_account = nil)
    xlsx = Roo::Spreadsheet.open(filename, extension: :xlsx)
    # Selecting main sheet as default sheet.
    xlsx.default_sheet = xlsx.sheets[1]
    version=designer_batch.version.to_sym
    version_method=version.to_s.underscore
    data, missed_variants ={}, []
    BulkUpload.meta_data[version][:categories_designs].to_a.each do |category|
      data[category] = Category.names_to_ids(category)
    end
    data = data.merge(Property.get_property_value_mapping(BulkUpload.meta_data[version][:designs_property_values].to_a))
    data = data.merge(OptionType.get_option_type_values(BulkUpload.meta_data[version][:variants].to_a))
    data[:collection] = designer_collections_mapper
    if format == 'amazon'
      option_type       = BulkUpload.meta_data[version][:extra][:option_type]
      variant_positions = BulkUpload.meta_data[version][:variant_positions]
      child_columns     = BulkUpload.meta_data[version][:child_cols]
      all_designs = {}
      xlsx.drop(2).each do |row|
        case row[child_columns[:parent_child]].try(:downcase)
        when 'parent'
          all_designs[row[0]] = row
        when 'child', 'size_child'
          parent_row = all_designs[row[child_columns[:parent_sku]]]
          option_type_name = option_type.presence || "#{row[child_columns[:sub_type]].try(:downcase)}_size"
          option_type_name = "#{parent_row[child_columns[:sub_type]].try(:downcase)}_size" if option_type_name == "_size" && parent_row.present?
          invalid_variant  = false
          if parent_row.present? && option_type_name.present? && row[child_columns[:size]].present?
            size_option_name = row[child_columns[:size]].is_a?(Numeric) ? ("%g" % row[child_columns[:size]]) : row[child_columns[:size]]
            ['design_code', 'quantity', 'price'].each do |atr|
              size_name = "#{option_type_name}_#{size_option_name.strip.underscore.tr(' .','_')}_#{atr}"
              if (pos = variant_positions[size_name]).present?
                all_designs[row[child_columns[:parent_sku]]][pos] = row[child_columns[atr.to_sym]]
              else
                invalid_variant = true
              end
            end
            missed_variants << {id: row[0], error: "Variant with size #{size_option_name} was not applied to Parent design"} if invalid_variant
          else
            missed_variants << {id: row[0], error: "Parent Design Not Found. Variant's parent design code is incorrect."}
          end
        when 'color_child'
          all_designs[row[0]] = row
          if (parent_design = all_designs[row[child_columns[:parent_sku]]]).present? && (parent_sku = parent_design[child_columns[:parent_sku]]).present?
            all_designs[row[0]][child_columns[:parent_sku]] = parent_sku
          end
        else
          missed_variants << {id: row[0], error: row} if row[1].present?
        end
      end
      all_designs = all_designs.values
    else
      all_designs = xlsx.drop(2)
    end
    design_bulk = []
    failed_design = []
    design_code, variant_code = [], []
    all_designs.each do |row|
      if row[1].present?
        row.map!(&:to_s)
        design=DesignBulk.new(designer_id:self.id,designer_batch_id:designer_batch.id)
        if self.is_transfer_model?
          design.set_transfer_price_based_on_version(row,version)
        end
        variant_codes = version_method == 'bag' ? BagUpload.bag(design, row, data) : BulkUpload.send(version_method, design, row, data)
        error = []
        error += design.errors.full_messages if design.errors.present? || !design.valid?
        error << "Design code already taken" if design_code.include?(design.design_code)
        error << 'Variant code already taken' if (variant_code & variant_codes).present?
        error << 'Variant SKU Code is already taken' if design.variants.present? && design.is_variant_design_code_uniq?(variant_codes)
        if self.is_transfer_model? && design.variants.present?
          discount_value = (100 - design.discount_percent).to_f/100
          design.variants.each do |variant|
            variant.transfer_price = design.transfer_price
            variant.price = ((design.transfer_price.to_f / (1 - (design.designer.transfer_model_rate/100.0) * (1 + IGST/100.0)))/discount_value).to_i
          end
        end
        design.quantity = design.variants.map(&:quantity).compact.sum if design.variants.present?
        unless error.present?
          design_bulk << design
          design_code << design.design_code
          variant_code += variant_codes
          designer_batch.increment(:passed, 1)
        else
          failed_design << VersionFailedDesign.new(error: error,designer_batch_id:designer_batch.id,row: row)
          designer_batch.increment(:failed, 1)
        end
        designer_batch.increment(:no_of_records,1)
        # self.save_single_record(row, designer_batch)
      else
        missed_variants << {id: row[0], error: 'Title of design was missing'} if row[0].present?
      end # end of IF
    end # end of row
    DesignBulk.import design_bulk,recursive:true,validate:false
    VersionFailedDesign.import failed_design,recursive:true,validate:false
    designer_batch.save
    SidekiqDelayGenericJob.perform_async(designer_batch.class.to_s, designer_batch.id, "bulk_upload_post_processing")
    #designer_batch.sidekiq_delay(queue: 'low').bulk_upload_post_processing
    DesignerMailer.sidekiq_delay(queue: 'low').sendout_design_error_notification((to_account.try(:email) || email),missed_variants,'Few Child Variants could not be uploaded', 'Following Mentioned variant codes could not be applied as their parent Designs could not be found.') if missed_variants.present?
  end

  def create_failed_design(row, errors, designer_batch)
    failed_design = FailedDesign.new
    failed_design.designer_batch_id = designer_batch.id
    failed_design.design_code = row[0]
    failed_design.title = row[1]
    failed_design.category = row[2]
    failed_design.package_details = row[3]
    failed_design.weight_in_gms = row[4]
    failed_design.fabric_of_saree = row[5]
    failed_design.work = row[9]
    failed_design.type_name = row[10]
    failed_design.width_of_saree_in_inches = row[8]
    failed_design.length_of_saree_in_metres = row[7]
    failed_design.saree_color = row[6]
    failed_design.blouse_availability = row[11]
    failed_design.blouse_as_shown_in_the_image = row[15]
    failed_design.size_of_blouse_in_cms = row[14]
    failed_design.fabric_of_blouse = row[12]
    failed_design.blouse_color = row[13]
    failed_design.blouse_work = row[16]
    failed_design.petticoat_availability = row[17]
    failed_design.size_of_petticoat_metres = row[18]
    failed_design.color_of_petticoat = row[19]
    failed_design.fabric_of_petticoat = row[20]
    failed_design.image = row[21]
    failed_design.image1 = row[22]
    failed_design.image2 = row[23]
    failed_design.image3 = row[24]
    failed_design.image4 = row[25]
    failed_design.tag_list = row[26]
    failed_design.quantity = row[27]
    failed_design.price = row[28]
    failed_design.discount_percent = row[29]
    failed_design.occasion = row[30]
    failed_design.look = row[31]
    failed_design.saree_border = row[32]
    failed_design.pallu_style = row[33]
    failed_design.region = row[34]
    failed_design.pattern = row[35]
    failed_design.embellish = row[36]
    failed_design.celebrity = row[37]
    failed_design.description = row[38]
    failed_design.product_type = row[39]
    failed_design.errors_found = errors.to_s
    failed_design.save
  end

  def stock_update(stock_batch,filename,updated_by_account,can_edit_price = false)
    first_part = "https://s3-ap-southeast-1.amazonaws.com/mirraw-test/"
    Design.country_code = "IN"
    Account.current_account = updated_by_account
    design_ids = []
    collections_mapper = designer_collections_mapper
    open(first_part + filename) do |f|
      CSV.new(f, :headers => :first_row, :header_converters => lambda { |h| h.try(:downcase) }).drop(1).each_slice(250) do |lines|
        design_identifiers = {ids: [], design_codes: [], group_ids: []}
        variant_identifiers= {ids: [], design_codes: []}
        grouped_lines,grouped_variants = {},{}
        lines.each do |line|
          if line['mirraw_id'].present?
            if ['Parent', 'Color Child'].include?(line['relationship_type'])
              design_identifiers[:ids] << line['mirraw_id']
              design_identifiers[:group_ids] << line['color_variation_mirraw_id']
              grouped_lines[line['mirraw_id']] = line
            elsif ['Child', 'Size Child'].include?(line['relationship_type'])
              variant_identifiers[:ids] << line['mirraw_id']
              grouped_variants[line['mirraw_id']] = line
            else
              design_ids << {id: line['vendor_sku_code'].presence || line['mirraw_id'], error: 'Design could not be found in the system'}
            end
          elsif (code = (line['vendor_sku_code'] || line['vendor_code'])).present?
            if line['relationship_type'] == 'Parent'
              design_identifiers[:design_codes] << code
              grouped_lines[code] = line
            elsif line['relationship_type'] == 'Child'
              variant_identifiers[:design_codes] << code
              grouped_variants[code] = line
            else
              design_ids << {id: line['vendor_sku_code'].presence || line['mirraw_id'], error: 'Design could not be found in the system'}
            end
          end
        end
        listed_designs = self.designs.select('designs.*, max(variants.in_stock_warehouse) as max_variant_sor').joins('left outer join variants on variants.design_id = designs.id').preload(:design_group).where('designs.id in (?) or designs.design_code in (?)', design_identifiers[:ids], design_identifiers[:design_codes]).group(:id)
        listed_parent_designs = self.designs.where(id: design_identifiers[:group_ids]).group_by(&:id).transform_values{|d| d.first}
        s_optv = 'variants.id as id, variants.design_code, variants.design_id as design_id, variants.quantity as designer_quantity, variants.quantity, variants.in_stock_warehouse as in_stock_warehouse, variants.price, variants.transfer_price'
        variants = Variant.select(s_optv).where('id in (?) or design_code in (?)', variant_identifiers[:ids], variant_identifiers[:design_codes]).preload(:design)
        changed_designs = []
        listed_designs.each do |design|
          line = grouped_lines["#{design.id}"] || grouped_lines[design.design_code]

          if design.designer_collection_id != (collection_id = collections_mapper[line['collection']])
            design.designer_collection_id = collection_id
          end

          %w(package_details title).each do |attr_name|
            if line[attr_name].present? && design[attr_name] != line[attr_name]
              design[attr_name] = StringModify.string_utf8_clean line[attr_name]
            end
          end

          %w(designer_quantity gst_rate hsn_code weight).each do |attr_name|
            if line[attr_name].present? && design[attr_name] != line[attr_name].to_i
              design.send("#{attr_name}=", line[attr_name].to_i)
            end
          end
          if line['preparation_time'].present? && design.eta.to_i != line['preparation_time'].to_i && ALLOWED_VENDOR_FOR_PREPARATION.include?(self.id)
            if updated_by_account.admin? && ['senior_vendor_team', 'super_admin'].include?(updated_by_account.role.try(:name)) && !design.ready_to_ship && !design.sor_available? && design.max_variant_sor.to_i == 0
              design.eta = line['preparation_time'].to_i
            else
              design.errors[:base] << 'Preparation time cannot be updated since you do not have access or product is marked as ready_to_ship'
            end
          end

          if line['web_price_before_discount'].present? && line['discount_percent'].present?
            unless line['web_price_before_discount'].to_i == design[:price]
              if can_edit_price
                design.price = line['web_price_before_discount'].to_i
              else
                design_ids << {id: (design.design_code.presence || design.id), error: "You are not allow to change price in promotion period."}
              end
            end
            unless line['discount_percent'].to_i == design[:discount_percent]
              if can_edit_price
                design.discount_percent = line['discount_percent'].to_i
              else
                design_ids << {id: (design.design_code.presence || design.id), error: "You are not allow to change price in promotion period."}
              end
            end
          elsif line['transfer_price'].present? && line['transfer_price'].to_i > 0 && line['transfer_price'].to_i != design.transfer_price
            if can_edit_price
              design.transfer_price = line['transfer_price'].to_i
            else
              design_ids << {id: (design.design_code.presence || design.id), error: "You are not allow to change price in promotion period."}
            end
          end
          parent_design = listed_parent_designs[line['color_variation_mirraw_id'].to_i]
          if parent_design.present? && parent_design.id != design.design_group.try(:parent_design_id)
            design.design_group_id = if parent_design.design_group_id.present?
                parent_design.design_group_id
              else
                changed_designs << parent_design
                design.create_design_group(parent_design: parent_design).id
              end
            parent_design.design_group_id = design.design_group_id
            if (child_as_parent = listed_parent_designs[design.id]).present?
              child_as_parent.design_group_id = design.design_group_id
              changed_designs << child_as_parent
            end
          elsif line['relationship_type'] == 'Color Child' && design.design_group_id.present? && line['color_variation_mirraw_id'].to_i == 0
            design.design_group_id = nil
          end
          changed_designs << design if design.changed?
        end
        variants.each do |variant|
          line = grouped_variants["#{variant.id}"] || grouped_variants[variant.design_code]
          if line['vendor_sku_code'].present? && variant.design_code != line['vendor_sku_code']
            variant.design_code = line['vendor_sku_code']
          end
          if line['web_price_before_discount'].present? && (new_price = line['web_price_before_discount'].to_i) > 0 && variant[:price] != new_price
            if can_edit_price
              variant.price = new_price
            else
              design_ids << {id: (variant.design_code.presence || variant.design_id), error: "You are not allow to change price in promotion period."}
            end
          elsif line['transfer_price'].present? && line['transfer_price'].to_i > 0 && line['transfer_price'].to_i != variant.transfer_price
            if can_edit_price
              variant.transfer_price = line['transfer_price'].to_i
            else
              design_ids << {id: (variant.design_code.presence || variant.design_id), error: "You are not allow to change price in promotion period."}
            end
          end

          if line['designer_quantity'].present? && variant.designer_quantity != line['designer_quantity'].to_i
            variant.designer_quantity = line['designer_quantity'].to_i
          end
          if variant.changed?
            variant_design = variant.design
            mark_oos = variant.quantity_changed? && variant_design.quantity_was > 0
            if variant.save
              if variant_design.quantity == 0 && mark_oos
                variant.design.unavailable
                stock_batch.increment(:passed)
              else
                changed_designs << variant.design
              end
            else
              stock_batch.increment(:failed)
              design_ids << {id: (variant.design_code.presence || variant.design_id), error: variant.errors.messages}
            end
          end
        end
        changed_designs.uniq!
        # preload for after save callbacks
        ActiveRecord::Associations::Preloader.new.preload(changed_designs, [:variants, :slugs, :categories, :images])
        changed_designs.each do |design|
          if design.errors.present? || (design.variants.blank? && design.going_to_sold_out && !design.unavailable) || !design.save
            design_ids << {id: (design.design_code.presence || design.id), error: design.errors.messages}
            stock_batch.increment(:failed)
          else
            stock_batch.increment(:passed)
          end
        end
      end
    end
    stock_batch.save
    DesignerMailer.sidekiq_delay.sendout_design_error_notification(updated_by_account.email, design_ids) unless design_ids.blank?
  rescue => e
    ExceptionNotify.sidekiq_delay.notify_exceptions(e,e.message)
  end

  def address
   self.street + ", " + self.city + " - " + self.pincode.to_s + ", " + self.state + ", " + self.country
  end

  def state_enum
    Designer.state_machines[:state_machine].states.map &:name
  end

  def designer_type_enum
    DESIGNER_TYPES
  end

  def production_type_enum
    PRODUCTION_TYPES.values.unshift(nil)
  end

  def get_rapid_delivery_tracking_num(dos = nil, wa_ids = nil)
    credentials = Mirraw::Application.config.rapid_delivery
    wa_ids = dos.warehouse_address_id if dos.present?
    company_name, shipping_telephone, shipping_address_1,shipping_address_2, shipping_city, shipping_pincode, shipping_state, _ = DesignerOrder.get_warehouse_billing_address(wa_ids)
    courier_address = COMPANY_LOCATIONS[:courier]
    tracking_num_url = credentials[:tracking_num_url]
    tracking_num_request = {}
    tracking_num_request[:client] = credentials[:client]
    tracking_num_request[:token] = credentials[:token]
    tracking_num_request[:consignee] = company_name
    tracking_num_request[:add1] = shipping_address_1
    tracking_num_request[:add2] = shipping_address_2
    tracking_num_request[:pin] = shipping_pincode
    tracking_num_request[:city] = shipping_city
    tracking_num_request[:state] = shipping_state
    tracking_num_request[:country] = 'India'
    tracking_num_request[:phone] = shipping_telephone
    tracking_num_request[:mode] = 'prepaid'
    tracking_num_request[:ret_add] = self.address
    tracking_num_request[:ship_pin] = shipping_pincode
    tracking_num_request[:ship_phone] = shipping_telephone
    tracking_num_request[:ship_company] = company_name
    if dos.present?
      tracking_num_request[:oid] = dos.order.number
      tracking_num_request[:weight] = dos.line_items.sane_items.size/10.0
      tracking_num_request[:amt] = dos.total
      return HTTParty.post(tracking_num_url,body:tracking_num_request).parsed_response
    else
      return tracking_num_request
    end
  end

  def get_invoice_number
    year = Date.today.strftime("%y")
    month = Date.today.strftime("%m")
    self.reload
    if %w(01 02 03).include?(month)
      date = "#{self.id}/#{year.to_i-1}#{year}/%05d" % self.invoice_number
    else
      date = "#{self.id}/#{year}#{year.to_i+1}/%05d" % self.invoice_number
    end
    self.update_column(:invoice_number,self.invoice_number+1)
    return date
  end

  def inactive_designer_state?
    self.banned? || self.inactive? || self.on_hold?
  end
  #-####################################################################
  #
  # State Machine
  #
  # Initial State Review - designs would be reviewed before getting
  # published if designer state is review from all states to any state
  #
  # -------------------------------------------------------------------
  # The states of designer are :
  # -------------------------------------------------------------------
  # - Review pending  ->  All other states
  # - Review          ->  All states except review_pending
  # - Approved        ->  All states except review_pending 
  # - On hold         ->  approved, review, inactive, banned
  # - Inactive        ->  approved, review, on_hold, banned
  # - Vacation        ->  review, approved, banned, on_hold, inactive
  # - Banned          ->  No where
  #
  #-#####################################################################

  state_machine :state_machine, :initial => :review_pending do
    before_transition to: :review do |designer|
      designer.last_review_on = Time.now
    end

    before_transition to: :approved do |designer|
      designer.last_approved_on = Time.now
    end

    before_transition to: :banned do |designer|
      designer.last_banned_on = Time.now
    end

    before_transition from: :vacation do |designer|
      designer.vacation_start_date, designer.vacation_end_date, designer.vacation_message = nil, nil, nil
    end

    before_transition from: :on_hold do |designer|
      designer.last_on_hold_at = Time.now
    end

    before_transition from: :inactive do |designer|
      designer.last_inactive_at = Time.now
    end

    before_transition on: :banned_1 do |designer|
      designer.banned_reason = 'banned'
    end

    before_transition on: :banned_for_high_odr do |designer|
      designer.banned_reason = 'high_odr'
    end

    before_transition on: :to_on_hold do |designer, transition|
      designer.banned_reason = 'on_hold'
      designer.banned_reason = 'inactive' if transition.from.in?(['review', 'approved'])
    end

    event :new_approved do
      transition [:review_pending] => :review
    end

    event :satisfied do
      transition [:review_pending, :review, :vacation, :inactive] => :approved
    end

    event :dissatisfied do
      transition [:review_pending, :approved, :vacation, :on_hold, :inactive] => :review
    end

    event :banned_1 do
      transition [:review_pending, :approved, :review, :on_hold, :vacation, :inactive] => :banned
    end

    event :banned_for_high_odr do 
      transition all - [:banned, :on_hold] => :on_hold
    end

    event :banned_for_high_cr_lsr do
      transition all - [:banned, :on_hold] => :on_hold
    end

    event :to_on_hold do
      transition [:review_pending, :review, :approved, :vacation] => :on_hold
    end

    event :to_inactive do
      transition [:review, :approved] => :inactive
    end

    event :on_vacation do
      transition [:review_pending, :approved, :review] => :vacation
    end

    after_transition to: :vacation do |designer|
      SidekiqDelayGenericJob.perform_async("Designer", nil, "move_designs_to_hold", {"Designer": designer.id})
      #Designer.sidekiq_delay.move_designs_to_hold(designer)
    end

    after_transition to: [:banned, :on_hold, :inactive] do |designer|
      Designer.sidekiq_delay.show_in_catalog('no', designer.id)
      # Designer.delay(priority: 4).move_design_to_banned_catalog(designer.id)
    end
    
    after_transition from: :review_pending, to: [:review, :approved] do |designer|
      SidekiqDelayGenericJob.perform_async(designer.class.to_s, designer.id, "create_default_designer_shippers") unless designer.designer_shippers.exists?
      #designer.sidekiq_delay.create_default_designer_shippers unless designer.designer_shippers.exists?
    end

    after_transition from: [:inactive, :on_hold], to: [:review, :approved] do |designer|
      Designer.sidekiq_delay.show_in_catalog('all', designer.id)
      # Designer.delay(priority: 4).move_design_from_banned_catalog(designer.id)
    end

    after_transition to: [:review, :approved] do |designer|
      SidekiqDelayGenericJob.perform_async(designer.class.to_s, nil, "move_designs_from_hold_to_instock", {"Designer": designer.id})
      #Designer.sidekiq_delay.move_designs_from_hold_to_instock(designer)
    end
  end

  def show_in_catalog(catalog)
    Designer.sidekiq_delay.show_in_catalog(catalog, id)
  end

  def self.show_in_catalog(catalog, designer_ids)
    catalog = catalog.downcase
    Designer.where(id: designer_ids).update_all(in_catalog: catalog)
    all_designs_ids = Design.where(designer_id: designer_ids).uniq.pluck(:id)
    Design.public_send("#{IN_CATALOG[catalog]}_catalog_remove", all_designs_ids)
  end

  def self.move_design_to_banned_catalog(*designer_ids)
    Design.where(designer_id: designer_ids).banned_catalog_remove
  end

  def self.move_design_from_banned_catalog(*designer_ids)
    Design.where(designer_id: designer_ids).no_catalog_remove
  end

  def self.move_designs_to_hold(designer)
    # Reason for comment - Extend the estimate delivery date Instead of hold all designs during vacation mode
    if DESIGNER_VACATION_CONSTRAINTS['design_on_hold_in_short_vacation'].try(:downcase) == 'true' || designer.vacation_end_date.to_date - designer.vacation_start_date.to_date > DESIGNER_VACATION_CONSTRAINTS['vacation_threshold']
      designer.designs.published.find_in_batches do |designs|
        #this preloading need to be changed based on save callbacks
        ActiveRecord::Associations::Preloader.new.preload(designs, [:variants, :slugs, :categories, :images]); nil
        designs.each(&:designer_on_vacation)
      end
    end
  end

  def self.move_designs_from_hold_to_instock(designer)
    designs = designer.designs.where('designs.state = ?', 'on_hold')
    designs.find_in_batches do |designs|
      # this preloading need to be changed based on save callbacks
      ActiveRecord::Associations::Preloader.new.preload(designs, [:variants, :slugs, :categories, :images]); nil
      designs.each(&:available)
    end
  end

  def multi_line_address(line_length = 35)
    multi_line_address_block = []
    i = 0
    street.split(/[ ,]/).each do |s|
      if multi_line_address_block[i].blank?
        multi_line_address_block[i] = s
      elsif (multi_line_address_block[i].length + s.length < line_length)
        multi_line_address_block[i] += ' ' + s
      else
        i += 1
        multi_line_address_block[i] = s
      end
    end
    multi_line_address_block
  end

  def check_gst_details?
    return self.gst_certificate? && self.gst_address? && self.gst_no? && self.taxpayer_trade_name?
  end

  # input params - none
  #
  # Returns array of designer allowed shipper ids
  def allowed_shipper_ids(region)
    w_enabled = {:designer_shippers => {:enabled => true}, :shippers => {:enabled => true}}
    if region == 'international'
      w_region = {:shippers => {:international => true}, :designer_shippers => {:international => true}}
    elsif region == 'cod'
      w_region = {shippers: {domestic: true}, designer_shippers: {cod: true}}
    else
      w_region = {:shippers => {:domestic => true}, :designer_shippers => {:domestic => true}}
    end
    shipper_ids = self.designer_shippers.joins(:shipper).where(w_enabled).where(w_region).pluck(:shipper_id)
  end

  def get_order_pickup_details(designer_order=nil, order=nil)
    if order.present? && designer_order.present? && designer_order.domestic_sor?(order)
      company_name, shipping_telephone, shipping_address_1,shipping_address_2, shipping_city, shipping_pincode, shipping_state, shipping_state_code = DesignerOrder.get_warehouse_billing_address(designer_order.warehouse_address_id)
      {
        name: 'Anup Nair',
        address: "#{shipping_address_1}, #{shipping_address_2}",
        city: shipping_city,
        pincode: shipping_pincode,
        state: shipping_state,
        phone: shipping_telephone,
        company: company_name,
        state_code: shipping_state_code,
        country_code: 'IN',
        email: '<EMAIL>'
      }
    else
      {
        name: self.name.presence || self.business_name,
        address: self.street.presence || self.business_street,
        city: self.city.presence || self.business_city,
        pincode: self.pincode.presence || self.business_pincode,
        state: self.state.presence || self.business_state,
        phone: self.phone,
        company: self.name.presence || self.business_name,
        state_code: self.state_code,
        country_code: 'IN',
        email: self.email
      }
    end
  end

  def can_cod?(pincode)
    cod = false
    if self.cod && (shipper_ids = self.allowed_shipper_ids('cod')).present?
      cod_shipper_ids = self.designer_shippers.where(shipper_id: shipper_ids, cod: true).pluck(:shipper_id)
      cod = true if Courier.shippable?(cod_shipper_ids, self.pincode, pincode, true)
    end
    cod
  end

  def create_master_addon(category_id, addon_type_id, addon_type_values)
    addon_type_values.each do |addon_type_value|
      self.master_addons.where(category_id: category_id,
        addon_type_id: addon_type_id, addon_type_value_id: addon_type_value.id,
        price: addon_type_value.price, prod_time: addon_type_value.prod_time).first_or_create
    end
  end

  def designer_collections_mapper
    FuzzyMapper.new designer_collection_to_id
  end

  def check_and_create_master_addon
    category_ids = self.category_ids
    saree_category_ids = Category.getids('sarees')
    salwar_kameez_category_ids = Category.getids('salwar-kameez')

    if (category_ids & saree_category_ids).present?
      sarees_category = Category.find_by_namei('sarees')
      addon_type_id, addon_type_values = AddonTypeValue.category_master_addons('blouse', sarees_category.id)
      self.create_master_addon(sarees_category.id, addon_type_id, addon_type_values)
    end
    if (category_ids & salwar_kameez_category_ids).present?
      salwar_kameez_category = Category.find_by_namei('salwar-kameez')
      addon_type_id, addon_type_values = AddonTypeValue.category_master_addons('salwar_kameez', salwar_kameez_category.id)
      self.create_master_addon(salwar_kameez_category.id, addon_type_id, addon_type_values)
    end
  end

  def self.alert_new_designers
    date_range = [Date.today-15.day,Date.today-45.day,Date.today-75.day,Date.today-105.day]
    designers = Designer.includes(:account).where('accounts.created_at::DATE in(?)', date_range).where(:state_machine => ['review', 'approved']).references(:account)
    designers.each do |designer|
      designer.alert_incomplete_profile_users
      if (design_count = designer.designs.count) < 50
        SidekiqDelayGenericJob.perform_async("DesignerMailer", nil, "only_few_designs_uploaded", {"Designer": designer.id}, design_count)
        #DesignerMailer.sidekiq_delay.only_few_designs_uploaded(designer, design_count)
      end
    end
  end

  def self.alert_existing_designers
    designers = Designer.includes(:account).where(:state_machine => ['review', 'approved'])
    designers.each do |designer|
      if (designer_account = designer.account).present?
        if ((Date.today - designer_account.created_at.to_date).to_i)%30 == 0
          designer.alert_incomplete_profile_users
          if (design_count = designer.designs.count) < 50
            DesignerMailer.sidekiq_delay.only_few_designs_uploaded(designer.id, design_count)
          end
        end
      end
    end
  end

  def alert_incomplete_profile_users
    if [email, phone, city, street, state, country, pincode].any?(&:blank?)
      DesignerMailer.sidekiq_delay.alert_profile_incomplete(self.id)
    end
  end

  def adjustment_allowed?
    EXCLUDE_VENDOR_FOR_ADJUSTMENTS.map(&:to_i).exclude?(id) && !is_transfer_model?
  end

  def update_reviews
    ratings = reviews.approved.nps.select('count(id) as count, avg(rating) as average')[0]
    self.update_attribute(:average_rating, ratings.average.to_f.round(2))
    SidekiqDelayGenericJob.perform_async(self.class.to_s, self.id, "update_rating_score", {param1:ratings.average.to_f.round(2),param2:ratings.count.to_i, sidekiq_request_params: true})
    #self.sidekiq_delay.update_rating_score({param1:ratings.average.to_f.round(2),param2:ratings.count.to_i})
  end

  def quality_of_designs
    quality_products = Rails.cache.fetch("quality_designs_#{id}", :expires_in => 7.days) do
      quality = {}
      quality['good'] = designs.published.where('quality_level >= ? and average_rating > ?', 1.1, 3.5).count
      quality['bad'] = designs.published.where('quality_level < ? and average_rating < ?', 0.6, 3.5).count
      quality['unidentified'] = designs.published.where('quality_level between ? and ? and average_rating < ?',0.6,1.1,4).count
      quality
    end
  end

  def get_reviews_of_design(start_date, end_date)
    result ={}
    review  = reviews.select('rating,design_id').where(created_at: start_date.beginning_of_day..end_date.end_of_day).where('order_id is not null').to_a
    result['total_sales'] = review.size
    unless result['total_sales'] == 0
      bad_reviewed_count = review.count{|r| r.rating < 3.5}
      bad_quality = bad_reviewed_count.to_f/result['total_sales'].to_f
      result['bad_quality_sale_percentage'] = (bad_quality * 100).round(1).to_s+' %'
      result['good_quality_sale_percentage'] = ((1.0 - bad_quality) * 100).round(1).to_s+' %'
      #result['color_class'] = bad_quality < 0.1 ? 'red' : (bad_quality > 0.5 ? 'green' : 'blue')
    else
      #result['color_class'] = 'gray'
      result['bad_quality_sale_percentage'] = '---'
      result['good_quality_sale_percentage'] = '---'
    end
    result['design_ids'] = review.collect(&:design_id)
    result
  end

  def self.search(search)
    if search.present?
      where('designers.id::text LIKE :search OR lower(designers.name) LIKE :search OR lower(designers.cached_slug) LIKE :search OR lower(designers.email) LIKE :search OR designers.phone LIKE :search', search: "%#{search.to_s.downcase}%")
    # else
    #   self
    end
  end

  %w(rating nps_review design_count per_day_sale fb).each do |attribute|
    send :define_method, "update_#{attribute}_score" do |* args|
      unless args.present?
        update_column(:score_flag, true)
      else
        VendorScore.send("#{attribute}_score", self, *args)
      end
    end
  end

  def get_review_rank(rating =nil)
    rating.present? ? self.class.where('score * average_rating > ?',score.to_i * rating).count + 1 : (self.score.present? ? self.class.where('score > ?',score.to_i).order('average_rating desc').count + 1 : 'unranked')
  end

  def initialise_rank
    initial_score = VendorScore.initialise_score(self)
    current_score = 0
    JSON.parse(initial_score).values.each{|key| current_score += key['score'].to_f if key.class == Hash}
    self.score_hash = initial_score
    self.score = current_score
    self.save
  end

  def self.update_designer_fb_score(score_hash={})
    return unless score_hash.present?
    minmax = score_hash.values.minmax
    Designer.where(id: score_hash.keys).each do |designer|
      designer.update_fb_score({param1: score_hash[designer.id], param2: minmax[0], param3:minmax[1]})
    end
  end

  def create_default_designer_shippers
    if (default_ds_priority_hash = SystemConstant.get('AUTO_CREATE_DESIGNER_SHIPPERS_AND_PRIORITY')).present? && default_ds_priority_hash != 'false'
      shipper_name_to_priority = {}
      JSON.parse(default_ds_priority_hash).each{|k, v| shipper_name_to_priority[k.downcase] = v}
      Shipper.select('name, id').where('lower(name) IN (?)', shipper_name_to_priority.keys).each do |sh|
        designer_shippers.create(shipper_id: sh.id, priority: shipper_name_to_priority[sh.name.downcase], enabled: true, domestic: true, international: true, cod: true)
      end
    end
  end

  def similar_count
    designs.where("similar_count > 0 and state <> 'reject'").count
  end

  def inventory_csv(state)
    s_design  = "designs.id, designs.quantity, designs.quantity as designer_quantity, designs.designable_type,  designs.in_stock_warehouse, designs.design_code, designs.title, designs.state, designs.price, designs.discount_percent, designs.package_details, designs.weight, designs.eta, designs.designer_collection_id, designs.gst_rate, designs.hsn_code, designs.design_group_id, designs.transfer_price"

    # Getting designer - 'designs_without_variants' vars
    designs   = self.designs.select(s_design).preload(:designer_collection,:design_group,variants_with_option_type_id: :option_type_values_variants).order('designs.id')

    if state == "unpublished"
      designs = designs.unpublished
    elsif state == "published"
      designs = designs.published
    else
      designs = designs.where(:state => state)
    end

    # Getting only variant_name - option type value [option_type_name_option_type_value] for designer designs
    optv_name_hash = OptionType.get_designers_option_type(self.id)
    design_keys = %w(id type design_code size parent_id designable_type title)
    dynamic_design_keys = self.is_transfer_model? ? %w(transfer_price designer_quantity package_details weight collection_name gst_rate hsn_code) : %w(price discount_percent designer_quantity package_details weight collection_name gst_rate hsn_code)

    file = CSV.generate do |csv|
      show_eta = ALLOWED_VENDOR_FOR_PREPARATION.include?(id)
      headers = %w(mirraw_id relationship_type vendor_sku_code size color_variation_mirraw_id category title)
      headers.push('preparation_time') if show_eta
      if self.is_transfer_model?
        headers.push('transfer_price')
      else
        headers.push(*%w(web_price_before_discount discount_percent))
      end
      headers.push(*%w(designer_quantity package_details weight collection gst_rate hsn_code))
      csv << headers
      headers = ['Unique Identification number maintained by mirraw', 'Parent - Main Design Child - Size Variant', 'Vendor SKU Code is the identification number maintained by the seller to keep track of skus', 'Size of the product (Apparel- S M L XL) Do not change this column', 'Mention Parent Mirraw_Id of Design which is color variant of the selected design', 'Type or category of product', 'Product Name or title']
      headers.push('Time required to dispatch product') if show_eta
      if self.is_transfer_model?
        headers.push('Desired Amount for particular design')
      else
        headers.push(*['Actual MRP of the Product without discount', 'Discount on product in percentage'])
      end
      headers.push(*['The qty available for the particular product', 'Contents of package delivered to user', 'Approx weight of the product', '', 'Gst rate should be from (0 3 5 12 18 28)', '4 digit HSN Code dependent on category'])
      csv << headers
      designs.find_each(batch_size: 500) do |design|
        design_values    = design.attributes.values_at(*design_keys)
        design_values[1] = 'Parent'
        design_values[2] = '="' + design.design_code.to_s + '"'
        if design.design_group.present? && design.design_group.parent_design_id != design.id
          design_values[1] = 'Color Child'
          design_values[4] = design.design_group.parent_design_id
        end
        design_values[7]= design.eta if show_eta
        design_values.push(*design.attributes.values_at(*dynamic_design_keys))
        if show_eta
          qty_pos, col_pos = self.is_transfer_model? ? [9,12] : [10,13]
        else
          qty_pos, col_pos = self.is_transfer_model? ? [8,11] : [9,12]
        end
        design_values[qty_pos] = design.variants_with_option_type_id.present? ? 'NA' : design.designer_quantity
        design_values[col_pos] = design.designer_collection.try(:name)
        csv << design_values

        design.variants_with_option_type_id.each do |design_variant|
          design_values    = design_variant.attributes.values_at(*design_keys)
          design_values[1] = 'Size Child'
          design_values[2] = '="' + design_variant.design_code.to_s + '"'
          design_values[3] = optv_name_hash[design_variant.option_type_values_variants.first.try(:option_type_value_id)]
          design_values[6] = design.title
          design_values[7] = 'NA' if show_eta
          design_values.push(*design_variant.attributes.values_at(*dynamic_design_keys))
          if show_eta
            qty_pos, col_pos = self.is_transfer_model? ? [nil,9] : [9,10]
          else
            qty_pos, col_pos = self.is_transfer_model? ? [nil,8] : [8,9]
          end
          design_values[qty_pos] = 'NA' if qty_pos.present?
          design_values[col_pos] = design_variant.designer_quantity
          csv << design_values
        end
      end
    end
  end

  def gst_number_formate
    if !gst_no.match(/^.{12}\d{1}[zZ]\w{1}$/)
      errors.add(:gst_no, ': Not a valid GST number')
    elsif !IN_STATE_CODE.values.include?(gst_no.first(2))
      errors.add(:gst_no, ': first 2 digit must contain state code') 
    elsif !gst_no[2..11].match(/[A-Za-z]{5}\d{4}[A-Za-z]{1}/)
      errors.add(:gst_no, ': from 3rd to 12th digits must contain a valid pan card number')
    end
  end

  def check_transfer_price_present
    if is_transfer_model? && designs.published.where{(transfer_price == nil) | (transfer_price <= 0)}.exists?
      errors.add(:transfer_model_rate, ': Cannot be changed since all designs do not have transfer price and gst_rate')
    end
  end

  def check_if_not_transfer_model
    if exclude_gst && is_transfer_model?
      errors.add(:exclude_gst, ': Cannot be changed since vendor is based on transfer price model')
    end
  end

  def check_for_banned_warning
    odr_percent_last_90_day = self[:max_odr_90_percent] || (odr_90.try(:value).to_f * 100)
    odr_percent_last_90_day > VENDOR_SUSPENSION_METRIC['min_odr_threshold'] && odr_percent_last_90_day < VENDOR_SUSPENSION_METRIC['max_odr_threshold']
  end

  def monetize_token
    @monetize_token ||= Base64.urlsafe_encode64 begin
      self.class.onlinesalses_cipher(self.account.id.to_s, :encrypt)
    end
  end

  def self.monetize_designer(token)
    token = Base64.urlsafe_decode64 token
    if account_id = onlinesalses_cipher(token, :decrypt)
      Account.find_by(
        id: account_id.to_i,
        accountable_type: 'Designer'
      ).try(:accountable)
    end
  end

  def self.onlinesalses_cipher(text, action, key = nil)
    cipher = OpenSSL::Cipher::Cipher.new("aes-256-cbc")
    cipher.send(action)
    cipher.key = key || Rails.application.secrets.secret_key_base
    cipher.iv = (0..15).to_a.pack("C*")
    cipher.update(text) << cipher.final rescue nil
  end

  def sunspot_index_products
    designs.find_in_batches do |design_batch|
      Sunspot.index(design_batch)
    end
  end
  handle_asynchronously :sunspot_index_products

  def eta
    super.to_i <= 0 ? SLA_CONFIG[:mirraw_designer_default_eta] : super.to_f
  end

  def ship_time
    super.to_f == 0 ? SLA_CONFIG[:vendor_default_dispatch_days] : super.to_f
  end

  def self.get_designer_performance(date: Date.current)
    metric_definition_mapping = MetricDefinition.where(visible_to_admin: true, visible_to_designer: true).all.pluck(:id,:name,:duration)
    metric_definition_mapping = metric_definition_mapping.map{|d| [d[0], "#{d[1].underscore}_#{d[2]}"]}.to_h
    join_clause = "LEFT OUTER JOIN metric_values ON metric_values.actor_id = designers.id
      AND metric_values.actor_type = 'Designer'
      AND metric_values.metric_definition_id IN(#{metric_definition_mapping.keys.join(',')})
      AND metric_values.generated_on = '#{date}'"
    select_clause = ['metric_values.actor_type', 'metric_values.actor_id']
    select_clause << metric_definition_mapping.map do |id, name|
      "round(sum(case when metric_definition_id = #{id} then value else 0 end)::numeric, 4) as #{name}"
    end
    select_clause << 'designers.*'
    joins(join_clause).select(select_clause).group(1,2,:id).order('3 desc')
  end

  def default_values
    self.production_type = default_production_type if PRODUCTION_TYPES.values.exclude?(production_type)
  end

  def default_production_type
    PRODUCTION_TYPES[:ready_to_ship]
  end

  def is_unicommerce_vendor
    unicommerce_enabled 
  end

  def get_designer_address
    {
      name: self.name.presence || self.business_name,
      address: self.street.presence || self.business_street,
      city: self.city.presence || self.business_city,
      pincode: self.pincode.presence || self.business_pincode,
      state: self.state.presence || self.business_state,
      phone: self.phone,
      company: self.name.presence || self.business_name,
      state_code: self.state_code,
      country_code: 'IN',
      email: self.email
  }
  end

  def get_oos_bestseller_designs
    RequestStore.cache_fetch("bestseller_designs_for_#{self.id}", expires_in: 6.hours) do
      result = designs
      .joins(line_items: :designer_order)
      .joins("LEFT JOIN variants ON line_items.variant_id = variants.id")
      .where('designer_orders.confirmed_at > ?', 30.days.ago)
      .where(designer_orders: { state: ['pending', 'pickedup', 'dispatched', 'completed'] })
      .group('designs.id', 'variants.id')
      .having('SUM(line_items.quantity) >= 5 AND (variants.id IS NULL AND (designs.quantity - COALESCE(designs.in_stock_warehouse, 0)) <= 5 OR variants.id IS NOT NULL AND (variants.quantity - COALESCE(variants.in_stock_warehouse, 0)) <= 5)')
      .order('designs.quantity DESC')
    end
  end

  protected

    def publish_designs_if
      if self.published
        designs.update_all "published = true"
      else
        designs.update_all "published = false"
      end
    end

  private

    UPLOAD_FILES.each do |file|
      define_method("randomize_file_name_#{file}") do
        extension = File.extname(self.send("#{file}_file_name")).downcase
        self.send(file).instance_write(:file_name, "#{SecureRandom.hex(16)}#{extension}")
      end
    end

    def validate_vacation_date
      if self.vacation_start_date.present? && self.vacation_end_date.present? && self.vacation_end_date <= self.vacation_start_date
        errors.add(:vacation_end_date, "vacation end date should be greater than vacation start date")
      end
    end

    def designer_collection_to_id
      Rails.cache.fetch("designer_collections_designer_#{id}") do
        hash ={}
        designer_collections.each do |designer_collection|
          hash[designer_collection.name] = designer_collection.id
        end
        hash
      end
    end

    def update_triggers
      if self.name.present? && (self.name_changed? || self.p_name.blank?)
        self.p_name = self.name.gsub('-',' ').split.map(&:capitalize).join(' ')
      end
      self.state_code = State.get_state_code(state, country) if state_changed? && country.present?
      if state_changed? || business_state_changed?
        zones = {north: ['Jammu and Kashmir','Himachal Pradesh','Punjab','Uttarakhand','Delhi','New Delhi','Uttar Pradesh','Haryana','Rajasthan','Chhattisgarh'], east: ['Bihar','Jharkhand','West Bengal','Assam','Sikkim','Nagaland','Meghalaya','Manipur','Mizoram','Tripura','Arunachal Pradesh'], central: ['Orissa','Odisha','Andhra Pradesh','Madhya Pradesh'], west: ['Gujarat','Goa','Maharashtra'], south: ['Karnataka','Kerala','Tamil Nadu','Telangana']}.freeze
        zones.each do |zone,states|
          self.zone = zone if states.include?(state) || states.include?(business_state)
        end
      end
      self.transaction_rate = 0 if self.is_transfer_model?
      if transaction_rate_changed? && is_gst_excluded? && is_variable_gst_model?
        self.variable_gst_model = true
      end
    end

    def initialize_values
      self.average_rating = 0
      self.ship_time = SLA_CONFIG[:vendor_default_dispatch_days]
    end

  extend DynamicTemplateMethod
  dynamic_alias_methods :name, :email, :phone, :city, :state, :country, :pincode, :transaction_rate, :vacation_start_date, :vacation_end_date, :vacation_message, :fb_page_name, :fb_page_id, :fb_page_token, :fb_page_link, :last_fb_post_at, :state_machine, :last_approved_on, :last_review_on, :last_banned_on, :business_name, :business_city, :business_state, :business_country, :business_pincode, :pickup_name, :pickup_company_name, :pickup_phone, :pickup_street, :pickup_city, :pickup_state_code, :pickup_pincode, :p_name, :average_rating, :additional_discount_start_date, :additional_discount_end_date, :vendor_additional_discount_percent, :adjustments, :designs, :designer_batches, :designer_issues, :designer_orders, :payout_managements, :pickups, :return_designer_orders, :reviews, :to_param

end
