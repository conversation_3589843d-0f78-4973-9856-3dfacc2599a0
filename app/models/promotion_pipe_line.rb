class PromotionPipeLine < ActiveRecord::Base
  include SidekiqHandleAsynchronous
  has_paper_trail
  has_attached_file :attached_file
  validates_attachment_content_type :attached_file, content_type: ['text/csv', 'text/plain']
  #attr_accessible :end_date, :images_hash, :variables_hash, :methods_hash,
                  # :start_date, :event_name, :user_id, :name, :country_code, :app_source
  validates_presence_of :end_date, :methods_hash, :start_date, :event_name
  validates :name, presence: true, uniqueness: true
  belongs_to :admin_account, class_name: 'Account', foreign_key: :user_id
  has_many :design_promotion_pipe_lines
  has_many :designs, through: :design_promotion_pipe_lines

  validate :valid_hash, on: :update

  after_save :create_promotion_history
  after_save :update_file_details_to_promotion, if: :attached_file_updated_at_changed?
  after_save :revert_direct_dollar_price, if: :is_direct_dollar_promotion?
  scope :active_promotions, ->{where{(start_date.lt Time.zone.now) & (end_date.gt Time.zone.now)} }
  scope :by_name, -> (query) {where("name ILIKE :search", search: "%#{query}%")}
  scope :changing_promotion, -> {where("(to_char(start_date , 'YYYY-MM-DD HH24')='#{(Time.now.getutc).strftime('%Y-%m-%d %H')}') or (to_char(end_date , 'YYYY-MM-DD HH24')='#{(Time.now.getutc).strftime('%Y-%m-%d %H')}')")}
  scope :changed_promotion, -> {where{(date(start_date).eq  (Time.zone.now.to_date + 1.day)) | (date(end_date).eq  (Time.zone.now.to_date - 1.day) )}}
  scope :app_source, ->(app_source) { where { "app_source ILIKE '%#{app_source}%' OR app_source IS NULL OR app_source=''" } }

  def country_code?(country_code)
    self.country_code.split(',').include?(country_code)
  end

  # currenlty used for generating promotion_id in google adword feeds
  def self.bmgnx_promotion_country_lable_hash
    PromotionPipeLine.active_promotions.where("name ILIKE ?",'%bmgnx%').collect do |promotion|
      bmgnx_var_hash = JSON.parse(promotion.variables_hash, symbolize_names: true).transform_values(&:to_f).with_indifferent_access
      value = "b#{bmgnx_var_hash[:m].to_i}g#{bmgnx_var_hash[:n].to_i}"
      [promotion.country_code, value]
    end.to_h
  end

  def self.bmgnx_hash
    country_code = Design.country_code
    RequestStore.cache_fetch("desktop_bmgnx_hash_#{country_code}") do
      if (promo = PromotionPipeLine.active_promotions.where("name ILIKE ?",'%bmgnx%').for_country(country_code).first).present?
        bmgnx_var_hash = JSON.parse(promo.variables_hash, symbolize_names: true).inject({}) { |result,(key,value)|
          if key == :scale
            result[key.to_sym] = value.to_f
          else
            result[key.to_sym] = value.to_i
          end
          result
        }
        if [bmgnx_var_hash[:m], bmgnx_var_hash[:n], bmgnx_var_hash[:x]].all?{|val|  val.present? && val.is_a?(Numeric) && val > 0}
          bmgnx_var_hash.merge({start_date: promo.start_date, end_date: promo.end_date})
        else
          {}
        end
      else
        {}
      end
    end
  end

  def self.current_bmgn_offer
    bmgn_config = self.bmgnx_hash
    return if bmgn_config.blank?
    "B#{bmgn_config[:m]}G#{bmgn_config[:n]}"
  end

  def self.for_country(country_code)
    where("promotion_pipe_lines.country_code = '' OR promotion_pipe_lines.country_code IS NULL OR promotion_pipe_lines.country_code ILIKE ?","%#{country_code}%")
  end

  def self.clear_cache_request_store
    Rails.cache.clear
    RequestStore.clear!
  end

  def is_flash_deals_promotion?
    /flash_deals_promotion.*/ === name
  end

  def is_design_free_stitching_promotion?
    /design_free_stitching.*/ === name
  end

  def self.design_free_stitching_promotions
    where('name like ?', 'design_free_stitching%')
  end

  def is_direct_dollar_promotion?
    /direct_dollar.*/ === name
  end

  def revert_direct_dollar_price
    if is_direct_dollar_promotion? && self.end_date < Time.now
      (design = Design.joins(:design_promotion_pipe_lines).where("design_promotion_pipe_lines.promotion_pipe_line_id": self.id)).update_all(buy_get_free: nil)
      Sunspot.index! design
    end
  end

  def update_file_details_to_promotion
    if attached_file.present? && is_design_free_stitching_promotion?
      # de-associate all present designs
      DesignPromotionPipeLine.where(promotion_pipe_line_id: self.id).delete_all
      file_name = "/tmp/#{SecureRandom.urlsafe_base64}"
      attached_file.copy_to_local_file(nil, file_name)
      CSV.foreach(file_name, headers: true).each_slice(500) do |lists|
        ids = lists.collect{|list| list['Design Id']}
        design_promotions = Design.select(:id).where(id: ids).collect do |design| 
          design_promotion_pipe_lines.new(design: design)
        end
        DesignPromotionPipeLine.import design_promotions, validate: false, on_duplicate_key_ignore: true
      end
    elsif attached_file.present? && is_flash_deals_promotion?
      file_name = "/tmp/#{SecureRandom.urlsafe_base64}"
      attached_file.copy_to_local_file(nil, file_name)
      CSV.foreach(file_name, headers: true) do |row|
        design = Design.find_by_id(row['Design Id'].to_i)
        if design.present?
          dp = design_promotion_pipe_lines.find_by(design_id: design) || design_promotion_pipe_lines.new(design_id: design.id)
          dp.start_date = Time.zone.parse(row['Start Date Time']).to_time
          dp.end_date = Time.zone.parse(row['End Date Time']).to_time
          dp.save!
          Sunspot.index! design
        end
      end
    elsif attached_file.present? && is_direct_dollar_promotion?
      DesignPromotionPipeLine.where(promotion_pipe_line_id: self.id).delete_all
      file_name = "/tmp/#{SecureRandom.urlsafe_base64}"
      attached_file.copy_to_local_file(nil, file_name)
      csv = CSV.parse(File.read("#{file_name}"), headers: true)
      csv.each do |row|
        design = Design.find_by_id(row['Design Id'].to_i)
        if design.present?
          Design.where(id:design.id).update_all(buy_get_free: '4')
          dp = design_promotion_pipe_lines.find_by(design_id: design) || design_promotion_pipe_lines.new(design_id: design.id)
          dp.promotion_pipe_line_id = self.id
          dp.save!
          Sunspot.index! design
        end
      end
      csv = nil
    end
  end
  handle_asynchronously :update_file_details_to_promotion, priority: -10, queue: 'high_priority'
  handle_asynchronously :revert_direct_dollar_price, priority: -10, queue: 'high_priority', run_at: 6.hours.from_now

  private

  def valid_hash
    parse_hash
    self.errors.add(:variables_hash, @parse_error) if @parse_error.present?
  end

  def parse_hash
    begin
      JSON.parse(self.images_hash)
      JSON.parse(self.variables_hash)
      return true
    rescue
      @parse_error = "invalid hash format"
    end
  end

  def create_promotion_history
    if self.changed?
      PromotionHistory.create!(end_date: self.end_date, offer: self.variables_hash,
        start_date: self.start_date, event_name: self.event_name, user_id: self.user_id,
        name: self.name, country_code: self.country_code)
    end
  end

  def self.active_promotion_for_timer(country_code = Design.country_code)
    RequestStore.cache_fetch("deal_end_global_category_#{country_code}", :expires_in => 24.hours) do
      PromotionPipeLine.active_promotions.where("name SIMILAR TO ?",'%(global_sale|category_discount_sale|bmgnx)%').for_country(country_code).order(:end_date).first || []
    end
  end
end
