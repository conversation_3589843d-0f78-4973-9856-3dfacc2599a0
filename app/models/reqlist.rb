class Reqlist < ActiveRecord::Base
  
  after_create :notify_designer

  validates :email,
            presence: { message: "Request List: Please specify an Email Address" },
            format: { with: /\A((?:[a-z]+[0-9_\.-]*)+[a-z0-9_\.-]*)@((?:[a-z0-9]+[\.-]*)+\.[a-z]{2,4})\z/i,
            message: "Request List: Please correct your Email Address" }, :on => :create
  
  def notify_designer
    email = Design.find(self.design).designer.email
    FollowMailer.sidekiq_delay.sendout_request_product_notification(email, self.design)
  end
  
end
