class SellerCampaign < ActiveRecord::Base
    validates :name, presence: true
    validates :description, presence: true
    validates :start_date, presence: true
    validates :end_date, presence: true
    has_many :designer_campaign_participations
    has_many :designers, through: :designer_campaign_participations
    validate :end_date_after_start_date

    private
  
    def end_date_after_start_date
      if end_date < start_date
        errors.add(:end_date, "must be greater than or equal to the start date")
      end
    end
end
