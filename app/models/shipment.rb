class Shipment < ActiveRecord::Base
  #attr_accessible :delivered, :invoicer_id, :packaging_type, :packer_id, :price, :shipment_state, :shipper_id, :weight, :order_id,
                  # :designer_order_id, :line_item_ids, :number, :shipper, :label, :created_at, :return_label, :shipment_type, :return_tracking_no, :system_shipment_charges, :system_weight,
                  # :payment_received, :estimated_delivery_timestamp, :in_transit_datetime, :cod_charge, :last_event_timestamp, :payment_state,
                  # :in_transit_sms_count, :delivery_exception_sms_count, :delivered_sms_count, :rto_sms_count, :out_for_delivery_sms_count, :mirraw_reference, :tofrom_label, :unique_tracking_number,
                  # :track_error, :courier_label_url, :automated, :track,:paid_to_courier_partner,:paid_courier_charges_date,:shipment_weight,:vendor_charged_date,:amount_charged_to_vendor,
                  # :chaged_to_vendor,:weight_charged_to_vendor,:claim_state,:claim_raised_date,:claim_rejected_date,:claim_approved_date,:claim_amount,:last_event,:csb_used


  attr_accessor :previous_event 

  belongs_to :shipper
  belongs_to :order
  belongs_to :designer_order
  has_many   :bulk_designer_orders, class_name: 'DesignerOrder', foreign_key: :bulk_shipment_id
  has_many   :bulk_orders, class_name: 'Order', through: :bulk_designer_orders, source: :order
  has_many   :line_items
  has_many   :shipment_invoice_items
  has_many   :inbound_line_items, class_name: 'LineItem', foreign_key: :inbound_shipment_id
  has_many :optional_pickups
  belongs_to :dhl_ecom_pickups
  belongs_to :invoicer, :class_name => "Account"
  belongs_to :packer,   :class_name => "Account"

  self.inheritance_column = :service_type
  # serialize :invoice_data, Hash
  scope :trackable_shipments, -> { where.not(shipment_state: ['delivered', 'rto', 'processing_abandoned', 'intransit_abandoned']) }
  scope :forward, -> { where(service_type: [nil,'ClickpostForward']) }
  after_create :check_order, unless: :rvp_shipment?
  include HasBarcode
  include StringModify
  include SidekiqHandleAsynchronous
  require 'barby/barcode/code_128'
  require 'dhl/dhl_api'
  require 'aramex/aramex_api'
  require 'atlantic/atlantic_api'
  require 'fedex/fedex_api'

  has_barcode :delhivery_barcode_image,
    :outputter => :svg,
    :type => :code_39,
    :value => Proc.new { |p| p.number }

  validates_presence_of :number
  validates :claim_state, :inclusion => {:in =>[nil,'RAISED','REJECTED','APPROVED']}

  def self.mirraw_bucket
    if (Rails.env.production? || Rails.env.admin? || Rails.env.seller?)
      'mirraw'
    else
      'mirraw-test'
    end
  end

  def self.payment_file_dir
    'shipments/payments/'
  end

  def self.generate_barcode(number,height = 50, rotate=false)
    if rotate
      svg_param = {height: height, rot: -90}
    else
      svg_param = {height: height}
    end    
    "data:image/svg+xml;base64,#{Base64.encode64(Barby::Code128.new(number).to_svg(svg_param))}"
  end

  # Input params type - current environment
  #
  # Returns fedex account number

  def self.fedex_account_number(type)
    account_number = YAML.load_file("#{Rails.root}/config/fedex.yml")[type]['account_number']
  end

  Paperclip.interpolates :created_at  do |attachment, style|
    attachment.instance.created_at.strftime('%Y/%B/%d')
  end

  has_attached_file :jewellery_invoice,
    content_type: "application/pdf",
    :storage => :s3,
    :s3_credentials => {
      :access_key_id => ENV['AWS_ACCESS_KEY_ID'],
      :secret_access_key => ENV['AWS_SECRET_ACCESS_KEY'],
      :bucket => Shipment.mirraw_bucket
      },
    :s3_host_name => 's3-ap-southeast-1.amazonaws.com',
    :path => '/:class/:created_at/:id/:id_jewellery_invoice.:extension'

  has_attached_file :web_pdf,
    content_type: "application/pdf",
    :storage => :s3,
    :s3_credentials => {
      :access_key_id => ENV['AWS_ACCESS_KEY_ID'],
      :secret_access_key => ENV['AWS_SECRET_ACCESS_KEY'],
      :bucket => Shipment.mirraw_bucket
      },
    :s3_host_name => 's3-ap-southeast-1.amazonaws.com',
    :path => '/:class/:created_at/:id/:id_web_pdf.:extension'

  has_attached_file :label,
    :storage => :s3,
    :s3_credentials => {
      :access_key_id => ENV['AWS_ACCESS_KEY_ID'],
      :secret_access_key => ENV['AWS_SECRET_ACCESS_KEY'],
      :bucket => Shipment.mirraw_bucket
      },
    :s3_host_name => 's3-ap-southeast-1.amazonaws.com',
    :path => '/:class/:created_at/:id/:id_label.:extension'

  has_attached_file :invoice,
    :storage => :s3,
    :s3_credentials => {
      :access_key_id => ENV['AWS_ACCESS_KEY_ID'],
      :secret_access_key => ENV['AWS_SECRET_ACCESS_KEY'],
      :bucket => Shipment.mirraw_bucket
      },
    :s3_host_name => 's3-ap-southeast-1.amazonaws.com',
    :path => '/:class/:created_at/:id/:id_invoice.:extension'

  has_attached_file :return_label,
    :storage => :s3,
    :s3_credentials => {
      :access_key_id => ENV['AWS_ACCESS_KEY_ID'],
      :secret_access_key => ENV['AWS_SECRET_ACCESS_KEY'],
      :bucket => Shipment.mirraw_bucket
      },
    :s3_host_name => 's3-ap-southeast-1.amazonaws.com',
    :path => '/:class/:created_at/:id/:id_return_label.:extension'

  has_attached_file :tofrom_label,
    :storage => :s3,
    :s3_credentials => {
      :access_key_id => ENV['AWS_ACCESS_KEY_ID'],
      :secret_access_key => ENV['AWS_SECRET_ACCESS_KEY'],
      :bucket => Shipment.mirraw_bucket
      },
    :s3_host_name => 's3-ap-southeast-1.amazonaws.com',
    :path => '/:class/:created_at/:id/:id_tofrom_label.:extension'

  validates_attachment :label, content_type: { content_type: ["application/pdf", "application/octet-stream", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "image/gif"] }
  validates_attachment :return_label, content_type: { content_type: ["application/pdf", "application/octet-stream", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "image/gif"] }

  state_machine :shipment_state, :initial => :processing do
    # in-transit when awb is entered
    # delivered
    # delivery exception - delay, held up
    # out-for-delivery
    # RTO / Return to Shipper

    event :shipment_dispatched do
      transition all => :in_transit
    end

    event :shipment_out_for_delivery do
      transition [:delivery_exception, :in_transit, :processing, :rto] => :out_for_delivery
    end

    event :shipment_delivered do
      transition [:delivery_exception, :out_for_delivery, :in_transit, :processing, :rto, :out_for_pickup] => :delivered
    end

    event :shipment_didnt_reach_buyer do
      transition [:in_transit, :out_for_delivery, :delivery_exception, :processing] => :rto
    end

    event :rto_delivered do
      transition [:rto] => :rto_delivered
    end

    event :shipment_delivery_exception do
      transition all => :delivery_exception
    end

    event :shipment_pickup_exception do
      transition all => :pickup_exception
    end

    event :shipment_lost do
      transition [:in_transit,:processing, :delivery_exception, :out_for_pickup] => :lost
    end

    event :abandoned do
      transition all - [:processing_abandoned] => :processing_abandoned
    end

    event :shipment_canceled do
      transition all - [:delivered] => :canceled
    end

    event :out_for_pickup do
      transition processing: :out_for_pickup
    end

    after_transition [:rto] => [:rto_delivered] do |shipment|
      designer_order=shipment.designer_order
      if designer_order.present?
        rto_delivered = shipment.shipment_state=="rto_delivered" && designer_order.state=="rto"
        # OrderMailer.sidekiq_delay.shipment_notification_for_rto_delivered(designer_order) if rto_delivered 
        SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "shipment_notification_for_rto_delivered", {designer_order.class.to_s => designer_order.id } ) if rto_delivered 
        Order.sidekiq_delay.call_gokwik_split_api(shipment.order.id)
      end
    end

    after_transition :to => :processing_abandoned do |shipment|
      shipment.line_items.update_all(shipment_id: nil)
      if shipment.updated_at >= shipment.created_at + 24.hours
        shipment.processing_abandoned_credit_note(shipment)
      else
        shipment.processing_abandoned_irn(shipment)
      end
    end

    after_transition :processing => :in_transit do |shipment|
      # Send app notification to customer
      if !shipment.rvp_shipment? && (order = shipment.order).present? && order.international? && shipment.designer_order.blank?
        # order.sidekiq_delay.send_line_items_notification('dispatched')
        SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "send_line_items_notification", 'dispatched')
        Order.sidekiq_delay.call_gokwik_split_api(order.id)
      end
      d_o = shipment.designer_order
      # d_o.sidekiq_delay.android_dispatched_notification if NOTIFICATIONS_ENABLE == 'true' && d_o.present?
      SidekiqDelayGenericJob.perform_async(d_o.class.to_s, d_o.id, "android_dispatched_notification") if NOTIFICATIONS_ENABLE == 'true' && d_o.present?
    end

    state :processing_abandoned
    state :intransit_abandoned

    after_transition :to => :rto do |shipment|
      if shipment.designer_order.present? && shipment.order.international? == false
        designer_order = shipment.designer_order
        designer_order.pickup = DateTime.now unless designer_order.pickup.present?
        designer_order.didnt_reach_buyer

        # Send email to customer
        # ShipmentMailer.delay.mail_shipment_info_customer(shipment) if shipment.line_item_ids.present?
        # Send sms to customer
        shipment.send_shipment_msg if shipment.line_item_ids.present?
        # designer_order.sidekiq_delay.send(:order_quality_event!,:add,'ReturnToOwner')
        Order.sidekiq_delay.call_gokwik_split_api(shipment.order.id)
        SidekiqDelayGenericJob.perform_async(designer_order.class.to_s, designer_order.id, "order_quality_event!", :add, 'ReturnToOwner') 
      end
    end

    after_transition :rto => [:delivered, :out_for_delivery, :delivery_exception] do |shipment|
      designer_order = shipment.designer_order
      # designer_order.sidekiq_delay.send(:order_quality_event!,:remove,'ReturnToOwner')
      Order.sidekiq_delay.call_gokwik_split_api(shipment.order.id)
      SidekiqDelayGenericJob.perform_async(designer_order.class.to_s, designer_order.id, "order_quality_event!", :remove, 'ReturnToOwner') 
    end

    before_transition to: :out_for_pickup do |shipment|
      shipment.out_for_pickup_on = (shipment.last_event_timestamp.presence || Time.current) if shipment.rvp_shipment? && shipment.out_for_pickup_on.blank?
    end

    before_transition to: :out_for_delivery do |shipment|
      shipment.out_for_delivery_on = (shipment.last_event_timestamp.presence || Time.current) if shipment.out_for_delivery_on.blank?
    end

    after_transition to: :out_for_pickup do |shipment|
      # shipment.sidekiq_delay.send_shipment_msg if shipment.rvp_shipment?
      Order.sidekiq_delay.call_gokwik_split_api(shipment.order.id)
      SidekiqDelayGenericJob.perform_async(shipment.class.to_s, shipment.id, "send_shipment_msg") if shipment.rvp_shipment?
    end

    after_transition out_for_pickup: :in_transit do |shipment|
      Order.sidekiq_delay.call_gokwik_split_api(shipment.order.id)
      if shipment.rvp_shipment? && (rdo = shipment.return_designer_order).present?
        rdo.product_sent!
      end
    end

    after_transition :to => [:out_for_delivery, :delivery_exception, :in_transit, :pickup_exception] do |shipment|
      #Sending customs_verification email
      countries_for_customs_check = Order::CUSTOMS_VERIFICATION_COUNTRIES_LIST
      if !shipment.rvp_shipment? && shipment.designer_order_id.blank? && shipment.order_id.present? && countries_for_customs_check.include?(shipment.order.country_code) && (!shipment.in_transit_datetime.present?) && (!shipment.order.shipments.any?{|s| s.in_transit_datetime.present?})
        shipment.send_notification_mail_for_customs_verification
      end

      shipment.in_transit_datetime = DateTime.current unless shipment.in_transit_datetime.present?
      if shipment.rvp_shipment?
        if ['delivery_exception', 'pickup_exception'].include?(shipment.shipment_state) && (rdo = shipment.try(:return_designer_order)).present? && rdo.products_kept
          rdo.return.update_return_payment
        end
      else
        shipment.update_designer_order_state
        # Send email to customer
        ShipmentMailer.sidekiq_delay.mail_shipment_info_customer(shipment.id) if shipment.line_item_ids.present? && shipment.shipment_state  == 'delivery_exception' && shipment.order.present? && shipment.order.international?
        # Send sms to customer
        shipment.send_shipment_msg if shipment.line_item_ids.present? || (shipment.order.try(:cod?) && shipment.shipment_state == 'out_for_delivery')
      end
      Order.sidekiq_delay.call_gokwik_split_api(shipment.order.id)
    end

    after_transition :to => :delivered do |shipment|
      AppEvent::OrderEvent.new(shipment.order.id, "Order Delivered").trigger_clevertap_event_deliver_later
      shipment.in_transit_datetime = DateTime.current unless shipment.in_transit_datetime.present?
      shipment.delivered_on = (shipment.last_event_timestamp.presence || Time.current) unless shipment.delivered_on.present?
      designer_order=shipment.designer_order
      order=shipment.order
      if designer_order.present?
        is_delivered = shipment.service_type.try(:downcase)=="ReverseShipment".downcase && designer_order.state.try(:downcase)=="buyer_returned" && designer_order.shipment.shipment_state=="delivered"
        # OrderMailer.delay.shipment_notification_for_delivered(designer_order) if is_delivered && designer_order.present?
        SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "shipment_notification_for_delivered", {designer_order.class.to_s => designer_order.id } ) if is_delivered && designer_order.present? 
      end
      # Send an email to the customer
   
      unless shipment.rvp_shipment?
        if shipment.line_item_ids.present?
          ShipmentMailer.sidekiq_delay.mail_delivery_details_to_customer(shipment.id)
          if order.present? && order.tracking_number.to_s == shipment.number && order.billing_international?
            SidekiqDelayGenericJob.set({queue: 'low'}).perform_in(24.hours.from_now, order.class.to_s, order.id, "send_confirmation_sms_international", 'delivered')
          end
        end
        if order.present? && (shipment.designer_order.blank? || (!order.international? && order.all_shipments_delivered?))
          # OrderMailer.sidekiq_delay.send_feedback_form(order)
          SidekiqDelayGenericJob.perform_async("OrderMailer", nil, "send_feedback_form", {order.class.to_s => order.id } )
          SidekiqDelayGenericJob.set({queue: 'low'}).perform_in(24.hours.from_now, order.class.to_s, order.id, "send_confirmation_sms", 'delivered')
          value = SystemConstant.get('FEEDBACK_NOTIFICATION_PERCENT')
          if value['threshold'].to_i.zero? || value['threshold'].to_i == value['counter'].to_i
            # order.sidekiq_delay.send_feedback_notification
            SidekiqDelayGenericJob.perform_async(order.class.to_s, order.id, "send_feedback_notification")
            unless value['threshold'].to_i.zero?
              value['counter'] = 0
              SystemConstant.find_by_name('FEEDBACK_NOTIFICATION_PERCENT').update_column(:value, value.to_json)
            end
          else
            value['counter'] = value['counter'].to_i + 1
            SystemConstant.find_by_name('FEEDBACK_NOTIFICATION_PERCENT').update_column(:value, value.to_json)
          end
          order.increment!(:notification_count,1)
        end
        shipment.update_designer_order_state
        # shipment.sidekiq_delay.update_delivery_and_events
        SidekiqDelayGenericJob.perform_async(shipment.class.to_s, shipment.id, "update_delivery_and_events")
        # Send sms to customer
        shipment.send_shipment_msg if shipment.line_item_ids.present?
      end

      # Send app notification to customer
      d_o = shipment.designer_order
      # d_o.delay(priority: 4).android_delivered_notification if NOTIFICATIONS_ENABLE == 'true' && d_o.present?
      SidekiqDelayGenericJob.set({queue: 'high'}).perform_async(d_o.class.to_s, d_o.id, "android_delivered_notification") if NOTIFICATIONS_ENABLE == 'true' && d_o.present?
      Order.sidekiq_delay.call_gokwik_split_api(order.id) 
    end
  end
  

  def self.update_shipment_bill_csv(file, email)
    bill_no = {}
    not_updated_shipments = []
    file.each { |row|  row[5].present? ? (bill_no[row[3]] = [row[5].strip, row[6].try(:strip), row[7].try(:strip), row[0].try(:strip)]) : not_updated_shipments << [row[5], row[6], row[7], row[0], row[3]] }
    Shipment.select('id, shipping_bill_no, number, shipper_id, packer_id, invoicer_id, weight, order_id, automated, track, shipping_bill_date, port_code').where.not(order_id: nil).where(number:bill_no.keys).find_in_batches(batch_size:200) do | shipments |
      shipment_bill_no = []
      shipments.each do |shipment|
        if bill_no[shipment.number][0].present?
          begin
            shipment.shipping_bill_date = DateTime.parse(bill_no[shipment.number][1]) if bill_no[shipment.number][1].present?
            shipment.port_code = bill_no[shipment.number][2] if bill_no[shipment.number][2].present?
            shipment.shipping_bill_no = bill_no[shipment.number][0] if shipment.shipping_bill_no != bill_no[shipment.number][0]
            shipment_bill_no << shipment
          rescue
            not_updated_shipments << ( bill_no[shipment.number] << shipment.number )
            next
          end
        end
      end
      Shipment.import [:id,:shipping_bill_no,:number, :shipper_id,:packer_id, :invoicer_id, :weight, :order_id, :automated, :track, :shipping_bill_date, :port_code],shipment_bill_no, validate:false, on_duplicate_key_update:{conflict_target: [:id], columns:[:shipping_bill_no, :shipping_bill_date, :port_code]}
    end
    if not_updated_shipments.flatten.present?
      file = CSV.generate do |csv|
        csv << ['shipping_bill_no', 'shipping_bill_date', 'port_code', 'order_number', 'awb_number']
        not_updated_shipments.each{|data| csv << data }
      end
      email ={ 'to_email' => email,'from_email_with_name' => 'Mirraw.com <<EMAIL>>' }
      OrderMailer.report_mailer('shipping bill failed update report','Following shipments failed to update because of some format violation or missing shipping bill number.',email,{'shipping bill failed update report.csv' => file}).deliver
    end
  end

  def self.add_row(shipments,filepath)
    CSV.open(filepath, "a") do |csv|
      shipments.each do |shipment|
        invoice_no = ''
        if shipment.order_notification.present?
          shipment.order_notification.split(':').each do |k|
            invoice_no =k.match(/invoice_MIR[0-9a-zA-Z|\/|\-]*$/) if k.match(/invoice_MIR[0-9|\/]+/) != nil
            invoice_no = invoice_no.to_s.tr('invoice_','')
          end
        end 
        csv << [shipment.number, shipment.invoice_date, invoice_no, shipment.shipment_number, shipment.shipper_name, shipment.shipping_bill_no, shipment.shipping_bill_date, shipment.port_code]
      end
    end
  end

  def processing_abandoned_irn(shipment)
    generate_irn = GenerateIrnNumber.new(shipment.order,{},shipment,nil,nil)
    response = generate_irn.remove_generated_irn
    if response.present? && response[:success] == true
      shipment.update_columns(irn_number: "", gst_barcode: "")
      notes = "Accounts: Irn number has been cancelled for shipment ID - #{shipment.id}, cancelled-irn-number: #{response[:result][:Irn]}"
      current_account = Account.find_by_email(shipment.order.user.email)
      shipment.order.add_notes_without_callback(notes,'shipment',current_account)
      OrderMailer.report_mailer("IRN Number Cancelled","Irn Number - #{response[:result][:Irn]} has been cancelled for Order Number #{shipment.order.number}",{'to_email'=> '<EMAIL>','from_email_with_name'=>'<EMAIL>','cc_email'=> '<EMAIL>,<EMAIL>'},{}).deliver
    else
      OrderMailer.report_mailer("Error While Cancelling IRN Number","Failed to cancel IRN number due to below reason <br>#{response} for Order Number #{shipment.order.number} and Tracking Number is #{shipment.number}",{'to_email'=> '<EMAIL>','from_email_with_name'=>'<EMAIL>'},{}).deliver
    end
  end


  def processing_abandoned_credit_note(shipment)
    current_account = Account.find_by_email(shipment.order.user.email)
    items = shipment.order.get_courier_automation_related_details(shipment, shipment.shipper.name, current_account.id)
    product_details,item_price_total = shipment.package_details(shipment,items[:shipment_data]["item"])
    SidekiqDelayClassSpecificGenericJob.set(queue: 'critical').perform_async("ShipmentDelivery::CreditNoteInvoice", 
                                                                              "generate_credit_note_invoice",
                                                                              {invoice_items: product_details,
                                                                                order_number: shipment.order.number,
                                                                                shipment: shipment.number,
                                                                                items_price_total: shipment.invoice_data["fob_value"],
                                                                                skip_object_creation: true
                                                                              }
                                                                            )
  end
  

  def self.shipment_bill_csv(date=nil)
    shipment = '' 
    subject = ''
    content = ''
    filename = 'Shipment bill report' + '.csv'
    filepath = '/tmp/' + filename
    CSV.open(filepath, "wb") do |csv|
      csv << ['order_no','invoice_date','invoice_no','awb_number','shipper_name','shipping_billing_no', 'shipping_bill_date(yy-mm-dd)', 'port_code']
    end
    if date.present?
      subject = "Order Report of #{date}"
      content = "csv contain all Order of #{date}."
      Shipment.forward.joins(:order,:shipper).select("shipments.id, orders.number as number, shipments.number as shipment_number, orders.other_details -> 'invoice_date' as invoice_date, orders.order_notification as order_notification, shippers.name as shipper_name, shipments.shipping_bill_no, shipments.shipping_bill_date, shipments.port_code").
      where('designer_order_id is null').where('orders.state = ?','dispatched').where('orders.created_at::Date between ? and ?' ,date.beginning_of_month, date.end_of_month).where('lower(orders.country) <> ?','india').find_in_batches(batch_size:200) do |shipments|
        add_row(shipments,filepath)
      end
    else
      subject = 'Order with no shipment_bill_no report'
      content = 'csv contain all Orders with no shipping bill no.'
      Shipment.forward.joins(:order,:shipper).select("shipments.id, orders.number as number, shipments.number as shipment_number, orders.other_details -> 'invoice_date' as invoice_date, orders.order_notification as order_notification, shippers.name as shipper_name, shipments.shipping_bill_no, shipments.shipping_bill_date, shipments.port_code").
      where('designer_order_id is null').where('shipments.shipping_bill_no is  null or shipments.shipping_bill_no = ?','').where('orders.state = ?','dispatched').where('orders.created_at::Date >= ?' ,'01/04/2017'.to_date).where('lower(orders.country) <> ?','india').find_in_batches(batch_size:200) do |shipments|
        add_row(shipments,filepath)
      end
    end
    email ={ 'to_email' =>'Mirraw.com <<EMAIL>>','from_email_with_name' => 'Mirraw.com <<EMAIL>>' }
    OrderMailer.report_mailer(subject,content,email,{'shipping bill report.csv' => File.read(filepath)}).deliver
  end

  def send_notification_mail_for_customs_verification
    if (order = self.order)
      notes = "Custom Verification email sent to #{order.name} for shipment_id = #{self.id} and order_id = #{order.id}"
      # ShipmentMailer.sidekiq_delay.send_mail_for_customs_verification(self)
      SidekiqDelayGenericJob.perform_async("ShipmentMailer", nil, "send_mail_for_customs_verification", {self.class.to_s => self.id } )
      order.add_notes_without_callback(notes,'shipment')
    end
  end

  def update_delivery_and_events
    all_klass = self.designer_order_id.present? ? [self.designer_order] : (self.bulk_designer_orders.present? ? self.bulk_designer_orders : [self.order])
    all_klass.each do |klass|
      dns   = klass.delivery_nps_info
      dns.update_attributes(actual_delivery_date: (self.delivered_on.presence || Date.today)) if dns.present?
      delivery_date = (self.delivered_on || Date.today).to_date
      promised_date = (dns.present? ? dns.promised_delivery_date : (klass.class.name == 'DesignerOrder' ? (klass.confirmed_at || klass.created_at).advance(days: SHIPPING_TIME) : (klass.confirmed_at || klass.created_at).advance(days: klass.shipping_time))).to_date
      events= if (delivery_date.mjd - self.created_at.to_date.mjd).between?(1,5) && delivery_date <= promised_date
                {'DeliveredByMinimumPromiseDate'=>['order_quality'],'DeliveredInFourDay'=>['delivery'],'DeliveredOnTime'=>['delivery']}
              elsif (delivery_date.mjd - self.created_at.to_date.mjd) == 6 && delivery_date <= promised_date
                {'DeliveredOnTime'=>['delivery'],'DeliveredInFiveDay'=>['order_quality']}
              elsif delivery_date <= promised_date
                {'DeliveredOnTime'=>['delivery'],'DeliveredByMinimumPromiseDate'=>['order_quality']}
              else
                {'DeadlineMissed'=>['delivery',(delivery_date.mjd-promised_date.mjd).abs]}
              end
      events.each do |key,value|
        klass.send(value[0]+'_event!',:add,key,value[1])
        klass.order.send(value[0]+'_event!',:add,key,value[1])if key == 'DeadlineMissed' && klass.class.name == 'DesignerOrder'
      end
    end
  end

  def rvp_shipment?
    self.service_type == 'ReverseShipment'
  end

  def add_label(label)
    file = StringIO.new(label) #mimic a real upload file

    original_filename1 = self.id.to_s + (self.rvp_shipment? ? '_return_label.pdf' : '_label.pdf')
    content_type1      = "application/pdf"
    
    metaclass = class << file; self; end
    metaclass.class_eval do
      define_method(:original_filename) { original_filename1 }
      define_method(:content_type) { content_type1 }
    end
    if self.rvp_shipment?
      self.return_label = file
    else
      self.label = file
    end
    self.save!
  end

  def add_invoice(invoice)
    file = StringIO.new(invoice) #mimic a real upload file

    original_filename1 = self.id.to_s + '_invoice.pdf'
    content_type1      = "application/pdf"
    
    metaclass = class << file; self; end
    metaclass.class_eval do
      define_method(:original_filename) { original_filename1 }
      define_method(:content_type) { content_type1 }
    end

    self.invoice = file
  end


  # def self.fedex_api(type)
  #   credentials = YAML.load_file("#{Rails.root}/config/fedex.yml")[type]
  #   fedex       = Fedex::Shipment.new(credentials.symbolize_keys!)
  # end

  # input params - none
  #
  # Gets tracking info for fedex shipment
  # Returns array / nil
  def fedex_track
    fedex = FedexApi.new() 
    begin
      results = fedex.tracking_api_call(self.number.gsub(' ',''))
      return results[:output][:completeTrackResults].first[:trackResults].first
    rescue => e
      self.track_error = e.message
    end
  end

  def xindus_track
    xindus_api = XindusApi.new
    begin
      result = xindus_api.tracking_api(self.number)
    rescue => exception
      self.track_error = exception.message
    end
  end
  
  def xpressbees_ups_track
    xpressbees_ups_api = UpsApi.new
    begin
      result = xpressbees_ups_api.tracking_api(self.number)
    rescue => exception
      self.track_error = exception.message
    end
  end
  
  def ups_track(awbno)
    order = Order.find_by_id(self.order_id)
    status_code, response_body = UPSApi.new(order).track(awbno)
    return status_code, response_body
  end

  def get_ups_shipment_state(tracking_info)
    final_status = ''
    tracking_body = JSON.parse(tracking_info)
    if tracking_body['trackResponse']['shipment'][0]['package'].present?
      activity = tracking_body['trackResponse']['shipment'][0]['package'][0]['activity'][0]
      shipment_state = activity['status']['type']
      self.last_event = activity['status']['description']
      self.save
      case shipment_state.downcase.to_sym
      when :d
        final_status = :delivered
        self.delivered_on = activity['date']
        self.save
      when :i
        final_status = :in_transit
      when :m, :mv
        # Do nothing
      when :p
        final_status = :out_for_pickup
      when :x
        final_status = :delivery_exception
      when :rs
        final_status = :rto
      when :na
        # Do nothing
      when :o
        final_status = :out_for_delivery
      else
        # Do nothing
      end
      return final_status
    else
      ExceptionNotify.sidekiq_delay.notify_exceptions('UPS Shipment Debug', 'UPS Tracking Failed',{ error: tracking_body })
    end
    
      # D Delivered
      # I In Transit
      # M Billing Information Received
      # MV Billing Information Voided
      # P Pickup
      # X Exception
      # RS Returned to Shipper
      # DO Delivered Origin CFS (Freight Only)
      # DD Delivered Destination CFS (Freight Only)
      # W Warehousing (Freight Only)
      # NA Not Available
      # O Out for Delivery
  end

  def dhl_track(awbno)
    return DhlApi.new.track(awbno)
  end

  def update_dhl_charges(request)
    response = DhlApi.new.quote(request)
    if response["Details"].present?
      shipping_charge = response["Details"]["ShippingCharge"]
      self.update_column(:shipment_charges, shipping_charge.is_a?(Array) ? shipping_charge[0].to_f.round(2) : shipping_charge.to_f.round(2))
      if shipping_charge.is_a?(Array)
        ExceptionNotify.sidekiq_delay.notify_exceptions('ShippingCharge Error','ShippingCharge Response is Array',{ order_id:self.order_id, shipping_charge:shipping_charge})
      end
      # For tax use follow
      # response["Details"]["TotalTaxAmount"].to_f
    end
  end

  # input params - none
  #
  # Returns boolean / nil
  def update_state(shipper_name,tracking_info = nil)
    new_shipment_state = nil
    case shipper_name
    when 'Xpreesbees_ups'
      tracking_info = self.xpreesbees_ups_track if tracking_info.nil?
      new_shipment_state = self.get_xpressbees_ups_shipment_state(tracking_info) if tracking_info.present?
    when 'Xindus'  
      tracking_info = self.xindus_track if tracking_info.nil?
      new_shipment_state = self.get_xindus_shipment_state(tracking_info) if tracking_info['data'].present?
    when 'UPS'
      status_code , tracking_info = self.ups_track(self.number) if tracking_info.nil?
      new_shipment_state = self.get_ups_shipment_state(tracking_info) if tracking_info.present? && status_code == 200
    when 'FEDEX', 'FEDEX-MIRRAW'
      tracking_info = self.fedex_track if tracking_info.nil?
      new_shipment_state = self.get_fedex_shipment_state(tracking_info) if tracking_info.present?
    when 'DHL'
      if created_at > 4.months.ago
        tracking_info = self.dhl_track(self.number) if tracking_info.nil?
        new_shipment_state = self.get_dhl_shipment_state(tracking_info) if tracking_info.present?
      end
    when 'DELHIVERY'
      tracking_info = Shipment.delhivery_track_api(self.number) if tracking_info.nil?
      new_shipment_state = self.get_delhivery_shipment_state(tracking_info) if tracking_info.present?
    when 'DHL ECOM'
      new_shipment_state = self.get_dhlecom_shipment_state(tracking_info) if tracking_info.present?
    when 'RAPID DELIVERY'
      new_shipment_state = get_rapid_delivery_shipment_state(tracking_info) if tracking_info.present?
    when 'Xpress Bees'
      new_shipment_state = get_xpressbees_shipment_state(tracking_info) if tracking_info.present?
    else
      #Do Nothing
    end
    self.update_shipment_state(new_shipment_state)
  end

  def self.track_gati_awb(awb_numbers)
    awb_numbers.each do |number|
      url = Mirraw::Application.config.gati_tracking_url
      tracking_code = Mirraw::Application.config.gati_tracking_code
      response = GatikweApi::Gati.track_gati_shipments(url, number, tracking_code)
    end
  end

  # input params - new_shipment_state - symbol
  #
  # Attempts shipment state transition if new_shipment_state <> self.shipment_state
  # Returns boolean
  def update_shipment_state(new_shipment_state)
    if new_shipment_state.present?
      begin
        if new_shipment_state != shipment_state.to_sym
          case new_shipment_state
          when :in_transit
            self.out_for_pickup! if self.rvp_shipment? && self.processing?
            self.shipment_dispatched!
          when :delivered
            self.update_shipment_state(:in_transit) if self.rvp_shipment? && (self.processing? || self.out_for_pickup?)
            self.shipment_delivered!
          when :rto
            self.shipment_didnt_reach_buyer! if self.can_shipment_didnt_reach_buyer?
          when :out_for_delivery
            self.invoice_data['out_for_delivery_attempt_count']  = self.invoice_data['out_for_delivery_attempt_count'].to_i  + 1
            self.update_shipment_state(:in_transit) if self.rvp_shipment? && (self.processing? || self.out_for_pickup?)
            self.shipment_out_for_delivery!
          when :delivery_exception
            self.shipment_delivery_exception!
          when :lost
            self.shipment_lost!
          when :out_for_pickup
            self.invoice_data['out_for_pickup_attempt_count']  = self.invoice_data['out_for_pickup_attempt_count'].to_i  + 1
            self.out_for_pickup!
          when :pickup_exception
            self.shipment_pickup_exception!
          when :rto_delivered
            self.shipment_didnt_reach_buyer! unless self.rto?
            self.rto_delivered!
          when :shipment_lost
            self.shipment_lost!
          else
            # Do Nothing
          end
          self.save!
        else
          send_shipment_msg if ['out_for_delivery', 'delivery_exception'].include?(shipment_state) && self.last_event != self.previous_event
          if ['out_for_delivery', 'out_for_pickup'].include?(shipment_state)
            self.invoice_data["#{shipment_state}_attempt_count"] = self.invoice_data["#{shipment_state}_attempt_count"].to_i + 1
            self.save!
          end
        end
      rescue => e
        ExceptionNotify.sidekiq_delay.notify_exceptions('Shipment Transition Error', 'Shipment Transition Error', { error: e.inspect })
      end
    end
  end

  # input params none
  # Track dhl shipment and update status
  # Tracking event code =>AR=Arrived at Delivery Facility in
  # AF=Arrived at Sort Facility
  # CA=Delivery attempted - consignee premises closed
  # CC=Awaiting collection by recipient as requested
  # DD=Delivered - Signed for by
  # DF=Departed Facility in
  # FD=Forwarded for delivery
  # HP=Shipment held - Available upon receipt of payment
  # MC=Shipment arrived at incorrect facility. Sent to correct destination
  # MS=Shipment arrived at incorrect facility. Sent to correct destination
  # OK=Delivered - Signed for by
  # SA=Shipment Accepted
  # SC=The requested service has changed; for assistance please contact DHL
  # BA=Address information needed; contact DHL
  # BN=Broker notified to arrange for clearance
  # BR=Delivered to broker as requested
  # CD=Clearance delay
  # CM=Recipient moved
  # CR=Clearance processing complete at
  # CS=Please contact DHL
  # DS=Shipper contacted
  # IC=Processed for clearance at
  # MD=Scheduled for delivery
  # NH=Delivery attempted; recipient not home
  # OH=Shipment on hold
  # PD=Partial delivery
  # PU=Shipment picked up
  # RD=Recipient refused delivery
  # RT=Returned to shipper
  # SS=Please contact DHL
  # TP=Delivery arranged no details expected
  # TR=Transferred through
  # UD=Clearance delay
  # WC=With delivery courier
  # Returns symbol or string
  def get_dhl_shipment_state(tracking_info)
    if tracking_info["AWBInfo"].present?
      shipment = tracking_info["AWBInfo"]
      status = shipment["EventCode"].to_s.downcase.to_sym
      self.last_event = shipment["Description"]
      case status
      when :ar , :af , :df , :fd , :mc , :ms , :bn , :cd , :cr , :ic , :pu , :ud , :tr , :tp , :oh , :md
        return :in_transit
      when :ok , :dd , :br
        self.delivered_on = Time.zone.now unless self.shipment_state == 'delivered'
        return :delivered
      when :rt
        return :rto
      when :wc
        return :out_for_delivery
      when :ba , :ca , :cc , :hp , :sc , :cm , :cm , :cs , :nh , :pd , :rd , :ss
        return :delivery_exception
      end
    elsif tracking_info["Error"].present?
      self.track_error = tracking_info["Error"]
    end
        self.save!
      return nil
  end


  def get_xindus_shipment_state(tracking_info)
    status = tracking_info['data'].first['status'].downcase.to_sym
    self.last_event = tracking_info['data'].first['description'].downcase
    case status
    when :shipment_out_for_pickup,
      :shipment_pickup_successful,
      :shipment_received_at_pickup_location,
      :shipment_accepted_at_pickup_location,
      :shipment_connected_to_hub,
      :shipment_recieved_at_hub,
      :shipment_accepted_at_hub,
      :shipment_at_origin_customs,
      :shipment_cleared_at_origin_customs,
      :shipment_on_hold_at_origin_customs,
      :shipment_connected_to_gateway_country,
      :shipment_arrived_at_destination_country,
      :shipment_cleared_at_destination_customs,
      :shipment_on_hold_at_destination_customs,
      :shipment_intransit_to_last_mile_hub,
      :shipment_recieved_at_last_mile_hub,
      :shipment_intransit,
      :shipment_ready_for_pickup,
      :shipment_docs_uploaded,
      :shipment_ready_for_drop,
      :shipment_received_at_pickup_location,
      :shipment_lastmile_label_generated,
      :shipment_onhold_at_hub,
      :shipment_released,
      :shipment_outscanned,
      :shipment_injected,
      :intransit_001,
      :intransit_002,
      :intransit_003,
      :intransit_007,
      :intransit_008
      return :in_transit
    when :shipment_out_for_delivery,
      :shipment_ready_for_customer_collection,
      :shipment_delivery_delayed,
      :shipment_delivery_attempted,
      :delivered_awaiting_confirmation
      return :out_for_delivery
    when :shipment_returning_to_sender ,
         :shipment_returned_to_sender,
         :shipment_marked_as_return,
         :shipment_returned_to_us_return_facility,
         :shipment_returned
      return :rto
    when :shipment_delivered
      self.delivered_on = Time.zone.now unless self.shipment_state == 'delivered'
      return :delivered
    when :shipment_lost_or_untraceable,
         :shipment_investigation_opened,
         :shipment_discarded,
         :shipment_forced_lost,
         :shipment_claim_closed,
         :shipment_claim_processed
      return :lost 
    when :shipment_cancelled
      return :cancelled
    when :shipment_created, :shipment_payment_completed
      return :processing
    when :shipment_delivery_address_changed
      return :delivery_exception
    when :shipment_pickup_reattempt , :shipment_pickup_failed, :shipment_exception
      return :pickup_exception
    when :shipment_lm_label_deleted, :shipment_cancellation_requested
      return :processing_abandoned
    end
  end

  def get_dhlecom_shipment_state(tracking_info)
    new_shipment_state = nil
    status = tracking_info['events'].sort_by{|event| event['dateTime']}.last['status']
    case status
    when '77014','77015','77032','77176'
      new_shipment_state = :in_transit
    when '77026','77205','77040'
      new_shipment_state = :out_for_delivery
    when '77093'
      self.delivered_on = Time.zone.now unless self.shipment_state == 'delivered'
      new_shipment_state = :delivered
    when '77106'
      new_shipment_state = :delivery_exception
    end
    self.update_shipment_state(new_shipment_state)
  end

  def get_xpressbees_shipment_state(tracking_info)
    new_shipment_state = nil
    status = tracking_info['StatusCode']
    self.last_event_timestamp = DateTime.parse(tracking_info['StatusDate'])
    self.last_event = tracking_info['Status']
    case status
    when 'DRC', 'PUC', 'PND', 'OFP'
      new_shipment_state = :processing
    when 'PUD', 'PKD', 'IT'
      new_shipment_state = :in_transit 
      self.in_transit_datetime = DateTime.parse(tracking_info['StatusDate']) if tracking_info['StatusDate'].present?
    when 'DLVD'
      self.delivered_on = DateTime.parse(tracking_info['DeliveryDate']) if self.shipment_state != 'delivered' && tracking_info['DeliveryDate'].present?
      new_shipment_state = :delivered
    when 'LOST'
      new_shipment_state = :lost
    when 'RTO', 'RTD', 'RTU'
      new_shipment_state = :rto
    end
    self.update_shipment_state(new_shipment_state)
  end

  def get_rapid_delivery_shipment_state(tracking_info)
    new_shipment_state = nil
    latest_track = tracking_info['scans'].try(:last)
    status = latest_track.try(:[],'status').try(:downcase)
    self.last_event_timestamp = latest_track.try(:[], 'timestamp')
    self.last_event = latest_track.try(:[], 'remarks')
    case status
    when 'picked', 'in transit', 'pending'
      new_shipment_state = :in_transit
    when 'dispatched'
      new_shipment_state = :out_for_delivery
    when 'delivered'
      new_shipment_state = :delivered
      self.delivered_on = latest_track.try(:[], 'timestamp') 
    when 'rto'
      new_shipment_state = :rto
    end
    is_not_pickedup = self.in_transit_datetime.blank?
    self.update_shipment_state(new_shipment_state)
    if is_not_pickedup && new_shipment_state == :in_transit && latest_track.try(:[], 'timestamp').present?
      designer_order_ids = self.order_id.blank? ? self.bulk_designer_orders.pluck(:id) : self.designer_order_id
      DesignerOrder.where(id: designer_order_ids).each do |des_o|
        des_o.add_notes_without_callback("Pickedup by Courier from Vendor On #{DateTime.parse(latest_track.try(:[], 'timestamp'))}", 'Courier Pickup')
      end
      LineItem.bulk_add_into_scan('DesignerOrder', designer_order_ids, 'Pickedup by Courier from Vendor', nil, nil, DateTime.parse(latest_track.try(:[], 'timestamp'))) 
    end
  end

  def cancel_clickpost_shipment
    click_post = ClickPostAutomation.new(order)
    click_post.cancel_shipment(self) unless self.canceled?
  end

  def clickpost_serviceable?
    self.service_type == 'ClickpostForward'
  end

  def update_clickpost_shipment_state(shipment_status)
    new_shipment_state = nil
    self.last_event_timestamp = DateTime.parse(shipment_status[:timestamp]) rescue Time.current
    self.last_event = shipment_status[:remark]
    case shipment_status[:clickpost_status_code]
    when 25
      if self.rvp_shipment?
        self.out_for_pickup_on = self.last_event_timestamp
        new_shipment_state = :out_for_pickup
      end
    when 4
      new_shipment_state = move_to_in_transit if self.rvp_shipment? || self.shipper_id != Shipper::ALL_SHIPPERS['SHIP DELIGHT']
    when 5
      new_shipment_state = move_to_in_transit
    when 8
      self.delivered_on = self.last_event_timestamp
      new_shipment_state = :delivered
    when 6
      self.out_for_delivery_on = self.last_event_timestamp
      new_shipment_state = :out_for_delivery
    when 17
      if self.invoice_data[:failed_delivery_count] == 3
        new_shipment_state = :delivery_exception
      else
        self.invoice_data[:failed_delivery_count] = self.invoice_data[:failed_delivery_count].to_i + 1
        self.invoice_data[:failed_delivery_reason] = shipment_status[:additional].try(:[],:ndr_status_description)
        self.save!
      end
    when 16
      new_shipment_state = :shipment_lost
    when 10, 7, 19, 9
      new_shipment_state = :delivery_exception
    when 3
      if self.invoice_data[:failed_pickup_count] == 3
        new_shipment_state = :pickup_exception
      else
        self.invoice_data[:failed_pickup_count] = self.invoice_data[:failed_pickup_count].to_i + 1
        self.save!
      end
    when 11, 12, 13, 21
      new_shipment_state = :rto
    when 14
        new_shipment_state = :rto_delivered
    end
    self.update_shipment_state(new_shipment_state)
  end

  def get_shadowfax_shipment_state(shipment_status)
    new_shipment_state = nil
    self.last_event_timestamp = DateTime.parse(shipment_status[:updated_at])
    self.last_event = shipment_status[:remark]
    case shipment_status[:status]
    when 'recd_at_rev_hub','sent_to_fwd','recd_at_fwd_hub'
      new_shipment_state = :in_transit
    when 'ofd'
      new_shipment_state = :out_for_delivery
    when 'lost'
      new_shipment_state = :lost
    when  'cancelled_by_customer','rts','rts_d'
      new_shipment_state = :rto
    when 'delivered'
      new_shipment_state = :delivered
      self.delivered_on = shipment_status[:updated_at]
    when 'cid','nc','na'
      new_shipment_state = :delivery_exception
    end
    self.update_shipment_state(new_shipment_state)
  end

  def get_ship_delight_shipment_state(shipment)
    new_shipment_state = nil
    self.last_event_timestamp = shipment["updated_date"]
    self.last_event = shipment['remarks']
    case shipment['status_code']
    when '108','664'
      new_shipment_state = :in_transit
      self.in_transit_datetime = shipment['updated_date']
    when '954'
      new_shipment_state = :out_for_delivery
    when '913'
      new_shipment_state = :lost
    when '925','506','285'
      new_shipment_state = :rto
    when '753'
      new_shipment_state = :delivered
      self.delivered_on = shipment["updated_date"]
    when '565','379','526','784','458','859','521','478','528','589','625','659','796','856','874','153','245','326','383','362','304','992'
      new_shipment_state = :delivery_exception
    end
    self.update_shipment_state(new_shipment_state)
  end

  # input params none
  # Track delhivery shipment and update status
  # 
  # Returns symbol or string
  def get_delhivery_shipment_state(tracking_info)
    if tracking_info["Error"].blank?
      shipment = tracking_info["ShipmentData"].first['Shipment']
      status = shipment["Status"]["Status"].gsub(' ','_').downcase
      self.last_event_timestamp = DateTime.iso8601(shipment["Status"]["StatusDateTime"])
      self.last_event = shipment["Status"]["Instructions"]
      case status
      when 'in_transit'
        self.in_transit_datetime = DateTime.iso8601(shipment["Scans"].last["ScanDetail"]["ScanDateTime"])
        return :in_transit if shipment["Scans"].count >= 3 
      when 'delivered'
        self.delivered_to = shipment["Status"]["ReceivedBy"]
        self.delivered_on = DateTime.iso8601(shipment["Scans"].last["ScanDetail"]["ScanDateTime"])
        return :delivered
      when 'rto', 'returned'
        return :rto
      when 'dispatched'
        return :out_for_delivery
      when 'pending'
        return :delivery_exception
      end
    else
      self.track_error = tracking_info["Error"]
    end
    return nil
  end

  # input params number - string / arrays
  # makes get request to delhivery tracking api
  #
  # Returns hash
  def self.delhivery_track_api(numbers)
    numbers = Array(numbers).flatten.collect{|number| number.to_s}
    # verbose=1 - gives scan details
    api = Mirraw::Application.config.delhivery_baseurl + '/api/packages/json/?token={token}&waybill={waybill}&verbose=1'
    api_params = api.sub('{token}', Mirraw::Application.config.delhivery_cod_token).sub('{waybill}', numbers.join(','))
    api_params_encoded = URI.encode(api_params)
    res = HTTParty.get(api_params_encoded)
    if res.code == 200 && res.headers['content-type'].downcase.match('json')
      res_json = JSON.parse(res.body)
    else
      {'Error' => true}
    end
  end

  # input params - Hash
  #
  # update fedex specific shipment info (including return shipment info if present)
  # Returns symbol / nil
  def get_fedex_shipment_state(tracking_info)
    final_status = ''
    if tracking_info[:estimatedDeliveryTimeWindow][:window].present?
      self.estimated_delivery_timestamp = DateTime.iso8601(tracking_info[:estimatedDeliveryTimeWindow][:window][:ends])
    end
    self.previous_event  = self.last_event.to_s
    if (event = tracking_info[:scanEvents].first).present?
      self.last_event = event[:eventDescription]
      status          = event[:eventType]
      self.last_event_timestamp = event[:date]
      case status.downcase.to_sym
      when :pu, :it, :ar, :lo, :dp
        final_status = :in_transit
      when :od
        # out for delivery
        final_status = :out_for_delivery
      when :dl
        # delivered
        final_status      = :delivered
        self.delivered_to = tracking_info[:deliveryDetails][:signedByName]  if tracking_info[:deliveryDetails].present?
        self.delivered_on = event[:date]
      when :rs
        # return to shipper
        final_status      = :rto
      when :se
        # Do Nothing
      when :de
        # delivery exception
        final_status = :delivery_exception
        case event[:exceptionCode]
        when '03', '03A', '03B', '03C', '03D'
          self.last_event = "Incorrect Address"
        when '07', '07A', '07B'
          self.last_event = "Refused by Recipient"
        when '08', '08A', '08C', '08D', '15'
          self.last_event = "Customer not Available"
        end
      else
        # Do Nothing
      end
      update_return_last_event if return_tracking_no.present? && final_status == 'rto'  
      return final_status
    else
      # Do Nothing
    end
  end

  def check_order
    order = self.order
    if self.line_item_ids.present? && designer_order_id.blank? && order_id.present?
      unless shipment_charges.present?
        ship_weight = order.get_normalized_weight(self.weight)
        sc = ShipmentCharge.where(weight: ship_weight ,shipper_id: shipper.id,country_id: Country.find_by_namei(order.country.try(:downcase)).try(:[], :id)).pluck(:price).first
        update_column(:shipment_charges,sc) if sc.present?
      end
      if order.check_if_all_items_dispatched? && self.order.can_package_shipped?
        order.package_shipped
        order.save!
        if (dns = order.delivery_nps_info).present? && dns.promised_delivery_date.present?
          line_items = order.line_items.sane_items.where('designer_orders.state not in (?)',['canceled','vendor_canceled'])
          addon_prod_values = order.addon_delivery_time
          addon_prod_time = addon_prod_values.max_by(&:first)
          advance_days    = order.calculate_advance_days(line_items,order.shipping_time,(addon_prod_time.present? ? addon_prod_time.last.to_i : 0))
          dns.revised_delivery_date = advance_days != 0 ? dns.promised_delivery_date.advance(days: advance_days) : nil
          dns.save if dns.revised_delivery_date.present?
        end
      end
      LineItem.bulk_add_into_scan('LineItem', line_item_ids, 'Shipment Created', packer_id)
      if (shipment_bucket = self.line_items.pluck(:shipment_bucket_id).compact).count == 1 && (shipment_bucket = ShipmentBucket.where(id: shipment_bucket).first).present?
        shipment_bucket.update_attributes(shipment_id: self.id)
      end
    end
    if order.present? && (dom_sor_dos = order.get_domestic_sor_designer_orders).present? && !dom_sor_dos.any? {|designer_order| ['pickedup', 'dispatched', 'completed'].exclude? designer_order.state}
      order.remove_rack_quantity
    end
  end

  handle_asynchronously :check_order

  def generate_labels(label, return_label, shipment)
    shipment.add_label(label.image)
    shipment.add_return_label(return_label.image) if return_label.present?
  end


  def add_return_label(return_label_image)
    file = StringIO.new(return_label_image) #mimic a real upload file

    original_filename1 = id.to_s + '_return_label.pdf'
    content_type1      = "application/pdf"
    
    metaclass = class << file; self; end
    metaclass.class_eval do
      define_method(:original_filename) { original_filename1 }
      define_method(:content_type) { content_type1 }
    end

    self.return_label = file
    self.save
  end

  def add_tofrom_label(pdf_content)
    file = StringIO.new(pdf_content)

    original_filename1 = id.to_s + '_return_label.pdf'
    content_type1      = "application/pdf"
    
    metaclass = class << file; self; end
    metaclass.class_eval do
      define_method(:original_filename) { original_filename1 }
      define_method(:content_type) { content_type1 }
    end

    self.tofrom_label = file
    self.save
  end

  def send_shipment_msg
    if (designer_order.present? || self.try(:return_designer_order).present?) && !order.try(:international?) && (Rails.env.production? || Rails.env.admin?)
      template = nil
      sms_count = 0
      shipper_contact_number = self.shipper.contact_number
      shipper_contact_number += ' / ' + self.shipper.alt_contact_number if self.shipper.alt_contact_number.present?
      case shipment_state.to_sym
      when :in_transit
        template = "Your Shipment Id #{designer_order_id} with Tracking No. #{number} has been Shipped. For queries, contact #{self.shipper.name} on #{shipper_contact_number}. Thanks, Mirraw.com!"
        sms_count = self.in_transit_sms_count + 1
        self.in_transit_sms_count = sms_count
      when :out_for_delivery
        template = "Your Shipment Id #{designer_order_id} with Tracking No. #{number} and Order No. #{order.number} is Out For Delivery. For queries, contact #{self.shipper.name} on #{shipper_contact_number}. Thanks, Mirraw.com"
        sms_count = self.out_for_delivery_sms_count + 1
        self.out_for_delivery_sms_count = sms_count
      when :rto
        #template = "Hi, we tried delivering your Shipment Id #{designer_order_id} on #{last_event_timestamp}  but were unable to deliver due to #{last_event}. Hence, your shipment is being returned back to us. For queries, contact #{self.shipper.name} on #{shipper_contact_number}. Thanks, Mirraw.com!" if last_event_timestamp.present?
        #sms_count = self.rto_sms_count + 1
        #self.rto_sms_count = sms_count
      when :delivery_exception
        if self.shipper.name.downcase != 'Delhivery'
          template = "Hi, we tried delivering your Shipment Id #{designer_order_id} with tracking No. #{number} on #{last_event_timestamp.strftime('%d/%m/%y %I:%m %P')} but were unable to deliver due to #{last_event}. For queries, contact #{self.shipper.name} on #{shipper_contact_number}. Thanks, Mirraw.com!" if last_event_timestamp.present?
          sms_count = self.delivery_exception_sms_count + 1
          self.delivery_exception_sms_count = sms_count
        end
      when :out_for_pickup
        product_names = self.return_designer_order.line_items.collect(&:design).compact.flatten.collect(&:title).each{|name| name.truncate(25)}.join(', ')
        template = "The pickup for your order #{order.number} is scheduled for today. Please keep following items: #{product_names} ready to be picked up in its original condition. If you have any queries, please contact customer care."
      when :delivered
        template = "Your Shipment Id #{designer_order_id} with Tracking No. #{number} has been successfully delivered. Thanks, Mirraw.com"
        sms_count = self.delivered_sms_count + 1
        self.delivered_sms_count = sms_count
      end

      phone = Order.get_mobile_num(order.phone)
      if template.present? && phone && phone != 0 && phone.length == 12 && sms_count <= MAX_SMS_EVENT
        SmsNotification::NotificationService.notify_later(phone, template)
        self.save! unless shipment_state == 'out_for_pickup'
      end

    end
  end

  def update_return_last_event
    fedex = FedexApi.new()
    begin
       shipment_result = fedex.tracking_api_call(self.return_number.gsub(' ',''))
       current_msg =  shipment_result[:output][:completeTrackResults].first[:trackResults].first[:scanEvents].first[:eventDescription]
    rescue => exception
      self.track_error = exception.message
      self.save!
    end
    update_attributes(:return_last_event => current_msg) if current_msg != return_last_event    
  end

  def update_designer_order_state
    all_dos = (designer_order.present? ? [self.designer_order] : self.bulk_designer_orders)
    all_dos = DesignerOrder.where(id: self.line_items.collect(&:designer_order_id).flatten.compact.uniq) if all_dos.blank? && self.order.present? && self.designer_order_id.blank? && self.order.international? && self.order.cod?
    if all_dos.present?
      all_dos.each do |dos|
        dos.skip_before_after_filter = true
        unless dos.dispatched?
          dos.add_notes_without_callback('Moved to dispatched state', 'dispatch')
          dos.shipment_update = 'dispatched'
          dos.got_awb
        end

        if !dos.completed? && ((shipment_state == 'delivered' && !dos.order.cod?) || (dos.order.cod? && shipment_state == 'delivered' && payment_state == 'paid'))
          dos.shipment_update = 'completed'
          dos.save unless dos.shipment_delivered_partner
        end
      end
    end
  end

  def self.payments_update(filename)
    base_path = 'https://s3-ap-southeast-1.amazonaws.com/'
    full_path = base_path + Shipment.mirraw_bucket + '/' + filename

    open(full_path) do |f|
      CSV.new(f, headers: :first_row, header_converters: :symbol).each_slice(250) do |lines|
        awb_details = Hash.new
        awb_nos = Array.new
        lines.each do |line|
          awb_no = line[:airwaybill_nbr]
          awb_nos << awb_no
          awb_details[awb_no] = Hash.new
          awb_details[awb_no][:amount] = line[:amount]
          awb_details[awb_no][:utr] = StringModify.string_utf8_clean_without_space line[:utr_no]
        end
        shipments = []
        Shipment.where(:number => awb_nos).preload(:bulk_designer_orders, :order, :designer_order).each do |shipment|
          awb_detail = awb_details[shipment.number]
          if shipment.shipment_type == 'COD' && shipment.price.present?
            price_validation = false
            if shipment.order.international?
              price = (shipment.invoice_data['cod_value'].presence || shipment.invoice_data['fob_value']).to_i
              price_validation = (price == awb_detail[:amount].to_i || (price - awb_detail[:amount].to_i).abs <= 2)
              if !price_validation && shipment.order.order_notification['paypal_rate'].present?
                price = (shipment.price / shipment.order.order_notification['paypal_rate'].to_f).to_i
                price_validation = (price == awb_detail[:amount].to_i || (price - awb_detail[:amount].to_i).abs <= 2)
              end
            else
              price_validation = shipment.price == awb_detail[:amount].to_i || ((shipment.price - awb_detail[:amount].to_i) < 51)
            end

            if price_validation
              shipment.payment_state = 'paid'
              shipment.shipment_state = 'delivered'
              shipment.utr_no = awb_detail[:utr]
              shipment.reconciliation_date = DateTime.current
              shipments << shipment
              shipment.update_designer_order_state
            end
          end
        end
        Shipment.import shipments, validate: false, on_duplicate_key_update: {conflict_target: [:id], columns: [:payment_state, :shipment_state, :utr_no, :reconciliation_date]}
      end
    end
  end

  def self.perform_courier_reconciliation(filename,original_filename,uploader_email)
    base_path = "https://s3-#{ENV['AWS_REGION']}.amazonaws.com/"
    full_path = base_path + ENV['S3_BUCKET'] + '/' + filename
    awb_details = Hash.new
    awb_nos = Array.new

    dir = "/tmp/reconciliation/dispute_file/"
    FileUtils.mkdir_p(dir) unless File.directory?(dir)
    dispute_file_path = dir + SecureRandom.hex(6)+"disputefile_for_#{original_filename}"

    CSV.open(dispute_file_path, "w+") do |csv|
      csv << ["AWB", "Order Number","system_weight", "weight_charged_by_vendor","Already Paid?","Paid Date","dispute_msg"]
    end

    CSV.new(open(full_path), {:headers => true, :header_converters => :symbol}).each do |line|
      awb_no = line[:airwaybill_nbr]
      awb_nos << awb_no
      awb_details[awb_no] = Hash.new
      awb_details[awb_no][:amount] = line[:amount]
      awb_details[awb_no][:date] = line[:date] # date when paid to partner
      awb_details[awb_no][:shipment_weight] = line[:shipment_weight] #weight charged by courier partner
    end

    shipments = Shipment.where(:number => awb_nos).preload(:order,designer_order: [ line_items: [design: :categories]],line_items: [design: :categories])
    shipments_number_array = shipments.map{|shipment| shipment.number}
    not_found_shipments_numbers = awb_nos - shipments_number_array

    shipments.each do |shipment|
      awb_detail = awb_details[shipment.number]
      check_shipment_data_for_courier_reconciliation(shipment,awb_detail,dispute_file_path)
    end

    CSV.open(dispute_file_path, "a+") do |csv|
      csv << ["Shipments not found"]
      csv << not_found_shipments_numbers
    end

    UploadReport.push_csv_aws3("Dispute_File",dispute_file_path,uploader_email)
    File.delete(dispute_file_path) if (File.exist?(dispute_file_path))
  end

  def self.shadowfax_create_domestic(designer_order)
    shipper= Shipper.find_by_name('Shadowfax')
    awb_numbers = AwbNumber.number_available(shipper.id,true)
    awb_numbers_count = awb_numbers.count
    if (awb_numbers_count <= 5000 && (awb_numbers_count%1000) == 0) ||  (awb_numbers_count < 1000 && (awb_numbers_count%100) == 0)
      OrderMailer.sidekiq_delay.report_mailer('Upload Awb Numbers for Shadowfax',"Only #{awb_numbers_count} Awb Numbers left",{'to_email' => "<EMAIL>,#{DEPARTMENT_HEAD_EMAILS['operations']}",'from_email_with_name' => DEPARTMENT_HEAD_EMAILS['operations'].to_s.split(',',2)[0]}, {})
    end
    if awb_numbers_count > 0
      awb_number_obj = awb_numbers.first
      awb_number_obj.update_column(:available, false)
      awb_number = awb_number_obj.number
    else
      designer_order.failed_shipment('Shadowfax AWB numbers not available. <NAME_EMAIL>')
      designer_order.save
      return
    end
    order = designer_order.order
    designer = designer_order.designer

    reference = "#{order.number}-#{designer_order.id}"
    packer_id, invoicer_id = designer.account.id, designer.account.id

    shipment = Shipment.new(
      shipper:            shipper,
      order_id:           order.id, 
      packer_id:          packer_id,
      invoicer_id:        invoicer_id,
      mirraw_reference:   reference,
      shipment_type:      'COD',
      payment_state:      'unpaid',
      designer_order_id:  designer_order.id,
      weight:             0,
      number:             awb_number
    )
    cod_amount = 0
    skus_attributes = Array.new
    invoice_items = Array.new
    seller_details = {
      seller_name: designer.name,
      seller_address: "#{designer.street},#{designer.city},#{designer.pincode}",
      seller_state: designer.state,
      gstin_number: designer.gst_no.to_s
    }
    cod_charge = 0
    cod_charge = designer_order.calculate_domestic_shipping_charge
    all_items = designer_order.line_items.not_canceled
    single_cod_charge  = cod_charge / all_items.sum(&:quantity).to_f

    all_items.each do |item|
      addon_text = item.vendor_addon_items.collect{|item| "[#{item.addon_type_value.name}]" }.join
      design = item.design
      designable_type = design.designable_type
      name        = design.categories[0].name.gsub('-', ' ').camelize
      name        << ' [sku: ' + design.design_code + ']' if design.design_code.present?
      name        += addon_text
      if order.cod? && order.currency_rate_market_value.present? && designer_order.ship_to != 'mirraw'
        price_per_item = order.international_cod_price(item.price_with_addons)
      else
        price_per_item = item.price_with_addons
      end
      items_price = price_per_item * item.quantity
      cod_amount  += items_price
      hsn_code,gst_rate = (item.purchase_hsn_code.present? && item.purchase_gst_rate.present?) ? [item.purchase_hsn_code,item.purchase_gst_rate] : item.find_hscode_gst(price_per_item)
      invoice_items << { name: name, quantity: item.quantity, price: price_per_item, total_price: items_price,hsn_code: hsn_code,gst_rate: gst_rate, designable_type: designable_type }
      invoice_items << { name: 'Shipping Charges', quantity: item.quantity, price: single_cod_charge, total_price: (single_cod_charge * item.quantity).round(2),hsn_code: '',gst_rate: gst_rate } if single_cod_charge > 0

      skus_attributes << {sku_name: design.title, client_sku_id: design.design_code.presence || design.id.to_s, price: items_price,seller_details:seller_details}
    end

    if cod_charge > 0
      shipment.cod_charge = cod_charge
    end
    invoice_data = Shipment.calculate_domestic_invoice(order, invoice_items, false, designer_order)
    shipment.price = invoice_data[:item_total].to_i

    order_pickup_details = designer.get_order_pickup_details(designer_order, order)

    pickup_details = {  
          name: order_pickup_details[:company],
          city: order_pickup_details[:city],
          state: order_pickup_details[:state],
          contact: order_pickup_details[:phone],
          address_line_1: order_pickup_details[:address],
          address_line_2: '',
          pincode: order_pickup_details[:pincode]
        }

    shipment_data = {
        order_details: {
          client_order_id: reference,
          awb_number: awb_number,
          actual_weight: 0,
          volumetric_weight: 0,
          product_value: shipment.price,
          cod_amount: shipment.price,
          payment_mode: "COD",
          promised_delivery_date: SHIPPING_TIME.days.from_now.iso8601
        },
        customer_details: {
          name: order.name,
          contact: order.phone,
          address_line_1: order.street_line_1,
          address_line_2: order.street_line_2,
          city: order.city,
          state: order.buyer_state,
          pincode: order.pincode
        },
        pickup_details: {
          name: designer.name,
          city: designer.city,
          state: designer.state,
          contact: designer.phone,
          address_line_1: designer.street,
          address_line_2: '',
          pincode: designer.pincode
        },
        rts_details: {
          name: designer.name,
          city: designer.city,
          state: designer.state,
          contact: designer.phone,
          address_line_1: designer.street,
          address_line_2: '',
          pincode: designer.pincode
        },
        product_details: skus_attributes
      }
    response = ShadowfaxApi.create_package(shipment_data)
    if response['message'].try(:downcase) == 'success' && shipment.save
      ShipmentsController.sidekiq_delay(queue: 'critical').generate_invoice(invoice_items, order.number, shipment.id, invoice_data: invoice_data)
      # shipment.sidekiq_delay(queue: 'critical').generate_tofrom_label
      SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(shipment.class.to_s, shipment.id, "generate_tofrom_label")
      designer_order.tracking_partner = 'Shadowfax'
      designer_order.shipper_id = shipper.id
      designer_order.tracking_num = shipment.number
      designer_order.shipment_status = 'created'
    else
      awb_number_obj.update_column(:available,nil) if awb_number_obj.present?
      designer_order.failed_shipment("#{response['errors']} .Please Try again in some time")
    end
    designer_order.save
  end

  def self.xpress_bees_create_domestic(designer_order, bulk_ids=[], bulk_designer=nil, reference=nil)
    shipper = Shipper.find_by_name('Xpress Bees')
    order = designer_order.try(:order)
    is_cod = designer_order.try(:ship_as_cod?)
    shipment_type = (is_cod ? 'COD' : 'PREPAID')
    is_ship_to_mirraw = (bulk_designer.present? || designer_order.try(:ship_to) == 'mirraw')
    awb_numbers = AwbNumber.available_number_by_service(shipper.id, true, shipment_type.downcase)
    awb_numbers_count = awb_numbers.count
    xpress_bees_urls = Mirraw::Application.config.xpress_bees
    if awb_numbers_count < 50
      #Shipment.sidekiq_delay.generate_xpress_bees_awb_numbers(shipper, xpress_bees_urls, shipment_type)
      SidekiqDelayGenericJob.perform_async("Shipment", 
                                            nil,
                                            "generate_xpress_bees_awb_numbers",
                                            {shipper.class.to_s => shipper.id},
                                            xpress_bees_urls.merge({"sidekiq_request_params"=>true}),
                                            shipment_type
                                           )
    end
    if awb_numbers_count > 0
      awb_number_obj = awb_numbers.first
      awb_number_obj.update_column(:available, false)
      awb_number = awb_number_obj.number
    elsif !is_cod && is_ship_to_mirraw
      return '', {'error' => 'Xpress Bees AWB numbers not available. <NAME_EMAIL>'}     
    else
      designer_order.failed_shipment('Xpress Bees AWB numbers not available. <NAME_EMAIL>')
      designer_order.save
      return 
    end
    designer = (bulk_designer.presence || designer_order.designer)

    reference = (reference.presence || "#{order.number}-#{designer_order.id}")
    packer_id, invoicer_id = designer.account.id, designer.account.id
    shipment_hash = {
      shipper_id:         shipper.id, 
      order_id:           order.try(:id), 
      packer_id:          packer_id,
      invoicer_id:        invoicer_id,
      mirraw_reference:   reference,
      shipment_type:      shipment_type,
      payment_state:      (is_cod ? 'unpaid' : 'paid'),
      designer_order_id:  designer_order.try(:id),
      weight:             0,
      number:             awb_number
    }    
    ship_create_request, invoice_items, shipment_hash, invoice_data = get_xpress_bees_shipment_request(shipment_hash, designer_order, order, designer, xpress_bees_urls[:push_XBkey], bulk_ids, reference)
    ship_create_response = JSON.parse(HTTParty.post(xpress_bees_urls[:prepare_manifest_url], body: ship_create_request.to_json, headers: {'Content-Type' => 'application/json'}).body)
    response_msg = ship_create_response['AddManifestDetails'][0]['ReturnMessage']
    if !is_cod && is_ship_to_mirraw
      return shipment_hash[:number], (response_msg.try(:downcase) != 'successful' ? {'error' => response_msg} : {'success' => response_msg})
    else
      shipment = Shipment.new(shipment_hash)
      if response_msg.try(:downcase) == 'successful' && shipment.save
        ShipmentsController.sidekiq_delay(queue: 'critical').generate_invoice(invoice_items, order.number, shipment.id, invoice_data: invoice_data)
        shipment.generate_tofrom_label
        designer_order.tracking_partner = shipper.name
        designer_order.shipper_id = shipper.id
        designer_order.tracking_num = shipment.number
        designer_order.shipment_status = 'created'
      else
        error_message = response_msg
        if shipment.errors.present? && shipment.errors.messages.present?
          error_message = error_message + ' Mirraw Issue '
          shipment.errors.messages.each do |key, values|
            error_message = error_message + ' ' + key.to_s
            values.each {|value| error_message = error_message + ' ' + value.to_s }
          end
        end
        designer_order.failed_shipment(error_message)
      end
      designer_order.save
    end
  end

  def self.delhivery_create_domestic(designer_order)
    order = designer_order.order
    cod_charge, cod = 0, designer_order.ship_as_cod?
    cod_charge = designer_order.calculate_domestic_shipping_charge
    single_cod_charge  = cod_charge / designer_order.line_items.sane_items.sum(:quantity).to_f
    invoice_items = Array.new
    designer_order.line_items.not_canceled.each do |item|
      addon_text = item.vendor_addon_items.collect{|item| "[#{item.addon_type_value.name}]" }.join
      designable_type = item.design.designable_type
      name        = item.design.categories.first.name.gsub('-', ' ').camelize
      name        += ' [sku: ' + item.design.design_code + ']' if item.design.design_code.present?
      name        += ' [size: ' + item.variant.option_type_values.collect(&:name).last + ']' if (variant = item.variant).present? && variant.option_type_values.present?
      name        += addon_text
      if cod && order.currency_rate_market_value.present? && designer_order.ship_to != 'mirraw'
        price_per_item = order.international_cod_price(item.price_with_addons)
      else
        price_per_item = item.price_with_addons
      end
      items_price = price_per_item * item.quantity
      hsn_code,gst_rate = (item.purchase_hsn_code.present? && item.purchase_gst_rate.present?) ? [item.purchase_hsn_code,item.purchase_gst_rate] : item.find_hscode_gst(price_per_item)
      invoice_items << { :name => name, :quantity => item.quantity, :price => price_per_item, :total_price => items_price,hsn_code: hsn_code,gst_rate: gst_rate, designable_type: designable_type}
      invoice_items << { name: 'Shipping Charges', quantity: item.quantity, price: single_cod_charge, total_price: (single_cod_charge * item.quantity).round(2),hsn_code: '',gst_rate: gst_rate } if single_cod_charge > 0
    end

    packer_id = invoicer_id = designer_order.designer.account.id
    shipper, reference = Shipper.find_by_name('Delhivery'), designer_order.shipment_reference

    shipment = Shipment.new(
      :shipper_id     => shipper.id,
      :order_id       => order.id, 
      :packer_id      => packer_id,
      :invoicer_id    => invoicer_id,
      :mirraw_reference => reference,
      :shipment_type => (cod ? 'COD' : 'PREPAID'),
      :payment_state => (cod ? 'unpaid' : 'paid'),
      :designer_order_id => designer_order.id,
      :weight => 0
    )
    invoice_data = Shipment.calculate_domestic_invoice(order, invoice_items, false, designer_order)
    shipment.price = invoice_data[:item_total].to_i

    if cod_charge > 0
      shipment.cod_charge = cod_charge
    end

    shipment.number, res_content = DesignerOrder.delhivery_fetch_awb(designer_order, designer_order.designer, designer_order.order, designer_order.ship_to, reference, shipment.price)

    if res_content['error'].blank? && res_content['success'] && shipment.save
      ShipmentsController.sidekiq_delay(queue: 'critical').generate_invoice(invoice_items, order.number, shipment.id, invoice_data: invoice_data)
      # shipment.sidekiq_delay(queue: 'critical').generate_tofrom_label
      SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(shipment.class.to_s, shipment.id, "generate_tofrom_label")
      designer_order.tracking_partner = 'Delhivery'
      designer_order.shipper_id = shipper.id
      designer_order.tracking_num = shipment.number
      designer_order.shipment_status = 'created'
    else
      error_message = res_content['error'].to_s + ' ' + res_content['rmk'].to_s
      if shipment.errors.present? && shipment.errors.messages.present?
        error_message = error_message + ' Mirraw Issue '
        shipment.errors.messages.each do |key, values|
          error_message = error_message + ' ' + key.to_s
          values.each {|value| error_message = error_message + ' ' + value.to_s }
        end
      end
      designer_order.failed_shipment(error_message)
    end
    designer_order.save
  end

  def self.create_warehouse_delhivery_surface_shipment(warehouse_order_id, rtv_shipment_id, current_account, weight)
    warehouse_order = WarehouseOrder.where(id: warehouse_order_id).includes(:designer, :warehouse_line_items).first
    designer = warehouse_order.designer
    rtv_shipment = RtvShipment.where(id: rtv_shipment_id).first
    w_line_items = warehouse_order.warehouse_line_items
    wa_ids = [warehouse_order.warehouse_address_id]
    if designer.present?
      begin
        awb_number = delhivery_awb('surface')
        reference ="#{warehouse_order.number}-#{rtv_shipment.id}"
        w_line_item_total = 0
        w_line_items.map{|wli| w_line_item_total += wli.snapshot_price * wli.quantity}
        w_line_item_total = w_line_item_total.round(2)
        warehouse_order_date = warehouse_order.dispatched_on.present? ? warehouse_order.dispatched_on.iso8601 : DateTime.now.iso8601
        product_desc = w_line_items.includes(design: :categories).collect{|wli| wli.design.categories.first.name}.uniq.join(',')
        shipments_req = {waybill: awb_number, name: designer.name, order: reference,products_desc: product_desc, order_date: warehouse_order_date, payment_mode: 'Prepaid', total_amount: w_line_item_total,cod_amount: 0.0, add: designer.street.gsub(/[&;]/,''), city: designer.city, state: designer.state, phone: designer.phone,country: designer.country, pin: designer.pincode, weight: "#{weight.to_f * 1000.0} gm"}
        res, res_content = get_delivery_response(shipments_req, wa_ids)
        if res.response.code == '200' && res_content['error'].blank? && res_content['success']
          rtv_label_url = generate_delhivery_label_url(awb_number, designer, reference, w_line_item_total, 'Delhivery', false, wa_ids)
          rtv_shipment.update_attributes(number: awb_number,shipper_name: 'Delhivery',done_by: current_account.id,label_url: rtv_label_url, shipment_success: true)
          w_line_items.update_all(rtv_shipment_error: nil)
        else
          rtv_shipment.destroy
          w_line_items.update_all(rtv_shipment_error: "#{res_content['error']}")
        end
      rescue => e
        rtv_shipment.destroy
        w_line_items.update_all(rtv_shipment_error: e.message)
      end
    else
      w_line_items.update_all(rtv_shipment_error: 'Designer does not exist.')
      rtv_shipment.destroy
    end
  end

  def self.create_delhivery_surface_shipment(designer_order_id, rtv_shipment_id, current_account, weight)
    designer_order = DesignerOrder.where(id: designer_order_id).includes(:designer,:order).first
    designer = designer_order.designer
    rtv_shipment = RtvShipment.where(id: rtv_shipment_id).first
    wa_ids = [designer_order.warehouse_address_id]
    _, shipping_telephone, shipping_address_1, shipping_address_2, shipping_city, shipping_pincode, shipping_state, _  = DesignerOrder.get_warehouse_billing_address(wa_ids)
    line_items = rtv_shipment.line_items
    if designer.present?
      awb_number = delhivery_awb('surface')
      pickup_location = {country: 'India',name: Mirraw::Application.config.delhivery_surface_client_name,phone: shipping_telephone,add:   shipping_address_1.gsub(/[&;]/,''),city: shipping_city,state: shipping_state,pin: shipping_pincode }
      reference ="#{designer_order.order.number}-#{rtv_shipment.id}"
      line_item_total = 0
      line_items.map{|l| line_item_total += (l.vendor_selling_price.present? ? l.vendor_selling_price : l.snapshot_price(RETURN_NORMAL)) * l.quantity}
      line_item_total = line_item_total.round(2)
      order_date = designer_order.confirmed_at.present? ? designer_order.confirmed_at.iso8601 : DateTime.now.iso8601
      product_desc = line_items.includes(:categories).collect{|li| li.categories.first.name}.uniq.join(',')
      shipments_req = {waybill: awb_number, name: designer.name, order: reference,products_desc: product_desc ,order_date: order_date, payment_mode: 'Prepaid', total_amount: line_item_total,cod_amount: 0.0, add: designer.street.gsub(/[&;]/,''), city: designer.city, state: designer.state, phone: designer.phone,country: designer.country, pin: designer.pincode, weight: "#{weight.to_f * 1000.0} gm"}
      res, res_content = get_delivery_response(shipments_req, wa_ids)
      if res.response.code == '200' && res_content['error'].blank? && res_content['success']
        rtv_label_url = generate_delhivery_label_url(awb_number, designer, reference, line_item_total, 'Delhivery', false, wa_ids)
        rtv_shipment.update_attributes(number: awb_number,shipper_name: 'Delhivery',done_by: current_account.id,label_url: rtv_label_url, shipment_success: true)
        designer_order.add_notes_without_callback("RTV Done. RTV Courier: Delhivery, RTV tracking number: #{awb_number}",'rtv',current_account) if designer_order.present?
        if DesignerOrder.create_adjustment_for_issue('RTV',designer_order,nil,weight)
          designer_order.add_notes_without_callback("Negative Adjustment Created for RTV Issue",'adjustment',current_account)
        end
        DesignerOrder.create_rtv_invoice_url(designer_order.id,line_items.collect(&:id), rtv_shipment.id)
        LineItem.bulk_add_into_scan('LineItem', line_items.collect(&:id), 'RTV Shipment Created', current_account.id)
        LineItem.update_rack_status(condition: {id: line_items.collect(&:id)}, status: 'rack_out', rack_id: designer_order.rack_list_id)
        line_items.update_all(rtv_shipment_error: nil)
      else
        line_items.update_all(rtv_shipment_error: "#{res_content['error']}")
        rtv_shipment.destroy
      end
    else
      rtv_shipment.destroy
      line_items.update_all(rtv_shipment_error: "Designer does not exist.")
    end
  end

  def self.create_warehouse_xpress_bees_surface_shipment(warehouse_order_id, rtv_shipment_id, current_account, weight)
    warehouse_order = WarehouseOrder.where(id: warehouse_order_id).includes(:designer, warehouse_line_items: :design).first
    designer = warehouse_order.designer
    shipper = Shipper.find_by_name 'Xpress Bees'
    rtv_shipment = RtvShipment.where(id: rtv_shipment_id).first
    wa_ids = [warehouse_order.warehouse_address_id]
    if rtv_shipment.present?
      w_line_items = warehouse_order.warehouse_line_items
      if designer.present?
        awb_number = AwbNumber.available_number_by_service(shipper.id, true, 'prepaid').first.try(:number)
        if awb_number.present?
          total_rtv_products_amount = 0
          multi_seller_info = []
          w_line_items.each do |l|
            design = l.design
            total_rtv_products_amount += l.snapshot_price * l.quantity
            multi_seller_info << {'ProductDesc' => design.title, 'ProductCategory' => design.designable_type, 'SellerName' => designer.name, "SellerAddress" => "#{designer.street},#{designer.city},#{designer.state}", "SellerPincode" => designer.pickup_pincode.presence || designer.pincode, "InvoiceDate" => Date.today.strftime('%d-%m-%Y')}
          end
          begin
            total_rtv_products_amount = total_rtv_products_amount.round(2)
            rtv_reference = {'manifest_id' => warehouse_order.id, 'order_no' => warehouse_order.number}
            reference ="#{warehouse_order.number}-#{rtv_shipment.id}"
            cust_address_details, cust_phone_details, pickup_details, delivery_details = get_customer_contact_details(designer, wa_ids)
            manifest_details = get_manifest_details(wa_ids,cust_address_details, cust_phone_details, pickup_details, delivery_details, nil, nil, designer, awb_number, multi_seller_info, total_rtv_products_amount, false, rtv_reference)
            response_msg = get_xpress_bees_delivery_response(manifest_details)
            if response_msg.try(:downcase) == 'successful'
              rtv_label_url = generate_delhivery_label_url(awb_number, designer, reference, total_rtv_products_amount, 'Xpress Bees', false, wa_ids)
              rtv_shipment.update_attributes(number: awb_number, shipper_name: 'Xpress Bees',done_by: current_account.id, label_url: rtv_label_url, shipment_success: true)
              w_line_items.update_all(rtv_shipment_error: nil)
            else
              rtv_shipment.destroy
              w_line_items.update_all(rtv_shipment_error: response_msg)
            end
          rescue => e
            rtv_shipment.destroy
            w_line_items.update_all(rtv_shipment_error: e.messages)
          end
        else
          w_line_items.update_all(rtv_shipment_error: 'Awb Number not available')
          rtv_shipment.destroy
        end
      else
        w_line_items.update_all(rtv_shipment_error: 'Designer does not exist.')
        rtv_shipment.destroy
      end
    end
  end

  def self.create_xpress_bees_surface_shipment(designer_order_id, rtv_shipment_id, current_account, weight)
    designer_order = DesignerOrder.where(id: designer_order_id).includes(:designer,:order).first
    designer = designer_order.designer
    order = designer_order.order
    shipper = Shipper.find_by_name 'Xpress Bees'
    wa_ids = [designer_order.warehouse_address_id]
    rtv_shipment = RtvShipment.preload(line_items: :design).where(id: rtv_shipment_id).first
    if rtv_shipment.present?
      line_items = rtv_shipment.line_items
      if designer.present?
        awb_number = AwbNumber.available_number_by_service(shipper.id, true, 'prepaid').first.try(:number)
        if awb_number.present?
          credentials = Mirraw::Application.config.xpress_bees
          total_rtv_products_amount = 0
          multi_seller_info = []
          line_items.each do |l| 
            design = l.design
            total_rtv_products_amount += (l.vendor_selling_price.present? ? l.vendor_selling_price : l.snapshot_price(RETURN_NORMAL)) * l.quantity
            multi_seller_info << {'ProductDesc' => design.title, 'ProductCategory' => design.designable_type, 'SellerName' => designer.name, "SellerAddress" => "#{designer.street},#{designer.city},#{designer.state}", "SellerPincode" => designer.pickup_pincode.presence || designer.pincode, "InvoiceDate" => Date.today.strftime('%d-%m-%Y')}
          end
          reference ="#{designer_order.order.number}-#{rtv_shipment.id}"
          total_rtv_products_amount = total_rtv_products_amount.round(2)
          cust_address_details, cust_phone_details, pickup_details, delivery_details = get_customer_details(designer, false, wa_ids)
          manifest_details = get_manifest_details(wa_ids,cust_address_details, cust_phone_details, pickup_details, delivery_details, designer_order, order, designer, awb_number, multi_seller_info, total_rtv_products_amount, false)
          response_msg = get_xpress_bees_delivery_response(manifest_details)
          if response_msg.try(:downcase) == 'successful' 
            rtv_label_url = generate_delhivery_label_url(awb_number, designer, reference, total_rtv_products_amount, 'Xpress Bees', false, wa_ids)
            rtv_shipment.update_attributes(number: awb_number, shipper_name: 'Xpress Bees',done_by: current_account.id, label_url: rtv_label_url, shipment_success: true)
            designer_order.add_notes_without_callback("RTV Done. RTV Courier: Rapid Delivery, RTV tracking number: #{awb_number}",'rtv',current_account) if designer_order.present?
            if DesignerOrder.create_adjustment_for_issue('RTV', designer_order, nil, weight)
              designer_order.add_notes_without_callback("Negative Adjustment Created for RTV Issue",'adjustment',current_account)
            end
            DesignerOrder.create_rtv_invoice_url(designer_order.id, line_items.collect(&:id), rtv_shipment.id)
            LineItem.bulk_add_into_scan('LineItem', line_items.collect(&:id), 'RTV Shipment Created', current_account.id)
            LineItem.update_rack_status(condition: {id: line_items.collect(&:id)}, status: 'rack_out', rack_id: designer_order.rack_list_id)
            line_items.update_all(rtv_shipment_error: nil)
          else
            rtv_shipment.destroy
            line_items.update_all(rtv_shipment_error: response_msg)
          end
        else
          rtv_shipment.destroy
          line_items.update_all(rtv_shipment_error: 'Awb Numbers of xpress bees for prepaid shipments are over. Please add new in system.')
        end
      else
        rtv_shipment.destroy
        line_items.update_all(rtv_shipment_error: "Designer does not exists.")
      end
    end
  end

  def self.create_warehouse_rapid_delivery_surface_shipment(warehouse_order_id, rtv_shipment_id, current_account, weight)
    warehouse_order = WarehouseOrder.where(id: warehouse_order_id).includes(:designer, :warehouse_line_items).first
    designer = warehouse_order.designer
    rtv_shipment = RtvShipment.where(id: rtv_shipment_id).first
    wa_ids = [warehouse_order.warehouse_address_id]
    if rtv_shipment.present?
      w_line_items = warehouse_order.warehouse_line_items
      begin
        if designer.present?
          w_line_item_total = 0
          w_line_items.map{|wli| w_line_item_total += wli.snapshot_price * wli.quantity}
          w_line_item_total = w_line_item_total.round(2)
          reference ="#{warehouse_order.number}-#{rtv_shipment.id}"
          awb_num = get_rapid_delivery_response(warehouse_order.try(:number), designer, w_line_item_total, weight, wa_ids)
          if awb_num.strip.split(' ').count == 1 && awb_num.length <= 254
            rtv_label_url = generate_delhivery_label_url(awb_num, designer, reference, w_line_item_total,'Rapid Delivery', false, wa_ids)
            rtv_shipment.update_attributes(number: awb_num,shipper_name: 'Rapid Delivery',done_by: current_account.id,label_url: rtv_label_url, shipment_success: true)
            w_line_items.update_all(rtv_shipment_error: nil)
          else
            rtv_shipment.destroy
            w_line_items.update_all(rtv_shipment_error: 'Invalid Tracking Number. Tracking Number too long.')
          end
        else
          w_line_items.updated_all(rtv_shipment_error: 'Designer does not exists.')
          rtv_shipment.destroy
        end
      rescue => e
        rtv_shipment.destroy
        w_line_items.update_all(rtv_shipment_error: e.message)
      end
    end
  end

  def self.create_rapid_delivery_surface_shipment(designer_order_id, rtv_shipment_id, current_account, weight)
    designer_order = DesignerOrder.where(id: designer_order_id).includes(:designer,:order).first
    designer = designer_order.designer
    order = designer_order.order
    rtv_shipment = RtvShipment.where(id: rtv_shipment_id).first
    wa_ids = [designer_order.warehouse_address_id]

    if rtv_shipment.present?
      line_items = rtv_shipment.line_items
      if designer.present?
        line_item_total = 0
        line_items.map{|l| line_item_total += (l.vendor_selling_price.present? ? l.vendor_selling_price : l.snapshot_price(RETURN_NORMAL)) * l.quantity}
        line_item_total = line_item_total.round(2)
        reference ="#{designer_order.order.number}-#{rtv_shipment.id}"
        awb_num = get_rapid_delivery_response(order.try(:number), designer,line_item_total,weight, wa_ids) 
        if awb_num.strip.split(' ').count == 1 && awb_num.length <= 254
          rtv_label_url = generate_delhivery_label_url(awb_num, designer, reference, line_item_total,'Rapid Delivery', false, wa_ids)
          rtv_shipment.update_attributes(number: awb_num,shipper_name: 'Rapid Delivery',done_by: current_account.id,label_url: rtv_label_url, shipment_success: true)
          designer_order.add_notes_without_callback("RTV Done. RTV Courier: Rapid Delivery, RTV tracking number: #{awb_num}",'rtv',current_account) if designer_order.present?
          if DesignerOrder.create_adjustment_for_issue('RTV',designer_order,nil,weight)
            designer_order.add_notes_without_callback("Negative Adjustment Created for RTV Issue",'adjustment',current_account)
          end
          DesignerOrder.create_rtv_invoice_url(designer_order.id,line_items.collect(&:id), rtv_shipment.id)
          LineItem.bulk_add_into_scan('LineItem', line_items.collect(&:id), 'RTV Shipment Created', current_account.id)
          LineItem.update_rack_status(condition: {id: line_items.collect(&:id)}, status: 'rack_out', rack_id: designer_order.rack_list_id)
          line_items.update_all(rtv_shipment_error: nil)
        else
          rtv_shipment.destroy
          line_items.update_all(rtv_shipment_error: 'Invalid Tracking Number. Tracking Number too long.')
        end
      else
        rtv_shipment.destroy
        line_items.update_all(rtv_shipment_error: "Designer does not exists.")
      end
    end
  end

  def self.delhivery_awb(type,format = 'json')
    api = Mirraw::Application.config.delhivery_baseurl + '/waybill/api/fetch/{format}/?token={token}&cl={cl}'
    if type == 'cod'
      api_params = api.sub('{format}', format).sub('{token}', Mirraw::Application.config.delhivery_cod_token).sub('{cl}', 'Mirraw COD')
    else
      if Rails.env.production? || Rails.env.admin?
        api_params = api.sub('{format}', format).sub('{token}', Mirraw::Application.config.delhivery_surface_token).sub('{cl}', 'Surface').sub('cl','mode')
      else
        api_params = api.sub('{format}', format).sub('{token}', Mirraw::Application.config.delhivery_surface_token).sub('{cl}', 'MIRRAWSURFACE')
      end
    end
    api_params_encoded = URI.encode(api_params)
    res = HTTParty.get(api_params_encoded)
    if res.response.code == '200'
      awb_number =  type == 'cod' ? res.body.match('\d+')[0] : res.parsed_response
    else
      nil
    end
  end

  def self.crc_country_wise(file_name,country)
    service_type = self.order.get_fedex_service_type
    company_name, shipping_telephone, shipping_address_1,shipping_address_2,shipping_city, shipping_pincode,_,shipping_state_code = DesignerOrder.get_warehouse_billing_address(DEFAULT_WAREHOUSE_ADDRESS_ID)
    fedex_error = []
    dhl_error = []
    aramex_error = []
    fedex = FedexApi.new()
    dhl_api =  DhlApi.new()
    state_code_countries = 'US', 'IN', 'CA', 'AE'
    fedex_package_options = {
      "shipper"=> {
        "address" => {
          "streetLines": shipping_address_1.split( /, */ )  << shipping_address_2,
          "city" => shipping_city,
          "stateOrProvinceCode" => shipping_state_code,
          "postalCode" => shipping_pincode,
          "countryCode" => "IN",
          } 
      },
      "recipient" => {
        "address" => {
          "streetLines" => ["1550 Union Blvd", "Suite 302"],
          "city" => country.crc_city,
          "postalCode" => country.crc_pincode,
          "countryCode" => country.iso3166_alpha2,
          "residential" => false
        }
      },
      "requestedPackageLineItems": [
        {
          "weight": {
            "units": "KG",
            "value": 0.5
          }
        }
      ],
      "serviceType" => service_type,
      "pickupType" => "DROPOFF_AT_FEDEX_LOCATION",
      'preferredCurrency' => 'USD',
      "customsClearanceDetail": {
        "commercialInvoice": {
          "shipmentPurpose": "RECIPIENT"
        },
        "dutiesPayment": {
          "paymentType": "SENDER",
          "payor": {}
        },
        "commodities": [
          {
            "description": "Ladies Anarkali Salwar Kameez",
            "quantity": 1,
            "quantityUnits": "PCS",
            "weight": {
              "units": "KG",
              "value": 5.0
            },
            "customsValue": {
              "amount": 100,
              "currency": "USD"
            }
          }
        ]
      },
    }
    dhl_post_quote = {"shipperPostCode"=>'400001', "receiverCountryCode"=>country.iso3166_alpha2, "postCode"=>country.crc_pincode, "declaredCurrency"=>"INR", "declaredValue"=>"59", "pieces"=>1, "shipPieceWt"=>'0.5', "shipPieceDepth"=>"0", "shipPieceWidth"=>"0", "shipPieceHeight"=>"0"}
    aramex_request = {"ClientInfo"=>{"UserName"=>"<EMAIL>", "Password"=>"omegaalpha", "Version"=>"v1.0", "AccountNumber"=>"********", "AccountPin"=>"443543", "AccountEntity"=>"BOM", "AccountCountryCode"=>"IN"}, "Transaction"=>{"Reference1"=>"mirraw_reference", "Reference2"=>"", "Reference3"=>"", "Reference4"=>"", "Reference5"=>""}, "OriginAddress"=>{"Line1"=>"Ground floor  plot - 7  Nashirwan Mansion  Kumta", "Line2"=>"Street  Off Shahid Bhagat Singh", "Line3"=>"Road  Ballard Estate", "City"=>"Mumbai", "StateOrProvinceCode"=>"MH", "PostCode"=>"400001", "CountryCode"=>"IN"}, "DestinationAddress"=>{"Line1"=>"DO NOT SHIP", "Line2"=>"", "Line3"=>"", "City"=>"San Francisco", "StateOrProvinceCode"=>"CA", "PostCode"=>"94105", "CountryCode"=>"US"}, "ShipmentDetails"=>{"Dimensions"=>{"Length"=>10, "Width"=>10, "Height"=>10, "Unit"=>"cm"}, "ActualWeight"=>{"Unit"=>"KG", "Value"=>0.5}, "ChargeableWeight"=>{"Unit"=>"KG", "Value"=>0.5}, "DescriptionOfGoods"=>"Lehengas", "GoodsOriginCountry"=>"IN", "NumberOfPieces"=>1, "ProductGroup"=>"EXP", "ProductType"=>"PPX", "PaymentType"=>"P", "PaymentOptions"=>"", "CustomsValueAmount"=>{"CurrencyCode"=>"USD", "Value"=>0.2}, "Services"=>"", "Items"=>[[{"ShipmentItem"=>{"PackageType"=>"Lehengas", "Quantity"=>1, "Weight"=>{"Unit"=>"KG", "Value"=>5.0}, "Comments"=>"", "Reference"=>""}}], [{"ShipmentItem"=>{"PackageType"=>"Sarees", "Quantity"=>1, "Weight"=>{"Unit"=>"KG", "Value"=>5.0}, "Comments"=>"", "Reference"=>""}}]]}}
    client = Savon.client(wsdl: Rails.root + 'lib/aramex/aramex-rates-calculator-wsdl.wsdl',strip_namespaces: true, pretty_print_xml: true,  log: true,logger: Rails.logger,log_level: :debug)
    file = CSV.generate do |csv|
      (0.5..30).step(0.5).each do |weight|
        begin
          fedex_package_options["requestedPackageLineItems"][0]["weight"]["value"] = weight
          rate = fedex.get_rate(fedex_package_options)
          charge = rate[:output][:rateReplyDetails].first[:ratedShipmentDetails].first[:totalNetCharge].to_f.round(2)
          csv << ['Fedex',country.name, charge,weight]
        rescue => e
          fedex_error << [e,country.name]
        end
        begin
          dhl_post_quote['shipPieceWt'] = weight.to_s
          dhl_quote =dhl_api.quote(dhl_post_quote)
          if (shipping_charge = dhl_quote['Details'].try(:[],'ShippingCharge').to_f) != 0
            charge = shipping_charge + dhl_quote['Details'].try(:[],'TotalTaxAmount').to_f
            csv << ['DHL',country.name, charge.to_f.round(2),weight]
          end
        rescue => e
          dhl_error << [e,country.name]
        end
        begin
          aramex_request['ShipmentDetails']['ActualWeight']['Value'] = weight
          aramex_request['ShipmentDetails']['ChargeableWeight']['Value'] = weight
          response = client.call(:calculate_rate,message: aramex_request)
          response_data = response.hash[:envelope][:body][:rate_calculator_response]
          unless response_data[:has_errors]
            csv << ['Aramex',country.name,response_data[:total_amount][:value].to_f,weight]
          end
        rescue => e
          aramex_error << [e,country.name]
        end
      end
    end
    if aramex_error.present? || dhl_error.present? || fedex_error.present?
      ExceptionNotify.sidekiq_delay.notify_exceptions( 'Shipment CRC Error','Shipment Data Fetch Error',{params: [aramex_error:  aramex_error,dhl_error: dhl_error,fedex_error: fedex_error]})
    end
    AwsOperations.create_aws_file(file_name,file,false)
  end

  def self.generate_crc
    crc_file_name = []
    Country.where('crc_street is not null and lower(name) <> ?','india').each_with_index do |country,index|
      name = "crc_data/country_#{country.name}_#{Time.now.strftime("%m_%d")}.csv"
      crc_file_name << name
      Shipment.sidekiq_delay_until((index*10).minutes.from_now).crc_country_wise(name,country)
    end
    Shipment.sidekiq_delay_until(72.hours.from_now).merge_crc_csv(crc_file_name)
  end

  def self.merge_crc_csv(crc_file_name)
    file = CSV.generate do |csv|
      crc_file_name.each do |file_name|
        CSV.new(open(AwsOperations.get_aws_file_path(file_name))).each do |row|
          csv << row
        end
      end
    end
    name = "crc_data/all_country/#{Time.now.strftime('%m_%d_%H_%M')}" + SecureRandom.hex(3) +'.csv'
    AwsOperations.create_aws_file(name,file,false)
    CrcVersion.create(url: AwsOperations.get_aws_file_path(name))
  end



  def self.generate_delhivery_label_url(awb_no, designer, mirraw_reference, line_item_total, shipper_name, shipment = false , wa_ids = [])
    label_file_name = "#{shipper_name.try(:downcase)}_surface_label/#{awb_no}_#{shipper_name}Surface"
    pdf_content =  ActionController::Base.new().render_to_string(
      :template => 'shipments/delhivery_surface_label.html.haml',
      :layout   => false ,
      :locals   => {awb_no: awb_no, designer: designer, mirraw_reference: mirraw_reference, line_item_total: line_item_total, shipment: shipment, shipper_name: shipper_name, wa_ids: wa_ids}
    )
    delhivery_surface_label = WickedPdf.new.pdf_from_string(pdf_content)
    return delhivery_surface_label if shipment
    AwsOperations.create_aws_file(label_file_name, delhivery_surface_label, false)
    download_url = AwsOperations.get_aws_file_path(label_file_name)
  end

  def generate_tofrom_label
    ShipmentsController.generate_tofrom_label(self)
  end

  # input params - DesignerOrder - object of DesignerOrder
  #
  # Return 
  def self.aramex_create_domestic(designer_order)
    order = designer_order.order
    order_pickup_details = designer_order.designer.get_order_pickup_details(designer_order, order)
    pay_type = order.pay_type

    # invoice items array for generating invoice
    cod_amount = 0
    cod_charge = 0
    cod = designer_order.ship_as_cod?
    cod_charge = designer_order.calculate_domestic_shipping_charge
    invoice_items = Array.new
    all_line_items = designer_order.line_items.not_canceled
    single_cod_charge  = cod_charge / all_line_items.sum(&:quantity).to_f
    all_line_items.each do |item|
      designable_type = item.design.designable_type
      addon_text = item.vendor_addon_items.collect{|item| "[#{item.addon_type_value.name}]" }.join
      name        = item.design.categories.first.name.gsub('-', ' ').camelize
      name        += ' [sku: ' + item.design.design_code + ']' if item.design.design_code.present?
      name        += addon_text
      if order.currency_rate_market_value.present? && designer_order.ship_to != 'mirraw'
        price_per_item = order.international_cod_price(item.price_with_addons)
      else
        price_per_item = item.price_with_addons
      end
      items_price = price_per_item * item.quantity
      cod_amount  += items_price
      hsn_code,gst_rate = (item.purchase_hsn_code.present? && item.purchase_gst_rate.present?) ? [item.purchase_hsn_code,item.purchase_gst_rate] : item.find_hscode_gst(price_per_item)
      invoice_items << { :name => name, :quantity => item.quantity, :price => price_per_item, :total_price => items_price,hsn_code: hsn_code,gst_rate: gst_rate, designable_type: designable_type }
      invoice_items << { name: 'Shipping Charges', quantity: item.quantity, price: single_cod_charge, total_price: (single_cod_charge * item.quantity).round(2),hsn_code: '',gst_rate: gst_rate } if single_cod_charge > 0
    end
    mirraw_reference = designer_order.shipment_reference
    shipment = Shipment.new(
      :order_id => order.id,
      :packer_id => designer_order.designer.account.id,
      :invoicer_id => designer_order.designer.account.id,
      :mirraw_reference => mirraw_reference,
      :shipment_type => cod ? 'COD' : 'PREPAID',
      :payment_state => 'unpaid',
      :designer_order_id => designer_order.id,
      :weight => all_line_items.count/10.0,
      :cod_charge => cod_charge
    )

    invoice_data = Shipment.calculate_domestic_invoice(order, invoice_items, false, designer_order)

    total = invoice_data[:item_total].to_i
    shipment.price = total

    customer_details = {
      :address =>
      {
        :city => order.city,
        :state => order.state,
        :state_code => order.state_code,
        :pincode => order.pincode,
        :street => order.street,
        :country_code => Country.find_by_namei_cached(order.country).try(:[], :iso3166_alpha2)
      },
      :contact =>
      {
        :name => order.name,
        :phone_number1 => order.phone,
        :email => order.email
      }
    }

    shipper_details = {
      :address =>
      {
        :city => order_pickup_details[:city],
        :state => order_pickup_details[:state],
        :state_code => order_pickup_details[:state_code],
        :pincode => order_pickup_details[:pincode],
        :street => order_pickup_details[:address],
        :country_code => order_pickup_details[:country_code]
      },
      :contact =>
      {
        :name => order_pickup_details[:name],
        :phone_number1 => order_pickup_details[:phone],
        :email => order_pickup_details[:email]
      }
    }

    mirraw_details = {
      :address =>
      {
        :city => SHIPPING_CITY,
        :state => 'Maharashtra',
        :state_code => 'MH',
        :pincode => SHIPPING_PINCODE,
        :street => SHIPPING_ADDRESS,
        :country_code => 'IN'
      },
      :contact => 
      {
        :name => 'MIRRAW',
        :phone_number1 => SHIPPING_TELEPHONE,
        :email => '<EMAIL>'
      }
    }
    response = AramexApi.new.aramex_shipment_api(shipper_details, customer_details, mirraw_details, all_line_items, mirraw_reference, pay_type, total)
    if response[:error]
      designer_order.tracking_partner = nil
      designer_order.pickup = nil
      designer_order.state  = 'pending'
      designer_order.shipment_status = 'failed'
      designer_order.shipment_error = response[:error_text]
      designer_order.save
    else
      shipper = Shipper.find_by_name('Aramex')
      shipment.shipper_id = shipper.id
      shipment.courier_label_url = response[:label_url].present? ? response[:label_url] : nil
      shipment.number = response[:awb]
      shipment.save
      ShipmentsController.sidekiq_delay(queue: 'critical').generate_invoice(invoice_items, order.number, shipment.id, invoice_data: invoice_data)
      # shipment.sidekiq_delay(queue: 'critical').download_label
      SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(shipment.class.to_s, shipment.id, "download_label")
      designer_order.shipper_id = Shipper.where('LOWER(name) = ?', 'aramex').first.id
      designer_order.tracking_partner = shipper.name
      designer_order.tracking_num = shipment.number
      designer_order.shipment_status = 'created'
      designer_order.shipment_error = nil
      designer_order.save
    end
  end




  # input params
  # address - String
  # line_length - Integer
  #
  # Split address String into Array based on line_length param
  #
  # Returns Array
  def self.multi_line_address(address, line_length)
    multi_line_address_block = []
    i = 0
    address.split(/[ ,]/).each do |s|
      if multi_line_address_block[i].blank?
        multi_line_address_block[i] = s
      elsif (multi_line_address_block[i].length + s.length < line_length)
        multi_line_address_block[i] += ' ' + s
      else
        i += 1
        multi_line_address_block[i] = s
      end
    end
    multi_line_address_block
  end

  def self.shadowfax_mass_tracking(numbers, shipper_id)
    if numbers.present?
      numbers.each_slice(10) do |number_slice|
        tracking_response = ShadowfaxApi.bulk_track({awb_numbers: number_slice})
        if tracking_response["message"] == 'Success'
          shipments = Shipment.where(shipper_id: shipper_id, number: number_slice).group_by(&:number)
          tracking_response['data'].each do |tracking_details|
            shipment = shipments[tracking_details['awb_number']].first
            shipment_status ={}
            tracking_data = tracking_details['tracking_details']
            shipment_status[:status] = tracking_details['status']
            shipment_status[:updated_at] = tracking_data.last['created']
            shipment_status[:remark] = tracking_data.last['remarks']
            shipment.get_shadowfax_shipment_state(shipment_status)
          end
          Shipment.where(number: (number_slice - tracking_response.keys)).update_all(track_error: 'Details Not Found')
        else
          Shipment.where(number: number_slice).update_all(track_error: tracking_response["message"])
        end
      end
    end
  end

  def self.delhivery_mass_tracking(numbers, shipper_id)
    if numbers.present?
      # numbers.each_slice(100) do |number_slice|
      numbers.each_slice(10) do |number_slice|
        tracking_response = delhivery_track_api(number_slice)
        if tracking_response['Error'].blank?
          shipments_info = tracking_response['ShipmentData']
          shipments_info.each do |shipment_info|
            awb_number = shipment_info['Shipment']['AWB']
            shipment = Shipment.where(:number => awb_number, :shipper_id => shipper_id).first
            if shipment.present?
              shipment_info_variable = {"ShipmentData" => [shipment_info]}
              shipment.update_state('DELHIVERY',shipment_info_variable)
            end
          end
        end
      end
    end
  end

  def update_aramex_shipment(result)
    self.last_event_timestamp = result[:update_date_time]
    self.last_event = "Aramex UpdateCode: #{result[:update_code]} - #{result[:update_description]} - Location: #{result[:update_location]} - Comments: #{result[:comments]}"
    new_shipment_state = nil
    case result[:update_code]
    when 'SH047', 'SH406', 'SH001', 'SH022', 'SH110', 'SH162'
      new_shipment_state = :in_transit
    when 'SH003', 'SH004','SH073', 'SH252'
      new_shipment_state = :out_for_delivery
    when 'SH069', 'SH247'
      new_shipment_state = :rto
    when 'SH033', 'SH294', 'SH043'
      new_shipment_state = :delivery_exception
    when 'SH005', 'SH006', 'SH154', 'SH234', 'SH239', 'SH394' 
      self.delivered_on = result[:update_date_time]
      self.delivered_to = result[:comments] ? result[:comments].truncate(253, omission: '') : result[:comments] #delivered_to column only stores 255 chatecter values 
      new_shipment_state = :delivered
    end
    self.update_shipment_state(new_shipment_state)
  end

  def self.aramex_mass_tracking(numbers, shipper_id)
    if numbers.present?
      numbers = Array(numbers).flatten.collect(&:to_s)
      # numbers.each_slice(50) do |number_slice|
      numbers.each_slice(10) do |number_slice|
        tracking_response = AramexApi.new.aramex_track_api(number_slice)
        if tracking_response[:error]
          Shipment.where(:shipper_id => shipper_id, :number => number_slice).update_all(:track_error => tracking_response[:error_text].to_s.truncate(250))
        else
          tracking_response[:results].each do |result|
            tracking_number = result[:waybill_number]
            shipment = Shipment.where(:shipper_id => shipper_id, :number => tracking_number).trackable_shipments.first
            if shipment.present?
              shipment.update_aramex_shipment(result)
            end
          end
        end
      end
    end
  end
  

  def download_label
    if self.courier_label_url.present? && ((self.rvp_shipment? && self.return_label.blank?) || self.label.blank?)
      curl = Curl::Easy.new(self.courier_label_url) do |curl|
        curl.follow_location = true
        curl.max_redirects   = 100
        curl.timeout         = 60
      end
      curl.perform
      shipment_label_download = curl.body
      header = DelayImage.new.parse_header(curl.header_str)
      if shipment_label_download.present? && (header.blank? || (header.present? && header[:content_type] == 'application/pdf'))
        self.add_label(shipment_label_download)
      else
        raise 'Unable to download label because of Courier partner service issue.'
      end
    end
  end

  # This method create shipment by aramex for international orders
  def self.aramex_create_international(order, shipment_hash, customer_details, mirraw_details, invoice_items, items_price_total, currency_code, shipping_charges)
    total_for_cod_aramex = nil
    if order.pay_type == COD && currency_code=='AED'
      total_by_rate = items_price_total
    elsif order.pay_type == COD
      total_by_rate = order.order_notification['paypal_rate'].present? ? ( (items_price_total *  order.currency_rate )/ order.order_notification['paypal_rate']) : nil
      total_by_cc_rate = (((items_price_total *  order.currency_rate )/ order.currency_rate ) * CurrencyConvert.find_by_country(order.country).paypal_rate.to_f) unless total_by_rate.present?
      total_for_cod_aramex = (total_by_rate || total_by_cc_rate).round(2)
    end
    customer_details = customer_details.nested_under_indifferent_access
    mirraw_details = mirraw_details.nested_under_indifferent_access
    shipment_hash = shipment_hash.nested_under_indifferent_access
    invoice_items = invoice_items.map{|item| item.nested_under_indifferent_access} 
    invoice_number = order.get_invoice_number
    response = AramexApi.new.aramex_international_shipment_api(customer_details, mirraw_details, invoice_items, shipment_hash[:mirraw_reference], order.pay_type, total_for_cod_aramex || items_price_total, shipment_hash[:weight], currency_code, invoice_number)
    if response[:error]
      ExceptionNotify.sidekiq_delay.notify_exceptions('Aramax API Errror',response, { params: 'error params' })
      order.add_tags_skip_callback('shipment_error')
      order.other_details['aramex_status'] = response[:error_text].to_s
      order.skip_before_filter = true
      order.save(validate: false)
      order.add_notes_without_callback(response[:error_text].to_s, 'Shipment Automation Error')
    else
      csb_reponse,error_msg = AramexApi.aramex_csb_integration(response[:awb],invoice_number, invoice_items,currency_code)
      if csb_reponse
        shipment = order.shipments.new(shipment_hash)
        if order.pay_type == COD
          shipment_type = 'COD'
          shipment.invoice_data['cod_value'] = (total_for_cod_aramex || items_price_total)
        else
          shipment_type = 'PREPAID'
        end
        shipment.assign_attributes(shipment_type: shipment_type, courier_label_url: response[:label_url].presence, number: response[:awb])
        if shipment.save
          Order.where(id: order.id).update_all(tracking_number: shipment.number, courier_company: 'Aramex' )        
          ShipmentsController.generate_invoice(invoice_items,order.number,shipment.id,shipping_charges,items_price_total)
          shipment.download_label
          order.remove_tags_skip_callback('shipment_error')
          order.reload
          order.other_details['aramex_status'] = 'successful'
          order.skip_before_filter = true
          order.save(validate: false)
          order.create_clickpost_tracking(shipment, invoice_items)
        end
      else
        order.add_tags_skip_callback('shipment_error')
        order.other_details['aramex_status'] = error_msg
        order.skip_before_filter = true
        order.save(validate: false)
        order.add_notes_without_callback(error_msg, 'Shipment Automation Error')
      end
    end
  rescue => error
    order.add_tags_skip_callback('shipment_error')
    order.other_details['aramex_status'] = error.message
    order.skip_before_filter = true
    order.save(validate: false)
    # OrderMailer.report_mailer("Aramex Api Shipment Automation Error","#{shipment_hash} ---- #{shipment}",{'to_email' => ['<EMAIL>'],'from_email_with_name' => ['<EMAIL>']},{}).deliver
    order.add_notes_without_callback(error.message, 'Shipment Automation Error')
  end

  # This method return shipment details for international shipping
  def self.aramex_international_shipment_hash(customer_details, mirraw_details, items, attachments, pay_type, total, mirraw_reference, weight)
    aramex = Mirraw::Application.config.aramex

    aramex_shipment = {
      'Shipment' => {
        'Reference1' => '',
        'Reference2' => '',
        'Reference3' => '',
        'Shipper' => {
          'Reference1' => mirraw_reference,
          'Reference2' => '',
          'AccountNumber' => aramex[:account_number],
          'PartyAddress' => aramex_address_hash(mirraw_details[:address]),
          'Contact' => aramex_contact_hash(mirraw_details[:contact]),
        },
        'Consignee' => {
          'Reference1' => '',
          'Reference2' => '',
          'AccountNumber' => '',
          'PartyAddress' => aramex_address_hash(customer_details[:address]),
          'Contact' => aramex_contact_hash(customer_details[:contact]),
        },
        'ThirdParty' => {
          'Reference1' => '',
          'Reference2' => '',
          'AccountNumber' => '',
          'PartyAddress' => aramex_address_hash(mirraw_details[:address]),
          'Contact' => aramex_contact_hash(mirraw_details[:contact]),
        },
        'ShippingDateTime' => Time.now.iso8601,
        'DueDate' => Time.now.iso8601,
        'Comments' => '',
        'PickupLocation' => '',
        'OperationsInstructions' => '',
        'AccountingInstrcutions' => '',
        'Details' => AramexApi.new.aramex_international_details_hash(pay_type, total, items, weight),
        'Attachments' => [{:FileName => '', :FileExtension => '', :FileContents => ''}],
        'TransportType' => '',
        'PickupGUID' => ''
      }
    }
  end




  # params
  # designer_order_id - integer
  # shipper_id - integer
  # tracking_number - string
  #
  # Attempts to create shipment object for designer_order
  #
  # Returns Hash

  def move_designer_orders_to_dispatched(shipper_name)
    (['rapid delivery']+DOMESTIC_PREPAID_COURIER_AUTOMATION['mirraw']).include?(shipper_name.downcase)  ? bulk_designer_orders.each(&:pickedup) : bulk_designer_orders.each(&:dispatched_by_designer)
    bulk_designer_orders.each{|dos| dos.check_track_and_add_events(shipper_name,dos.tracking_num,false)}
  end

  def self.dispatch_bulk_automated_shipment(shipper, designer, invoice_number, dos_ids)
    case shipper.name.try(:downcase)
    when 'delhivery'
      total = DesignerOrder.where(id: dos_ids).sum(:total)
      tracking, response = DesignerOrder.delhivery_fetch_awb(nil,designer, nil, 'mirraw', invoice_number, total)
    when 'xpress bees'
      tracking, response = Shipment.xpress_bees_create_domestic(nil, dos_ids, designer, invoice_number)
    else
      response = {'error'=>'Shipper not found'}
    end
    if response['error'].blank? && response['success']
      Shipment.create_non_automated(dos_ids, shipper.id, tracking, true, true, invoice_number)
    else
      error_message = response['error'].to_s + ' ' + response['rmk'].to_s
      DesignerOrder.where(id: dos_ids).update_all(state: 'pending', shipment_status: 'failed', shipment_error: error_message)
    end
  end

  def self.generate_international_invoice_details(designer_order_id, bulk_dispatch=false)
    designer_order = DesignerOrder.where(id: designer_order_id).preload(:order).first unless bulk_dispatch
    all_line_items = LineItem.sane_items.where(designer_order_id: designer_order_id).preload(:line_item_addons,design: :categories,variant: :option_type_values)
    ActiveRecord::Associations::Preloader.new.preload(all_line_items, [:designer_order, :order]) if bulk_dispatch
    all_line_items = all_line_items.where('rtv_quantity > ?', 0) if designer_order.try(:replacement_pending?)
    total_items  = all_line_items.size
    total_weight = total_items/10.0
    total_price, dos_payout_ratio  = {}, designer_order.try(:get_payout_ratio)

    invoice_items      = []
    all_line_items.each do |item|
      quantity = designer_order.try(:replacement_pending?) ? item.rtv_quantity : item.quantity
      items_weight = (total_weight/total_items).to_f * quantity
      addon_price = item.vendor_addon_items.to_a.sum{|addon| addon.snapshot_price(RETURN_NORMAL)}
      addon_text = item.vendor_addon_items.collect{|item| "[#{item.addon_type_value.name}]" }.join
      name         = item.design.categories.first.name.gsub('-', ' ').camelize
      name         += ' [sku: ' + item.design.design_code + ']' if item.design.design_code.present?
      name         += " : #{item.order.number}" if bulk_dispatch
      name         += ' [size: ' + item.variant.option_type_values.collect(&:name).last + ']' if (variant = item.variant).present? && variant.option_type_values.present?
      name         += addon_text
      name         += '[DNS]' if item.available_in_warehouse
      item_price   =  item.snapshot_price(RETURN_NORMAL) + addon_price
      item_price   = (item_price * (dos_payout_ratio || item.designer_order.get_payout_ratio)).round(2)
      items_price  = item_price * quantity
      sku = (item.variant_id.present? ? item.variant_id : item.design.design_code.present? ? item.design.design_code : item.design_id).to_s
      hsn_code,gst_rate = item.find_hscode_gst(item_price)
      total_price[item.designer_order_id] = item.designer_order.total.to_i
      invoice_items << { :name => name, :quantity => quantity, :price => item_price, :total_price => items_price,hsn_code: hsn_code,gst_rate: gst_rate, designable_type: item.design.designable_type, dos_id: item.designer_order_id,sku: sku}
    end

    shipment = Shipment.new(
      :order_id => designer_order.try(:order_id),
      :shipment_type => 'PREPAID',
      :payment_state => 'paid',
      :designer_order_id => designer_order.try(:id),
      :price => designer_order.try(:total) || total_price.values.sum,
      :weight => total_weight
    )
    items_and_invoice_data = [all_line_items,invoice_items,shipment]
  end

  def self.create_non_automated(designer_order_id, shipper_id, tracking_number, pickup_service = false, move_to_dispatched = true, invoice_number = nil)
    response = {:error => false}
    shipper = Shipper.find_by_id(shipper_id)
    bulk_dispatch = designer_order_id.is_a?(Array)
    designer_order = DesignerOrder.where(id: designer_order_id).preload(:order).first unless bulk_dispatch

    if bulk_dispatch || designer_order.present?
      all_line_items,invoice_items,shipment = self.generate_international_invoice_details(designer_order_id,bulk_dispatch)
      designer = all_line_items.first.designer_order.designer if bulk_dispatch

      shipment.shipper_id = shipper_id
      shipment.packer_id  = (designer_order.present? ? designer_order.designer.account.id : designer.account.id)
      shipment.number     = tracking_number
      shipment.automated  = pickup_service.present?
      shipment.track      = pickup_service.present?
      shipment.invoicer_id = shipment.packer_id
      shipment.mirraw_reference = designer_order.try(:shipment_reference) || "#{designer.id} - #{invoice_number}"
      shipment.invoice_number = invoice_number if bulk_dispatch

      if bulk_dispatch || designer_order.order.international? || designer_order.ship_to == 'mirraw'
        shipment.inbound_line_item_ids = all_line_items.collect(&:id)
      else
        shipment.line_item_ids = all_line_items.collect(&:id)
      end

      if !bulk_dispatch && shipper.present? && shipper.name.present? && ['city courier','tirupati courier'].include?(shipper.name.downcase)
        shipment.track = false
      end

      if shipment.save!
        if bulk_dispatch
          DesignerOrder.where(id: designer_order_id).update_all(bulk_shipment_id: shipment.id, shipment_status: 'created', tracking_partner: shipper.name, tracking_num: tracking_number)
          # shipment.sidekiq_delay(queue: 'critical').move_designer_orders_to_dispatched(shipper.name)
          SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(shipment.class.to_s, shipment.id, "move_designer_orders_to_dispatched", shipper.name)
          shipment.make_purchase_order_entry(designer_order_id, shipment.invoice_number, shipment.number, all_line_items.to_a.sum(&:quantity))
          # ShipmentsController.sidekiq_delay(queue: 'critical').generate_bulk_invoice(invoice_items, shipment.id, designer)
          SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async("ShipmentsController",
                                                                         nil, 
                                                                         "generate_bulk_invoice",
                                                                         invoice_items, 
                                                                         shipment.id, 
                                                                         {designer.class.to_s => designer.id}
                                                                        )
        else
          if move_to_dispatched
            pickup_service ? designer_order.pickedup : designer_order.dispatched_by_designer
          end
          designer_order.update_columns(shipment_status: 'created', tracking_partner: shipper.name, tracking_num: tracking_number)
          ShipmentsController.sidekiq_delay(queue: 'critical').generate_invoice(invoice_items, designer_order.order.number, shipment.id)
        end
        # Shipment.sidekiq_delay(queue:'critical').generate_rapid_delivery_label((designer_order.try(:order).try(:number) || invoice_number),
        #                                                                        (designer_order.try(:designer) || designer),
        #                                                                         shipment, 
        #                                                                         nil, 
        #                                                                         designer_order.warehouse_address_id
        #                                                                         ) if shipper.try(:name).try(:downcase) == 'rapid delivery' || pickup_service
        designer_order_obj = designer_order.try(:order).try(:number) || designer
        SidekiqDelayGenericJob.set({queue:'critical'}).perform_async("Shipment", 
                                                                    nil,
                                                                    "generate_rapid_delivery_label",
                                                                    (designer_order.try(:order).try(:number) || invoice_number),
                                                                    {designer_order_obj.class.to_s => designer_order_obj.id},
                                                                    {shipment.class.to_s => shipment.id},
                                                                    nil,
                                                                    designer_order.warehouse_address_id
                                                                  ) if shipper.try(:name).try(:downcase) == 'rapid delivery' || pickup_service
      else
        error_message = ''
        if shipment.errors.present? && shipment.errors.messages.present?
          shipment.errors.messages.each do |key, values|
            error_message = error_message + ' ' + key.to_s
            values.each {|value| error_message = error_message + ' ' + value.to_s }
          end
        end
        response[:error] = true
        response[:error_text] = error_message
        DesignerOrder.where(id: designer_order_id).update_all(state: 'pending', shipment_status: 'failed', shipment_error: error_message)
      end
    else
      response[:error] = true
      response[:error_text] = 'designer order not found'
    end    
    response
  end

  def self.create_replacement_shipment(designer_order_id, shipper_id, tracking_number, pickup_service = true)
    response = {:error => false}
    designer_order = DesignerOrder.find_by_id(designer_order_id)
    line_items = designer_order.line_items.dispatchable.where('rtv_quantity > ?', 0)
    order = designer_order.order
    if designer_order.present? && tracking_number.present?
      shipper = Shipper.find_by_id(shipper_id)
      rtv_shipment = RtvShipment.new(
        number: tracking_number, 
        shipper_name: shipper.name, 
        weight: line_items.size/10.0,
        shipment_type: 'replacement'
      )
      rtv_shipment.line_items << line_items
      if rtv_shipment.save
        designer_order.update_column(:recent_tracking_number, rtv_shipment.number)
        designer_order.completed_at.present? ? designer_order.replacement_completed : designer_order.replacement_dispatched
        # Shipment.sidekiq_delay(queue:'high').generate_rapid_delivery_label(order.number, designer_order.designer, nil, rtv_shipment, designer_order.warehouse_address_id) if shipper.try(:name).try(:downcase) == 'rapid delivery' || pickup_service
        SidekiqDelayGenericJob.set({queue:'high'}).perform_async("Shipment", 
                                                                  nil, 
                                                                  "generate_rapid_delivery_label", 
                                                                  order.number,
                                                                  {designer_order.designer.class.to_s => designer_order.designer.id},
                                                                  nil,
                                                                  {rtv_shipment.class.to_s => rtv_shipment.id},
                                                                  designer_order.warehouse_address_id
                                                                ) if shipper.try(:name).try(:downcase) == 'rapid delivery' || pickup_service
      else
        error_message = ''
        if rtv_shipment.errors.present? && rtv_shipment.errors.messages.present?          
          rtv_shipment.errors.messages.each do |key, values|
            error_message = error_message + ' ' + key.to_s
            values.each {|value| error_message = error_message + ' ' + value.to_s }
          end
        end
        response[:error] = true
        response[:error_text] = error_message
      end  
    else
      response[:error] = true
      response[:error_text] = 'Designer Order not found or tracking number not available.'
    end
    response
  end

  def self.bluedart_mass_tracking(numbers, shipper_id)
    if numbers.present?
      numbers = Array(numbers).flatten.collect(&:to_s)
      # numbers.each_slice(50) do |number_slice|
      numbers.each_slice(10) do |number_slice|
        tracking_response = bluedart_track_api(number_slice)
        if tracking_response[:error]
          Shipment.where(:shipper_id => shipper_id, :number => number_slice).update_all(:track_error => tracking_response[:error_text])
        else
          tracking_response[:results].each do |result|
            tracking_number = result['WaybillNo']
            shipment = Shipment.where(:shipper_id => shipper_id, :number => tracking_number).first
            if shipment.present?
              shipment.update_bluedart_shipment(result)
            end
          end
        end
      end
    end
  end

  # params
  # numbers - Array
  #
  # Fires track query to bluedart api
  #
  # Returns Hash
  def self.bluedart_track_api(numbers)
    numbers = Array(numbers).flatten.join(',')
    shipment_hash = {:error => false, :error_text => ''}
    bluedart = Mirraw::Application.config.bluedart
    bluedart_request_url = bluedart[:tracking_url].sub('{license_key}', bluedart[:tracking_lickey]).sub('{loginid}', bluedart[:loginid]).sub('{version}', '1').sub('{scans}', '0').sub('{awb_number}', numbers)
    response = HTTParty.get(bluedart_request_url)
    response_hash = Hash.from_xml(response.body)
    shipment_hash[:results] = response_hash['ShipmentData']['Shipment']
    shipment_hash[:results] = [shipment_hash[:results]] if shipment_hash[:results].is_a?(Hash)
    shipment_hash
  end

  # params
  # result - Hash
  #
  # Updates shipment with available data
  def update_bluedart_shipment(result)
    self.last_event_timestamp = DateTime.parse(result['StatusDate']) if result['StatusDate'].present?
    self.last_event = result['Status']
    self.estimated_delivery_timestamp = DateTime.parse(result['ExpectedDeliveryDate']) if result['ExpectedDeliveryDate'].present? && self.estimated_delivery_timestamp.blank?
    new_shipment_state = nil
    case result['StatusType']
    when 'IT'
      new_shipment_state = :in_transit
    when 'RT'
      new_shipment_state = :rto
    when 'UD'
      new_shipment_state = :delivery_exception
    when 'DL'
      self.delivered_on = last_event_timestamp
      self.delivered_to = result['ReceivedBy']
      new_shipment_state = :delivered
    end
    self.update_shipment_state(new_shipment_state)
  end

  def self.dtdc_mass_tracking(numbers)
    if numbers.present?
      numbers = Array(numbers).flatten.collect(&:to_s)
      # numbers.each_slice(25) do |number_slice|
      numbers.each_slice(10) do |number_slice|
        tracking_response = dtdc_track_api(number_slice)
        shipper = Shipper.find_by_name('DTDC')
        if tracking_response[:error]
          Shipment.where(:shipper_id => shipper.id, :number => number_slice).update_all(:track_error => tracking_response[:error_text])
        else
          tracking_response[:results].each do |result|
            tracking_number = result[:awbrefno]
            shipment = Shipment.where(:shipper_id => shipper.id).where('LOWER(number) = ?', tracking_number).first
            if shipment.present?
              shipment.update_dtdc_shipment(result)
            end
          end
        end
      end
    end
  end

  def self.dtdc_track_api(numbers)
    numbers = Array(numbers).flatten.join(',')
    shipment_hash = {:error => false, :error_text => ''}
    dtdc = Mirraw::Application.config.dtdc
    response = HTTParty.post(dtdc[:tracking_url], :headers => {'Content-Type' => 'application/x-www-form-urlencoded'}, :body => {:action => 'track', :Ttype => 'awb_no', :strCnno => numbers})
    if response.code == 200
      shipment_hash[:results], columns = [], []
      html_doc = Nokogiri::HTML(response.body)
      html_doc.css('table[summary="Tracking Result"] tr').each_with_index do |tr, index|
        if index == 0
          tr.css('th').each {|td| columns << td.text.downcase.gsub(/\W/,'').to_sym }
        else
          result = {}
          columns.each {|column| result[column] = nil }
          tr.css('td').each_with_index do |td, index|
            text = td.text.gsub(/[\r\n\t]/,'')
            if text.present?
              case columns[index]
              when :datetime
                result[:datetime] = DateTime.parse(text).advance(:minutes => -330)
              else
                result[columns[index]] = text.downcase.sub('notify me','')
              end
            end
          end
          shipment_hash[:results] << result
        end
      end
    else
      shipment_hash[:error] = true
      shipment_hash[:error_text] = "Response Code #{response.code}"
    end
    shipment_hash
  end

  def update_dtdc_shipment(result)
    self.last_event_timestamp = result[:datetime]
    self.last_event = "location #{result[:location]}"
    new_shipment_state = nil
    case result[:status]
    when 'delivered'
      self.delivered_on = self.last_event_timestamp
      new_shipment_state = :delivered
    when 'reverted'
      new_shipment_state = :rto
    when 'in transit'
      new_shipment_state = :in_transit
    end
    self.update_shipment_state(new_shipment_state)
  end

  def self.speedpost_mass_tracking(numbers, shipper_id)
    if numbers.present?
      numbers = Array(numbers).flatten.collect(&:to_s)
      numbers.each_slice(10) do |number|
        tracking_response = speed_post_track_api(number)
        if tracking_response[:error]
          Shipment.where(:shipper_id => shipper_id, :number => number).update_all(:track_error => tracking_response[:error_text], :track => false)
        else
          result = tracking_response[:result]
          tracking_number = result['ItemNumber'].try(:downcase)
          shipment = Shipment.where(:shipper_id => shipper_id).where('LOWER(number) = ?', tracking_number).first
          if shipment.present?
            shipment.update_speed_post_shipment(result)
          end
        end
      end
    end
  end

  def self.speed_post_track_api(number)
    shipment_hash = {:error => false, :error_text => ''}
    mashape = Mirraw::Application.config.mashape
    response = HTTParty.get(mashape[:speed_post_api], :headers => {"X-Mashape-Key" => "#{mashape[:key]}", "Accept" => "application/json"}, :query => {:itemno => number})
    if response.code == 200 && response.headers['content-type'].match('json')
      response_data = JSON.parse(response.body)
      if response_data['error'].present?
        shipment_hash[:error] = true
        shipment_hash[:error_text] = response_data['error']    
      else
        shipment_hash[:result] = response_data
      end
    else
      shipment_hash[:error] = true
      shipment_hash[:error_text] = "Response Code #{response.code}"
    end
    shipment_hash
  end

  def update_speed_post_shipment(result)
    if result['DetailsTable'].present?
      last_scan = result['DetailsTable'].first
      result['DetailsTable'].each do |current_scan|
        last_scan['DateTime'] = (last_scan['Date'] + ' ' + last_scan['Time']).to_datetime
        current_scan['DateTime'] = (current_scan['Date'] + ' ' + current_scan['Time']).to_datetime
        last_scan = current_scan if current_scan['DateTime'] > last_scan['DateTime']
      end
      self.last_event_timestamp = last_scan['DateTime']
      self.last_event = last_scan['Status']
      new_shipment_state = nil
      case last_scan['Status']
      when 'Item Received', 'Bag Opened', 'Bag Received'
        new_shipment_state = :in_transit
      else
        if last_scan['Status'].match('Item Delivered').present?
          self.delivered_on = Date.parse result['DeliveredOn']
          self.delivered_to = result['DeliveredAt']
          new_shipment_state = :delivered
        end
      end
      self.update_shipment_state(new_shipment_state)
    end
  end

  def self.fedex_mass_tracking(numbers, shipper_id)
    common_update_shipment_state('FEDEX',numbers,shipper_id)
  end

  def self.common_update_shipment_state(shipment_name, numbers , shipper_id)
    shipments = Shipment.where(:number => numbers).where(shipper_id: shipper_id).trackable_shipments
    shipments.each {|s| s.update_state(shipment_name)}
  end 

  def self.xindus_mass_tracking(numbers, shipper_id)
    common_update_shipment_state('Xindus',numbers,shipper_id)
  end

  def self.shipdelight_mass_tracking(numbers, shipper_id)
    if numbers.present?
      numbers = Array(numbers).flatten
      url = Mirraw::Application.config.ship_delight_tracking_url
      numbers.each_slice(10) do |number_slice|
        begin
          tracking_info = ShipDelightApi::ShipDelight.track_ship_delight(url,number_slice.join(','))
          tracking_info.each do |result|
            if (detail = result['scan_detail']).present?
              shipment = Shipment.where(shipper_id: shipper_id, number: detail.last['awbno']).first
              shipment.get_ship_delight_shipment_state(result['scan_detail'].last) if shipment.present?
            elsif (detail = result['airwaybilno']).present?
              shipment = Shipment.where(shipper_id: shipper_id,number: detail.scan(/\d+/).first).first
              shipment.update_column(:track_error,'Invalid shipment number') if shipment.present?
            end
          end
        rescue => error
          Shipment.where(number: number_slice).update_all(track_error:error.message.to_s.first(240))
          next
        end
      end
    end
  end

  def self.dhl_mass_tracking(numbers, shipper_id)
    shipments = Shipment.where(:number => numbers).where(shipper_id: shipper_id).trackable_shipments
    shipments.each {|s| s.update_state('DHL')}
  end

  def self.ups_mass_tracking(numbers, shipper_id)
    shipments = Shipment.where(:number => numbers).where(shipper_id: shipper_id).trackable_shipments
    shipments.each {|s| s.update_state('UPS')}
  end

  def self.xpressbees_mass_tracking(numbers, shipper_id)
    xpress_bees_urls = Mirraw::Application.config.xpress_bees
    numbers.each_slice(10) do |num|
      shipments = Shipment.where(number: num).where(shipper_id: shipper_id)
      track_request = { 'XBkey' => xpress_bees_urls[:get_XBkey], 'AWBNo' => num.join(',')}
      track_response = JSON.parse(HTTParty.post(xpress_bees_urls[:track_url], body: track_request.to_json, headers: {'Content-Type' => 'application/json'}).body)
      shipments.each do |s|  
        shipment_track_response = track_response['GetBulkShipmentStatus'].find{|info| info['AWBNO'] == s.number}
        if shipment_track_response && shipment_track_response['ReturnMessage'].try(:downcase) == 'successful'
          s.update_state('Xpress Bees', shipment_track_response)
        else
          s.update_column(:track_error, shipment_track_response.try(:[], 'ReturnMessage'))
        end
      end
    end
  end

  def self.dhlecom_mass_tracking(numbers,shipper_id)
    numbers.each_slice(20) do |num|
      begin
        shipments = Shipment.where(:number => num).where(shipper_id: shipper_id).trackable_shipments
        dhl_ecom_urls = Mirraw::Application.config.dhl_ecom
        token_response = HTTParty.get("#{dhl_ecom_urls[:token_url]}?clientId=#{dhl_ecom_urls[:credentials]['client_id']}&password=#{dhl_ecom_urls[:credentials]['password']}&returnFormat=json")
        if token_response['accessTokenResponse']['responseStatus']['code'] == '100000'
          token = token_response['accessTokenResponse']['token']
          header = {
            messageType: "TRACKITEM",
            accessToken: token,
            messageDateTime: DateTime.now.strftime("%Y-%m-%dT%H:%M:%S%Z"),
            messageVersion: "1.0",
            messageLanguage: "en"
          }

          body = {
            customerAccountId: nil,
            pickupAccountId: dhl_ecom_urls[:credentials]['pickup_account_id'],
            soldToAccountId: dhl_ecom_urls[:credentials]['sold_to_account_id'],
            trackingReferenceNumber: num 
          }

          tracking_request = {
            trackItemRequest: {
              hdr: header,
              bd: body
            }
          }

          tracking_response = HTTParty.post(dhl_ecom_urls[:tracking_url],body:tracking_request.to_json,headers: {'Content-Type' => 'application/json'})
          response = tracking_response['trackItemResponse']['bd']
          if response['responseStatus']['code'] == '200' && response['responseStatus']['message'] == 'SUCCESS'
            shipments.each {|s| s.update_state('DHL ECOM',response['shipmentItems'].find{|a| a['shipmentID'] == s.number})} 
          else
            shipments.update_all(track_error:response['responseStatus']['messageDetails'][0]['messageDetail'])
          end
        else
          raise 'Token Expired While In Execution.'
        end
      rescue => error
        shipments.update_all(track_error:error.message)
        next
      end
    end
  end

  def self.create_dhl_shipment(ddp,is_csb_used, order, shipment, quote_request, invoice_items, shipping_charges, items_price_total, request_hash)
    label = DhlApi.new.createShipment(request_hash, is_csb_used)
    shipment = order.shipments.new(shipment)
    shipment.assign_attributes(number: label[:tracking_number], courier_label_url: label[:url], delivery_duty_paid: ddp)
    params_list = {payload: request_hash, ddp: ddp, is_csb_used: is_csb_used, order_id: order, shipment_id: shipment, quote_request: quote_request, invoice_items: invoice_items, shipping_charges: shipping_charges, items_price_total: items_price_total}
    #OrderMailer.report_mailer("DHL Service Parameters","REQ Params:. #{params_list}",{'to_email' => ['<EMAIL>'],'from_email_with_name' => ['<EMAIL>']},{}).deliver
    if shipment.save!
      order.update_attributes(tracking_number: shipment.number, courier_company: shipment.shipper.name )
      shipment.download_label
      shipment.update_dhl_charges(quote_request)
      ShipmentsController.generate_invoice(invoice_items, order.number, shipment.id, shipping_charges, items_price_total)
      order.remove_tags_skip_callback('shipment_error')
      order.reload
      order.other_details['dhl_status'] = 'Successful'
      order.save(validate: false)
      shipment.update_dhl_shipment_count
      order.create_clickpost_tracking(shipment, invoice_items)
    end
  rescue => error
    order.add_tags_skip_callback('shipment_error')
    order.other_details['dhl_status'] = error.message
    order.save(validate: false)
    ExceptionNotifier.notify_exception(
      Exception.new("DHL Shipment error-#{order.number}"),
      data: {payload: request_hash, ddp: ddp, is_csb_used: is_csb_used, order_id: order, shipment_id: shipment, quote_request: quote_request, invoice_items: invoice_items, shipping_charges: shipping_charges, items_price_total: items_price_total}
    )
    # OrderMailer.report_mailer("#{shipment.shipper.name} Api Shipment Automation Error","#{request_hash}",{'to_email' => ['<EMAIL>'],'from_email_with_name' => ['<EMAIL>']},{}).deliver
    order.add_notes_without_callback(error.message, 'Shipment Automation Error')
  end

  def self.atlantic_shipment(order, shipment_request, shipment, invoice_items, shipping, items_price_total)
    order.skip_before_filter = true
    label_details = AtlanticApi.new.create_shipment(shipment_request)
    if label_details["Response"]["RefNo"] == shipment[:mirraw_reference]
      label_content = Base64.decode64(label_details["Response"]["Pdfdownload"])
      shipment = order.shipments.new(shipment)
      shipment.number = label_details["Response"]["AWBNo"]
      shipment.invoice_data['cod_value'] = items_price_total if order.pay_type == COD && order.international?
      order.update_attributes(tracking_number: shipment.number, courier_company: shipment.shipper.name ) if shipment.save
      shipment.add_label(label_content)
      ShipmentsController.generate_invoice(invoice_items, order.number, shipment.id, shipping, items_price_total)
      order.remove_tags_skip_callback('shipment_error')
      order.reload
      order.other_details['atlantic_status'] = 'Successful'
      order.save(validate: false)
      order.create_clickpost_tracking(shipment, invoice_items)
    else
      raise label_details[:error_data][:error_list].values.join(', ')
    end
  rescue => error
    AtlanticApi.new.notify("Atlantic Shipment Error", "Atlantic Shipment and Label Generation Failed", shipment_request, label_details, error)
    order.add_tags_skip_callback('shipment_error')
    order.other_details['atlantic_status'] = error.message
    order.save(validate: false)
    # OrderMailer.report_mailer("#{shipment.shipper.name} Api Shipment Automation Error","#{shipment_request}",{'to_email' => ['<EMAIL>'],'from_email_with_name' => ['<EMAIL>']},{}).deliver
    order.add_notes_without_callback(error.message, 'Shipment Automation Error')
  end

  def self.atlantic_mass_tracking(numbers, shipper_id)
    response_mapper = {
      'In Transit'  => :in_transit,
      'OK DELIVERY' => :delivered,
    }
    Shipment.where(number: numbers, shipper_id: shipper_id).find_in_batches(batch_size: 50) do |shipments|
      shipments.each do |shipment|
        begin
          response = AtlanticApi.new.track(shipment.number)
          response = response["Response"]
          if status = response["Tracking"].first["Status"]
            if last_event_data = response["Events"].first
              shipment.last_event_timestamp = Time.zone.parse("#{last_event_data["EventDate"]} #{last_event_data["EventTime1"]}") || Time.current
              shipment.last_event = last_event_data["Status"]
            end
            if response_mapper[status] == :delivered
              shipment.delivered_on = Time.zone.parse("#{response["Tracking"].first["DeliveryDate"]} #{response["Tracking"].first["DeliveryTime"]}") || Time.current
            end
            shipment.update_shipment_state(response_mapper[status]) if response_mapper[status]
          else
            shipment.update_column(:track_error,'Invalid Shipment number')
          end
        rescue => error
          AtlanticApi.new.notify("Atlantic Track Error", "Track Shipment Failed", shipment, response, error)
        end
      end
    end
  end


  def self.rapiddelivery_mass_tracking(numbers, shipper_id)
    credentials = Mirraw::Application.config.rapid_delivery
    shipments = Shipment.where(:number => numbers).where(shipper_id: shipper_id).find_each(batch_size: 20) do |ship| 
      track_url = "#{credentials[:track_url]}client=#{credentials[:client]}&token=#{credentials[:token]}&waybill=#{ship.number}" 
      tracking_response = HTTParty.post(track_url)
      if tracking_response.parsed_response.present? && ['token not sent','error: 201'].exclude?(tracking_response.parsed_response.try(:downcase))
        ship.update_state('RAPID DELIVERY',JSON.parse(tracking_response.parsed_response))
      else
        ship.update_column(:track_error,error.message)
      end 
    end
  end

  def self.fedexmirraw_mass_tracking(numbers, shipper_id)
    shipments = Shipment.where(:number => numbers).where(shipper_id: shipper_id)
    shipments.each {|s| s.update_state('FEDEX-MIRRAW')}
  end

  #Pass awb numbers to track status of shipment.
  def self.gati_mass_tracking(numbers, shipper_id)
    shipments = Shipment.where(:number => numbers).where(shipper_id: shipper_id)
    track_gati_awb(shipments.map(&:number))
  end
  
  def get_skynet_shipment_state(tracking_status)
    new_shipment_state = nil
    if (progress = tracking_status["Progress"][0]).present?
      self.last_event_timestamp = progress['actiondate']
      self.last_event = progress['exception'].to_s + ' ' + progress['location'].to_s
      case tracking_status['status'].try(:downcase)
      when 'in transit'
        new_shipment_state = :in_transit
      # when '954'
      #   new_shipment_state = :out_for_delivery
      # when '913'
      #   new_shipment_state = :lost
      # when '925','506','285'
      #   new_shipment_state = :rto
      when 'delivered'
        self.delivered_on = Date.parse(tracking_status['delivery_date'])
        new_shipment_state = :delivered
      # when '565','379','526','784','458','859','521','478','528','589','625','659','796','856','874','153','245','326','383','362','304','992'
      #   new_shipment_state = :delivery_exception
      end
      self.update_shipment_state(new_shipment_state)
    end
  end

  def self.skynet_mass_tracking(numbers, shipper_id)
    if numbers.present?
      url = Mirraw::Application.config.skynet_baseurl
      shipments = Shipment.where(shipper_id: shipper_id,number: numbers).group_by{|s| s.number}
      numbers.each_slice(10) do |number_slice|
        response = HTTParty.post("#{url}/package/tracknew",body: {'tracking_num'=> number_slice.join(',')})
        tracking_info = response.parsed_response
        if tracking_info['status'].downcase == 'success' && (details = tracking_info['details']).present? && details['Error'].blank?
          details['Shipments'].each do |shipment_detail|
            shipment = shipments[shipment_detail['awbno']]
            shipment.first.get_skynet_shipment_state(shipment_detail) if shipment.present?
          end
        else
          Shipment.where(:shipper_id => shipper_id, :number => number_slice).update_all(track_error: tracking_info['message'] || tracking_info['error'])
        end
      end
    end
  end

  # def self.track_shipments
  #   shipper_ids = Shipper.where(name: ['Bluedart','Fedex','Fedex-Mirraw','Aramex','Delhivery','SpeedPost','Gati','Dhl']).pluck(:id)
  #   shipper_name_shipments = self.select('shippers.name as name').select(:number).joins(:shipper).where('shipment_state NOT IN (?)', ['delivered', 'rto']).where(shipper_id: shipper_ids, track: true).group_by{|s| s['name']}
  #   shipper_name_shipments.each do |shipper_name, shipments|
  #     numbers = shipments.collect(&:number)
  #     shipper_name_trimmed = shipper_name.downcase.gsub(/[^a-z]/,'')
  #     method_to_invoke = (shipper_name_trimmed + '_mass_tracking').to_sym
  #     self.delay(:priority => 1).send(method_to_invoke, numbers) if self.respond_to?(method_to_invoke)
  #   end
  # end
  # input params - designer_order_id - integer
  #
  # Creates bluedart shipment
  #
  # Returns
  def self.bluedart_create_domestic(designer_order_id)
    designer_order = DesignerOrder.find_by_id(designer_order_id)
    order = designer_order.order
    if designer_order.present?
      weight = 0.1
      total, cod_charge, invoice_items = designer_order.shipment_attributes
      shipper = Shipper.find_by_name('Bluedart')
      shipment = Shipment.new(
        shipper_id: shipper.id, order_id: order.id, payment_state: 'unpaid',
        mirraw_reference: designer_order.shipment_reference,
        designer_order_id: designer_order.id
      )
      invoice_data = Shipment.calculate_domestic_invoice(order, invoice_items, false, designer_order)
      total = invoice_data[:item_total].to_i
      shipment.price = total
      params = self.bluedart_shipment_request_hash(designer_order, total, invoice_items, weight)
      response = Bluedart::Shipment.new(params).response
      
      unless response[:error]
        response[:weight] = weight
        response[:cod_charge] = cod_charge
        response[:shipper] = shipper
        response[:invoice_items] = invoice_items
        response[:total] = total
        response[:label] = response[:content][:awb_print_content]
        response[:awb_no] = response[:content][:awb_no]
      end

      self.update_designer_order(designer_order, response, shipment, invoice_data)
    end
  end

  # invoice items array for generating invoice
  def self.generate_domestic_invoice_details(designer_order)
    invoice_items = Array.new
    cod_amount = 0
    order      = designer_order.order

    cod_charge = 0
    cod_charge = designer_order.calculate_domestic_shipping_charge
    all_items = designer_order.line_items.not_canceled
    single_cod_charge  = cod_charge / all_items.sum(&:quantity).to_f
    all_items.each do |item|
      addon_text = item.vendor_addon_items.collect{|item| "[#{item.addon_type_value.name}]" }.join
      designable_type = item.design.designable_type
      name        = item.design.categories.first.name.gsub('-', ' ').camelize
      name        += ' [sku: ' + item.design.design_code + ']' if item.design.design_code.present?
      name        += addon_text
      # addon_price = item.vendor_addon_items.to_a.sum{|addon|addon.snapshot_price(RETURN_NORMAL)}
      if order.cod? && order.currency_rate_market_value.present? && designer_order.ship_to != "mirraw"
        price_per_item = order.international_cod_price(item.price_with_addons)
      else
        price_per_item = item.price_with_addons
      end
      items_price =  price_per_item* item.quantity
      cod_amount  += items_price
      hsn_code,gst_rate = item.find_hscode_gst(price_per_item)
      sku = (item.variant_id.present? ? item.variant_id : item.design.design_code.present? ? item.design.design_code : item.design_id).to_s
      invoice_items << { name: name, quantity: item.quantity, price: price_per_item, total_price: items_price,gst_rate: gst_rate,hsn_code: hsn_code, designable_type: designable_type, sku: sku}
      invoice_items << { name: 'Shipping Charges', quantity: item.quantity, price: single_cod_charge, total_price: (single_cod_charge * item.quantity).round(2),hsn_code: '',gst_rate: gst_rate,sku: sku} if single_cod_charge > 0
    end
    shipment = Shipment.new(
      :order_id => order.id,
      :mirraw_reference => designer_order.shipment_reference,
      :shipment_type => order.pay_type == COD ? 'COD' : 'PREPAID',
      :payment_state => 'unpaid',
      :designer_order_id => designer_order.id,
      :weight => designer_order.line_items.sane_items.count/10.0,
      :cod_charge => cod_charge
    )
    invoice_data = Shipment.calculate_domestic_invoice(order, invoice_items, false, designer_order)
    total = invoice_data[:item_total].to_i
    shipment.price = total
    invoice_items_and_total = [invoice_items, total, cod_amount, cod_charge, invoice_data, shipment]
  end

  def self.gati_credentails
    gati_credntails = {}
    gati_credntails["url"] = Mirraw::Application.config.gati_api_url
    gati_credntails["custcode"] = Mirraw::Application.config.gati_custcode
    gati_credntails
  end

  def self.gati_consignee_shipment_details(designer_order, total, gati_awb_number)
    shipper = Shipper.where('lower(name) =?','gati').first
    order = designer_order.order
    designer = designer_order.designer
    pickup_date = designer_order.pickup.strftime("%d-%m-%y %H:%M")
    cust_ship_details = {}
    cust_ship_details["pickuprequest"] = pickup_date
    cust_ship_details["docket_no"] = gati_awb_number
    cust_ship_details["delivery_stn"] = Courier.where(pincode: designer_order.order.pincode).first.delivery_stn_code
    cust_ship_details["goods_code"] = 'ECOM'
    cust_ship_details["decl_cargo_val"] = total
    cust_ship_details["actual_wt"] = '0.1'
    cust_ship_details["charged_wt"] = '0.1'
    cust_ship_details["order_number"] = order.number
    cust_ship_details["cod_amt"] = total
    cust_ship_details["cod_in_favour_of"] = 'G'
    cust_ship_details["recevier_code"] = '99999'
    cust_ship_details["recevier_name"] = order.name
    cust_ship_details["recevier_add1"] = order.street
    cust_ship_details["recevier_add2"] = order.buyer_state
    cust_ship_details["recevier_add3"] = order.country
    cust_ship_details["recevier_city"] = order.city
    cust_ship_details["receiver_phone"] = order.phone
    cust_ship_details["receiver_email"] = order.email
    cust_ship_details["receiver_pincode"] = order.pincode
    cust_ship_details["number_of_pkgs"] = '1'
    cust_ship_details["receiver_mob"] = order.phone
    cust_ship_details["receiver_del_date"] = ''
    cust_ship_details["special_instruction"] = ''
    cust_ship_details["vendor_code"] = designer.gati_vendor_code
    cust_ship_details["prod_ser_code"] = '1'
    cust_ship_details
  end

  def self.ship_delight_consignee_shipment_details(designer_order,cod_charge)
    order, designer = designer_order.order, designer_order.designer
    collectable_value = designer_order.total + cod_charge
    all_line_items = designer_order.line_items.sane_items
    product_value = all_line_items.to_a.sum(&:snapshot_price) + cod_charge
    order_pickup_details = designer.get_order_pickup_details(designer_order, order)
    pickup_details = {
      "vendor_name"=> order_pickup_details[:company], 
      "vendor_address"=> order_pickup_details[:address], 
      "vendor_city"=> order_pickup_details[:city],
      "pickup_pincode"=> order_pickup_details[:pincode], 
      "vendor_phone1"=> order_pickup_details[:phone]
    }      

    params = {
               "api_key"=> Mirraw::Application.config.ship_delight_api_key, 
               "transaction_id"=>"", 
               "order_no"=> designer_order.shipment_reference,
               "consignee_first_name"=> order.name,
               "consignee_last_name"=>"", 
               "consignee_address1"=> order.street,
               "consignee_address2"=>"", 
               "destination_city"=> order.city,
               "destination_pincode"=> order.pincode, 
               "state"=> order.buyer_state, 
               "telephone1"=> order.phone, 
               "telephone2"=> "",                
               "pay_type"=> "COD", 
               "item_count" => all_line_items.size,
               "item_description"=> all_line_items.joins(:design).pluck('designs.title').join(','),
               "qty"=>"1", 
               "collectable_value"=> collectable_value,
               "cod_charges" => order.cod_charge,
               "product_value"=> product_value,
               "actual_weight"=> "0.1", 
               "volumetric_weight"=> "0.1", 
               "length"=> "", 
               "breadth"=> "", 
               "height"=> ""
              }
    params.merge(pickup_details)
  end

  def self.ship_delight_create_domestic(designer_order)
    url = Mirraw::Application.config.ship_delight_api_url
    order = designer_order.order    
    invoice_items, total, cod_amount, cod_charge, invoice_data, shipment = self.generate_domestic_invoice_details(designer_order)
    mirraw_reference = designer_order.shipment_reference
    designer_acc_id = designer_order.designer.account.id
    params = self.ship_delight_consignee_shipment_details(designer_order,cod_charge)
    result = ShipDelightApi::ShipDelight.create_ship_delight(url, params)
    if result.present?
      if result["response"][0]["error"].present?
        designer_order.failed_shipment(result["response"][0]["error"])
        designer_order.save
      else
        shipper = Shipper.find_by_name('Ship Delight')
        shipment.shipper_id = shipper.id
        shipment.packer_id = designer_acc_id
        shipment.invoicer_id = designer_acc_id
        shipment.number = result["response"][0]["awbno"]
        if shipment.save
          # self.sidekiq_delay(queue: 'critical').generate_invoice(shipper, shipment, designer_order, invoice_items, order, invoice_data)
          SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(self.class.to_s,
                                                                         self.id, 
                                                                         "generate_invoice", 
                                                                        {shipper.class.to_s => shipper.id},
                                                                        {shipment.class.to_s => shipment.id},
                                                                        {designer_order.class.to_s => designer_order.id},
                                                                        invoice_items,
                                                                        {order.class.to_s => order.id},
                                                                        invoice_data 
                                                                      )
        end
      end
    end
    rescue Net::ReadTimeout => e
    designer_order.failed_shipment(e.message)
    designer_order.skip_before_after_filter = true
    designer_order.save 
  end

  def self.generate_invoice(shipper, shipment, designer_order, invoice_items, order, invoice_data)
    ShipmentsController.generate_invoice(invoice_items, order.number, shipment.id, invoice_data: invoice_data)
    shipment.ship_delight_label
    designer_order.tracking_partner = shipper.name
    designer_order.shipper_id = shipper.id
    designer_order.tracking_num = shipment.number
    designer_order.shipment_status = 'created'
    designer_order.shipment_error = nil
    designer_order.save   
  end

  def self.make_shipment(shipper, order, designer_order, total, result, cod_charge, designer_acc_id, mirraw_reference)
    shipment = Shipment.create(
      :shipper_id => shipper.id,
      :order_id => order.id,
      :packer_id => designer_acc_id,
      :invoicer_id => designer_acc_id,
      :mirraw_reference => mirraw_reference,
      :shipment_type => order.pay_type == COD ? 'COD' : 'PREPAID',
      :payment_state => 'unpaid',
      :designer_order_id => designer_order.id,
      :weight => designer_order.line_items.sane_items.count/10.0,
      :price => total,
      :number => result["response"][0]["awbno"],
      :cod_charge => cod_charge
    )
  end


  def self.gati_create_domestic(designer_order)
    shipper_gati = Shipper.where('lower(name) =?','gati').first
    awb_number = AwbNumber.number_available(shipper_gati.id,true).first
    order = designer_order.order
    if awb_number.present?
      invoice_items, total, cod_amount, cod_charge, invoice_data, shipment = self.generate_domestic_invoice_details(designer_order)
      cust_ship_details = self.gati_consignee_shipment_details(designer_order, total, awb_number.number)
      gati_credentials = self.gati_credentails
      mirraw_reference = designer_order.shipment_reference
      response = GatikweApi::Gati.create_gati_config(cust_ship_details,gati_credentials)
      designer_acc_id = designer_order.designer.account.id
      if response.present?
        if response["response"]["result"]=="failed"
          designer_order.tracking_partner = nil
          designer_order.pickup = nil
          designer_order.state  = 'pending'
          designer_order.shipment_status = 'failed'
          designer_order.shipment_error = response["response"]["errmsg"]
          designer_order.save
        else
          shipper = Shipper.find_by_name('Gati')
          shipment.shipper_id = shipper.id
          shipment.packer_id  = designer_acc_id
          shipment.invoicer_id= designer_acc_id
          shipment.courier_label_url = response[:label_url].present? ? response[:label_url] : nil
          shipment.number = awb_number.number
          if shipment.save
            awb_number.available = false
            awb_number.save!
            ShipmentsController.sidekiq_delay(queue: 'critical').generate_invoice(invoice_items, order.number, shipment.id, invoice_data: invoice_data)
            # shipment.sidekiq_delay(queue: 'critical').gati_label
            SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(shipment.class.to_s, shipment.id, "gati_label")
            designer_order.shipper_id = shipper.id
            designer_order.tracking_partner = shipper.name
            designer_order.tracking_num = shipment.number
            designer_order.shipment_status = 'created'
            designer_order.shipment_error = nil
            designer_order.save
          end
        end
      end
    end
  end

  def gati_label
    order = self.order
    pdf_content =  ActionController::Base.new().render_to_string(
      :template => '/shipments/gati_label',
      :layout   => false,
      :locals   => {:@order => order, :@shipment => self}
    )
    label = WickedPdf.new.pdf_from_string(pdf_content)
    self.add_label(label)
  end

  def ship_delight_label
    order = self.order
    pdf_content =  ActionController::Base.new().render_to_string(
      :template => '/shipments/ship_delight_label',
      :layout   => false,
      :locals   => {:@order => order, :@shipment => self}
    )
    label = WickedPdf.new.pdf_from_string(pdf_content)
    self.add_label(label)
  end

  def skynet_label
    if (order = self.order).present?
      pdf_content =  ActionController::Base.new().render_to_string(
        :template => '/shipments/skynet_label',
        :layout   => false,
        :locals   => {:@order => order, :@shipment => self}
      )
      label = WickedPdf.new.pdf_from_string(pdf_content)
      self.add_label(label)
    end
  end

  # input params
  # designer_order - DesignerOrder object
  # total - Integer
  # cod_amout - Integer
  # invoice_items - Array
  # weight - Numeric
  #
  # Creates request Hash for Bluedart Shipment API
  #
  # Returns Hash
  def self.bluedart_shipment_request_hash(designer_order, total, invoice_items, weight)
    order = designer_order.order
    customer_code_key_suffix = designer_order.ship_as_cod? ? 'COD' : 'PREPAID'
    params = {
      shipper_details: self.bluedart_shipper_hash(designer_order, customer_code_key_suffix),
      consignee_details: self.bluedart_consignee_hash(order), services: self.bluedart_services_hash(designer_order, order, total, weight),
      mode: Rails.env.production? ? 'prod' : 'dev', creds: self.bluedart_creds_hash}
    params
  end

  # input params
  # designer_order - DesignerOrder object
  # customer_code_key_suffix - String
  #
  # Creates shipper Hash for Bluedart Shipment API
  #
  # Returns Hash
  def self.bluedart_shipper_hash(designer_order, customer_code_key_suffix)
    pickup_details = designer_order.designer.get_order_pickup_details(designer_order, designer_order.try(:order))
    params = {
      customer_code: '', customer_name: pickup_details[:name],
      address: pickup_details[:address], origin_area: '',
      customer_pincode: pickup_details[:pincode],
      customer_telephone: pickup_details[:phone],
      customer_mobile: '', customer_email_id: pickup_details[:email], sender: '',
      vendor_code: '', isToPayCustomer: false
    }
    params[:origin_area] = Shipment.bluedart_pincode_details(params[:customer_pincode])[:content][:area_code]
    params[:customer_code] = SystemConstant.get('BLUEDART_CUSTOMER_CODE_' + customer_code_key_suffix)
    params
  end

  # input params
  # order - Order object
  #
  # Creates consignee Hash for Bluedart Shipment API
  #
  # Returns Hash
  def self.bluedart_consignee_hash(order)
    params = {
      consignee_name: order.name, address: order.street, consignee_pincode: order.pincode,
      consignee_telephone: order.phone, consignee_mobile: '', consignee_attention: ''
    }
    if order.international?
      _, shipping_telephone, shipping_address_1, _, _, shipping_pincode, _, _ = order.get_warehouse_shipping_address
      params[:address] = shipping_address_1
      params[:consignee_pincode] = shipping_pincode
      params[:consignee_telephone] = shipping_telephone
    end
    params
  end

  # input params
  # designer_order - DesignerOrder object
  # order - Order object
  # total - Integer
  # weight - Numeric
  #
  # Creates services Hash for Bluedart Shipment API
  #
  # Returns Hash
  def self.bluedart_services_hash(designer_order, order, total, weight)
    datetime = Time.zone.now.advance(hour: 2)
    params = {
      piece_count: 1, actual_weight: weight, pack_type: '',
      invoice_no: designer_order.id, special_instruction: '', p_d_f_output_not_required: false,
      declared_value: '', credit_reference_no: "#{order.number}D#{designer_order.designer_id}", dimensions: [{height: 1, breadth: 1, count: 1, length: 1}],
      pickup_date: datetime.to_date, pickup_time: "#{datetime.hour}#{datetime.min}", commodities: ['EthnicWear'], product_type: 'Dutiables',
    }

    params[:collactable_amount] = params[:declared_value] = total

    if order.cod?
      params[:product_code] = SystemConstant.get('BLUEDART_COD_PRODUCT_CODE')
      params[:sub_product_code] = SystemConstant.get('BLUEDART_COD_SUB_PRODUCT_CODE')
    else
      params[:product_code] = SystemConstant.get('BLUEDART_PRODUCT_CODE')
      params[:sub_product_code] = SystemConstant.get('BLUEDART_SUB_PRODUCT_CODE')
    end
    params
  end

  # input params
  #
  # Creates creds Hash for Bluedart Shipment API
  #
  # Returns Hash
  def self.bluedart_creds_hash    
    params = {}
    params[:login_id] = SystemConstant.get('BLUEDART_LOGIN_ID')
    params[:license_key] = SystemConstant.get('BLUEDART_SHIPPING_API_LICENSE_KEY')
    params
  end

  # input params
  # pincode - Integer
  #
  # Makes request for GetServicesForPincode - Bluedart
  #
  # Returns Hash
  def self.bluedart_pincode_details(pincode)
    params = {pincode: pincode, mode: Rails.env.production? ? 'prod' : 'dev', creds: self.bluedart_creds_hash}
    request = Bluedart::PincodeService.new(params).response
  end

  # input params
  # designer_order - DesignerOrder object
  # response - Hash
  #
  # Updates designer order and conditionally creates a shipment
  #
  # Returns Boolean
  def self.update_designer_order(designer_order, response, shipment, invoice_data)
    if response[:error]
      designer_order.shipment_create_failed(response[:error_text])
      designer_order.save
    else
      self.create_shipment_designer_order(designer_order, response, shipment, invoice_data)
    end
  end

  # input params
  # designer_order - DesignerOrder object
  # response - Hash
  #
  # Creates Shipment object
  #
  # Returns Boolean
  def self.create_shipment_designer_order(designer_order, params, shipment, invoice_data)
    account_id = designer_order.designer.account.id
    order = designer_order.order
    shipment.weight = params[:weight]
    shipment.number = params[:awb_no]
    shipment.cod_charge = params[:cod_charge]
    shipment.packer_id = shipment.invoicer_id = account_id
    shipment.courier_label_url = params[:label_url].present? ? params[:label_url] : nil
    shipment.shipment_type = order.pay_type == COD ? 'COD' : 'PREPAID'
    shipment.save
    # shipment.sidekiq_delay(queue: 'critical').generate_tofrom_label
    SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(shipment.class.to_s, shipment.id, "generate_tofrom_label")
    if shipment.courier_label_url.present?
      # shipment.sidekiq_delay(queue: 'critical').download_label
      SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(shipment.class.to_s, shipment.id, "download_label")
    end
    ShipmentsController.sidekiq_delay(queue: 'critical').generate_invoice(params[:invoice_items], order.number, shipment.id, invoice_data: invoice_data)
    designer_order.shipment_create_pass(shipment)
    designer_order.save
  end

  # input params - none
  #
  # Sends mail to shippers about pickup locations
  #
  # Returns Boolean
  def self.mail_pickup_list
    if SystemConstant.get('SHIPPER_PICKUP_MAIL') == '1'
      bet_last_2days = Time.now.advance(days: -2)..Time.now
      s_clause = 'designers.*, COUNT(shipments.id) as shipments'
      g_clause = 'designers.id'
      Shipper.where(pickup_notify: true).where{ email != nil }.each do |shipper|
        w_clause = {shipments: {created_at: bet_last_2days, shipper_id: shipper.id, shipment_state: 'processing', shipment_type: 'COD'}}
        designer_shipments = Designer.select(s_clause).where(w_clause).joins(designer_orders: :shipment).group(:id)
        ShipmentMailer.pickup_list(designer_shipments, shipper).deliver if designer_shipments.present?
      end
    end
    true
  end
  
  def self.ups_create_international(order_no, params)
    order = Order.find_by_number(order_no)
    params[:mirraw_reference] = order.shipment_reference
    response = ups_shipment_api(order, params)
    update_order(order, response, params, 'ups')
  end

  def self.ups_services
    services = UPS.const_get(:SERVICES).collect{|key, value| [value,key]}
  end

  def self.ups_packaging_type
    packaging_type = UPS.const_get(:PACKAGING).collect{|key, value| [value,key]}
  end

  def add_label_by(type, params={})
    if params[:file].present?
      self.label = params[:file]
    elsif params[:file_content].present?
      case type
      when 'PDF'
        self.add_label(params[:file_content])
      else
        raise NotImplementedError
      end
    else
      raise NotImplementedError
    end
  end

  def self.get_gst_rate(hsn_code,item_price,market_rate)
    gst_rate_hash = HSN_RATES[hsn_code] || {}
    flat = "flat"
    if gst_rate_hash.keys.include?(flat)
      gst_rate = gst_rate_hash[flat]
    elsif gst_rate_hash.keys.size > 0
      if hsn_code == "********"
        gst_rate = gst_rate_hash['else']
      elsif hsn_code == "********"
        gst_rate = item_price*market_rate > 500 ? gst_rate_hash['>=500'] : gst_rate_hash['<500']
      else
        gst_rate = item_price*market_rate > 1000 ? gst_rate_hash['>=1000'] : gst_rate_hash['<1000']
      end
    else
      gst_rate = 0
    end
    return gst_rate
  end

  def self.download_invoices_in_bulk(account,start_date,end_date,type,shipper)
    if type == 'Excel Sheet'
      rows = []
      shipper_id = shipper == 'All' ? nil : {shipper_id: shipper}
      countries    = Country.select('name,iso3166_alpha2').map{|c| [c.name,c.iso3166_alpha2]}.to_h
      market_rates = Hash[CurrencyConvert.select('distinct symbol,market_rate,exchange_rate').map{|cc| [cc.symbol,(cc.exchange_rate.presence || cc.market_rate)]}]
      Shipment.forward.joins(:shipment_invoice_items,:line_items).where('shipments.designer_order_id is null and order_id is not null and shipments.created_at >= ? and shipments.created_at <= ?',start_date,end_date).group('shipments.id').where(shipper_id).preload(:shipment_invoice_items, :shipper, :order).find_in_batches(batch_size: 250) do |shipments|
        shipments.each do |shipment|
          order                = shipment.order
          shipment_invoice_items = shipment.shipment_invoice_items
          shipper_name  = shipment.shipper.name.try(:upcase)
          country_code_name = countries[order.country].presence || order.country_code
          payment_gateway = order.payment_gateway.presence || order.pay_type
          shipment_total_amt = 0
          if (duplicate_orders = order.find_parent_duplicate_order).present?
            source_payment = duplicate_orders.join(', ')
          elsif order.referral_discount.present? || order.refund_discount.present?
            source_payment = 'Mirraw Wallet and ' + payment_gateway.to_s
          else
            source_payment = payment_gateway
          end
          shipment_invoice_items.each do |shipment_invoice_item|
            design_id = shipment_invoice_item.line_item && shipment_invoice_item.line_item.design_id.presence || ""
            market_rate = (shipment.exchange_rate || market_rates[shipment_invoice_item.currency] || 1).to_f
            rows << [shipment.number, shipment.invoice_number, shipment.shipment_state, shipment.created_at.strftime('%d/%m/%Y'), order.name.try(:upcase), order.city.try(:upcase), order.state_code.try(:upcase),order.buyer_state, country_code_name, order.state,  shipper_name == 'DHL' ? 'TOTAL' : (order.shipping.to_i > 0 ? 'C&F' : 'FOB'), shipment_invoice_item.description.try(:upcase), shipment_invoice_item.quantity, shipment_invoice_item.rate, shipment_invoice_item.discount, shipment_invoice_item.discounted_rate, shipment_invoice_item.total_amount, (shipment_invoice_item.total_amount.to_f * market_rate).round(2) , shipment_invoice_item.currency, 0, (shipment.invoice_data['packaging_charges'].to_i + shipment.invoice_data['freight_charges'].to_i), shipment_invoice_item.hsn_code, shipment.shipping_bill_no, shipment.shipping_bill_date, shipment.port_code, 'PAID', shipment_invoice_item.taxable_value, shipment_invoice_item.gst_tax, shipment_invoice_item.weight, order.number, design_id, shipper_name, source_payment]
            shipment.invoice_data['packaging_charges'], shipment.invoice_data['freight_charges'] = 0, 0
            shipment_total_amt += shipment_invoice_item.total_amount
          end
        end
      end
      dir = "tmp/reports/"
      FileUtils.mkdir_p(dir) unless File.directory?(dir)
      filepath = dir + "Shipment_to_be_sent.csv"
      CSV.open(filepath, "wb", {:col_sep => ","}) do |csv|
        csv << ['FECNO','INVOICENO','State of AWB','INVOICE DT','CneeName','CneeCity','CneeState','StateName','CountryShortName', 'OrderState' , 'ContractType','ItmDesc','Qty','UnitValue', 'Discount', 'Discounted Price', 'Total Price', 'Total Invoice Value in INR' , 'CurrencyCode','InsAmt','PkgChg','RITCNo', 'Shipping Bill No', 'Shipping Bill Date', 'PORTNO','IGST STATUS','TAXABLE VALUE','IGST AMT','Weight','Order No','Product ID','Courier Patner','Source of Payment']
        rows.each do |row|
          csv << row
        end
      end
      ShipmentMailer.mail_invoices_in_bulk(type,filepath,account.email,start_date,end_date).deliver
    else
      pdf = CombinePDF.new
      shipper_id = shipper == 'All' ? nil : "shipments.shipper_id = #{shipper}"
      Order.joins(:shipments).where(shipper_id).where('shipments.designer_order_id is null and shipments.created_at >= ? and shipments.created_at <= ?',start_date,end_date).find_in_batches(batch_size: 500) do |orders|
        orders.uniq.each do |order|
          if order.order_notification.present? && (key=order.order_notification.keys.find{|k| k.to_s.include? 'invoice'}).present?
            pdf << CombinePDF.parse(HTTParty.get(order.order_notification[key]).body)
          end
        end
        if ((pdf.save 'Order_Invoice.pdf').to_f/1024000) >= 20
          ShipmentMailer.mail_invoices_in_bulk(type,pdf,account.email,start_date,end_date).deliver
          pdf = CombinePDF.new
        end
      end
      ShipmentMailer.mail_invoices_in_bulk(type,pdf,account.email,start_date,end_date).deliver
    end
  end

  def self.generate_rapid_delivery_label(order_number, designer, shipment, replacement_shipment = nil, wa_ids)
    shipper_name = (Shipper::ALL_SHIPPERS.key(shipment.try(:shipper_id)) || replacement_shipment.try(:shipper_name).try(:upcase))
    pdf_content =  ActionController::Base.new().render_to_string(
        :template => '/shipments/rapid_delivery_label',
        :layout   => false,
        :locals   => { order_number: order_number, awb_number: (shipment || replacement_shipment).number , designer: designer, shipper_name: shipper_name, wa_ids: wa_ids}
      )
    label = WickedPdf.new.pdf_from_string(pdf_content)
    if replacement_shipment.present?
      label_file_name = "rapid_delivery_label/#{replacement_shipment.number}_#{order_number}"
      AwsOperations.create_aws_file(label_file_name, label, false)
      download_url = AwsOperations.get_aws_file_path(label_file_name)
      replacement_shipment.update_column(:label_url, download_url)      
    else
      shipment.add_label(label)
    end
  end

  def self.generate_invoice_inr(order, shipment, old_shipment_detail, old_items, date, market_rate)
    shipment_detail = old_shipment_detail.deep_dup
    shipment_detail['currency_code'] = 'INR'
    items = old_items.collect do |old_item|
      item = old_item.deep_dup
      item[:price] = (item[:price].to_f * market_rate).round(2)
      item[:total_price] = (item[:total_price].to_f * market_rate).round(2)
      item[:item_discount] = (item[:item_discount].to_f * market_rate).round(2)
      item[:item_discounted_price] = (item[:item_discounted_price].to_f * market_rate).round(2)
      item
    end
    shipment_detail['total_price'] = (shipment_detail['total_price'] * market_rate).round(2)
    pdf_content =  ActionController::Base.new().render_to_string(
      template: '/shipments/commercial_invoice',
      layout: false,
      locals: {:@order => order, :@items => items, :@shipment_detail => shipment_detail, :@date => date, :@market_rate => market_rate}
    )
    invoice = WickedPdf.new.pdf_from_string(pdf_content)
    AwsOperations.create_aws_file("invoice_inr_#{order.number}_#{shipment.number}.pdf", invoice, false)
    order.other_details["invoice_inr_#{shipment.number}"] = "invoice_inr_#{order.number}_#{shipment.number}.pdf"
    order.skip_before_filter = true
    order.save
  end

  def self.calculate_invoice_items(items,invoice_items, invoice_data, product_name, shipping_charge = 0, report_flag = false, total_tax: 0, cargo_shipment: false)
    invoice_data[:items_id]     += items.collect{|i| i[:item_id]}
    invoice_data[:discount]      = 0.0 if (invoice_data[:order_total_price].round <= invoice_data[:discount].round + 200) # Sdded 2 Dollar Delta  + 100 for USD 0.56 Float USD Cases
    items_count,items_price,item_discounted_price,item_discount,designer_discount  = 0,0,0,0,0
    hsn_code,item_weight,designable_type,gst_tax, meis, category_name,sku_number = '',0.0,'',0.0, false,'',''
    items.each do |item|
      items_count += item[:quantity].to_i
      if invoice_data[:commercial]
        items_price += (item[:price].to_i + item[:addon_price].to_i) * item[:quantity].to_i
        designer_discount += item[:designer_discount].to_i
      else
        items_price += item[:price].to_i * item[:quantity].to_i
      end
      unless cargo_shipment
        category_name = item[:category_name]
        hsn_code = item[:hsn_code]
        item_weight += item[:weight].to_f
        designable_type = item[:designable_type]
        sku_number = item[:sku_id]
      end
      meis = ActiveRecord::Type::Boolean.new.type_cast_from_user(item[:meis]) unless meis
    end
    item_price  = (items_price / items_count)+ shipping_charge + total_tax
    item_discount = ((item_price / invoice_data[:order_total_price].to_f) * invoice_data[:discount]) + designer_discount
    item_discounted_price = item_price - item_discount
    items_price = (item_discounted_price * items_count / invoice_data[:paypal_rate].to_f).round(2)
    name = product_name
    if cargo_shipment
      key_identifier = (items.first[:cargo_designer_wise] ? items.first[:designer_id] : items.first[:item_id])
      items_inr_price = (items_price * invoice_data[:market_rate].to_f).round(2)
      invoice_items[key_identifier][:items] << {
        name: name.gsub('-', ' ').camelize,
        quantity: items_count,
        total_foreign_value: items_price,
        total_price: items_inr_price,
        item_id: items.first[:item_id]
      }
      invoice_items[key_identifier][:total_price] += items_inr_price
    else
      product_type = (['other','bag','consumable','jacket'].include?(designable_type.try(:downcase))) ? 'other' : product_name.downcase
      item_price = (item_price / invoice_data[:paypal_rate].to_f).round(2)
      item_discount = (item_discount / invoice_data[:paypal_rate].to_f).round(2)
      item_discounted_price = (item_discounted_price / invoice_data[:paypal_rate].to_f).round(2)
      gst_rate = Shipment.get_gst_rate(hsn_code,item_discounted_price,invoice_data[:market_rate])
      gst_tax = (items_price - items_price/(1+(gst_rate/100.0)))
      taxable_value = ((items_price - gst_tax) * invoice_data[:market_rate]).round(2)
      LineItem.where(id: items.collect{|l| l["item_id"]}).update_all(gst_rate: gst_rate) unless report_flag
      item_id = items.first['item_id']
      invoice_items << {
        name: name.gsub('-', ' ').camelize,
        quantity: items_count,
        price: item_price,
        item_discount: item_discount,
        item_discounted_price: item_discounted_price,
        total_price: items_price,
        weight: item_weight,
        hsn_code: hsn_code,
        gst_tax: (gst_tax * invoice_data[:market_rate]).round(2),
        taxable_value: taxable_value,
        gst_rate: gst_rate,
        designable_type: designable_type,
        meis_scheme: meis,
        category_name: category_name,
        sku_number: sku_number,
        line_item_id: item_id
      }
    end
    [items_count, item_discounted_price, items_price, hsn_code]
  end

  def total_items_quantity
    self.order.line_items.sum(:quantity)
  end

  def calculate_tax_for_returns(tax_amount)
    return tax_amount / total_items_quantity

  end

  def calculate_shipping_per_item(shipping_amt)
    return shipping_amt/total_items_quantity
  end

  def total_shipment_items(selected_items)
    total_items = 0
    selected_items.each do |item|
      total_items += item[:quantity].to_i
    end
    return total_items
  end

  def get_invoice_data(order_total_price,order,market_rate,commercial,paypal_rate,shipment)
    {
      items_id: [],
      order_total_price: order_total_price,
      discount: order.get_total_discount,
      market_rate: market_rate,
      commercial: commercial,
      paypal_rate: paypal_rate,
      shipper_name: shipment.shipper.name
    }
  end


  def package_details(shipment,items,class_name=nil)
    order = shipment.order
    commercial, paypal_rate, currency_code = order.get_commercial_values(false, shipment.shipper.name)
    selected_items = []
    items.each { |_key, i| selected_items << i }
    total_items = total_shipment_items(selected_items)
    reference, shipping_amt, order_total_price,tax_amount = order.get_order_invoice_details(commercial, shipment.shipper.id, selected_items.collect { |i| i[:item_id] })
    shipping_charges = (class_name.nil? || class_name.downcase.exclude?('return')) ? calculate_shipping_per_item(shipping_amt) : 0
    tax_per_item = calculate_tax_for_returns(tax_amount)
    selected_items_group = selected_items.group_by { |item| item[:name] }
    market_rate = ((cc = CurrencyConvert.where { (symbol == currency_code) | (iso_code == currency_code) }.first).exchange_rate.presence || cc.market_rate)
    invoice_data = get_invoice_data(order_total_price,order,market_rate,commercial,paypal_rate,shipment)
    selected_items_weight_total = @total_weight.to_f
    items_price_total           = 0
    items_weight_total          = 0
    invoice_items               = []
    selected_items_group.each do |product_name, items|
      items_count, item_discounted_price, items_price, hsn_code = Shipment.calculate_invoice_items(items, invoice_items, invoice_data, product_name, shipping_charges, total_tax: tax_per_item)
      items_weight = (selected_items_weight_total / total_items).to_f * items_count
      items_price_total  += items_price
      items_weight_total += items_weight
    end
    [invoice_items,items_price_total]
  end


  def self.calculate_domestic_invoice(order, items, mirraw_domestic, designer_order = nil, bulk_designer = nil)
    invoice_data = Hash.new(0)
    if !mirraw_domestic && order.present?
      designer = designer_order.designer
    end
    billing_status = !mirraw_domestic && order.present? && designer_order.ship_to != 'mirraw' && order.cod?
    if billing_status
      line_items = designer_order.line_items
      dos_total  = line_items.to_a.sum{|li| (li.snapshot_price * li.quantity)}.to_f + items.find{|i| i[:name] == 'Shipping Charges'}.try(:[],:total_price).to_i
      order_total_price = order.line_items.preload(:line_item_addons).to_a.sum{|li| li.snapshot_price * li.quantity} + items.find{|i| i[:name] == 'Shipping Charges'}.try(:[],:total_price).to_i
      designer_order_discount = designer_order.discount.to_i
      order_total_discount = order.get_total_discount.to_i
    end
    invoice_data[:all_dos_tax] = Hash.new(0)
    designer_order_discount, order_total_discount, order_total_price, dos_total = if billing_status && order.currency_rate_market_value.present?
      [designer_order_discount, order_total_discount, order_total_price, dos_total].map{|i| order.international_cod_price(i)}
    end
    items.each do |item|
      if billing_status
        designer_discount = (item[:price] * item[:quantity] * designer_order_discount/dos_total).round(2)
        item[:item_discount] = (((item[:price] / order_total_price.to_f) * order_total_discount) + designer_discount).round(2)
      end
      unless mirraw_domestic
        item[:item_discounted_price] = (item[:price] - item[:item_discount].to_f).round(2)
        taxable_value = (item[:item_discounted_price]/(1+item[:gst_rate].to_f/100)).round(2)
        item[:taxable_value] = (taxable_value * item[:quantity]).round(2).nonzero? || '-'
        item[:gst_tax] = ((item[:item_discounted_price] - taxable_value) * item[:quantity]).round(2)
        item[:total_price] = (item[:item_discounted_price] * ( item[:quantity] || 1)).round(2)
      end
      invoice_data[:gst_total] += item[:gst_tax].to_f
      invoice_data[:total_taxable_value] += item[:taxable_value].to_f
      invoice_data[:item_total] += item[:item_discounted_price] * ( item[:quantity] || 1)
      invoice_data[:all_dos_tax][item[:dos_id]] += item[:gst_tax].to_f
    end
    if !mirraw_domestic && ((bulk_designer.present? && bulk_designer.gst_no.blank?) || (designer.present? && designer.gst_no.blank?))
      invoice_data[:payable_gst] = (invoice_data[:gst_total]).round(2)
      invoice_data[:total_taxable_value] = 0
      invoice_data[:gst_total] = 0
      items.each{|i| i.merge!({gst_rate: 0, gst_tax: 0, taxable_value: nil})}
    end
    invoice_data[:igst_flag] = ((mirraw_domestic && order.buyer_state.try(:upcase) == SHIPPER_STATE.upcase) || (!mirraw_domestic && bulk_designer.present? && bulk_designer.gst_no.present? && bulk_designer.state.try(:upcase) == SHIPPER_STATE.upcase) || (!mirraw_domestic && designer.present? && designer.gst_no.present? && ((designer_order.ship_to == 'mirraw' && designer.state.try(:upcase) == SHIPPER_STATE.upcase) || (designer_order.ship_to == 'customer' && designer.state.try(:upcase) == order.buyer_state.try(:upcase))))) ? false : true
    invoice_data
  end

  def self.check_for_volumetric_lockout
    shipment_counts = Shipment.select('count(shipments.id) as total_shipments, sum(case when (ceiling(orders.actual_weight * 2)/2) < orders.volumetric_weight then 1 else 0 end) as over_volume_shipments').joins(:order).where('designer_order_id is null and shipments.created_at::date = ?', Date.today).to_a.first
    shipment_counts.total_shipments != 0 && ((shipment_counts.over_volume_shipments.to_f/shipment_counts.total_shipments)*100).round(2) >= 0.5
  end

  def self.generate_manual_shipment(order_id, tracking_number, shipper, current_account)
    order = Order.where(id: order_id).preload([designer_orders: [line_items: [:addon_type_values, [replaced_product: [:line_item_addons, :designer_order]], [design: [[property_values: :property], :categories]]]]], :order_addon,line_items: :line_item_addons).first
    selected_items = []
    found_shipper_name = (Shipper::ALL_SHIPPERS.keys & [shipper.upcase.strip])[0].try(:downcase)
    shipper = Shipper.find_by('lower(name) = ?' , found_shipper_name || 'other')
    order.designer_orders.each do |designer_order|
      dos_line_items = designer_order.line_items.to_a
      dos_total      = designer_order.get_item_total
      dos_line_items.each do |line_item|
        if line_item.shipment_id.nil? && line_item.received == 'Y' && designer_order.state != 'canceled' && line_item.sent_to_invoice.present? && line_item.status.blank?
          item = {}
          design = line_item.design
          orignal_item = line_item.get_replacement_product
          item_name = ((['saree','kurta','kurti','salwarkameez','lehenga','jewellery'].include? design.designable_type.try(:downcase)) ? design.invoice_category_name('dhl').titleize : design.categories.first.name)
          item[:item_id] =  line_item.id
          item[:designable_type] = design.designable_type
          item[:hsn_code] = design.categories.hsn_code
          item[:addon_price] = orignal_item.line_item_addons.to_a.sum(&:snapshot_price)
          item[:designer_discount] = (orignal_item.snapshot_price * orignal_item.quantity * orignal_item.designer_order.discount.to_i/dos_total).round(2)
          item[:name] = item_name
          item[:quantity] = orignal_item.quantity
          item[:weight] = line_item.weight.to_f/1000
          item[:price] = orignal_item.snapshot_price
          item[:meis]  = line_item.elligible_for_meis?
          selected_items << item
        end
      end
    end

    if selected_items.present?
      if COMMERCIAL_FOR_AUTOMATION == 'true'
        commercial, paypal_rate, currency_code = order.get_commercial_values
      else
        commercial = false
        paypal_rate = 1
        currency_code = 'INR'
      end
      total_weight = order.actual_weight.to_f > order.volumetric_weight.to_f ? order.actual_weight : order.volumetric_weight
      reference, shipping_amt, order_total_price, tax_amount = order.get_order_invoice_details(commercial, shipper.id, selected_items.collect{|i| i[:item_id]})
      selected_items_group = selected_items.group_by{|item| item[:name]}
      market_rate = ((cc = CurrencyConvert.where{(symbol == currency_code) | (iso_code == currency_code)}.first).exchange_rate.presence || cc.market_rate)
      invoice_data = {
        items_id: [],
        order_total_price: order_total_price,
        discount: order.get_total_discount,
        market_rate: market_rate,
        commercial: commercial,
        paypal_rate: paypal_rate
      }

      invoice_items, items_price_total, items_count = [], 0, 0
      shipping_charges = shipping_amt / selected_items.sum{|i| i[:quantity].to_i }
      total_tax = tax_amount / selected_items.sum{|i| i[:quantity].to_i }
      selected_items_group.each do |product_name, items|
        items_count, item_discounted_price, items_price, hsn_code = Shipment.calculate_invoice_items(items, invoice_items, invoice_data, product_name, shipping_charges, total_tax: total_tax)
        items_price_total  += items_price
      end

      shipment = Shipment.new(
        :number         => tracking_number,
        :shipper        => shipper,
        :price          => (items_price_total * paypal_rate).to_i,
        :weight         => total_weight,
        :line_item_ids  => invoice_data[:items_id],
        :order_id       => order_id,
        :invoicer_id    => current_account.id,
        :packer_id      => current_account.id,
        :mirraw_reference => reference,
        automated: false,
        track: false
      )
      if shipment.save
        ShipmentsController.generate_invoice(invoice_items, order.number, shipment.id, shipping_charges, items_price_total)
        #Sending email for customs_verification to recipients from certain specific countries
        countries_for_customs_check = Order::CUSTOMS_VERIFICATION_COUNTRIES_LIST
        if shipment.designer_order_id.blank? && shipment.order_id.present? && countries_for_customs_check.include?(order.country_code)
          shipment.send_notification_mail_for_customs_verification
        end
        return true
      else
        return false
      end
    else
      return false
    end
  end

  def update_dhl_shipment_count
    shipment_count = SystemConstant.get('DHL_SHIPMENT_COUNT')
    shipment_count.default = 0
    if shipment_count['month_no'] == Date.current.month
      shipment_count['monthly_count'] += 1
    else
      shipment_count['monthly_count'] = 1
      shipment_count['month_no'] = Date.current.month
    end
    SystemConstant.where(name: 'DHL_SHIPMENT_COUNT').update_all(value: shipment_count.to_json)
  end

  def make_purchase_order_entry(des_order_ids, invoice_num, awb_num, total_qty)
    p_order = PurchaseOrder.where(number: "#{invoice_num}/#{Time.current.strftime('%d/%m')}").first_or_initialize
    p_order.assign_attributes(awb_number: awb_num, invoice_quantity: total_qty)
    DesignerOrder.where(id: des_order_ids).update_all(purchase_order_id: p_order.id) if p_order.save
  end

  class << self
    private

    def get_delivery_response(shipments_req, wa_ids)
      wa_ids = DEFAULT_WAREHOUSE_ADDRESS_ID unless wa_ids.present?
      _, shipping_telephone, shipping_address_1, _, shipping_city, shipping_pincode, shipping_state, _ = DesignerOrder.get_warehouse_billing_address(wa_ids)
      pickup_location = {country: 'India',name: Mirraw::Application.config.delhivery_surface_client_name,phone: shipping_telephone,add:   shipping_address_1.gsub(/[&;]/,''),city: shipping_city,state: shipping_state,pin: shipping_pincode }
      request = 'format=json&data='+{pickup_location: pickup_location, shipments: [shipments_req]}.to_json
      api = Mirraw::Application.config.delhivery_baseurl + '/cmu/push/json/?token={token}'
      api_params = api.sub('{token}', Mirraw::Application.config.delhivery_surface_token)
      api_params_encoded = URI.encode(api_params)
      res = HTTParty.post(api_params_encoded, body: request, headers: {'Content-Type' => 'application/x-www-form-urlencoded'})
      if res.body.present? && res.headers.present? && res.headers.content_type.present? && res.headers.content_type.match('json').present?
        res_content = JSON.parse(res.body)
      else
        res_content = {'error' => res.response.code, 'rmk' => 'retry after 10 mins'}
      end
      [res, res_content]
    end

    def get_rapid_delivery_response(order_id, designer, line_item_total, weight, wa_ids)
      company_name, shipping_telephone, shipping_address_1, shipping_address_2, shipping_city, shipping_pincode, shipping_state, _ = DesignerOrder.get_warehouse_billing_address(wa_ids)
      credentials = Mirraw::Application.config.rapid_delivery
      awb_num_url = credentials[:tracking_num_url]
      awb_num_request = {client: credentials[:client], token: credentials[:token], oid: order_id, consignee: designer.name, add1: designer.try(:street), add2: '', pin: designer.try(:pincode), city: designer.try(:city), state: designer.try(:state), country: designer.try(:country), phone: designer.try(:phone), weight: weight, mode: 'prepaid', amt: line_item_total, ret_add: "#{shipping_address_1}, #{shipping_address_2}-#{shipping_pincode}, #{shipping_state}, India", ship_pin: shipping_pincode, ship_phone: shipping_telephone, ship_company: company_name }
      awb_num = HTTParty.post(awb_num_url,body:awb_num_request).parsed_response
    end

    def get_xpress_bees_delivery_response(manifest_details)
      credentials = Mirraw::Application.config.xpress_bees
      shipment_req = { "XBkey" => credentials[:push_XBkey], "VersionNumber" => "V5", "ManifestDetails" => manifest_details }
      ship_create_response = JSON.parse(HTTParty.post(credentials[:prepare_manifest_url], body: shipment_req.to_json, headers: {'Content-Type' => 'application/json'}).body)
      response_msg = ship_create_response['AddManifestDetails'][0]['ReturnMessage']
    end

    def get_customer_contact_details(designer, wa_ids)
      cust_address_details, cust_phone_details = get_customer_details(designer, false, wa_ids)
      _, shipping_telephone, shipping_address_1, _, shipping_city, shipping_pincode, shipping_state, _ = DesignerOrder.get_warehouse_billing_address(wa_ids)
      pickup_details =   {'PickupVendor' => 'Mirraw.com', 'PickVendorPhoneNo' => shipping_telephone, 'PickVendorAddress' => shipping_address_1.gsub(/[&;]/,''), 'PickVendorCity' => shipping_city, 'PickVendorState' => shipping_state, 'PickVendorPinCode' => shipping_pincode}
      delivery_details = {'CustomerName' => designer.name, 'CustomerCity' => designer.city, 'ZipCode' => designer.pincode, 'CustomerState' => designer.state}
      [cust_address_details, cust_phone_details, pickup_details, delivery_details]
    end

    def get_line_discharge(country_code_name)
      case country_code_name.try(:downcase)
      when 'us'
        'NYC'
      when 'ca'
        'YTO'
      end
    end

    def ups_shipment_api(order, params)
      ups = UPS::Connection.new(test_mode: Rails.env.production? ? true : false)
      response = ups.ship ups_shipment_builder(params, order)
      required_response = ups_shipment_required_content(response)
    end

    def ups_shipment_builder(params, order)
      key, username, password = ups_creds
      label_size = {height: 4, width: 6}
      request = UPS::Builders::ShipConfirmBuilder.new
      request.add_access_request key, username, password
      request.add_shipper ups_shipper_from_hash(order)
      request.add_description 'EthnicWear'
      request.add_ship_from ups_shipper_from_hash(order)
      request.add_ship_to ups_consignee_hash(order)
      request.add_service params[:service_code]
      request.add_package params
      request.add_payment_information SystemConstant.get('UPS_ACCOUNT_NUMBER')
      request.add_label_specification('GIF', label_size)
      request
    end

    def ups_shipment_required_content(response)
      content = {error: false, error_text: ''}
      if response.success?
        content[:tracking_number] = response.tracking_number
        content[:file_path] = response.graphic_image
        content[:file_type] = 'GIF'
      else
        content[:error] = true
        content[:error_text] = response.error_description
      end
      content
    end

    def ups_creds
      return [SystemConstant.get('UPS_API_KEY'), SystemConstant.get('UPS_USERNAME'), SystemConstant.get('UPS_PASSWORD')]
    end

    def ups_shipper_from_hash(order)
      params = {}
      company_name, shipping_telephone, shipping_address_1, shipping_address_2, shipping_city, shipping_pincode, shipping_state, _ = order.get_warehouse_shipping_address
      from_address = "#{shipping_address_1},#{shipping_address_2}"
      address_array = Order.multi_line_address(50, from_address)
      params[:company_name] = company_name
      params[:attention_name] = company_name
      params[:phone_number] = shipping_telephone
      params[:address_line_1] = address_array[0]
      params[:address_line_2] = address_array[1]
      params[:city] = shipping_city
      params[:state] = shipping_state
      params[:postal_code] = shipping_pincode
      params[:country] = 'IN'
      params[:shipper_number] = SystemConstant.get('UPS_ACCOUNT_NUMBER')
      params
    end

    def ups_consignee_hash(order)
      params = {}
      address_array = order.multi_line_address(30)
      params[:company_name] = order.name
      params[:attention_name] = order.name
      params[:address_line_1] = address_array[0]
      params[:address_line_2] = address_array[1]
      params[:phone_number] = order.phone
      params[:city] = order.city
      params[:state] = order.state_code
      params[:postal_code] = order.pincode
      params[:country] = Country.find_by_namei_cached(order.country).try(:[],:iso3166_alpha2)
      params
    end

    def ups_package(opts={})
      params = {
        packaging_type: {code: opts[:packaging_type_code]},
        package_weight: {weight:opts[:weight]},
        reference_number: {code:'TN',value:opts[:mirraw_reference]}}
    end

    def update_order(order, response, params, shipper_name)
      if response[:error]
        order.fedex_shipment_error = response[:error_text]
      else
        order.fedex_shipment_error = ''
        create_shipment_order(order, response, params, shipper_name)
      end
      order.save!
    end

    def create_shipment_order(order, response, params, shipper_name)
      shipper_id = Shipper.where('LOWER(name) = ?', shipper_name.downcase).first.id
      shipment = Shipment.create(number: response[:tracking_number], shipper_id: shipper_id, price: params[:price], weight: params[:weight],
        line_item_ids: params[:ids], order_id: order.id, packaging_type: params[:packaging_type], invoicer_id: params[:invoicer_id],
        packer_id: params[:packer_id], mirraw_reference: order.shipment_reference
      )
      shipment.add_label_by(response[:file_type], {file: response[:file_path], file_content: response[:file]})

      # TODO Need to make it work with validation
      # Did this for UPS EPL File
      shipment.save!(validate: false)

      ShipmentsController.generate_invoice(params[:invoice_items], order.number, shipment.id)
    end

    def calculate_weight_of_shipment(shipment)
      weight = [shipment.order.actual_weight.to_f,shipment.order.volumetric_weight.to_f].max unless shipment.order.country == 'India'
      if weight.nil?
        weight = 0
        if shipment.designer_order_id.present?
          shipment.designer_order.line_items.each do |li|
            weight += calculate_weight_of_line_item(li)
          end
        elsif shipment.line_items.present?
          shipment.line_items.each do |li|
            weight += calculate_weight_of_line_item(li)
          end
        end
      end
      return weight
    end

    def calculate_weight_of_line_item(lineItem)
      line_item_weight = 0
      design = lineItem.design
      design_weight = design.weight.to_i
      if design_weight!=0
        line_item_weight = design_weight
      else
        line_item_weight = design.approx_weight.to_f/1000
      end
    end

    def check_shipment_data_for_courier_reconciliation(shipment,awb_detail,dispute_file_path)
      awb = shipment.number
      weight = calculate_weight_of_shipment(shipment)
      already_paid = shipment.paid_to_courier_partner
      already_paid_date = shipment.paid_courier_charges_date
      shipment.paid_courier_charges_date = awb_detail[:date]
      shipment.shipment_weight =awb_detail[:shipment_weight].to_f
      shipment.shipment_charges =awb_detail[:amount]
      shipment.paid_to_courier_partner = 'y'
      if weight - awb_detail[:shipment_weight].to_f < -0.5 || already_paid == 'y'
        dispute_msg = (already_paid == 'y' ?  "Already Paid" : "Weight Difference is greater than 0.5 KG")
        CSV.open(dispute_file_path, "a+") do |csv|
          csv << [awb,shipment.order.number,weight,awb_detail[:shipment_weight],already_paid,already_paid_date,dispute_msg]
        end
      end
      shipment.save!
    end

    def get_invoice_and_item_details(shipment, designer_order, order, designer, bulk_des_ord_ids=[])
      cod_amount = 0
      multi_seller_info = Array.new
      invoice_items = Array.new
      cod_charge = 0
      is_cod = designer_order.try(:ship_as_cod?)
      cod_charge = designer_order.calculate_domestic_shipping_charge
      all_items = LineItem.sane_items.where(designer_order_id: (designer_order.try(:id).presence || bulk_des_ord_ids)).preload(:line_item_addons,design: :categories,variant: :option_type_values)
      ActiveRecord::Associations::Preloader.new.preload(all_items, [:designer_order, :order]) unless designer_order
      single_cod_charge  = cod_charge / all_items.to_a.sum(&:quantity).to_f

      all_items.each do |item|
        design = item.design
        addon_text = item.vendor_addon_items.collect{|item| "[#{item.addon_type_value.name}]" }.join
        designable_type = design.designable_type
        name        = design.categories[0].name.gsub('-', ' ').camelize
        name        << ' [sku: ' + design.design_code + ']' if design.design_code.present?
        name        += addon_text
        if is_cod && order.currency_rate_market_value.present?
          price_per_item = order.international_cod_price(item.price_with_addons)
        else
          price_per_item = item.price_with_addons
        end
        items_price = price_per_item * item.quantity
        cod_amount  += items_price
        hsn_code,gst_rate = (item.purchase_hsn_code.present? && item.purchase_gst_rate.present?) ? [item.purchase_hsn_code,item.purchase_gst_rate] : item.find_hscode_gst(price_per_item)
        invoice_items << { name: name, quantity: item.quantity, price: price_per_item, total_price: items_price,hsn_code: hsn_code,gst_rate: gst_rate, designable_type: designable_type }
        invoice_items << { name: 'Shipping Charges', quantity: item.quantity, price: single_cod_charge, total_price: (single_cod_charge * item.quantity).round(2),hsn_code: '',gst_rate: gst_rate } if single_cod_charge > 0

        multi_seller_info << {'ProductDesc' => design.title, "ProductCategory" => name, "SellerName" => designer.name, "SellerAddress" => "#{designer.street},#{designer.city},#{designer.state}", "SellerPincode" => designer.pickup_pincode.presence || designer.pincode, "InvoiceDate" => Date.today.strftime('%d-%m-%Y')}
      end
      invoice_data = Shipment.calculate_domestic_invoice(order, invoice_items, false, designer_order) if designer_order.present?

      if cod_charge > 0
        shipment[:cod_charge] = cod_charge
        cod_amount += cod_charge
      end
      shipment[:price] = invoice_data.try(:[],:item_total).to_i.nonzero? || cod_amount
      return invoice_items, multi_seller_info, cod_amount, cod_charge, shipment, invoice_data
    end

    def get_xpress_bees_shipment_request(shipment, designer_order, order, designer, xbkey, bulk_ids=[], reference=nil)
      ship_to_mirraw = (bulk_ids.present? || (designer_order.try(:ship_to) == 'mirraw'))
      cust_address, cust_phone = get_customer_details(order, ship_to_mirraw)
      invoice_items, multi_seller_info, cod_amount, cod_charge, shipment, invoice_data = get_invoice_and_item_details(shipment, designer_order, order, designer, bulk_ids)
      order_pickup_details = designer.get_order_pickup_details(designer_order, order)
      pickup_details = {"PickupVendor" => order_pickup_details[:company], "PickVendorPhoneNo" => order_pickup_details[:phone], "PickVendorAddress" => order_pickup_details[:address], "PickVendorCity" => order_pickup_details[:city], "PickVendorState" => order_pickup_details[:state], "PickVendorPinCode" => order_pickup_details[:pincode]}
      if ship_to_mirraw
        company_name, _, _,_,shipping_city, shipping_pincode,shipping_state,_= order.get_warehouse_shipping_address
        delivery_details = {'CustomerName' => company_name, 'CustomerCity' => shipping_city, 'ZipCode' => shipping_pincode, 'CustomerState' => shipping_state}
      else
        delivery_details ={'CustomerName' => order.name, 'CustomerCity' => order.city, 'ZipCode' => order.pincode, 'CustomerState' => order.buyer_state}
      end
      manifest_details = get_manifest_details([designer_order.warehouse_address_id], cust_address, cust_phone, pickup_details, delivery_details, designer_order, order, designer, shipment[:number], multi_seller_info, shipment[:price], true, reference)

      shipment_req = {
        'XBkey' => xbkey,
        'VersionNumber' => 'V5',
        'ManifestDetails' => manifest_details
      }
      return shipment_req, invoice_items, shipment, invoice_data
    end

    def get_customer_details(object, ship_to_mirraw = false, wa_ids = [])
      wa_ids = object.designer_orders.collect(&:warehouse_address_id).uniq if object.class == Order
      if ship_to_mirraw
        _, shipping_telephone, shipping_address_1,_, _, _, _, _ = DesignerOrder.get_warehouse_billing_address(wa_ids)
        (customer_address ||= []) << {"Type" => "Primary", "Address" => shipping_address_1}
        (customer_phone ||= []) << {"Type" => "Primary", "MobileNo" => shipping_telephone}
      else
        (customer_address ||= []) << {"Type" => "Primary", "Address" => "#{object.street}, #{object.city}, #{object.state}"}
        (customer_phone ||= []) << {"Type" => "Primary", "MobileNo" => object.phone}
      end
      return customer_address, customer_phone
    end

    def get_manifest_details(wa_ids, cust_address_details, cust_phone_details, pickup_details, delivery_details, designer_order, order, designer, awb_num, multi_seller_info, total_amount, is_cod = true, reference=nil, rtv_reference = nil)
      ship_to_mirraw = (rtv_reference.present? || designer_order.try(:ship_to) == 'mirraw' || !order.try(:cod?))
      manifest_data = designer_order ? {manifest_id: designer_order.id, order_no: order.number} : {manifest_id: reference, order_no: reference}
      manifest_data = rtv_reference if rtv_reference
      manifest_hash = {  
        "ManifestID" => manifest_data[:manifest_id],
        "OrderNo" => manifest_data[:order_no],
        "CustomerAddressDetails" => cust_address_details,
        "CustomerMobileNumberDetails" => cust_phone_details,
        "AirWayBillNO" => awb_num,
        "ServiceType" => "SD",
        "Quantity" => 1,       
        "DeclaredValue" => total_amount,
        "GSTMultiSellerInfo" =>multi_seller_info 
      }
      if is_cod
        manifest_hash['OrderType'] = (ship_to_mirraw ? 'Prepaid' : 'COD')
        manifest_hash['PaymentStatus'] = (ship_to_mirraw ? 'Prepaid' : 'COD')
        manifest_hash['RTOPinCode'] = designer.pickup_pincode.presence || designer.pincode
        manifest_hash['RTOName'] = designer.pickup_name.presence || designer.name
        manifest_hash['RTOMobileNo'] = designer.pickup_phone.presence || designer.phone
        manifest_hash['RTOAddress'] = designer.pickup_street.presence || designer.street
        manifest_hash['RTOToCity'] = designer.pickup_city.presence || designer.city
        manifest_hash['RTOToState'] = designer.state
        manifest_hash['PickupType'] = 'Vendor'
        manifest_hash['PickupVendorCode'] = designer.id
        manifest_hash['CollectibleAmount'] = total_amount unless ship_to_mirraw
      else
        company_name, shipping_telephone, shipping_address_1, shipping_address_2, shipping_city, shipping_pincode, shipping_state, _  = DesignerOrder.get_warehouse_billing_address(wa_ids)
        manifest_hash['OrderType'] = 'Prepaid'
        manifest_hash['PaymentStatus'] = 'Prepaid'
        manifest_hash['RTOPinCode'] = shipping_pincode
        manifest_hash['PickupVendorCode'] = 'Mirraw.com'
        manifest_hash['RTOName'] = 'Mirraw.com'
        manifest_hash['RTOMobileNo'] = shipping_telephone
        manifest_hash['RTOAddress'] = shipping_address_1.gsub(/[&;]/,'')
        manifest_hash['RTOToCity'] = shipping_city
        manifest_hash['RTOToState'] = shipping_state
      end
      manifest_hash.merge(pickup_details).merge(delivery_details)
    end

    def generate_xpress_bees_awb_numbers(shipper, xpress_bees_urls, type='COD')
      batch_request = {"BusinessUnit" => "ECOM", "ServiceType" => "FORWARD", "DeliveryType" => type}
      batch_response = JSON.parse(HTTParty.post(xpress_bees_urls[:batch_generation_url], body: batch_request.to_json, headers: {'Content-Type' => 'application/json', 'XBkey' => xpress_bees_urls[:push_XBkey]}).body)
      if batch_response['ReturnMessage'].try(:downcase) == 'successful'
        awb_num_request = { "BusinessUnit" => "ECOM", "ServiceType" => "FORWARD", "BatchID" => batch_response['BatchID']}
        awb_num_res = JSON.parse(HTTParty.post(xpress_bees_urls[:awb_generation_url], body: awb_num_request.to_json, headers: {'Content-Type' => 'application/json', 'XBkey' => xpress_bees_urls[:push_XBkey]}).body)
        if awb_num_res['ReturnMessage'].try(:downcase) == 'successful'
          to_be_imported = []
          awb_num_res['AWBNoSeries'].each_slice(200) do |numbers|
            numbers.each do |number|
              awb_num = AwbNumber.where(shipper_id: shipper.id, number: number).first_or_initialize
              if awb_num.new_record?
                awb_num.available = true
                awb_num.service_type = type.downcase
                to_be_imported << awb_num
              end
            end
          end
          AwbNumber.import to_be_imported, validate: false
        end
      end
    end

    def clickpost_shipment_state_track(shipments)
      shipment_objs = Shipment.where(number: shipments)
      shipment_objs.each do |shipment|
        click_post = ClickPostAutomation.new(shipment.order)
        tracking_details = click_post.clickpost_track(shipment)
      end
    end

  end

  private
    def move_to_in_transit
      self.in_transit_datetime = self.last_event_timestamp
      :in_transit
    end
end
