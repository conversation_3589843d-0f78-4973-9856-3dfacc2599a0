class Shipper < ActiveRecord::Base
  #attr_accessible :name, :website, :contact_number, :alt_contact_number, :international, :domestic, :email, :pickup_notify
  has_many :designer_shippers
  has_many :design_shipper_weights
  has_many :awb_numbers
  has_many :couriers
  has_many :priority_designer_orders, class_name: 'DesignerOrder', foreign_key: :priority_shipper_id
  has_many :shipment_charges
  has_many :shipper_fuel_prices
  ALL_SHIPPERS = Shipper.all.map{|s| [s.name.upcase,s.id]}.to_h
  DEFAULT_SHIPPERS  = ['Maruti Air', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Professional', 'Ma<PERSON><PERSON> Courier', 'Skyking'].map(&:upcase)

  # This method set price with surcharge
  def set_price_with_surcharge
    variable_rate = get_variable_rate
    if variable_rate
      self.shipment_charges.update_all("with_surcharge = price * #{variable_rate}")
    end
  end

  # This method return variable rate for shipment charges
  def get_variable_rate
    shipper_name = self.name.titleize
    case shipper_name
    when "Fedex"
      get_rate_by_name('FEDEX_RATE')
    when "Aramex"
      get_rate_by_name('ARAMEX_RATE')
    when "Ups"
      get_rate_by_name('UPS_RATE')
    when "Skynet"
      get_rate_by_name('SKYNET_RATE')
    else
      false
    end
  end

  # This method return variable rate by name for shipment charges
  def get_rate_by_name(name)
    SystemConstant.find_by_name(name).value
  end
end
