class StitchingMeasurement < ActiveRecord::Base
  belongs_to :design
  belongs_to :line_item
  belongs_to :order
  belongs_to :stylist
  belongs_to :user
  after_create :send_clevertap_event_notification
  default_scope { order('stitching_measurements.id ASC').where('measurement_group = ?', 'stylist') }
  scope :child, -> { unscope(:where).where('parent_measurement_id is not null') }
  #attr_accessible :code, :measurement_name,:design_id, :line_item_id, :order_id, :size_around_ankle, :shoulder_size, :product_designable_type,
                  # :length, :embroidary, :chest_size, :sleeves_length, :sleeves_around, :front_neck, :back_neck,
                  # :size_around_thigh, :bottom_length, :size_around_arm_hole, :waist_size, :hip_size, :size_around_knee,
                  # :measurement_locked, :padded, :style_no, :hook, :weight, :height, :under_bust, :stitching_label_url, :measurement_info_done_by,
                  # :stitching_notes,:shoulder_to_apex,:measurement_type,:state,:reject_mail_sent_at,:suggested_measurements,:stylist_id
  attr_accessor :remark
  serialize :suggested_measurements, Hash
  #serialize :user_review, ActiveRecord::Coders::Hstore
  has_many :child_measurements, -> (object){unscope(:where).where(parent_measurement_id: object.id)}, class_name: 'StitchingMeasurement'
  has_one :stylist_measurement, -> (object){unscope(:where).where(parent_measurement_id: object.id, measurement_group: 'stylist')}, class_name: 'StitchingMeasurement'
  belongs_to :parent_measurement, -> (object){unscope(:where).where(id: object.parent_measurement_id)}, class_name: 'StitchingMeasurement'
  has_many :tailoring_info, through: :line_item
  validate :measurement_quantity, if: :new_record?

  def self.create_measurement(order)
    stitching_measurements, line_item_ids = [], []
    line_items = order.line_items.sane_items.includes(line_item_addons: :addon_type_value).where.not(addon_type_values: { prod_time: 0, price: 0})
    if line_items.present?
      stylist_id = order.stylist_id || Stylist.assign_to_stylist(order, '')
      line_items.each do |line_item|
        line_item.quantity.times do
          stitching_measurement = StitchingMeasurement.new(design_id: line_item.design_id, line_item: line_item, user_id: order.user_id, order: order, stylist_id: stylist_id)
          line_item.line_item_addons.each do |addon|
            next unless should_create_mesurement?(addon)
            case addon.addon_type_value.try(:name)
            when 'Pre-Stitched Saree'
              stitching_measurements << prestitched_measurement(stitching_measurement.dup, addon)
              line_item_ids << line_item.id
            end
          end
        end
      end
      if stitching_measurements.present?
        StitchingMeasurement.import stitching_measurements
        stitching_measurements.each do |stitching_measurement|
          StitchingMeasurement.sidekiq_delay.generate_pdf(stitching_measurement.line_item_id)
        end
        order.send_stitching_info_mail_to_user
      end
      StitchingMeasurement.create_standard_stylist_measurements(line_item_ids) if line_item_ids.present?
    end
  end

  def self.generate_pdf(line_item_id, group='user')
    group_name_to_search = (group == 'all' ? ['user', 'stylist', 'tailor'] : [group])
    grouped_stitching_measurements = StitchingMeasurement.unscoped.preload(line_item: [line_item_addons: [addon_type_value: :addon_type_value_group]]).where(line_item_id: line_item_id, measurement_group: group_name_to_search).to_a.group_by(&:measurement_group)
    grouped_stitching_measurements.each do |group_name, stitching_measurements|
      if stitching_measurements.length > 1
        pdf = CombinePDF.new
        stitching_measurements.each do |stitching_measurement|
          pdf << CombinePDF.parse(stitching_measurement.to_pdf(false))
        end
        storable_stitch_mes = (stitching_measurements.to_a.find{|sm| sm.stitching_label_url.present?}.presence || stitching_measurements.first)
        storable_stitch_mes.save_stitching_measurement_pdf(pdf.to_pdf)
      elsif stitching_measurements.present?
        stitching_measurements.first.to_pdf
      end
    end
  end

  def self.check_fnp(line_item_id)
    line_item = LineItem.preload(:line_item_addons).find_by_id(line_item_id)
    if line_item.present? && line_item.stitching_measurements.blank? && line_item.line_item_addons.any?{|li_addon| li_addon.try(:addon_type_value).try(:name).to_s.downcase == 'fall and pico'}
      sm = StitchingMeasurement.create(line_item_id: line_item.id,order_id: line_item.designer_order.order_id,design_id: line_item.design_id, product_designable_type: 'fnp', measurement_group: 'user')
      sm.to_pdf
      sty_mes = sm.create_stylist_measurement
      sty_mes.to_pdf
    end
  end

  def send_clevertap_event_notification
    AppEvent::ItemEvent.new(self.line_item.id, "Stitching Form #{self.state.capitalize}").trigger_clevertap_event_deliver_later
  end

  def self.create_standard_stylist_measurements(item_ids)
    item_wise_mes = StitchingMeasurement.unscope(:where).where('line_item_id in (?)', item_ids).group_by(&:line_item_id)
    item_wise_mes.each do |item_id, mes|
      next if (group_names = mes.to_a.map(&:measurement_group)).present? && (group_names.count('user') <= group_names.count('stylist'))
      user_measurements = mes.select{|m| m.measurement_group == 'user'}
      stylist_measurements = mes.select{|m| m.measurement_group == 'stylist'}
      user_measurements.each do |um|
        next if stylist_measurements.find{|sm| sm.parent_measurement_id == um.id}.present?
        um.create_stylist_measurement 
      end
      StitchingMeasurement.sidekiq_delay.generate_pdf(item_id, 'stylist')          
    end
  end

  def create_stylist_measurement
    new_mes = self.dup
    new_mes.assign_attributes(measurement_group: 'stylist', parent_measurement_id: self.id)
    new_mes.save
    new_mes
  end

  def to_pdf(to_save=true)
    pdf_content = ActionController::Base.new.render_to_string(
      template:  '/designer_orders/stitching_label',
      layout:  false,
      locals: { delayed_attempt: true, :@stitching_measurement => self }
    )
    stitching_info_label = WickedPdf.new.pdf_from_string(pdf_content)
    self.save_stitching_measurement_pdf(stitching_info_label) if to_save
    return stitching_info_label
  end

  def update_order_state(measurement_repeat,similar_products)
    update_column(:measurement_info_done_by,'User')
    if measurement_repeat == 'Yes'
      if (remain_qty = (self.line_item.quantity - (StitchingMeasurement.where(line_item_id: line_item_id).count-1))) > 0      
        remain_qty.times do
          stitching_measurement = self.dup
          stitching_measurement.measurement_name = nil
          stitching_measurement.save
          stitching_measurement.create_stylist_measurement
        end
      end
    end

    StitchingMeasurement.apply_measurements_to_similar_products(similar_products,self.id) if similar_products.present?
    StitchingMeasurement.sidekiq_delay(queue:'low').check_for_system_measurement_approval(similar_products.to_a << line_item_id)
    StitchingMeasurement.sidekiq_delay.generate_pdf(self.line_item_id, 'all')    
  end

  def add_padding_cancan(item)
    notes = item.line_item_addons.pluck(&:notes).compact.join(',').downcase
    self.padded = notes.include?('padded') ? 'Yes' : 'No'
    self.cancan = notes.include?('cancan') ? 'Yes' : 'No'
  end

  def save_stitching_measurement_pdf(stitching_info_label)
    directory = Fog::Storage.new(provider: 'AWS',
                                 aws_access_key_id: ENV['AWS_ACCESS_KEY_ID'],
                                 aws_secret_access_key: ENV['AWS_SECRET_ACCESS_KEY'],
                                 region: ENV['AWS_REGION']).directories.new(key: ENV['S3_BUCKET'])

    filename = 'stitching_measurement/'+SecureRandom.hex(4)+'_'+ id.to_s + '_stitching_info_' + line_item_id.to_s + '.pdf'
    # upload that file
    file = directory.files.create(
      key:    filename,
      body:   stitching_info_label,
      public: true
    )
    self.new_record? ? self.stitching_label_url = file.public_url : update_column(:stitching_label_url, file.public_url) 
    file.public_url
  end

  state_machine :initial => :processing do
    event :measurement_approved do
      transition [:processing,:phone_call,:hold, :rejected] => :approved
    end

    event :approved_by_suggestion do
      transition :rejected => :approved
    end

    event :measurement_rejected do
      transition [:processing,:phone_call,:hold, :approved] => :rejected
    end

    event :reprocess_measurement do
      transition :rejected => :processing
    end

    event :phone_call_arranged do
      transition :rejected => :phone_call
    end

    event :move_to_hold do 
      transition :processing => :hold
    end

    after_transition to: [:approved, :rejected, :phone_call, :hold] do |measurement|
      AppEvent::ItemEvent.new(measurement.line_item.id, "Stitching Form #{measurement.state.capitalize}").trigger_clevertap_event_deliver_later
      measurement.public_send("#{measurement.state}_at=", Time.current)      
      state_name_with_timestamp = "#{measurement.state.titleize} #{Time.current.strftime('(%d-%m-%Y)')}"
      measurement.measurement_flow = (measurement.measurement_flow.present? ? "#{measurement.measurement_flow},#{state_name_with_timestamp}" : state_name_with_timestamp)
      measurement.save!
    end
  end 

  def add_notes(note, to_save, current_account = nil)
    user_name = current_account.nil? ? 'System' : current_account.email.split('@')[0]
    user_name = 'User' if current_account.present? && current_account.user?
    note_update(user_name,note)
    self.save if to_save
  end

  def note_update(user,note)
    note_content = "#{Date.today.strftime("%m/%d")} : #{user} : #{note}"
    self.stitching_notes ||= ''
    unless self.stitching_notes.blank?
      self.stitching_notes += ' ... '
    end
    self.stitching_notes += note_content
  end

  def self.apply_measurements_to_similar_products(similar_products,mes_id)
    stitch_mes = StitchingMeasurement.unscoped.find_by_id(mes_id) 
    if stitch_mes.present?  
      measurement_array = []
      line_items = LineItem.where(id:similar_products)
      line_items.each do |item|
        item.quantity.times do
          stitching_measurement = stitch_mes.dup
          stitching_measurement.measurement_name = nil
          stitching_measurement.line_item_id = item.id
          stitching_measurement.design_id = item.design_id
          measurement_array << stitching_measurement
        end
      end
      StitchingMeasurement.import measurement_array, validate: false 
      similar_products.each do |product|
        StitchingMeasurement.sidekiq_delay.generate_pdf(product)  
      end
      StitchingMeasurement.sidekiq_delay.create_standard_stylist_measurements(similar_products)
    end
  end

  def self.check_for_system_measurement_approval(line_item_ids)    
    StitchingMeasurement.where(line_item_id:line_item_ids).where('LOWER(product_designable_type) IN (?) AND state = ?',['lehenga_choli','blouse'],'processing').find_each(batch_size:10) do |sm|
      mark_approve = true
      correct_mes = CORRECT_MEASUREMENTS["chest-size-#{sm.chest_size}"]
      correct_mes.to_a.each do |k,v|
        next if sm.product_designable_type.try(:downcase).to_s.include?('blouse') && k == 'bottom_length'
        measurements = v.split('-')
        case measurements.size
        when 2
          is_correct = sm.send(k.to_sym).to_f.between?(measurements[0].to_f , measurements[1].to_f)
        when 3
          is_correct = sm.send(k.to_sym).to_s.try(:downcase) == measurements[0] || sm.send(k.to_sym).to_f.between?(measurements[1].to_f , measurements[2].to_f)
        else
          is_correct = measurements.include?(sm.send(k.to_sym))
        end
        
        unless is_correct
          mark_approve = false
          break
        end
      end
      if correct_mes.present? && mark_approve
        sm.add_notes('Approved Using System Measurements',false)
        sm.user_review['system_approved'] = 'true'
        sm.measurement_approved
      end
    end
  end

  def measurement_quantity
    if (item = LineItem.find_by_id(line_item_id)) && (item.quantity <= item.stitching_measurements.size)
      errors[:base] << 'Attempt Of Creating extra measurements detected.'
    end
  end


  class << self
    def prestitched_measurement(stitching_measurement, line_item_addon)
      size = line_item_addon.line_item.get_prestitch_saree_size(type: nil)
      stitching_measurement.waist_size = size['Waist']
      stitching_measurement.length = size['Length']
      stitching_measurement.product_designable_type = 'pre_stitched_saree'
      stitching_measurement
    end

    def should_create_mesurement?(line_item_addon)
      addon_names = line_item_addon.line_item.line_item_addons.map(&:addon_type_value).compact.map(&:name)
      return addon_names == ['Pre-Stitched Saree'] || addon_names == ['Pre-Stitched Saree', 'Fall and Pico']
    end
  end

end
