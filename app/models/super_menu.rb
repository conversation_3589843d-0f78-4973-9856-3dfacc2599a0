class SuperMenu < ActiveRecord::Base
  scope :mirraw, -> { where(app_name: [nil,""]) }
  scope :luxe, -> { where(app_name: 'luxe') }
  
  has_many :menus
  
  include IdentifyLinkType
  before_save :identify_link
  
  # Scopes for filtering
  scope :visible, -> { where(is_hidden: false) }
  
  # Find super menus with their associated menus, columns, and items
  # filtered by country and app_source
  def self.with_menu_hierarchy(country_code)
    SuperMenu.mirraw.visible
      .where("(super_menus.country ILIKE '%#{country_code}%' OR super_menus.country IS NULL OR super_menus.country='')")
      .where("(super_menus.app_source IS NULL OR super_menus.app_source ILIKE '%#{APP_SOURCE[0].downcase}%' OR super_menus.app_source='')")
      .order(:position)
  end
  
  # Get menus for a specific super menu
  def filtered_menus(country_code)
    menus.mirraw.includes(:menu_columns => :menu_items)
      .where(hide: false, menu_columns: {hide: false}, menu_items: {hide: false})
      .where("(menus.country ILIKE '%#{country_code}%' OR menus.country IS NULL OR menus.country='')")
      .where("(menus.app_source IS NULL OR menus.app_source ILIKE '%#{APP_SOURCE[0].downcase}%' OR menus.app_source='')")
      .where("(menu_columns.country ILIKE '%#{country_code}%' OR menu_columns.country IS NULL OR menu_columns.country='')")
      .where("(menu_columns.app_source IS NULL OR menu_columns.app_source ILIKE '%#{APP_SOURCE[0].downcase}%' OR menu_columns.app_source='')")
      .where("(menu_items.country ILIKE '%#{country_code}%' OR menu_items.country IS NULL OR menu_items.country='')")
      .where("(menu_items.app_source IS NULL OR menu_items.app_source ILIKE '%#{APP_SOURCE[0].downcase}%' OR menu_items.app_source='')")
      .order(:position)
  end
end
