class SuperMenu < ActiveRecord::Base
  # Scopes for different app types
  scope :mirraw, -> { where(app_name: [nil, ""]) }
  scope :luxe, -> { where(app_name: 'luxe') }
  scope :visible, -> { where(is_hidden: false) }
  scope :ordered, -> { order(:position) }

  # Associations
  has_many :menus, dependent: :nullify
  
  # Validations
  validates :title, presence: true
  validates :position, presence: true, uniqueness: { scope: [:country, :app_source] }

  # Class method to get super menus with their menus for a specific country
  def self.with_menus_by_country(country)
    includes(:menus).visible.ordered
      .where("(super_menus.country ILIKE '%#{country}%' OR super_menus.country IS NULL OR super_menus.country='')")
      .where("(super_menus.app_source IS NULL OR super_menus.app_source ILIKE '%#{APP_SOURCE[0].downcase}%' OR super_menus.app_source='')")
  end

  # Instance method to get visible menus for this super menu
  def visible_menus
    menus.where(hide: false).order(:position)
  end

  # Method to check if super menu has any visible menus
  def has_visible_menus?
    visible_menus.exists?
  end
end
