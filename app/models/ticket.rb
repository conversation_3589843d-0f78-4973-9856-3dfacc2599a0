class Ticket < ActiveRecord::Base
  # attr_accessible :title, :body
  include SidekiqHandleAsynchronous
  belongs_to :created_by, class_name: 'Account'
  belongs_to :resolved_by, class_name: 'Account'
  belongs_to :worked_by, class_name: 'Account'
  belongs_to :reopened_by, class_name: 'Account'
  belongs_to :order
  belongs_to :return
  belongs_to :design
  after_create :get_freshdesk_tickets
  scope :active_tickets, -> { where(state: %w(open reopen working)) }
  scope :open_tickets, -> { where(state: 'open') }
  scope :tickets_raised, -> { order('created_at ASC, tickets_raised DESC') }
  scope :seller_tickets, -> { where(department: 'seller') }
  scope :by_state, ->(state) { where("state = ?", state) if state.present? }
  scope :by_start_date, ->(start_date) { where("created_at >= ?", start_date) if start_date.present? }
  scope :by_end_date, ->(end_date) { where("created_at <= ?", end_date.to_date.end_of_day) if end_date.present? }
  paperclip_hash={
    storage: :s3,
    s3_credentials: AWS_ACCESS_KEYS,
    path: ":class/:id/:basename_:style.:extension",
    bucket: ENV['S3_BUCKET'],
    url: ":s3_alias_url",
    s3_headers: { 'Cache-Control' => 'max-age=*********', 'Expires' => 10.years.from_now.httpdate },
    s3_host_alias: "s3-#{ENV['AWS_REGION']}.amazonaws.com/#{ENV['S3_BUCKET']}"}

  has_attached_file :stitching_issue_image_1, paperclip_hash
  validates_attachment_file_name :stitching_issue_image_1, :matches => [/png\Z/, /jpe?g\Z/, /gif\Z/]
  do_not_validate_attachment_file_type :stitching_issue_image_1

  has_attached_file :stitching_issue_image_2, paperclip_hash
  validates_attachment_file_name :stitching_issue_image_2, :matches => [/png\Z/, /jpe?g\Z/, /gif\Z/]
  do_not_validate_attachment_file_type :stitching_issue_image_2

  TICKET_ISSUES_WITH_TAT = TICKET_ISSUES.values.flatten.collect{|k| [k.keys[0], k.values[0]['tat']]}.to_h
  TICKET_ISSUES_WITH_TAT_FOR_HIGHER_ESC = TICKET_ISSUES_FOR_HIGER_TAT.values.flatten.collect{|k| [k.keys[0], k.values[0]['tat']]}.to_h

  attr_accessor :reopen_message

  state_machine initial: :open, use_transactions: false do
    event :resolve_ticket do
      transition [:working,:open,:reopen,:reassign] => :close
    end
    event :working_on do
      transition [:reopen,:open,:reassign] => :working
    end
    event :reject_ticket do
      transition [:working,:open,:reopen,:reassign] => :reject
    end

    event :reopen_ticket do
      transition [:close,:reject,:reassign] => :reopen
    end

    event :reassign_ticket do
      transition [:working, :open, :reopen, :close, :reject, :reassign] => :reassign
    end

    after_transition to: :close do |ticket|
      TicketMailer.sidekiq_delay.send_ticket_resolution_mail(ticket.id)
      if ticket.return_id.present? && (return1 = ticket.return).present? && return1.pending_payment?
        return1.payment_made!
      end
      # ticket.sidekiq_delay(queue: 'critical').resolve_freshdesk_ticket(ticket.freshdesk_ticket_number) if ticket.freshdesk_ticket_number.present?
      SidekiqDelayGenericJob.set({queue: 'critical'}).perform_async(ticket.class.to_s, 
                                                                    ticket.id, 
                                                                    "resolve_freshdesk_ticket",
                                                                    ticket.freshdesk_ticket_number
                                                                   ) if ticket.freshdesk_ticket_number.present?
      if ticket.order_id.present?
        ticket.order.add_notes_without_callback("Ticket #{ticket.id} resolved - comment: " + ticket.resolve_message.to_s,'ticket',ticket.resolved_by)
      end
    end
    after_transition to: :reject do |ticket|
      TicketMailer.sidekiq_delay.send_ticket_resolution_mail(ticket.id)
      if ticket.order_id.present?
        ticket.order.add_notes_without_callback("Ticket #{ticket.id} rejected - comment: " + ticket.resolve_message.to_s,'ticket', ticket.resolved_by)
      end
    end

    after_transition to: :reopen do |ticket|
      if ticket.order_id.present?
        ticket.order.add_notes_without_callback("Ticket #{ticket.id} reopened - comment: " + ticket.reopen_message.to_s, 'ticket',ticket.reopened_by)
      end
    end
  end

  def self.get_issues(role = nil)
    if role == 'operations' 
      questions = TICKET_ISSUES['operations'].collect{|k| [k.keys[0], [k.keys[0], k.values[0]['department']]]}
    elsif role == 'senior_vendor_team'
      questions = TICKET_ISSUES['senior_vendor_team'].collect{|k| [k.keys[0], [k.keys[0], k.values[0]['department']]]}
    elsif role == 'accounts'
      questions = TICKET_ISSUES['accounts'].collect{|k| [k.keys[0], [k.keys[0], k.values[0]['department']]]}
    elsif role == 'sales'
      questions = TICKET_ISSUES['sales'].collect{|k| [k.keys[0], [k.keys[0], k.values[0]['department']]]}
    elsif role == 'stylist'
      questions = TICKET_ISSUES['stylist'].collect{|k| [k.keys[0], [k.keys[0], k.values[0]['department']]]}
    elsif role == 'seller'
      questions = TICKET_ISSUES['seller'].collect{|k| [k.keys[0], [k.keys[0], k.values[0]['department']]]}
    elsif role == 'all'
      questions = TICKET_ISSUES.values.flatten.collect{|k| k.keys[0]}
    else
      questions = TICKET_ISSUES['others'].collect{|k| [k.keys[0], [k.keys[0], k.values[0]['department']]]}
    end
    return questions
  end

  def resolve_automated(transaction_id, amount = nil)
    note = "Ticket Auto Resolved By System Transaction ID: #{transaction_id}"
    note += ". Amount Refunded - #{amount}" if amount.present?
    self.resolve_message = note
    self.resolved_at = Time.now
    self.resolved_by = nil
    self.resolve_ticket! if can_resolve_ticket?
  end

  def resolve_freshdesk_ticket(freshdesk_ticket_id)
    ticket = FreshdeskTicket.find_by_ticket_number freshdesk_ticket_id
    user_id = Account.find_by_email(ticket.agent_email).try(:accountable).try(:freshdesk_user_id) if ticket.present?
    body_content = if ["Delayed Delivery", "Ordered for Occasion", "TAT Adherence"].include?(self.issue)
      ActionController::Base.new().render_to_string('freshdesk_ticket_mailer/order_deliver_ticket_template', locals: {order: self.order})
    elsif self.issue == 'Issue Auto Created By System' && self.return.present?
      ActionController::Base.new().render_to_string('freshdesk_ticket_mailer/refund_complete_ticket_template', locals: {rtn: self.return})
    end
    if body_content.present?
      json_payload = {body: body_content.html_safe}
      json_payload.merge!({user_id: user_id}) if user_id.present?
      close_ticket = true
      freshdesk_api_url  = "/tickets/#{freshdesk_ticket_id}/reply"
      if (response = FreshdeskApi::Ticket.post_reply(freshdesk_api_url, json_payload)).present?
        if response['code'].present? || response['errors'].present?
          json_payload.delete(:user_id)
          response = FreshdeskApi::Ticket.post_reply(freshdesk_api_url, json_payload)
          if response['code'].present? || response['errors'].present?
            close_ticket = false
            ExceptionNotify.sidekiq_delay.notify_exceptions('Freshdesk Ticket Reply not sent', (response['description'].presence || 'Ticket was not replied on Freshdesk'), { error: response['errors'].try(:inspect) || response['code'] || 'Issue in replying' })
          end
        end
      end
      if ['closed','resolved'].exclude?(ticket.try(:status).try(:downcase)) && close_ticket
        json_payload = {status: 4}
        freshdesk_api_url  = "/tickets/#{freshdesk_ticket_id}"
        FreshdeskApi::Ticket.update_ticket(freshdesk_api_url, json_payload)
      end
    end
  end

  def get_freshdesk_tickets
    if self.order_id.present? && self.department !="seller"
      freshdesk_api_url = "https://mirraw.freshdesk.com/api/v2/tickets?email=#{order.email}"
      headers = { 'Authorization' => Base64.encode64(FRESHDESK_API_KEY), 'Content-Type' => 'application/json' }
      options = { headers: headers }
      response = HTTParty.get(freshdesk_api_url, options)
      parsed_response = response.parsed_response
      unless parsed_response.class.name == 'Hash'
        email_count = 0
        phone_call = 0
        parsed_response.each do |response|
          if ((order_number = response['custom_fields']['order_number']).present? && order_number.upcase == order.number) ||
             response['subject'].to_s.upcase.include?(order.number)
            response['source'] == 3 ? phone_call += 1 : email_count += 1
          end
        end
        update_attributes(tickets_raised: email_count, calls_received: phone_call, updated_at: Time.now)
      end
    end
  end

  def add_notes_without_callback(note, to_save, current_account = nil)
    user_name = current_account.nil? ? 'System' : current_account.email.split('@')[0]
    note_update(user_name,note)
    update_column(:notes, self.notes) if to_save
  end

  def note_update(user,note)
    note_content = "#{Date.today.strftime("%m/%d")} : #{user} : #{note}"
    self.notes ||= ''
    unless self.notes.blank?
      self.notes += ' ... '
    end
    self.notes += note_content
  end

  def process_tickets(tats_hash, escalation_count)
    spended_hours = (Time.now - self.created_at) / 3600
    tat = tats_hash[self.issue].to_i
    
    if spended_hours > tat
      TicketMailer.sidekiq_delay.send_escalation_mail(self,escalation_count)
      self.escalation_count = escalation_count
      self.save
    end
  end

  handle_asynchronously :get_freshdesk_tickets

  require 'freshdesk_api/freshdesk_api'
end
