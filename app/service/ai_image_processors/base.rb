require 'net/http'
require 'uri'
require 'redis'

module AiImageProcessors
  class Base
    ENABLE_AI_CROPPING = ENV['ENABLE_AI_CROPPING'] == 'true'
    LOCK_TTL = 60

    def initialize(record)
      @record = record
      @photo = record.photo
      @file_name = record.photo_file_name
      @tmp_file = nil
    end

    def perform
      return unless should_process?

      lock_key = "ai_image_processor:lock:#{@record.class.name}:#{@record.id}"
      redis = Redis.new
      acquired = redis.set(lock_key, Time.now.to_i, nx: true, ex: LOCK_TTL)

      unless acquired
        Rails.logger.info "[AI Reprocessor] Lock exists for #{@record.class.name} #{@record.id}, skipping duplicate processing."
        return false
      end

      start_time = Time.now
      Rails.logger.info "[AI Reprocessor] Started processing #{@record.class.name} #{@record.id}"

      original_path, original_name = fetch_original_image
      return unless original_path

      backup_original_to_s3(original_path, "image_original.jpeg")
      prepare_tempfile(original_path)

      MediaPipeService.new(@tmp_file.path).process!

      adapter = Paperclip.io_adapters.for(@tmp_file)
      adapter.original_filename = original_name

      @record.skip_reprocess_with_ai = true
      @record.ai_processed = true

      if @photo.queued_for_write[:original]
        @photo.queued_for_write[:original] = adapter
      else
        @record.photo = adapter
        @record.processed = true
        @record.save(validate: false)
        @photo.reprocess!
      end

      Rails.logger.info "[AI Reprocessor] Completed in #{(Time.now - start_time).round(2)}s for #{@record.class.name} #{@record.id}"
      true
    rescue => e
      handle_error(e)
      false
    ensure
      redis.del(lock_key) if acquired
      cleanup_tmp_file
    end

    private

    def should_process?
      ENABLE_AI_CROPPING &&
        @photo.present? &&
        !@record.skip_reprocess_with_ai &&
        !@record.ai_processed &&
        @record.errors.full_messages.blank? &&
        !processing_placeholder? &&
        (@file_name ? @file_name.length : 0) <= 150
    end

    def processing_placeholder?
      @photo.url(:original) =~ %r{/processing\.(jpg|jpeg|webp|png)}i
    end

    def fetch_original_image
      if @photo.queued_for_write[:original]
        path = @photo.queued_for_write[:original].path
        return [path, File.basename(path)]
      elsif @photo.exists?
        url = @photo.url(:original, timestamp: false)
        tmp = download_to_tempfile(url)
        @tmp_file = tmp # Retain reference to prevent GC
        return [tmp.path, File.basename(URI.parse(url).path)]
      end
      nil
    end

    def backup_original_to_s3(path, name)
      @record.send(:save_original_to_s3, path, name)
    end

    def prepare_tempfile(path)
      @tmp_file ||= Tempfile.new(["ai_original", File.extname(path)])
      @tmp_file.binmode
      File.open(path, "rb") { |f| @tmp_file.write(f.read) }
      @tmp_file.rewind
    end

    def download_to_tempfile(url)
      uri = URI.parse(url)
      basename = File.basename(uri.path)
      ext = File.extname(basename)
      tmp = Tempfile.new(["downloaded_image", ext])
      tmp.binmode

      Net::HTTP.start(uri.host, uri.port, use_ssl: uri.scheme == "https") do |http|
        request = Net::HTTP::Get.new uri
        http.request(request) do |response|
          unless response.is_a?(Net::HTTPSuccess)
            raise "Download failed: #{response.code} for #{url}"
          end
          response.read_body { |chunk| tmp.write(chunk) }
        end
      end

      tmp.rewind
      raise "Downloaded file is empty or unreadable" if tmp.size == 0
      tmp
    end

    def handle_error(e)
      @record.skip_reprocess_with_ai = true
      Rails.logger.error "[MediaPipeService] Error processing #{@record.class.name} #{@record.id}: #{e.message}"
      ExceptionNotify.sidekiq_delay.notify_exceptions(
        'MediaPipeService Failure',
        "[MediaPipeService] Error: #{e.message}"
      )
    end

    def cleanup_tmp_file
      @tmp_file.close if @tmp_file
      @tmp_file.unlink if @tmp_file
    rescue => e
      Rails.logger.warn "[AI Reprocessor] Failed to cleanup tempfile: #{e.message}"
    end
  end
end
