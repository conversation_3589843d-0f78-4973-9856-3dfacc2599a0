require 'mime/types'

module AiImageProcessors
  class MediaPipeService
    DETECTION_API_URL = ENV['FACE_DETECTION_API_URL']

    def initialize(file_path)
      @file_path = file_path
    end

    def process!
      image = MiniMagick::Image.open(@file_path)
      response = post_image(@file_path)


      puts response.code.to_s + " " * 1000
      return unless response.code.to_i == 200

      result = JSON.parse(response.body)
      return if result["person_count"] > 1
      width  = result["width"]
      height = result["height"]
      face   = result["face"]
      person = result["person"]

      Rails.logger.info "MediaPipe processing The Image"
      return unless face && person

      crop_data = calculate_crop(face, person, width, height)
      apply_crop(image, crop_data)

      # Resize if necessary
      if image.width < 800 || image.height < 1100
        image.resize "800x1100^"  
        image.gravity "center"    
        image.extent "800x1100"  
      end

      image.write(@file_path)
      return true
    rescue => e
      Rails.logger.error "MediaPipe processing failed: #{e.message}"
      ExceptionNotify.sidekiq_delay.notify_exceptions('MediaPipeService Faliure',  "MediaPipe processing failed: #{e.message}")
    end

    private

    def post_image(path)
      boundary = "----RubyMultipartPost"
      uri = URI.parse(DETECTION_API_URL)

      filename = File.basename(path)
      file_contents = File.binread(path)
      mime_type = MIME::Types.type_for(filename).first.to_s

      post_body = []
      post_body << "--#{boundary}\r\n"
      post_body << "Content-Disposition: form-data; name=\"image\"; filename=\"#{filename}\"\r\n"
      post_body << "Content-Type: #{mime_type}\r\n\r\n"
      post_body << file_contents
      post_body << "\r\n--#{boundary}--\r\n"

      request = Net::HTTP::Post.new(uri.path)
      request["Content-Type"] = "multipart/form-data; boundary=#{boundary}"
      request.body = post_body.join

      http = Net::HTTP.new(uri.host, uri.port)
      http.read_timeout = 60
      http.request(request)
    end

    def calculate_crop(face, person, img_width, img_height)
      person_left   = (person["left"] * img_width).to_i
      person_top    = (person["top"] * img_height).to_i
      person_width  = (person["width"] * img_width).to_i
      person_height = (person["height"] * img_height).to_i
      person_bottom = person_top + person_height

      face_top = (face["top"] * img_height).to_i

      top_padding    = (person_height * 0.065).to_i
      bottom_padding = (person_height * 0.01).to_i

      crop_top    = [face_top - top_padding, 0].max
      crop_bottom = [person_bottom + bottom_padding, img_height].min
      crop_height = crop_bottom - crop_top

      target_width = (crop_height * 5.0 / 6.0).round
      crop_width = [target_width, img_width].min

      person_center_x = person_left + (person_width / 2)
      ideal_left = person_center_x - (crop_width / 2)
      crop_left = [[ideal_left, 0].max, img_width - crop_width].min

      {
        width: crop_width.to_i,
        height: crop_height.to_i,
        left: crop_left.to_i,
        top: crop_top.to_i
      }
    end

    def apply_crop(image, crop)
      image.crop "#{crop[:width]}x#{crop[:height]}+#{crop[:left]}+#{crop[:top]}"
      image.combine_options do |c|
        c.unsharp "1.2x0.8+0.7+0.02"
      end
    end
  end
end
