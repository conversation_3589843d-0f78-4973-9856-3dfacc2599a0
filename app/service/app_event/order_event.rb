module AppEvent
  class OrderEvent < AppEvent::Event

    attr_accessor :order,:app_sorce

    def initialize(id,event_name)
      @order = Order.find id
      @evt_name = event_name
    end
    
    def app_source
      if order.app_source.include?('Android')
        :android
      elsif order.app_source.include?('iOS')
        :ios
      else 
        :web 
      end
    end

    def event_name
      @evt_name
    end

    def clever_tap_payload
      identity = order.user.account.present? ? order.user.account.id.to_s : order.user.id.to_s
      {
        "d" => [
          {
            "identity" => identity,
            "type" => "event",
            "evtName" => event_name,
            "evtData" => clever_tap_event_data
          }
        ]
      }
    end

    def clever_tap_event_data
       {
          "Order Number" => order.number,
          "Billing name" =>  order.billing_name,
          "email" => order.email,
          "billing phone number" => order.billing_phone, 
          "Total Amount" => order.total,
          "Currency" => order.currency_code,
          "Country" => order.country_code
        }
    end
  end
end