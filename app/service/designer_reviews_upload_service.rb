class DesignerReviewsUploadService
  attr_accessor :designer_ids, :enable_reviews, :current_account_email

  def initialize(options = {})
    @designer_ids = options[:designer_ids]
    @enable_reviews = options[:enable_reviews]
    @current_account_email = options[:current_account_email]
  end

  def process_designer_reviews
    failed_designers = []
    updated_designers = []
    
    # Parse comma-separated designer IDs
    designer_id_array = parse_designer_ids(@designer_ids)
    
    if designer_id_array.empty?
      send_updated_data_status(
        "Designer Reviews Update Failed", 
        "No valid designer IDs provided"
      )
      return
    end

    # Process each designer ID
    designer_id_array.each do |designer_id|
      begin
        designer = Designer.find_by(id: designer_id)
        
        if designer.present?
          designer.update_column(:enable_reviews, @enable_reviews)
          updated_designers << designer_id
          
          # Log the change for audit purposes
          Rails.logger.info "Designer #{designer_id} reviews setting updated to #{@enable_reviews} by #{@current_account_email}"
        else
          failed_designers << designer_id
          Rails.logger.warn "Designer #{designer_id} not found during reviews update by #{@current_account_email}"
        end
      rescue => exception
        failed_designers << designer_id
        Rails.logger.error "Error updating designer #{designer_id} reviews setting: #{exception.message}"
      end
    end

    # Send notification email with results
    if failed_designers.present?
      send_updated_data_status(
        "Designer Reviews Update Partially Failed",
        build_result_message(updated_designers, failed_designers)
      )
    else
      send_updated_data_status(
        "Designer Reviews Updated Successfully",
        build_success_message(updated_designers)
      )
    end
  end

  private

  def parse_designer_ids(designer_ids_string)
    return [] if designer_ids_string.blank?
    
    # Split by comma, whitespace, or other non-digit characters and convert to integers
    designer_ids_string.split(/\W+/).map(&:strip).reject(&:blank?).map(&:to_i).uniq
  end

  def build_result_message(updated_designers, failed_designers)
    message = []
    
    if updated_designers.present?
      message << "Successfully updated #{updated_designers.count} designers: #{updated_designers.join(', ')}"
    end
    
    if failed_designers.present?
      message << "Failed to update #{failed_designers.count} designers: #{failed_designers.join(', ')}"
    end
    
    message << "Reviews setting: #{@enable_reviews ? 'Enabled' : 'Disabled'}"
    message.join("\n\n")
  end

  def build_success_message(updated_designers)
    message = []
    message << "All #{updated_designers.count} designers updated successfully"
    message << "Designer IDs: #{updated_designers.join(', ')}"
    message << "Reviews setting: #{@enable_reviews ? 'Enabled' : 'Disabled'}"
    message.join("\n\n")
  end

  def send_updated_data_status(subject, body)
    begin
      MirrawAdminMailer.send_updated_data_status(subject, body).deliver_now
    rescue => exception
      Rails.logger.error "Failed to send notification email: #{exception.message}"
    end
  end
end
