class PartialDispatchService
    def initialize(params)
      @params = params
    end
  
    def call
      fetch_line_items
      filter_by_region if @params[:region].present?
      filter_by_stitch_check if @params[:stitch_check].present?
      group_line_items_by_order
      return download_data_as_csv if @params[:mail_rfd_data] == "Download Data"
      sort_and_paginate_line_items
    end
  
    private
  
    def fetch_line_items
      @line_items_grouped_by_order = LineItem
                                      .joins(:order, :designer_order)
                                      .where(orders: { state: 'partial_dispatch' }, shipment_id: nil, status: nil).where.not(designer_orders: { state: 'canceled' })
                                      .where(
                                        "(line_items.stitching_required = 'Y' AND line_items.stitching_done = 'Y') OR 
                                        (line_items.stitching_required IS NULL AND line_items.received = 'Y' AND line_items.qc_status = 'true') OR 
                                        (line_items.stitching_required = 'N' AND line_items.received = 'Y' AND line_items.qc_status = 'true')"
                                      )
    end
  
    def filter_by_region
      @line_items_grouped_by_order = if @params[:region] == 'International'
                                       @line_items_grouped_by_order.where.not(orders: { country: 'India' })
                                     else
                                       @line_items_grouped_by_order.where(orders: { country: 'India' })
                                     end
    end
  
    def filter_by_stitch_check
      stitch_condition = @params[:stitch_check] == 'Yes' ? 'Y' : [nil, 'N']
      @line_items_grouped_by_order = @line_items_grouped_by_order.where(stitching_required: stitch_condition)
    end
  
    def group_line_items_by_order
      @line_items_grouped_by_order = @line_items_grouped_by_order
                                      .includes(order: [:tags, :events, :delivery_nps_info], designer_order: :rack_list)
                                      .group_by(&:order)
    end

    def sort_and_paginate_line_items
      grouped_array = @line_items_grouped_by_order.to_a.sort_by do |order, _items|
        event = find_partial_dispatched_event(order.events)
        event_time = event.present? ? event.event_timestamp : Time.at(0)
        -((Time.current - event_time) / 1.hour).round
      end
      
      page = (@params[:page] || 1).to_i
      per_page = 50
    
      @line_items_grouped_by_order = WillPaginate::Collection.create(page, per_page, grouped_array.length) do |pager|
        start = (page - 1) * per_page
        pager.replace grouped_array[start, per_page]
      end
    end
  
    def download_data_as_csv
      csv_data = CSV.generate(headers: true) do |csv|
        csv << csv_headers
        @line_items_grouped_by_order.each do |order, items|
          csv << build_csv_row(order, items)
        end
      end
      @csv_data = csv_data
    end
  
    def csv_headers
      ['Order', 'Order Country', 'Customer Name', 'Rack', 'Unpack-Id', 'Total Items', 'Tags', 'Stitching', 'Hours Passed', 'EDD']
    end
  
    def build_csv_row(order, items)
      tags = format_tags(order.tags)
      rack = formatted_rack_codes(items)
      unpack_ids = items.map(&:id).join(', ')
      total_items = items.sum { |li| li.quantity.to_i }
      stitching = format_stitching(items)
      hours_passed = calculate_hours_passed(order)
      edd = order.delivery_nps_info.promised_delivery_date.to_date || ''
  
      [
        order.number,
        order.country,
        order.name,
        rack,
        unpack_ids,
        total_items,
        tags,
        stitching,
        hours_passed,
        edd
      ]
    end
  
    def format_tags(tags)
      all_tags = tags.present? ? tags.collect(&:name) : []
      all_tags.reject { |tag| tag.include?('convert-mkt') }.join(', ').presence || "Tags: none"
    end
  
    def formatted_rack_codes(items)
      items.map do |li|
        next unless li.designer_order
    
        rack_code = li.designer_order.rack_code
        rack_list = li.designer_order.rack_list
        rack_list_code = rack_list.code if rack_list
    
        next unless rack_code.present? && rack_list_code.present?
    
        "#{rack_code}-#{rack_list_code}"
      end.uniq.compact.join(', ')
    end
  
    def format_stitching(items)
      items.map { |li| li.stitching_required == 'Y' ? 'Yes' : 'No' }.uniq.join(', ')
    end
  
    def calculate_hours_passed(order)
      partial_dispatched_event = find_partial_dispatched_event(order.events)
      if partial_dispatched_event.present?
        ((Time.current - partial_dispatched_event.event_timestamp) / 1.hour).to_i
      else
        '-'
      end
    end

    def find_partial_dispatched_event(events)
      events.find { |e| e.notes.include?("partial dispatched") || e.notes.include?("partial_dispatch") }
    end
    
  end
  