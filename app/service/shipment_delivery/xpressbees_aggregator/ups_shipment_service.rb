require 'xpressbees_aggregators/ups_api'
require 'zip'

module ShipmentDelivery::XpressbeesAggregator
  class UpsShipmentService
    def initialize(number, input_data, invoice_items, items_price_total, reference, currency_code, item_ids, shipping_charges)
      @order_number = number
      @order = Order.preload(:order_addon, line_items: :line_item_addons).where(number: number).first
      @input_data = input_data
      @invoice_items = invoice_items
      @items_price_total = items_price_total
      @reference = reference
      @currency_code = currency_code
      @item_ids = item_ids
      @shipping_charges = shipping_charges
    end
  
  
    def shipment_generation_process
      xpressbees_ups_api = XpressbeesAggregators::UpsApi.new
      shipment_req = get_shipment_request_data
      begin
        response = xpressbees_ups_api.create_shipment(shipment_req, @order, @invoice_items)
        if response.present?
          post_shipment_generation_process(response)
        else
          OrderMailer.report_mailer("Xpressbees-UPS Api Error","REQ : Shipment Request: #{shipment_req}, Order: #{@order.number}, Invoice Items: #{@invoice_items} RES : #{response}, Exception: #{exception.inspect}",{'to_email' => ['<EMAIL>', '<EMAIL>', '<EMAIL>'],'from_email_with_name' => ['<EMAIL>']},{}).deliver
        end
      rescue => exception
        @order.add_notes_without_callback(exception.inspect, "Xpressbees-UPS Shipment Creation Error")
        OrderMailer.report_mailer("Xpressbees-UPS Api Error","REQ : Shipment Request: #{shipment_req}, Order: #{@order.number}, Invoice Items: #{@invoice_items} RES : #{response}, Exception: #{exception.inspect}",{'to_email' => ['<EMAIL>', '<EMAIL>', '<EMAIL>'],'from_email_with_name' => ['<EMAIL>']},{}).deliver
      end
    end
  
    def post_shipment_generation_process(response)
      shipper = Shipper.where('lower(name) = ?','xpressbees_ups').first
      awb_number = response.first
      shipment = Shipment.new(
        number: awb_number,
        shipper: shipper,
        price: @items_price_total,
        weight: @input_data[:total_weight],
        line_item_ids: @item_ids,
        order_id: @order.id,
        packaging_type: @input_data[:packaging_type],
        invoicer_id: @input_data[:packer_id],
        packer_id: @input_data[:packer_id],
        mirraw_reference: @reference,
      )
      if shipment.save
        @order.update_attributes(tracking_number: shipment.number, courier_company: shipment.shipper.name)
        ShipmentDelivery::XpressbeesAggregator::UpsShipmentService.label_generation(shipment.id,response)
        SidekiqDelayClassSpecificGenericJob.set(queue: 'default').perform_async("ShipmentDelivery::Invoice", 
                                                                                "generate_invoice", 
                                                                                {invoice_items: @invoice_items, 
                                                                                  order_number: @order.number, 
                                                                                  shipment_id: shipment.id,
                                                                                  upload_invoice: false,  
                                                                                  shipping_charges: @shipping_charges, 
                                                                                  items_price_total: @items_price_total, 
                                                                                  skip_object_creation: true}
                                                                              )

      end
      
    end
  
    def self.label_generation(shipment_id,awb_list)
      @shipment = Shipment.find shipment_id
      xpressbees_ups_api = XpressbeesAggregators::UpsApi.new
      courier_label_image, status_code = xpressbees_ups_api.download_label_zip([awb_list.first])
      if status_code == "200"
        dirctory_path = Rails.root.join('public', 'xpressbees_ups_api')
        unless Dir.exist?(dirctory_path)
          FileUtils.mkdir_p(dirctory_path)  
        end
        output_directory = Rails.root.join('public', 'xpressbees_ups_api' , "152120990031747.zip")
        FileUtils.rm output_directory if output_directory.exist?
        zip_data = courier_label_image.body
        binary_data = zip_data.encode('ASCII-8BIT')
        File.open(output_directory, 'wb') do |file|
          file.write(binary_data)
        end
        file = Rails.root.join('public', 'xpressbees_ups_api', "#{awb_list.first}.pdf")
        File.delete file if file.exist?
        Zip::File.open(output_directory) do |zip_file|
          zip_file.each do |entry|
            entry.extract(File.join(dirctory_path, entry.name))
          end
        end

        if file.exist?
          aws_obj = AwsOperations.create_aws_file("#{awb_list.first}.pdf", File.open(file, 'rb'), false)
          if aws_obj.present? && aws_obj.public_url.present?
            @shipment.label = aws_obj.public_url
            @shipment.save
            File.delete file
            FileUtils.rm output_directory
          end
        end
      end
    end
    
    
    def get_shipment_request_data
      form_data = [
        ['referenceNumber',  @order_number],
        ['pickupLocation', 'Warehouse'],
        ['isPickupAddressSameAsConsignorAddress', 'true'],
        ['consignorPan', '**********'],
        ['consignorGst', '27**********1ZH'],
        ['consignorIec', '0313074984'],
        ['consignorDoc1Type', 'aadhar'],
        ['consignorDoc2Type', 'telephoneBill'],
        ['productsType', 'nonDoc'],
        ['currencyCode', 'USD'],
        ['igstRate', '0'],
        ['igstAmount', '100'],
        ['igstPaymentStatus', 'lut'],
        ['gstInvoiceNumber', '27**********1ZH'],
        ['termsofInvoice', 'FOB'],
        ['weight',  ((@input_data[:total_weight].to_f || 11) * 1000).to_s],
        ['length', @input_data[:shipment_length] || '11'],
        ['height', @input_data[:shipment_height] || '11'],
        ['breadth', @input_data[:shipment_width] || '11'],
        ['csbType', 'csb5'],
        ['consignorAdCode', '05106486000009'],
        ['consignorPanFile', File.open(Rails.root.join('app', 'assets', 'images', 'xpressbees_aggregators', 'pancard.jpg'), 'rb')],
        ['consignorGstFile', File.open(Rails.root.join('app', 'assets', 'images', 'xpressbees_aggregators', 'gst-certificate.jpg'), 'rb')],
        ['consignorIecFile', File.open(Rails.root.join('app', 'assets', 'images', 'xpressbees_aggregators', 'IEC-certificate.jpg'), 'rb')],
        ['consignorAdCodeFile', File.open(Rails.root.join('app', 'assets', 'images', 'xpressbees_aggregators', 'ad-code.jpg'), 'rb')],
        ['consignorDoc1File', File.open(Rails.root.join('app', 'assets', 'images', 'xpressbees_aggregators', 'aadhar-card.jpg'), 'rb')],
        ['consignorDoc2File', File.open(Rails.root.join('app', 'assets', 'images', 'xpressbees_aggregators', 'IEC-certificate.jpg'), 'rb')],
        ['partnerId', 'XBFP13'],
        ['serviceType', 'express']
      ]
    end
  
  end
end