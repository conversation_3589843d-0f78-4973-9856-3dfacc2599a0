module Unbxd
  class BaseService
    IMAGE_SIZES = {'thumb' => '_thumb', 
        'small' => '_small', 
        'small_m' => '_small_m', 
        'small_mo' => '_small_mo',
        'large' => '_large_m',
        'original' => '_zoom',
        'long' => '_long',
        'large_desktop' => '_large',
        'long_desktop' => '_long'
      }
      
    def initialize(params={})
      @params = params.with_indifferent_access
    end
    
    def get_request(url, callback_function_name="")
      url = URI.parse url
      http = Net::HTTP.new url.host, url.port
      http.verify_mode = OpenSSL::SSL::VERIFY_NONE
      http.use_ssl = true
      http.start do |agent|
        @response =  agent.get(url)
        @unbxd_response = JSON.parse(@response.read_body)
      end
      send(callback_function_name.to_sym) if callback_function_name.present?
    end

    def generate_design_metadata(type='')
      return {} if @unbxd_response.empty? || (@unbxd_response.present? && @unbxd_response["#{@params['widget'].downcase}"].nil?)
      data = @unbxd_response["#{@params['widget'].downcase}"]['recommendations']
      all_designs = []
      data.each do |product|
        all_image_sizes = get_all_size_of_image(product['image_urls'] || [])
        design = {
          "id": product['uniqueId'],
          "title": product['title'],
          "design_path": product['product_url'],
          "price": product['price'],
          "discount_price": product['discount_price'].present? ? product['discount_price'] : 0,
          "designer": product['designer'],
          "category_id": product['category_id'],
          "mirraw_recommended": product['mirraw_recommended'],
          "average_rating": product['rating'],
          "grade": product['grade'].present? ? product['grade'] : 0,
          "discount_percent": product['discount_percent'].present? ? product['discount_percent'] : 0,
          "hex_symbol": "20B9",
          "symbol": "Rs",
          "string_symbol": "",
          "sizes": all_image_sizes,
          "total_review": product['total_review'].present? ? product['total_review'] : 0,
          "likes_count": product['likes_count'].present? ? product['likes_count'] : 0,
          "ready_to_ship": product['ready_to_ship'],
          "premium": product['premium'] ? product['premium'] : false
        }
        design.delete('sizes') unless design['sizes']


        all_designs << design
      end
      return all_designs
    end

    def get_all_size_of_image(image_url)
      if image_url.is_a?(Array)
        return {} if image_url.length.eql?(0)
        image_url = image_url.first
      end

      split = image_url.split('.')
      extension = image_url.scan(/.(?:jpg|jpeg|gif|png|JPG|webp)/i)
      file_name_without_extension = image_url.gsub(extension[0], '')
      all_image_sizes = {}
      base_url = "#{ENV['IMAGE_BASE_URL']}#{file_name_without_extension}"
      IMAGE_SIZES.each do |size_type, size_snippet|
        this_size_url = nil
        this_size_url = "#{base_url}#{size_snippet}#{extension.first}" if extension.length != 0 
        all_image_sizes[size_type] = this_size_url
      end

      return all_image_sizes
    end
  end
end