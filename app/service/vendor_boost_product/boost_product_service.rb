module VendorBoostProduct
  class BoostProductService
    def initialize(boosted_design_id)
      @boosted_design = BoostedDesign.find boosted_design_id
      return unless @boosted_design
      @design = @boosted_design.design
      @category = @boosted_design.category
    end
  
    def call
      ActiveRecord::Base.transaction do
        update_previous_states
        update_design_rank
        index_design
        mark_status_active
        DemoteBoostedProductJob.perform_at(@boosted_design.boost_end_time, @boosted_design.id)
      end
    end

    def mark_status_active
      @boosted_design.activate
    end

    def index_design
      Sunspot.index(@design)
      Sunspot.commit
    end

    def fetch_all_platform_country_tags
      # Get All Exisiting Grading Tags and Create ALL Platform ALL Country Tag if not Present 
      grading_tags = GradingTag.includes(:design_grading_tags).where(
        app_name: 'Mirraw',
        grading_taggable_type: "Category",
        grading_taggable_id: @category.id
      )
      
      all_platform_country_tag = grading_tags.find_by(
        platform: "All",
        country_code: "All"
      )
      
      # creating all platform all country tag
      unless all_platform_country_tag
        all_platform_country_tag = GradingTag.create!(
          name: "mirraw_all_all_Category_#{@category.id}",
          platform: "All",
          country_code: "All",
          grading_taggable_id: @category.id,
          grading_taggable_type: "Category",
          app_name: "Mirraw"
        )
      end
  
      grading_tags = grading_tags.to_a.push(all_platform_country_tag).uniq
    end
  
    def update_design_rank
      grading_tags = fetch_all_platform_country_tags
      grading_tags.each do |grading_tag|
        existing_tags = grading_tag.design_grading_tags.order(rank: :desc).to_a
        existing_design_tag = existing_tags.find { |dgt| dgt.design_id == @design.id }
        batches = existing_tags.each_slice(3).to_a
        added = false
  
        batches.each do |batch|
          next if batch.any?(&:currently_boosted?)
          random_entry = batch.sample

          calculated_new_rank = random_entry.rank - 1
          new_rank = [calculated_new_rank, 1].max # Ensures rank is at least 1
  
          if existing_design_tag
            existing_design_tag.update!(rank: new_rank)
          else
            DesignGradingTag.create!(
              design_id: @design.id,
              grading_tag_id: grading_tag.id,
              rank: new_rank
            )
          end
  
          added = true
          break
        end
  
        unless added
          # Determine the base rank from the lowest existing rank or use a default.
          base_rank_for_new_entry = if existing_tags.last && existing_tags.last.rank
                                      existing_tags.last.rank
                                    else
                                      100 # Default if no existing tags or their rank is nil/false
                                    end
          
          calculated_rank = base_rank_for_new_entry - 1
          new_rank = [calculated_rank, 1].max # Ensures rank is at least 1
  
          if existing_design_tag
            existing_design_tag.update!(rank: new_rank)
          else
            DesignGradingTag.create!(
              design_id: @design.id,
              grading_tag_id: grading_tag.id,
              rank: new_rank
            )
          end
        end
      end
    end
  
    def update_previous_states
      grading_tags = GradingTag.where(app_name: 'Mirraw', grading_taggable_type: "Category", grading_taggable_id: @category.id)
      previous_states = {}
      grading_tags.each do |grading_tag|
        existing_tag = grading_tag.design_grading_tags.find_by(design_id: @design.id)
        previous_states[grading_tag.name] = existing_tag.rank if existing_tag
      end
      previous_states
      @boosted_design.update_attributes({previous_states: previous_states.to_json})
    end
  end
end