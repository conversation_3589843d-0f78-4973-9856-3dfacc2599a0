  module VendorBoostProduct
  class CreateAdjustmentService

    DEFAULT_ADJUSTMENT_DETAILS = {
      billing_name: "Adjustment Order Do NOT Dispatch",
      billing_email: "<EMAIL>",
      billing_phone: "9967430880",
      billing_pincode: 400070,
      billing_country: "India",
      billing_city: "Mumbai",
      billing_state: "Maharashtra",
      billing_street: "Adjustment Order Do NOT Dispatch",
      name: "Adjustment Order Do NOT Dispatch",
      email: "<EMAIL>",
      phone: "9967430880",
      pincode: 400070,
      country: "India",
      city: "Mumbai",
      buyer_state: "Maharashtra",
      street: "Adjustment Order Do NOT Dispatch",
      country_code:"IN",
      pay_type: "NONE",
      app_source: "web",
      state: "cancel",
    }.freeze

    def initialize(boosted_design)
      @boosted_design = boosted_design
      @design = @boosted_design.design  
      @designer = @boosted_design.designer
    end

    def call
      create_negative_adjustments
    end

    def create_negative_adjustments
      amount = @boosted_design.boost_fee
      order = Order.create!(DEFAULT_ADJUSTMENT_DETAILS.merge(number: create_random_adjustment_number))
      order.add_notes_without_callback("Negeative Adjustment Order for Designer #{@designer.id} and Design #{@design.id}", "Adjustment Order")
      dos = order.designer_orders.create({designer_id: @designer.id, ship_to: 'customer'})
      dos.add_notes_without_callback("Negative Adjustment Created For Product Boost Design - ID #{@design.id}",'adjustment')
      adjustment_note = "Product Boost Cost for Design - ID #{@design.id}. Duration #{@boosted_design.boost_start_time} to #{@boosted_design.boost_end_time}"
      Adjustment.create!(amount: -1*amount, designer_id: dos.designer.id, order_id: order.id, notes: adjustment_note, status: 'unpaid', designer_order_id: dos.id)
    end

    def create_random_adjustment_number
      record = true
      while record
        random = "Ad#{Array.new(8){rand(9)}.join}"
        record = Order.unscoped.where(number: random).exists?
      end
      random
    end
  end
end
