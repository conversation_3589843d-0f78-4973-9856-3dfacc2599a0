= javascript_include_tag 'designs'
= javascript_include_tag 'admin_mirraw'
= form_tag admin_designs_under_review_url, method: :post do
  Designer Name:
  = select_tag "designer_id" , options_from_collection_for_select(@designers,:first,:last,params[:designer_id])
  = select_tag "state", options_for_select(['review','reject','processing'],params[:state])
  = label_tag :start_date, "Start Date"
  = text_field_tag :start_date, params[:start_date] , class: "datepicker start_date", style: 'color:black; padding: 6.5px;', autocomplete: 'off'    
  = label_tag :end_date, "End Date"
  = text_field_tag :end_date, params[:end_date] , class: "datepicker end_date", style: 'color:black; padding: 6.5px;',autocomplete: 'off'
  = submit_tag "GET LIST", class: "btn btn-primary"
= will_paginate @designs, params: { designer_id: params[:designer_id], state: params[:state], start_date: params[:start_date], end_date: params[:end_date] }
= form_tag admin_designs_under_review_url, method: :post do
  %list
    - if @designs.present?
      %table.table.table-bordered
        %tr
          %th
            = link_to 'All | ', 'javascript:void(0)', :class => "unpublish_designs_checkbox_all"
            = link_to 'None', 'javascript:void(0)', :class => "unpublish_designs_checkbox_none"
          %th Img
          %th Imgs
          %th
          %th Price
          %th Selling Price
          %th Desc
          %th Spec
          %th Cat
          %th Cats
          %th State
          - if params[:state] == 'reject'
            %th  Notes
          %th Action
        %tbody
          - @designs.each do |design|
            - if design && design.images.present? && design.categories.present? && design.designer.present?
              %tr
                %td= check_box_tag "design_ids[]", design.id
                %td{style:'width:225px;'}
                  %div{style: 'max-width:200px;height:300px'}
                    .carousel.slide{'id' => "myCarousel#{design.id}", data: {interval: false}}
                      .carousel-inner
                        -design.images.each_with_index do |image, i|
                          .item{class: "#{'active' if i==0}"}
                            =image_tag(image.photo.url(:small), style: 'max-height:225px;')
                            %p{style: 'width:inherit,height:70px;padding:2px;'}
                              -if image.photo_file_size.present?
                                Size : #{number_to_human_size(image.photo_file_size)}
                              -if image.height.present? && image.width.present?
                                %br
                                Resolution : #{image.width} x #{image.height}
                              -if image.photo_content_type.present?
                                %br
                                Format : #{image.photo_content_type}
                      -if design.images.length > 1
                        %p{style:'height: 30px'}
                          %a.left.btn-sm.btn-primary{"data-slide" => "prev", :href => "#myCarousel#{design.id}"}
                            %span.glyphicon.glyphicon-circle-arrow-left
                          %a.right.btn-sm.btn-primary{"data-slide" => "next", :href => "#myCarousel#{design.id}"}
                            %span.glyphicon.glyphicon-circle-arrow-right
                %td= design.images.size
                %td
                  = link_to design.title, designer_design_path(design.designer_id, design.id)
                  = '[ '
                  = link_to "edit", edit_designer_design_path(design.designer_id, design.id)
                  = ' ] - '
                  = link_to design.designer.name, search_designer_url(design.designer_id)
                %td= design.price
                %td= design.discount_price
                %td= design.description
                %td
                  -if design.specification.present?
                    %p Specification: #{design.specification}
                  -if design.package_details.present?
                    %p Package Details: #{design.package_details}
                  -if design.weight.present?
                    %p Weight: #{design.weight}
                  -if design.color.present?
                    %p Colour: #{design.color}
                  %hr
                  - design_spec_hash = get_spec_data(design, nil, DESIGN_SPEC_SUB_GROUP[design.designable_type.presence].to_a) || {}
                  - design_spec_hash.with_indifferent_access.except(*EXCLUDED_DESIGN_SPEC_KEYS).each do |spec, values|
                    - if values.present? && values.is_a?(Hash)
                      %b= "#{spec.to_s.titleize}#{' Details' unless spec.to_s == 'other_data'}"
                      %br
                      - values.each do |key,value|
                        %p
                          ="#{key.to_s.titleize} :"
                          -if key == 'type' && design.categories.present? && (breadcrumb = design.categories.first.breadcrumb_path.to_a.last).present?
                            - title,url = breadcrumb.first,breadcrumb.second
                            = link_to url, target: '_blank', class: 'brand-link' do
                              = title.to_s.titleize
                            = ", "
                          = value.to_s.titleize
                %td
                  - design.categories.each do |cat|
                    - if cat.title.present?
                      = cat.title + ', '
                    - else
                      = cat.name + ', '
                  - if dynamic_size_chart = design.dynamic_size_chart
                    %button#dynamic-size-chart.btn.btn-link{"data-target" => "#modal-dynamic-size-chart-#{design.id}", "data-toggle" => "modal", type: "button"} Size Chart
                    .modal.fade{role: "dialog", id: "modal-dynamic-size-chart-#{design.id}"}
                      .modal-dialog
                        .modal-content
                          .modal-header
                            %button.close{"aria-label" => "Close", "data-dismiss" => "modal", type: "button"}
                              %span{"aria-hidden" => "true"} &times;
                            %h4.modal-title.text_common_black
                              Sizing Guide
                          .modal-body
                            = render dynamic_size_chart

                %td= design.categories.size
                %td{:id => design.id.to_s+'_state'}= design.state
                - if params[:state] == 'reject'
                  %td{:id => design.id.to_s + '_notes'}= design.notes
                %td
                  = button_tag 'Approved', :value => 'approved', :data => {:id => design.id, :refresh => 0}, :class => 'button-small state-change btn btn-sm btn-success'
                  = button_tag 'Delete', :value => 'delete', :data => {:id => design.id, :refresh => 0, confirm: "Are you sure?"}, :class => 'button-small state-change btn btn-sm btn-danger'
                  = button_tag 'Reject', :value => 'reject', :data => {:id => design.id, :refresh => 0}, :class => 'button-small state-change btn btn-sm btn-warning'
          %tr
            %td{:colspan => '100%'}
              Reason :
              =text_field_tag 'reason', nil, :size => 100, :placeholder => 'Enter reason for reject only'
    - else
      %h3 No Designs Found      
  = submit_tag 'approved', :class => 'btn btn-sm btn-success', :name => 'state_change'
  = submit_tag 'delete', :class => 'btn btn-sm btn-danger', :name => 'state_change',data: {confirm: "Are you sure?"}
  = submit_tag 'reject', :class => 'btn btn-sm btn-warning', :name => 'state_change'


:javascript
  $(document).ready(function() {
      MR.bestsellers.datePicker();  
  })
