/ #= form_tag review_designer_batches_url, :method => "GET",:remote => 'true' do
/   Designer Name:
/   #= select_tag 'designer_id' , options_for_select(@designers, @designer), :id => 'select_designer'
/   #= select_tag 'bulk_upload_ids', options_for_select(['--No Batch Selected--']), hidden: :true
/   #= submit_tag 'GET LIST',id: :'review_list', hidden: :true
= javascript_include_tag 'designs'
.grid-block
  = form_tag review_designer_batches_url, class: "form-inline", method: :post do
    .small-12.medium-4.grid-content
      Designer Name :
      = select_tag 'designer_name', options_from_collection_for_select(@designer_name,:first,:last,params[:designer_name])
      = select_tag 'version', options_for_select(@version,session[:review_version])
      = select_tag 'filter', options_for_select({ "All" => "all", "Passed" => "passed", "Failed" => "failed" }, params[:filter])
      = submit_tag 'GET LIST', class: "btn btn-primary btn-sm"
      %br
      %br
    .small-12.medium-4.grid-content
      Bulk Upload Id:
      = select_tag 'bulk_upload_ids', options_for_select(@ids,params[:bulk_upload_ids])
      &nbsp;&nbsp;&nbsp;
      Dates 
      %input.datepicker{:name=>'date_from',:id=>'txtFrom',:type => "text", class: "form-control"}
      Between
      %input.datepicker{:name=>'date_to',:id=>'txtTo',:type => "text", class: "form-control"}
      = submit_tag 'GET LIST', class: "btn btn-primary btn-sm"
      %br
      %br
  - if params[:bulk_upload_ids].present? and (design = @designs.to_a.first).present? and params[:date_from].blank? and params[:date_to].blank?
    %table.table.table-bordered
      %tr
        %th Designer Batch
        %th Designer Name
        %th Total Failed
        %th Total Succeded
        %th Total Records
        %th File Url
      %tbody
        %tr
          %td= "#{params[:bulk_upload_ids]}"
          %td= "#{design.designer.try(:name)}"
          %td= "#{design.designer_batch.try(:failed)}"
          %td= "#{design.designer_batch.try(:passed)}"
          %td= "#{design.designer_batch.try(:no_of_records)}"
          %td= link_to "#{design.designer.try(:name)}_#{params[:bulk_upload_ids]}", "#{design.designer_batch.try(:filename)}"

= form_tag review_designer_batches_url, method: :post do
  =hidden_field_tag :designer_name,params[:designer_name]
  =hidden_field_tag :version,params[:version]
  =hidden_field_tag :bulk_upload_ids,params[:bulk_upload_ids]
  =hidden_field_tag :date_from,params[:date_from]
  =hidden_field_tag :date_to,params[:date_to]
  %list
  - if @designs.present?
    = will_paginate @designs, params: params
    %table.table.table-bordered
      %tr
        %th
          = link_to 'All | ', 'javascript:void(0)', :class => "unpublish_designs_checkbox_all"
          = link_to 'None', 'javascript:void(0)', :class => "unpublish_designs_checkbox_none"
        %th Image
        %th Images Count
        %th Title
        %th Price
        %th Desc
        %th Category
        %th State
        %th design_code
        %th Designable/Properties
        - if params[:state] == 'reject'
          %th  Notes
        %th Action
      %tbody
        - @designs.each do |design|
          - if design.designer.present?
            %tr
              %td= check_box_tag "design_ids[]", design.id
              %td{style:'width:225px;'}
                -if design.images.present?
                  %div{style: 'max-width:200px;height:300px'}
                    .carousel.slide{'id' => "myCarousel#{design.id}", data: {interval: false}}
                      .carousel-inner
                        -design.images.each_with_index do |image, i|
                          .item{class: "#{'active' if i==0}"}
                            =image_tag(image.photo.url(:small), style: 'max-height:225px;')
                            %p{style: 'width:inherit,height:70px;padding:2px;'}
                              -if image.photo_file_size.present?
                                Size : #{number_to_human_size(image.photo_file_size)}
                              -if image.height.present? && image.width.present?
                                %br
                                Resolution : #{image.width} x #{image.height}
                              -if image.photo_content_type.present?
                                %br
                                Format : #{image.photo_content_type}
                      -if design.images.length > 1
                        %p{style:'height: 30px'}
                          %a.left.btn-sm.btn-primary{"data-slide" => "prev", :href => "#myCarousel#{design.id}"}
                            %span.glyphicon.glyphicon-circle-arrow-left
                          %a.right.btn-sm.btn-primary{"data-slide" => "next", :href => "#myCarousel#{design.id}"}
                            %span.glyphicon.glyphicon-circle-arrow-right
              %td= design.images.length
              %td
                = link_to design.title, designer_design_path(design.designer_id, design.id)
                = '[ '
                = link_to "edit", edit_designer_design_path(design.designer_id, design.id)
                = ' ] - '
                = link_to design.designer.name, search_designer_url(design.designer.id)
              %td= design.price
              %td= design.description
              %td
                -if design.categories.present?
                  - design.categories.each do |cat|
                    - if cat.title.present?
                      = cat.title + ', '
                    - else
                      = cat.name + ', '
                %br
                - if dynamic_size_chart = design.dynamic_size_chart
                  %button#dynamic-size-chart.btn.btn-link{"data-target" => "#modal-dynamic-size-chart-#{design.id}", "data-toggle" => "modal", type: "button"} Size Chart
                  .modal.fade{role: "dialog", id: "modal-dynamic-size-chart-#{design.id}"}
                    .modal-dialog
                      .modal-content
                        .modal-header
                          %button.close{"aria-label" => "Close", "data-dismiss" => "modal", type: "button"}
                            %span{"aria-hidden" => "true"} &times;
                          %h4.modal-title.text_common_black
                            Sizing Guide
                        .modal-body
                          = render dynamic_size_chart
              %td{:id => design.id.to_s+'_state'}= design.state
              - if params[:state] == 'reject'
                %td{:id => design.id.to_s + '_notes'}= design.notes
              
              %td= design.design_code
              %td 
                -if design.specification.present?
                  %p Specification: "#{design.specification}" 
                -if design.package_details.present?
                  %p Package Details: "#{design.package_details}" 
                -if design.weight.present?
                  %p Weight: "#{design.weight}"
                -if design.color.present?
                  %p Colour: "#{design.color}"
                %hr
                - if (designable = design.designable).present?
                  - designable.attributes.select{|key,value| value.present?}.except("id","created_at","updated_at").each do |key,value|
                    %p= key.to_s + ":" + value.to_s
                %hr
                - if (delay_designable=design.delay_designables.first).present?
                  - delay_designable.get_params.each do |key,value|
                    %p= key.to_s + ":" + value.to_s

              %td
                = button_tag 'Approve', :value => 'approved', :data => {:id => design.id, :refresh => 0}, :class => 'btn btn-success btn-sm state-change'
                = button_tag 'Reject', :value => 'reject', :data => {:id => design.id, :refresh => 0}, :class => 'btn btn-warning btn-sm state-change'
                %br
                %br
                %br
                = button_tag 'Delete', :value => 'delete', :data => {:id => design.id, :refresh => 0, confirm: "Are you sure?"}, :class => 'btn btn-danger btn-sm state-change'
        %tr
          %td{:colspan => '100%'}
            .form-group.col-md-5
              Reason :
              =text_field_tag 'reason', nil, :placeholder => 'Enter reason for reject only', class: 'form-control'
  - else
    %h3 No Designs Found
  = submit_tag 'approved', :value => 'approved', :class => 'btn btn-success btn-sm', :name => 'state_change'
  = submit_tag 'reject', :value => 'reject', :class => 'btn btn-warning btn-sm', :name => 'state_change'
  .pull-right= submit_tag 'delete', :value => 'delete', :class => 'btn btn-danger btn-sm', :name => 'state_change',data: {confirm: "Are you sure?"}

- if params[:bulk_upload_ids].present? && @failed_images.present?
  .row
    %hr
    %h4
      %b Designs with Failed Images -
      =link_to 'View / Update Failed Images', failed_designs_path(@failed_images[0].design.designer_id, failed_batch_id: params[:bulk_upload_ids])
    .col-md-8
      %table.table.table-bordered
        %tr
          %th mirraw id
          %th SKU code
          %th Design State
          %th Message
          %th url
        %tbody
          - @failed_images.each do |image|
            =form_tag(review_designer_batches_url,method: 'post') do
              %tr{:id => "row_#{image.id}"}
                -if image.message.present?
                  %td= image.design_id
                  %td= image.design.design_code
                  %td= image.design.state
                  %td.col-md-4= "Error: " + image.message.to_s
                  %td.col-md-4= link_to image.url, image.url.truncate(30)
                -else
                  %td= image.design_id
                  %td= image.design.design_code
                  %td= image.design.state
                  %td= "Processing"
                  %td= image.url.truncate(30)
