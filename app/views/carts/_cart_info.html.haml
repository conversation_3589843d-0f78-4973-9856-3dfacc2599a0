- @design_view_ga_params = {}
#cart_info
  - if country_or_currency_indian? && !PAY_WITH_AMAZON_DISABLED
    %script{src: PayWithAmazon.widget_url}
  - if @cart.id != -1 && @cart.line_items.present?
    - bmgnx_hash = PromotionPipeLine.bmgnx_hash
    - if bmgnx_hash.present?
      - bmgnx_free_items = @cart.get_free_items_bmgnx(bmgnx_hash)
    #cart-details.row
      - if params[:controller] == 'carts' && params[:action] == 'show'
        .col-md-12.col-lg-12
          #shopping-info.col-sm-3.cart-card= "SHOPPING CART | (#{pluralize(@cart.line_items.length, "Item")}) "
      .col-md-12.col-lg-8.nopadding
        - offer_text, cart_add_disc = get_best_offer_for_cart_text(@cart, @conversion_rate, @actual_country, @country_code, @symbol)
        - if BEST_OFFER_THRESHOLD.to_i >= 0
          - if offer_text.present?
            .row.best_offer
              %span= offer_text
        - if bmgnx_hash.present?
          -bmgnx_offer_msg = @cart.get_bmgnx_notice
          - if bmgnx_offer_msg
            .row.best_offer.animate-border
              =link_to "B#{bmgnx_hash[:m]}G#{bmgnx_hash[:n]} scheme :",'/buy-m-get-n-free', class: 'hyperlink_text'
              %span= bmgnx_offer_msg
        - elsif (qpm_msg = @cart.next_qpm_msg).present?
          .row.best_offer.animate-border
            %span= qpm_msg
        -cart_addon_design = @cart.cart_addon_designs(@country_code)
        - for store in @cart.designer_stores
          - first = true
          - items  = (store[:items] - @cart.cart_addon_item(@country_code))
          - for item in items
            - store_name = truncate(store[:name].camelize, length: 20, omission: "", separator: ' ')
            - total_addons_price = 0
            - if (@addons = item.get_addons())
              - @addons.each do |addon|
                - addon_price = get_sub_total_in_currency(addon.snapshot_price, item.quantity)
                - total_addons_price += addon_price
            .row.cart-card
              - image_source = item.design.master_image.present? ? item.design.master_image.photo(:small) : asset_path("app_screenshot7.jpg")
              .product-delete
                - if current_account.try(:user?)
                  = link_to "MOVE TO WISHLIST", line_item_path(item, move_to_wishlist: true), method: :delete, remote: true, class: "remove_item move_to_wishlist", title: 'Move To Wishlist'
                  |
                = link_to "REMOVE", line_item_path(item, remove_from_cart: true), method: :delete, remote: true, class: "remove_item label label-default", title: 'Delete from cart'
              .product-img-tab.col-sm-3
                = link_to designer_design_path(item.design.designer, item.design), target: '_blank' do
                  = image_tag(image_source, alt: item.title, class: 'product-image img-responsive')
              .product-desc.col-sm-9
                .col-sm-12
                  %h4
                    = link_to truncate(item.design.title.capitalize, length: 52), designer_design_path(item.design.designer, item.design), target: '_blank', class: 'product-name'
                .col-sm-12.designer-name= "By #{store_name}"
                .col-sm-12{style: 'padding: 0px; margin: 12px 0px;'}
                  .col-sm-6.product-quantity
                    - design_quantity = item.max_quantity_on_cart
                    - design_quantity = 10 if design_quantity > 10
                    %span Qty : 
                    = select_tag "quantity_#{item.id}", options_for_select((1..design_quantity).to_a, item.quantity), class: 'quantity_list update_quantity', data: {id: item.id.to_s}
                  .col-sm-6.product-price.text-right= "Price : #{get_price_in_currency_with_symbol(item.price)}"
                - if bmgnx_hash.present? && item.buy_get_free == 1
                  - message = "Buy #{bmgnx_hash[:m]} #{'item'.pluralize(bmgnx_hash[:m])} and get "
                  - message += bmgnx_hash[:x] != 100 ? "#{bmgnx_hash[:x]}% off on next #{bmgnx_hash[:n]} #{'item'.pluralize(bmgnx_hash[:n])}" : "next #{bmgnx_hash[:n]} #{'item'.pluralize(bmgnx_hash[:n])} free"
                  .col-sm-12{style: 'padding: 0px; margin: 12px 2px;'}
                    .b1g1.col-sm-10
                      - bmgnx_message = Promotions.bmgnx_offer_message
                      =link_to bmgnx_message, '/buy-m-get-n-free', title: "View More - #{bmgnx_message}"
                      .close_design_promo_tnc.promo_tnc_close x
                      .design_promo_tnc
                        .b1g1_text.b1g1_info{title: "How to avail this offer : #{message}", data: {toggle:'tooltip', placement: 'right'}} i
                        = render :partial => '/shared/bmgn_tnc_in_detail'
                    -#   .info_text_b1g1.b1g1_info.col-sm-12
                    -#     = link_to "How to avail this offer : #{message} - click here", '/buy-m-get-n-free'
                - note_exists_without_bmgn = item.note_without_bmgn.present?
                - if note_exists_without_bmgn
                  .col-sm-12{class: ('minim-height' unless (@addons.present? || note_exists_without_bmgn)), style: 'padding-right:0px;'}
                    = render partial: '/carts/notes', locals: {item: item}
                - if @addons.present?
                  .col-sm-12{style: 'padding-right: 0px;'}
                    .cart_addons= render partial: 'carts/cart_info_addon', locals: {item: item}
                .row
                  - item_total = @country_code == "IN" ? (get_sub_total_in_currency(item.price,item.quantity) + total_addons_price).ceil : (get_sub_total_in_currency(item.price,item.quantity) + total_addons_price).round(2)
                  - if bmgnx_free_items.present? && bmgnx_free_items.keys.include?(item.id)
                    .col-sm-12.text-right.product-total= "Price of Item : #{@symbol} #{item_total}"
                    - discount = bmgnx_free_items[item.id][0] * bmgnx_free_items[item.id][1]
                    - discount = bmgnx_hash[:x] != 100 ?  discount * bmgnx_hash[:x]/100.to_f : discount 
                    - item_discount = get_price_in_currency(discount).round(2)
                    - bmgnx_message = bmgnx_hash[:x] != 100 ? "You got #{bmgnx_hash[:x]} % off on #{bmgnx_free_items[item.id][1]} products" : (bmgnx_free_items[item.id][1] == item.quantity ? "This item is free" : "Quantity #{bmgnx_free_items[item.id][1]} of this item is free") 
                    .col-sm-12.text-right.product-total
                      .b1g1_colored=bmgnx_message
                    .col-sm-12.text-right.product-total= "Final Price : #{@symbol} #{(item_total - item_discount).round(2).abs}"
                  - else
                    .col-sm-12.text-right.product-total= "Final Price : #{@symbol} #{item_total}"
            -if cart_addon_design[item.design.designable_type].present?
              = render partial: '/carts/cart_addon_design', locals: {cart_addon_design: cart_addon_design[item.design.designable_type]}
              - cart_addon_design[item.design.designable_type] = nil
      .col-md-12.col-lg-4     
        -wallet_present = @cart.coupon.blank? && current_account.try(:has_valid_money, @country_code).to_i > 0
        -referral_checked = @cart.referral_amount > 0
        #coupon-tab.col-lg-12.col-md-12.col-sm-12.col-xs-12.nopadding.cart-card
          -if both_coupon_and_reward_rendered = (!account_signed_in? && @cart.coupon.blank? || wallet_present && !referral_checked)
            #apply-coupon-or-wallet
              .row
                = button_tag "APPLY COUPON", disable_with: 'Wait...', class: 'apply-btn coupon-button'
                %span OR
                = button_tag "APPLY REWARD", disable_with: 'Wait...', class: 'apply-btn wallet-button'
          #apply-coupon-tab{style: ('display: block' if (!wallet_present || @cart.coupon.present?) && !both_coupon_and_reward_rendered)}
            .row
              = form_tag('/carts/apply_coupon', {method: "post", remote: true}) do
                = hidden_field_tag 'coupon_cart_id', @cart.id
                .col-sm-6
                  = text_field_tag 'coupon_code', nil, required: true, placeholder: 'Enter Coupon Code',id: 'coupon-box'
                .col-sm-6
                  - coupon_text = @cart.coupon.present? ? "TRY OTHER" : "APPLY COUPON"
                  = submit_tag coupon_text, disable_with: 'Wait...', class: 'btn', id: 'coupon-btn'
          #apply-wallet-tab{style: ('display: block' if referral_checked)}
            - if wallet_present
              - wallet_text = referral_checked ? 'REMOVE' : "APPLY REWARD"
              =form_tag(apply_wallet_discount_path, {id: 'apply_wallet_discount_form',method: 'post', remote:true}) do
                = hidden_field_tag 'wallet_cart_id', @cart.id
                = hidden_field_tag 'wallet_user_id', current_account.try(:user).try(:id)
                = hidden_field_tag 'referral', referral_checked ? 'remove' : 'apply'
                -if referral_checked
                  .col-sm-8{style: "padding-right: 0px"}
                    #wallet-alert-msg.col-sm-12.small.nopadding.alert-msg #{get_price_with_symbol(@cart.referral_amount, @cart.wallet.currency_symbol)} applied as wallet discounts
                  .col-sm-4{style: "padding-left: 0px"}
                    = submit_tag wallet_text, class: 'btn', id: 'referral_checkbox', name: 'referral_checkbox'
                -else
                  .col-sm-6
                    - usable_amount = @cart.applicable_referral_amount
                    .usableText
                      Usable Reward: #{get_price_in_currency_with_symbol(usable_amount, true)}
                      %i (#{Wallet.usage_percent}% of Sub Total can be used #{"upto #{Wallet.usage_upto}" if Wallet.usage_upto?})
                  .col-sm-6
                    = submit_tag wallet_text, class: 'btn', id: 'referral_checkbox', name: 'referral_checkbox'
            - else
              %p.sign-in-link
                You are not signed in. Please
                = link_to 'Sign In', account_session_path, class: 'link'
                to apply reward points.
          .row
            - if @cart.coupon.present? && @cart.errors[:coupon].blank?
              #coupon-alert-msg.col-sm-12.small.alert-msg{style:'color: #4ca958;'}= "#{@cart.coupon.code} Coupon Applied"
            - elsif @cart.errors[:coupon].present?
              #coupon-alert-msg.col-sm-12.small.alert-msg
                Coupon #{@cart.errors[:coupon].first}
                - if @coupon.present? && @cart.errors[:coupon].first.include?('minimum')
                  Purchase items worth more than #{get_price_in_currency_with_symbol(@coupon.min_amount)}!
            - elsif @cart.errors[:wallet].present?
              #wallet-alert-msg.col-sm-12.small.alert-msg= "Wallet #{@cart.errors[:wallet].first}"
            - else
              #coupon-alert-msg.col-sm-12.small.alert-msg
            - if false #delibrately false
              .col-sm-3{style: 'text-align: right;'}
                = link_to 'See Coupons', coupons_path, id: 'coupon-link-url', target: '_blank'
          -if wallet_present
            .use-msg
              %span.small Only one of 
              = button_tag "coupon", disable_with: 'Wait...', class: 'apply-btn-link small coupon-button coupon'
              %span.small or
              = button_tag "reward", disable_with: 'Wait...', class: 'apply-btn-link small wallet-button wallet'
              %span.small can be used per order
          - unless GIFT_WRAP_PRICE.to_f < 0 || country_or_currency_indian?
            .row.gift_wrap_option
              - price = get_price_in_currency_with_symbol(GIFT_WRAP_PRICE.to_f, false, nil)
              - label = session[:gift_wrapped] ? 'Gift Wrapped!' : "Add gift wrap for #{price}"
              = check_box_tag(:gift_wrap_checkbox, 'Gift Wrap', session[:gift_wrapped])
              = label_tag(:gift_wrap_label, label, data: {price: price}, class: 'gift_wrap_label')
              %span.glyphicon.glyphicon-gift{style: 'margin-left: 5px;'}
          .row.order-summary
            .text ORDER SUMMARY
            - totals.each do |key, total_attr|
              .row
                %div{class: total_attr[:class], style: "border: none; padding: 0px;"}
                  .col-sm-6
                    = total_attr[:title]
                  .col-sm-6.text-right
                    - if [:cart_discounts, :coupon_discounts].include?(key)
                      %span.discounts_text
                        = "(#{(total_attr[:amount]/totals[:item_total][:amount]*100.0).round(0)}% OFF)"
                    %span
                      #{get_price_with_symbol(total_attr[:amount], @symbol)}
          - if @country_code == 'IN' && totals[:shipping][:amount].present? && totals[:shipping][:amount] > 0 && ESSENTIAL_DESIGNERS["total_below_x"] <= 0
            .row
              .col-sm-12.add_more_items
                Shop for 
                %span.charges 
                  = get_price_with_symbol(add_more_items_value_for(totals[:grandtotal][:amount], totals[:shipping][:amount]), @symbol)
                more  to  get  
                %span.strong 
                  FREE DELIVERY
          - if country_or_currency_indian? && !PAY_WITH_AMAZON_DISABLED && @cart.has_minimun_quantity
            .row
              .col-sm-12
                = render 'carts/pay_with_amazon'
          .row
            .col-sm-12.order-checkout
              = link_to new_order_url(protocol: Mirraw::Application.config.partial_protocol), id: "checkout", class: "checkout_link" do
                = 'place Order'
        - cart_total = totals[:item_total][:amount] - (totals[:coupon_discounts] ? totals[:coupon_discounts][:amount] : 0)
        - grand_total = totals[:grandtotal][:amount]*@conversion_rate
        - amount_to_credit = Wallet.cashback_for_amount(grand_total)
        - if free_shipment_text.present? || (cart_add_disc.present? && cart_total <= get_price_in_currency(cart_add_disc[1].last.to_i)) || @free_stitching_text.present? || amount_to_credit.to_i > 0 || @country_code == 'IN'
          #offer-tab.col-lg-12.col-md-12.col-sm-12.col-xs-12.nopadding.cart-card
            .col-sm-12.text OFFERS
            - if @country_code == 'IN' && ESSENTIAL_DESIGNERS["total_below_x"] <= 0
              .col-sm-12
                .free_shipment_text.text-center
                  %div
                    - if DOMESTIC_SHIPPING_CHARGES.keys.last.to_i > 0
                      Free Shipping on Orders above Rs #{DOMESTIC_SHIPPING_CHARGES.keys.last.to_i}
                    - else
                      Free Shipping on all Orders
            .col-sm-12
              - if amount_to_credit.to_i > 0
                .free_shipment_text.text-center 
                  %div
                    You will get cashback worth #{get_price_in_currency_with_symbol(amount_to_credit)} in your mirraw wallet.
                    - if country_or_currency_indian?
                      %span (Only if you choose to opt for payment during the order).
                    %i.continue (Cash back you will receive is #{Wallet.cashback_percent}% of the amount payable #{"upto #{Wallet.cashback_upto}" if Wallet.cashback_upto?})
                  = link_to '| T&C', offer_tnc_url(anchor: 'loyalty_tnc'), :target => "_blank"
              - if free_shipment_text.present?
                .free_shipment_text.text-center
                  = free_shipment_text
                  %br
                  - if @cart_recommendations_designs.present?
                    .continue See Recommended Products!!!
              - if @free_stitching_text.present?
                .free_shipment_text.text-center
                  = @free_stitching_text
            - max_discount = 0
            - cart_add_disc[1].each_with_index do |amount, index|
              - amount = get_price_in_currency(cart_add_disc[1][index].to_i)
              - if cart_total <= amount
                .col-sm-12
                  .free_shipment_text.text-center= "Extra #{cart_add_disc[0][index]}% off on Item Total above #{@symbol} #{amount}"
              - elsif max_discount <= cart_add_disc[0][index].to_i && BEST_OFFER_THRESHOLD.to_i >= 0
                - max_discount = cart_add_disc[0][index].to_i
            - if max_discount > 0 && BEST_OFFER_THRESHOLD.to_i >= 0
              .col-sm-12
                .free_shipment_text.text-center= "You got extra #{max_discount}% off"
            .col-sm-12{style: 'margin-bottom: 10px;'}
              .continue.text-center
                = link_to "Continue Shopping", ( params[:controller] == 'carts' && params[:action] == 'show') ? recent_url : "javascript:void(0)", class: ('hidecart' unless params[:controller] == 'carts' && params[:action] == 'show')
        #cart-email-tab.col-lg-12.col-md-12.col-sm-12.col-xs-12.nopadding.cart-card
          .row
            .col-sm-5.text LINKED EMAIL:
            .col-sm-7.email-text.nopadding= @cart.email
          .row{style: 'margin: 10px 0px 5px 0px; '}
            = form_tag('/carts/save_email', {method: "post", id: 'save-cart-email'}) do
              = hidden_field_tag 'cart_id', @cart.id
              .col-sm-6
                = email_field_tag 'email', '', class: 'form-control', style: 'border-radius: 2px;', placeholder: 'Enter Email'
              .col-sm-6
                = button_tag 'Save My Cart', class: 'form-control', id: 'save-email-btn', type: 'submit'
          .row.cart_email
            .col-sm-12.msg
    :javascript
      loadJsArr.push(["#{asset_path('web_cart.js')}", 4]);
  - else
    %div.empty-info
      %div.icon
      %h4 Cart is Empty
      %p Looks like you have no items in your shopping cart
      .continue.text-center.continue-shop
        = link_to "Return to shop", ( params[:controller] == 'carts' && params[:action] == 'show') ? recent_url : "javascript:void(0)", class: ('hidecart' unless params[:controller] == 'carts' && params[:action] == 'show')
- if totals.present? && @is_duplicate_ga_update_request.nil?
  - currency_convert = CurrencyConvert.find_by(country_code: @country_code).try(:market_rate) || 1
  - bmgn_discount_inr = @cart.bmgnx_discounts if @cart.present?
  - bmgn_discount = bmgn_discount_inr.present? ? (get_price_in_currency(bmgn_discount_inr) * currency_convert).round(2) : 0
  - tax = totals[:taxes][:amount] if totals[:taxes].present? 
  - total_tax = (tax * currency_convert).to_f.round(2) if tax.present?
  - item_total =   (totals[:item_total][:amount] * currency_convert).round(2)
  - if @cart.coupon.present?
    - coupon_price =  (totals[:coupon_discounts][:amount] * currency_convert).to_f.round(2) if totals[:coupon_discounts].present?
    - coupon_percent = (coupon_price / item_total * 100).to_f.round(2) if coupon_price.present?
    - coupon_code = @cart.coupon.code
    - item_total = ((item_total - (item_total * coupon_percent / 100))).to_f.round(2) if coupon_percent.present?
  - addon_charges = totals[:addon_charges].present? ? (totals[:addon_charges][:amount] * currency_convert).to_f.round(2) : 0
  - gift_wrap = totals[:gift_wrap].present? ? (totals[:gift_wrap][:amount] * currency_convert).to_f.round(2) : 0
  - item_total = (item_total + addon_charges + gift_wrap).round(2)
  - items = @cart.line_items
  - @item_ga_data_array = items.map{|item| item.line_item_wise_ga_data(@index, item.get_breadcrumb, @country_code)}
  - @design_view_ga_params = {event: "ga4_view_cart", ecommerce: {currency: "INR", country_code: @country_code, value: item_total, shipping: (totals[:shipping][:amount] * currency_convert).to_f.round(2), tax: total_tax || 0, coupon: coupon_code || "",coupon_discounts: coupon_price || 0,customization: addon_charges,gift_wrap: gift_wrap,offers_discount: bmgn_discount, items: @item_ga_data_array}}
  :javascript
    //$(document).ready(function(){
      var design_view_ga_params = #{@design_view_ga_params.to_json.html_safe}
      $("#header_cart").attr('ga4-view-cart-data', JSON.stringify(design_view_ga_params))
    //});

-unless referral_checked
  :javascript
    addEvent(window, 'load', function(){
      $(' #AmazonPayButton, #PayWithAmazonOr').show();
    });
-else
  :javascript
    addEvent(window, 'load', function(){
      $('#apply-coupon-tab, #cart_coupon_wallet_or, #AmazonPayButton, #PayWithAmazonOr').hide();
    });

-if current_account && current_account.user.present?
  :javascript
    addEvent(window, 'load', function(){
      function toggleloader() {
        if ($("#cart-loader").is(':visible')) {
          $("#cart-loader").hide();
          return $('#cart-details').css('opacity', '1');
        } else {
          $('#cart-details').css('opacity', '0.5');
          return $("#cart-loader").show();
        }
      }
      $(document).on('click', '#referral_checkbox', function(){
        toggleloader();
        $('#apply_wallet_discount_form').submit();
      });
    });
