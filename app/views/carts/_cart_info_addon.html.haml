- if @addons.any?{|adon| adon.free_stitching?} && @addons.any?(&:is_free_stitched_addon?)
  .free_stitching_addon_msg_label Free Stitching
- @addons.each_with_index do |addon|
  .row#addon-display
    - addon_price = get_sub_total_in_currency(addon.snapshot_price, item.quantity)
    - addon_notes = addon.notes.present?
    - price_class = addon_notes == true ? 'col-lg-2 col-md-4 col-sm-12 pull-right' : 'col-sm-12 col-lg-7 col-md-7'
    .col-lg-5.col-md-12.col-sm-12.nopadding= "&#x2702; #{addon.addon_type_value.name.camelize}".html_safe
    .text-right.nopadding{class: price_class}
      - if addon_price != 0
        = get_price_in_currency_with_symbol(addon_price,true)
      - elsif addon.present?
        - design_id = nil 
        - design_id = addon.addon_type_value.design.design_check if addon.addon_type_value.design_id.present?
        - required_addon = addon.addon_type_value.addon_type.addon_type_values.where(design_id: nil).where(published: 'true').where(addon_type_value_group_id: [1, 2]).where(position:2).order('addon_type_value_group_id').last
        - if item.design.designable_type.present? && ['Lehenga', 'SalwarKameez'].include?(item.design.designable_type)
          - print = 'Stitching'
        - else
          - print = addon.addon_type_value.name.camelize.gsub(/( )+(.)*/).first
        -if required_addon.present?  && required_addon.try(:name) != 'Pre-Stitched Saree' && [1,2].exclude?(addon.addon_type_value.try(:addon_type_value_group_id))
          - currency =  get_price_in_currency_with_symbol(required_addon.price,true).gsub(/[a-zA-Z]*/).first
          - price = CurrencyConvert.convert_to(currency,required_addon.effective_price(item.design),@country_code).round(2)
          .col-sm-10.pull-left.text-left.nopadding= link_to "Add #{print} for #{currency} #{price}",add_addon_on_current_cart_path(addon.id), method: :get, remote: true, class: 'upsell-addon', data: {addon_type: required_addon.addon_type.camelcase_name, addon_type_value: required_addon.camelcase_name}
    - if addon_notes
      .col-lg-5.col-md-5.col-sm-5.nopadding.hidden-xs
        - addon.notes.split(',').each do |note|
          = note.sub(/.*?Select/, '')
          %br
