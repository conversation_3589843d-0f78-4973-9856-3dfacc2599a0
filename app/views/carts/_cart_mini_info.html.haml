.cart_mini_info
  = hidden_field_tag 'cart_final_id', @cart.id
  = hidden_field_tag 'free_shipping_country', Address.free_shipping_countries
  - if @cart.items_total_price > 0
    .order_head
      %ul
        %li.head_padding.col-lg-2.col-md-2.col-sm-2.col-xs-3 Item
        %li.head_padding.col-lg-2.col-md-2.col-sm-2.col-xs-3 Quantity
        %li.head_padding.col-lg-4.col-md-4.col-sm-4.col-xs-3 Price
        %li.head_padding.col-lg-4.col-md-4.col-sm-4.col-xs-3 Sub-total
    - index = 0
    - item_ga_data_array = []
    - for store in @cart.designer_stores
      - first = true
      - for item in store[:items]
        - item_ga_data_array << item.line_item_wise_ga_data(index, item.get_breadcrumb, @country_code)
        - index += 1
        .order_content
          %ul{:id => 'design_' + item.design_id.to_s}
            %li.first-child.col-lg-2.col-md-2.col-sm-2.col-xs-3
              = link_to designer_design_path(item.design.designer, item.design), :target => '_blank' do
                = image_tag(item.thumbnail_image_order_new, :alt => item.title) if item.thumbnail_image_order_new
            %li.col-lg-2.col-md-2.col-sm-2.col-xs-3= item.quantity
            %li.col-lg-4.col-md-4.col-sm-4.col-xs-3.item_snapshot_price
              = get_price_in_currency_with_symbol(item.price)
              - if PromotionPipeLine.bmgnx_hash.present? && item.buy_get_free == 1
                %br  
                .b1g1= Promotions.bmgnx_offer_message
            - addons_total_price = 0
            - addons_delivery_time = 0
            - if (addons = item.get_addons)
              - addons.each do |addon|
                - addons_total_price += get_sub_total_in_currency(addon.snapshot_price,item.quantity)
                - if addon.addon_type_value.prod_time > addons_delivery_time
                  - addons_delivery_time = addon.addon_type_value.prod_time
            %li.col-lg-4.col-md-4.col-sm-4.col-xs-3.item_total_price= (get_sub_total_in_currency(item.price,item.quantity) + addons_total_price)
            .clr
          - @item = item
          - @addons_total_price = 0
          = render :partial => 'carts/cart_mini_addon.html.haml'
    .promotion_text
      .free_shipment_text.label-info.text-center.pull-left
        - free_shipment_text = @cart.free_shipping_charges_text(@actual_country, @conversion_rate, @symbol, false)
        - if free_shipment_text != false
          = free_shipment_text
          %br
            = link_to "Continue Shopping", :back, style: 'color: black !important;'
    -if @display_rts_msg
      .notice_class (Your cart contains both Ready To Ship and Non - Ready To Ship products and hence the Estimated delivery date has been changed.)
    .order_total
      .order_content
        %ul
          %li.col-lg-3 Delivery
          %li.col-lg-3.delivery_time= (6+addons_delivery_time+@cart.vacation_day_count).to_s + ' days'
      .order_content
        %ul
          %li.col-lg-3.col-md-3.col-sm-3.col-xs-3 Item Total
          - item_total = @cart.items_total_without_addons(@conversion_rate)
          - addon_charges = @cart.addons_total(@conversion_rate)
          - discounted_item_total = (item_total + addon_charges - get_actual_price_in_currency(@cart.additional_discounts, @conversion_rate)).round(2)
          %li.col-lg-3.col-md-3.col-sm-3.col-xs-3#items_total_price{data: {discounted_item_total: discounted_item_total}}
            #{get_price_in_currency_with_symbol(item_total,true)}
      - discounts = @cart.discounts
      - if discounts > 0  
        .order_content
          %ul
            %li.col-lg-3.col-md-3.col-sm-3.col-xs-3 Discounts
            %li.col-lg-3.col-md-3.col-sm-3.col-xs-3#order_discount
              #{get_price_in_currency_with_symbol(discounts)}
      .order_content.cod{:style => 'display:none'}
        %ul
          %li.col-lg-3 COD Charges
          %li.col-lg-3
            .cod_charges.last#order_cod_charge
              /- cod_charges = @order.get_currency_cod_charges(@conversion_rate)
              /= get_price_in_currency_with_symbol(cod_charges, true)

      - if addon_charges > 0
        .order_content
          %ul
            %li.col-lg-3 Customisation
            %li.col-lg-3
              #{get_price_in_currency_with_symbol(addon_charges, true)}
      .order_content
        %ul
          %li.col-lg-3.shipping_label Shipping
          %li.col-lg-3
            %span.shipping_cost#order_shipping
              - shipping = 0
              -# @order_retry_shipping_cost will always return currency specific rate so we dont need to convert it again
              - if (shipping = @order_retry_shipping_cost).present?
                = shipping
              - elsif (shipping = @cart.shipping()) > 0
                #{get_price_in_currency_with_symbol(shipping)}
              - else
                FREE
      - gift_wrap_price = 0
      - unless GIFT_WRAP_PRICE.to_f < 0 || country_or_currency_indian?
        - if session[:gift_wrapped]
          - gift_wrap_price = get_price_in_currency(GIFT_WRAP_PRICE.to_f)
          .order_content.last-child_1
            %ul
              %li.col-lg-3.col-md-3.col-sm-3.col-xs-3 Gift Wrap Charges
              %li.col-lg-3.col-md-3.col-sm-3.col-xs-3
                #{get_price_in_currency_with_symbol(gift_wrap_price, true, nil)}
      - taxe_rate = @cart.get_tax_rate(@country_code)
      - grandtotal, taxes, shipping, wallet_discount = @cart.get_all_the_cart_details(@country_code, @actual_country , nil ,@conversion_rate, nil, session[:gift_wrapped])
      - if taxe_rate != 0
        .order_content
          %ul
            %li.col-lg-3.col-md-3.col-sm-3.col-xs-3 Taxes
            %li.col-lg-3.col-md-3.col-sm-3.col-xs-3.total_tax#order_discount
              #{get_price_with_symbol(taxes, @symbol)}
              -# -tax = get_price_with_symbol(taxes, @symbol) 
              -# -split_array = tax.split()
              -# -tax_without_sym = split_array[1].to_f
      - wallet_credits = @cart.wallet_discounts
      - if wallet_credits > 0  
        .order_content.last-child
          %ul
            %li.col-lg-3.col-md-3.col-sm-3.col-xs-3 Wallet Discounts
            %li.col-lg-3.col-md-3.col-sm-3.col-xs-3#order_wallet_discount
              #{get_price_with_symbol(wallet_credits, @cart.wallet.currency_symbol)}
      .clr
    .net-total
      - if @order.billing_country == 'India' && @order.pay_type == COD
        /- grandtotal += cod_charges
        - grandtotal += Order.domestic_cod_charge(@order.billing_pincode, @cart) || 0
      .order_content
        %ul
          %li Total
          - grandtotal = 0 if grandtotal < 0
          %li#grand_total= get_price_in_currency_with_symbol(grandtotal,true)
      - item_total = get_price_in_currency_with_symbol(item_total,true)
      - split_array = item_total.split()
      - item_total_without_sym = split_array[1].to_f
      - cc = CurrencyConvert.find_by(country_code: @country_code) 
      - currency_convert = cc.try(:market_rate) || 1
      - bmgn_discount = (get_price_in_currency(@cart.additional_discounts) * currency_convert)
      - item_total = (( item_total_without_sym * cc.market_rate) - (bmgn_discount)).round(2)
      - if @cart.coupon.present?
        - coupon_discount  = (get_price_in_currency(@cart.coupon_discounts) * currency_convert).to_f.round(2) if @cart.present?
        - coupon_percent = (coupon_discount / item_total * 100).to_f.round(2) if coupon_discount.present?
        - coupon_code = @cart.coupon.code
        - item_total = ((item_total - (item_total * coupon_percent / 100))).to_f.round(2)
      - addon_charges = addon_charges.present? ? (addon_charges * currency_convert).to_f.round(2) : 0
      - gift_wrap = gift_wrap_price.present? ? (gift_wrap_price * currency_convert).to_f.round(2) : 0
      - item_total = (item_total + addon_charges + gift_wrap).round(2)
      - item_ids = []
      - contents = []
      - content_ids = []
      - num_items = [item_ga_data_array.length]
      - item_ga_data_array.each do |design, index|
        - item_ids << { id: design[:item_id] , google_business_vertical: 'retail' }
        - content_ids << design[:item_id]
        - contents << {id: design[:item_id], quantity: design[:quantity]}
      - gads_params = {item_ids: item_ids,contents: contents,content_ids: content_ids,num_items: num_items}
      - ga_checkout_hash = {event: 'ga4_begin_checkout', ecommerce: { currency: 'INR', country_code: @country_code, value: item_total, shipping: (shipping * (cc.try(:market_rate) || 1)).round(2).to_f, tax: (taxes * (cc.try(:market_rate) || 1)).to_f.round(2) || 0,coupon: coupon_code || "", coupon_discounts: coupon_discount || 0,customization: addon_charges,gift_wrap: gift_wrap,offers_discount: bmgn_discount.round(2), items: item_ga_data_array,item_ids: item_ids}}
  - else
    %p{:style => "text-align:center;padding-top:20px;"} Your cart is empty.

:javascript
    window.dataLayer = window.dataLayer || [];
    var gads_items_id = #{gads_params.to_json.html_safe}
    var ga4_checkout_params = #{ga_checkout_hash.to_json.html_safe}
    dataLayer.push({ ecommerce: null });
    dataLayer.push(ga4_checkout_params);