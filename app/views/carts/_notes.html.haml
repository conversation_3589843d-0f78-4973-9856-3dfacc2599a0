- if false #delibrately hidden  
  .notes.col-lg-12.col-md-12.col-sm-12.col-xs-12.nopadding{id: "note_" + item.id.to_s }
    .note_area{style: "display:none;"}
      .note_loading
      .col-sm-8.note-text
        = text_area_tag 'note', nil, id: "item_note"
      .col-sm-4{style: "padding-right: 0px;"}
        = link_to 'Save', "javascript:void(0)", class: "save_note col-sm-12", id: item.id
        = link_to 'Cancel', "javascript:void(0)", id: "cancel_note", class: "col-sm-12"
    .note_link
      - if item.note.blank?
        = link_to 'Add Note', "javascript:void(0)", id: "add_note_to_line_item"
        %p.small{style: "font-size:0.8em;"}
          %sup *
          Add size/color requirement here if applicable.
      - else
        - if item.note.match('Rakhi Schedule Delivery')
          - rakhi_hide_note = item.note.sub('Rakhi Schedule Delivery','')
          %p.note-content= rakhi_hide_note
        - else 
          %p.note-content= item.note
        - unless item.variant_id.present? || item.note.match('Rakhi Schedule Delivery')
          = link_to 'Edit Note', "javascript:void(0)", id: "edit_note_to_line_item"
- elsif item.note.present?
  - note = item.note.match('Rakhi Schedule Delivery') ? item.note.sub('Rakhi Schedule Delivery','') : item.note
  - if item.note_without_bmgn.present?
    .notes.col-lg-12.col-md-12.col-sm-12.col-xs-12.nopadding{id: "note_" + item.id.to_s }
      .note_link
        %p.note-content= item.note_without_bmgn

-# Ready to ship label is being hidden due to express delivery feature
- if false && item.design.ready_to_ship? && !item.paid_addons? && !country_or_currency_indian?
  .col-lg-5.col-md-5.col-sm-5.col-xs-5.nopadding{style: "margin-bottom: 15px;"}
    %span.ready_to_cart_ship
      %span.bullet{style: "vertical-align: middle; font-size: 20px;"} &bull;
      &nbsp;Ready To Ship