- if @edit_invoice
  %link{rel: "stylesheet", href: "https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css"}
  %script{src: "https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"}
  %script{src: "https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"}
  :javascript
    $(function() {
      if (typeof ($().popover) === 'function') {
        return $('[data-toggle="popover"]').popover().popover('show');
      }
    });
  %center
    .jumbotron
      %b.text-info Please enter your own invoice number or leave as it is for system generated number
      %br/
      %br/
      = form_tag edit_designer_invoice_path, :method => "post", class: "form-inline" do
        %label Invoice Number   :
        = text_field_tag 'invoice_number', @invoice_number.presence || @designer_order.try(:invoice_number), class: "form-control", pattern: "^[^ ]{1,18}$", title: 'Step 1', data: {toggle:'popover',content: "Enter GST Invoice Number which should be between 1 to 17 characters and should not contain spaces", placement: "bottom", trigger: 'hover'}
        = submit_tag 'Download', class: "btn btn-sm btn-primary", title: 'Step 2', data: {toggle: 'popover', content: "Download the invoice and sign stamp the same and upload the invoice to be eligible for payment", placement: "right", trigger: 'hover'}

- if @show_invoice_pdf || @show_invoice_from_admin_panel
  - w_order_invoice = WarehouseOrderInvoice.new(@designer_order)
  - @designer_order.warehouse_address_id = LUXE_WAREHOUSE_ADDRESS_ID if @designer_order.designer.designer_type == "Tier 1 Designer"
  - company_name, shipping_telephone, shipping_address_1,shipping_address_2, shipping_city, shipping_pincode, shipping_state, _ = DesignerOrder.get_warehouse_billing_address([@designer_order.warehouse_address_id])
  -if @rtv
    -if params.present?
      -all_line_items = @designer_order.line_items.joins(:rtv_shipments).where(rtv_shipments: {number: params[:rtv]}).group('line_items.id').to_a
    -else
      -all_line_items = @designer_order.line_items.where(id: line_items_id)
  -else
    -all_line_items = @line_items.presence || @designer_order.line_items.sane_items.preload(design: [:categories,property_values: :property],variant: :option_type_values)
  .section
    -if true#(@designer_order.ship_to.blank? && @order.international?) || (@designer_order.ship_to == 'mirraw')
      %table{border: "1", :cellpadding => "0", :cellspacing => "0", style: "border-collapse:collapse;border:none", :width => "100%"}
        - total_taxable_value = 0.0
        - value_of_good = ''
        - hsn_codes = []
        - gst_total = 0.0
        - igst_flag = @designer.gst_no.present? && ((@invoice_number.present? && @designer.state.try(:upcase) == SHIPPER_STATE.upcase) || (((@designer_order.present? && @designer_order.ship_to == 'mirraw') || @rtv) && @designer.state.try(:upcase) == SHIPPER_STATE.upcase) || (@order.present? && @designer_order.present? && @designer_order.ship_to == 'customer' && @designer.state.try(:upcase) == @order.buyer_state.try(:upcase)))
        %tr
          %td{colspan: "22", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %p.MsoNormal
              %center
                -if @rtv #|| @designer_order.try(:domestic_sor?, @order)
                  %br
                  =w_order_invoice.ship_to.html_safe
                -else
                  = "Name: " + (@designer.business_name.presence || @designer.name)
                  %br
                  = "Address: " + (@designer.business_street.presence || @designer.street) + ", " + (@designer.business_city.presence || @designer.city) + " - " + (@designer.business_pincode.presence || @designer.pincode) + ", " + (@designer.business_state.presence || @designer.state) + ", " + (@designer.business_country.presence || @designer.country)
                  %br
                  -# = "Phone: #{@designer.phone}"
                  -# %br
                  = "GST no: " + @designer.gst_no.to_s

        %tr
          %td{colspan: "22", valign: "top"}
            %p.MsoNormal
              %center= @designer.gst_no.present? ? "Tax Invoice" : "Invoice"

        %tr
          %td{colspan: "14", style: "padding:0in 5.4pt 0in 5.4pt;height:18.7pt", valign: "top"}
            %span.MsoNormal{style: "font-weight:bold"} Recipient :
            %p.MsoNormal
              -if @rtv
                = "Name: #{@designer.name}"
                %br
                = "Address: #{@designer.street}, #{@designer.city} - #{@designer.pincode} , #{@designer.state} ,  #{@designer.country}"
                %br
                = "Phone: #{MIRRAW_CONTACT_INFO}"
                %br
                = "PAN no: " + @designer.try(:pan_no).to_s
                %br
                = "GST no: " + @designer.gst_no.to_s
              -elsif (@invoice_number.present? && @order.blank?) || (@designer_order.ship_to.blank? && @order.international?) || (@designer_order.ship_to == 'mirraw')
                %br
                =w_order_invoice.ship_to.html_safe
              -else
                = "Name: " + @order.name
                %br
                = "Address: " + @order.street + ", " + @order.city + " - " + @order.pincode + ", " + @order.buyer_state + ", " + @order.country
                %br
                -# = "Phone: " + @order.phone


            %td{colspan: "8", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %div
                %span.MsoNormal{style: "font-weight:bold"} Invoice No :
                %span.MsoNormal
                  -if @invoice_number.present?
                    =@invoice_number
                  -elsif @designer_order.invoice_number.present? && @rtv
                    ='MIR/' + @designer_order.invoice_number.to_s
                  -else
                    =@designer_order.invoice_number
                %br
                %span.MsoNormal{style: "font-weight:bold"} Invoice Date :
                %span.MsoNormal= @invoice_number.present? ? Time.current.strftime("%d/ %m/ %Y") : (@designer_order.pickup.presence || @designer_order.completed_at.presence || @designer_order.confirmed_at || @designer_order.created_at).strftime("%d/ %m/ %Y")
                %br
              %br
              %div
                %span.MsoNormal{style: "font-weight:bold"} Purchase Order No :
                %span.MsoNormal= @invoice_number.present? ? "#{@invoice_number}/#{Time.current.strftime('%d/%m')}" : @order.number
                %br
                %span.MsoNormal{style: "font-weight:bold"} State Code :
                %span.MsoNormal=@rtv ? IN_STATE_CODE[@designer.state_code] : (@invoice_number.present? || (@designer_order.ship_to.blank? && @order.international?) || (@designer_order.ship_to == 'mirraw') ? '27' : IN_STATE_CODE[@order.state_code])
                %br
                %span.MsoNormal{style: "font-weight:bold"} Place of Supply :
                %span.MsoNormal=@rtv ? (@designer.business_state.presence || @designer.state) : (@invoice_number.present? || (@designer_order.ship_to.blank? && @order.international?) || (@designer_order.ship_to == 'mirraw') ? 'Maharashtra' : @order.buyer_state)

          -if @rtv
            -total_plus_shipping = 0
          -elsif @invoice_number.present?
            - total_vendor_payout = 0
          -elsif @order.international? || @designer_order.ship_to == 'mirraw'
            - total_vendor_payout = @designer_order.payout
          -elsif @order.currency_rate_market_value.present? && @designer_order.ship_to != "mirraw" && @order.international?
            - total_plus_shipping = @order.international_cod_price(@designer_order.scaled_total + @designer_order.shipping)
          -else
            - total_plus_shipping = @designer_order.total + @designer_order.shipping

          %tr
            %td{colspan: "11", rowspan: 2, style: "padding:0in 5.4pt 0in 5.4pt;border-right: 1px solid;", valign: "top"}
              %p.MsoNormal
                %b Description of  goods

            %td{colspan: "1", rowspan: 2, style: "border:1px solid;padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %p.MsoNormal
                %b HSN CODE

            %td{colspan: "1", rowspan: 2, style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %p.MsoNormal
                %b Quantity

            %td{colspan: "1", rowspan: 2, style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %p.MsoNormal
                %b Value (INR)

            %td{colspan: "1", rowspan: 2, style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %p.MsoNormal
                %b Discount (INR)

            %td{colspan: "1", rowspan: 2, style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %p.MsoNormal
                %b Net Value (INR)

            %td{colspan: "1", rowspan: 2, style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %p.MsoNormal
                %b Taxable Value

            -(titles = igst_flag ?  ['CGST', 'SGST'] : ['IGST']).each do |title|
              %td{colspan: ((3 - titles.length) * 2).to_s, style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
                %p.MsoNormal
                  %b=title


            %td{colspan: "1", rowspan: 2, style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %p.MsoNormal
                %b Amount (INR)

          %tr
            -(titles = ('Rate,Amount (INR),' * (igst_flag ? 2 : 1)).split(',')).each do |title|
              %td{colspan: (4 - titles.length).to_s, style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
                %p.MsoNormal
                  %b=title

        - payable_gst = 0.0
        - jewellery_flag = false  #if all line_items designable type is jewellery then false else true
        - billing_status = !@rtv && !@invoice_number && @designer_order.ship_to != 'mirraw' && @order.cod?
        - if billing_status
          - dos_total  = all_line_items.to_a.sum{|li| (li.snapshot_price * li.quantity)}.to_f
          - order_total_price = @order.line_items.to_a.sum{|li| li.price_with_addons * li.quantity}
          - designer_order_discount = @designer_order.discount.to_i
          - total_plus_shipping += designer_order_discount if designer_order_discount > 0
          - order_total_discount = @order.cod? ? 0 : @order.get_total_discount.to_i
        -designer_order_discount, order_total_discount, order_total_price = if billing_status && @order.currency_rate_market_value.present?
          -[designer_order_discount, order_total_discount, order_total_price].map{|i| @order.international_cod_price(i)}
        - all_line_items.each do |item|
          / - if item.design.sell_count == 0
          /   - qc_not_required = false
          / - elsif (item.design.return_count >= 10 && (item.design.return_count*100)/item.design.sell_count <= 5)
          /   - qc_not_required = true
          / - elsif (item.design.return_count < 10 && (item.design.return_count*100)/item.design.sell_count <= 1)
          /   - qc_not_required = true
          / - else
          /   - qc_not_required = false
          -if item.design.skip_qc
            -qc_not_required = true
          -else
            -qc_not_required = false

          - stitching_status = item.stitching_required == 'Y' ? "STITCHING REQUIRED" : ""
          - if @designer.name == 'Aapno Rajasthan' && item.design.addon_types.collect(&:name).include?('Rakhi Gifts')
            - vendor_sku = item.line_item_addons.present? ? item.line_item_addons.first.addon_type_value.description.to_s : item.design.design_code.to_s + '_only'
          - else
            - vendor_sku = item.design.design_code.present? ? item.design.design_code : ''
          - addon_price = item.vendor_addon_items.to_a.sum{|addon|addon.snapshot_price(RETURN_NORMAL)}
          - addon_text = item.vendor_addon_items.collect{|item| "[#{item.addon_type_value.name}]" }.join
          %tr{style: "padding-top:10px;"}
            %td{colspan: "11", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal
                %span= item.design.title + " - " + item.design.categories.first.name.singularize + " - " + "# M" + item.design.id.to_s
                %p{style: "background-color: #000000;"}
                  %b{style: "color: #ffffff; font-weight: bold; font-size: larger; padding: 8px; line-height: 1.7"}
                    ="Vendor[SKU] - #" + vendor_sku
                    =qc_not_required == true ? "" : 'ALPHA'
                    =item.stitching_required == 'Y' ? "STITCHING REQUIRED" : ""
                    - if (variant = item.variant).present? && (option_type = variant.option_type_values).present?
                      ="[size : #{option_type.collect(&:name).last}]"
                    - if @invoice_number.present?
                      = " : #{item.order.number}"
                    - if addon_text.present?
                      =addon_text
                    - if item.available_in_warehouse
                      [DNS]
                %img{src: design_image_data(item), width: '170px', height: '250px', style: 'margin-top:2px;'}
            - design = item.design
            - product_name = design.invoice_category_name('dhl').titleize
            - hsn_code = (@designer.hsn_approved && design.gst_rate.present? && design.hsn_code.present?) ? design.hsn_code.to_s : design.categories.hsn_code.to_s
            - hsn_codes << hsn_code
            - jewellery_flag = design.designable_type.try(:downcase) != 'jewellery'
            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal= hsn_code
            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal= item.quantity

            -if @rtv
              -item_snapshot_price = (item.vendor_selling_price.present? ? (item.vendor_selling_price * (100-@designer_order.transaction_rate)/100.0).to_i : item.snapshot_price(RETURN_NORMAL))
            -elsif @invoice_number.present?
              -item_snapshot_price = (item.snapshot_price(RETURN_NORMAL) * item.designer_order.get_payout_ratio).round(2)
              -item_snapshot_price += addon_price
            -elsif @order.international? || @designer_order.ship_to == 'mirraw'
              -item_snapshot_price = ((item.snapshot_price(RETURN_NORMAL) + addon_price) * @designer_order.get_payout_ratio).round(2)
            -elsif @order.currency_rate_market_value.present? && @designer_order.ship_to != "mirraw" && @order.international?
              -item_snapshot_price = @order.international_cod_price(item.snapshot_price + addon_price)
            -else
              -item_snapshot_price = item.snapshot_price(RETURN_NORMAL) + addon_price
            - designer_discount, discount, discounted_rate = 0, 0, item_snapshot_price
            - if billing_status
              - designer_discount = (item_snapshot_price * item.quantity * designer_order_discount/dos_total).round(2)
              - discount = ((item_snapshot_price / order_total_price.to_f) * order_total_discount) + designer_discount
              - discount = 0 if discount > item_snapshot_price
              - total_plus_shipping -= (discount * item.quantity) if billing_status
              - discounted_rate = (item_snapshot_price - discount)
            - _,gst_rate = item.find_hscode_gst(item_snapshot_price, hsn_code, false)
            -if @designer.gst_no.present?
              - gst_total += gst_tax = ((discounted_rate - discounted_rate/(1+gst_rate/100.0))*item.quantity).round(2)
              - taxable_value = (discounted_rate/(1+gst_rate/100.0)).round(2)
              - total_taxable_value += (taxable_value * item.quantity).round(2)
            -total_vendor_payout += (item_snapshot_price * item.quantity) if @invoice_number.present?
            %td{colspan: "1", style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal=item_snapshot_price #@designer.gst_no.present? ? taxable_value : item_snapshot_price

            %td{colspan: "1", style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal= discount.nonzero?.try(:round,2) || '-'

            %td{colspan: "1", style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal= discounted_rate.round(2)

            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal= @designer.gst_no.present? ? (taxable_value * item.quantity).round(2) : "-"

            -(gst_div = igst_flag ? 2 : 1).times do
              %td{colspan: (3 - gst_div).to_s, style: "padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                %p.MsoNormal= @designer.gst_no.present? ? ((gst_rate.to_f / gst_div.to_f).round(2).nonzero? || '-') : '-'

              %td{colspan: (3 - gst_div).to_s, style: "padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                %p.MsoNormal= (gst_tax.to_f / gst_div.to_f).round(2).nonzero? || '-'

            %td{colspan: "1", style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top", :width => "64"}
              -if @rtv
                -total_plus_shipping += (discounted_rate * item.quantity).round(2)
              -elsif @designer.gst_no.blank?
                -payable_gst += ((discounted_rate - (discounted_rate/(1+gst_rate/100.0)).round(2)) * item.quantity).round(2)
              %p.MsoNormal= (discounted_rate * item.quantity).round(2)

              - if false#(lta = item.line_item_addons)[0].present? && !@rtv
                - lta.each do |addon|
                  - if addon.snapshot_price != 0
                    %tr{style: "padding-top:10px;"}
                      %td{colspan: "11", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                        %p.MsoNormal= addon.addon_type_value.name
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                        %p.MsoNormal= item.quantity
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                        -if @order.currency_rate_market_value.present? && @designer_order.ship_to != "mirraw"
                          %p.MsoNormal= (addon_snapshot_price = @order.international_cod_price(addon.snapshot_price))
                        -else
                          %p.MsoNormal= (addon_snapshot_price = addon.snapshot_price)
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                      %td{colspan: "1", style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                        %p.MsoNormal= (addon_snapshot_price * item.quantity)

        - if @designer_order.present? && @designer_order.shipping != 0
          %tr{style: "padding-top:10px;"}
            %td{colspan: "11", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal Shipping Charges
            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -

            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -


            %td{colspan: "1", style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top", :width => "64"}
              - if @order.present? && @order.currency_rate_market_value.present? && @designer_order.ship_to != "mirraw" && !@rtv
                %p.MsoNormal= @order.international_cod_price(@designer_order.shipping)
              -elsif @designer_order.present?
                %p.MsoNormal= @designer_order.shipping

            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -


            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -


            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -


            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -


            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -


            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -


            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -

            %td{colspan: "1", style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top", :width => "64"}
              - if @order.present? && @order.currency_rate_market_value.present? && @designer_order.ship_to != "mirraw" && !@rtv
                %p.MsoNormal= @order.international_cod_price(@designer_order.shipping)
              -elsif @designer_order.present?
                %p.MsoNormal= @designer_order.shipping

        - all_line_items.count.upto(5) do
          %tr{style: "height:20pt;border:none"}
            %td{colspan: "11", style: "padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
            -6.times do
              %td{colspan: "1", style: "padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
            -(gst_div = igst_flag ? 4 : 2).times do
              %td{colspan: ((6 - gst_div)/2).to_s, style: "padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
            %td{colspan: "1", style: "padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top"}


        %tr{style: "height:3.5pt"}
          %td{style: "padding:0in 5.4pt 0in 5.4pt;height:3.5pt;", valign: "top", colspan: 21}
            %p.MsoNormal Total
          %td{style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;height:3.5pt;", valign: "top",colspan: 1}
            %p.MsoNormal
              -if (@invoice_number.present? || @order.international? || @designer_order.ship_to == "mirraw") && !@rtv
                %b= total_vendor_payout.round(2)
              -else
                %b= total_plus_shipping.round(2)
        %tr
          %td{colspan: "14",rowspan: (igst_flag ? 6 : 5).to_s, style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %p.MsoNormal
              -if (@invoice_number.present? || @order.international? || @designer_order.ship_to == "mirraw") && !@rtv
                %b= "Amount Chargeable: " + DesignerOrdersHelper.number_to_words(total_vendor_payout).to_s
              -else
                %b= "Amount Chargeable: " + DesignerOrdersHelper.number_to_words(total_plus_shipping).to_s
            %p.MsoNormal
              = "Details of Transportation"
              %br
              = "E-way bill no:"
              %br
              = "Date of E-way Bill:"
              %br
              = "Name of Transporter"
              %br
              = "Mode:"
              %br
              = "Vehicle No:"
              %br
              = "Date & Time:"
          %td{colspan: "4", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %p.MsoNormal Total Amount Before Tax
          %td{colspan: "4", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %p.MsoNormal= @designer.gst_no.present? ? total_taxable_value.round(2) : ((@invoice_number.present? || @order.international? || @designer_order.ship_to == "mirraw") && !@rtv ? total_vendor_payout : total_plus_shipping.round(2))
          -(titles = igst_flag ? ['CGST', 'SGST'] : ['IGST']).each do |title|
            %tr
              %td{colspan: "4", style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
                %p.MsoNormal= "Add: #{title}"
              %td{colspan: "4", style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
                %p.MsoNormal= (gst_total / titles.length).round(2)
        %tr
          %td{colspan: "4", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %p.MsoNormal Total Amount
          %td{colspan: "4", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %p.MsoNormal
              -if (@invoice_number.present? || @order.international? || @designer_order.ship_to == "mirraw") && !@rtv
                %b= total_vendor_payout
              -else
                %b= total_plus_shipping.round(2)
                - value_of_good = "#{(total_plus_shipping).round(2)} INR"
        %tr
          %td{colspan: "4", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %p.MsoNormal
              GST payable on Reverse Charge
              -if payable_gst > 0
                %br
                Tax Amount
          %td{colspan: "4", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %p.MsoNormal
            -if payable_gst > 0
              YES
              %br
              =payable_gst.round(2)
        %tr
          %td{colspan: "22", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %p.MsoNormal
        %tr{style: "height:35.6pt"}
          %td{colspan: "18", style: "padding:0in 5.4pt 0in 5.4pt;height:35.6pt", valign: "top", :width => "497"}
            -if @order.present? && @order.pay_type.downcase == COD.downcase && !@rtv
              %span.MsoNormal Return Process:
              %p.MsoNormal
                If you want to return above product please visit www.mirraw.com/pages/faq
            %span.MsoNormal Declaration:
            %p.MsoNormal We declare that this invoice shows the actual price of the goods described and that all particulars are true and correct.
          %td{colspan: "4", style: "padding:0in 5.4pt 0in 5.4pt;height:31.2pt", valign: "top"}
            %p.MsoNormal
              -if @rtv
                %u{style: "text-underline:double"} For Mirraw
              -else
                %u{style: "text-underline:double"}= "For " +  @designer.name
            %br
            %p.MsoNormal
              %u{style: "text-underline:double"} Signature

    -else
      %table{:border => "1", :cellpadding => "0", :cellspacing => "0", style: "border-collapse:collapse;border:none", :width => "100%"}
        %tr
          %td{colspan: "8", style: "text-align:center;"}
            %b PROFORMA INVOICE
            = @order.designer_orders.count == 1 ? "(SINGLE)" : ''
            = @order.designs.all?(&:skip_qc?) ? '(SKIP QC)' : ''

        %tr
          %td{colspan: "4", :rowspan => "3", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %span.MsoNormal{style: "font-weight:bold"} Shipper
            %p.MsoNormal
              -if @rtv
                =w_order_invoice.ship_to.html_safe
              -else
                = "Name: " + @designer.name
                %br
                = "Address: " + @designer.street + ", " + @designer.city + " - " + @designer.pincode + ", " + @designer.state + ", " + @designer.country
                %br
                = "Phone: #{MIRRAW_CONTACT_INFO}"
                %br
                = "PAN no: " + @designer.try(:pan_no).to_s
                %br
                = "GST no: " + @designer.gst_no.to_s

          %td{colspan: "2", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %span.MsoNormal{style: "font-weight:bold"} Invoice No & Date
            %div.MsoNormal= DateTime.now.strftime("%d/ %m/ %Y")

          %td{colspan: "2", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %span.MsoNormal{style: "font-weight:bold"} Exporter's Ref

        %tr
          %td{colspan: "4", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %span.MsoNormal{style: "font-weight:bold"} Buyer's Order No & Date
            %div.MsoNormal= @order.number + " " + @order.created_at.strftime("%d/ %m/ %Y")

        %tr
          %td{colspan: "4", style: "padding:0in 5.4pt 0in 5.4pt;height:17.45pt", valign: "top"}
            %span.MsoNormal{style: "font-weight:bold"} Buyer's Order No
            %div.MsoNormal= @order.number

        %tr
          %td{colspan: "4", :rowspan => "2", style: "padding:0in 5.4pt 0in 5.4pt;height:18.7pt", valign: "top"}
            %span.MsoNormal{style: "font-weight:bold"} Consignee :
            %p.MsoNormal
              -if @rtv
                = "Name: #{@designer.name}"
                %br
                = "Address: #{@designer.street}, #{@designer.city} - #{@designer.pincode} , #{@designer.state} ,  #{@designer.country}"
                %br
                = "Phone: #{MIRRAW_CONTACT_INFO}"
                %br
                = "PAN no: " + @designer.try(:pan_no).to_s
                %br
                = "GST no: " + @designer.gst_no.to_s
              -elsif checking_for_addons?(@order)
                %br
                =w_order_invoice.ship_to.html_safe
              -elsif (@designer_order.ship_to.blank? && @order.international?) || (@designer_order.ship_to == 'mirraw')
                %br
                =w_order_invoice.ship_to.html_safe
              -else
                = "Name: " + @order.name
                %br
                = "Address: " + @order.street + ", " + @order.city + " - " + @order.pincode + ", " + @order.buyer_state + ", " + @order.country
                %br
                = "Phone: " + @order.phone


            %td{colspan: "4", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %p.MsoNormal Buyer ( If other than consignee)

          %tr
            %td{colspan: "2", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %span.MsoNormal Place of Origin
              -if @rtv
                %div.MsoNormal{style: "font-weight:bold"}=  "#{shipping_city}, #{shipping_state}"
              -else
                %div.MsoNormal{style: "font-weight:bold"}= @designer.city + ", " + @designer.state

            %td{colspan: "2", style: "padding:0in 5.4pt 0in 5.4pt", valign: "top"}
              %span.MsoNormal Final Destination
              -if @rtv
                %div.MsoNormal{style: "font-weight:bold"}= @designer.city + ", " + @designer.state
              -elsif @order.international? && (@designer_order.ship_to == 'mirraw' || @designer_order.ship_to.blank?)
                %div.MsoNormal{style: "font-weight:bold"}=  "#{shipping_city}, #{shipping_state}"
              -else
                %div.MsoNormal{style: "font-weight:bold"}= @order.city + ", " + @order.buyer_state


          %tr
            %td{style: "padding:0in 5.4pt 0in 5.4pt;", colspan: "2"}
              %p.MsoNormal Pre-Carriage by
              %p.MsoNormal NA

            %td{style: "padding:0in 5.4pt 0in 5.4pt;height:12.65pt", colspan: "2"}
              %p.MsoNormal Place of receipt by pre Carrier
              %p.MsoNormal NA

            %td{colspan: "4", :rowspan => "3", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %p.MsoNormal{style: "font-size:15.0pt;"}
                -if @rtv
                  -total_plus_shipping = 0
                -elsif @order.international? || @designer_order.ship_to == 'mirraw'
                  - total_vendor_payout = @designer_order.payout
                -elsif @order.currency_rate_market_value.present? && @designer_order.ship_to != "mirraw"
                  - total_plus_shipping = @order.international_cod_price(@designer_order.scaled_total + @designer_order.shipping)
                -else
                  - total_plus_shipping = @designer_order.total + @designer_order.shipping

                -if @order.pay_type.downcase == COD.downcase && !@rtv
                  %b= @order.pay_type.upcase
                  %p.MsoNormal
                    %b= "Amount to be collected (in INR): " + total_plus_shipping.to_s
                -else
                  %b PREPAID

          %tr
            %td{style: "padding:0in 5.4pt 0in 5.4pt;height:15.55pt", valign: "top", colspan: "2"}
              %span.MsoNormal Vessel / Flight No
              %p.MsoNormal{style: "font-weight:bold"} COURIER

            %td{style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top", colspan: "2"}
              %p.MsoNormal  Port of Loading
              -if @rtv
                %p.MsoNormal{style: "font-weight:bold"}= COMPANY_LOCATIONS[:user][:city]
              -else
                %p.MsoNormal{style: "font-weight:bold"}= @designer.city

          %tr
            %td{style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top", colspan: "2"}
              %span.MsoNormal Port of Discharge
              -if @rtv
                %p.MsoNormal{style: "font-weight:bold"}= shipping_city
              -else
                %p.MsoNormal{style: "font-weight:bold"}= @designer.city

            %td{style: "padding:0in 5.4pt 0in 5.4pt;height:15.55pt", valign: "top", colspan: "2"}
              %span.MsoNormal Final Destination
              -if @rtv
                %p.MsoNormal{style: "font-weight:bold"}= @designer.city
              -elsif @order.international? && (@designer_order.ship_to == 'mirraw' || @designer_order.ship_to.blank?)
                %p.MsoNormal{style: "font-weight:bold"}= shipping_city
              -else
                %p.MsoNormal{style: "font-weight:bold"}= @order.city

          %tr
            %td{colspan: "5", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %p.MsoNormal
                %b Description of  goods

            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %p.MsoNormal
                %b Quantity

            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %p.MsoNormal
                %b Rate (INR)

            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %p.MsoNormal
                %b Amount (INR)
        - all_line_items.each do |item|
          / - if item.design.sell_count == 0
          /   - qc_not_required = false
          / - elsif (item.design.return_count >= 10 && (item.design.return_count*100)/item.design.sell_count <= 5)
          /   - qc_not_required = true
          / - elsif (item.design.return_count < 10 && (item.design.return_count*100)/item.design.sell_count <= 1)
          /   - qc_not_required = true
          / - else
          /   - qc_not_required = false
          -next if item.available_in_warehouse
          -if item.design.skip_qc
            -qc_not_required = true
          -else
            -qc_not_required = false

          - stitching_status = item.stitching_required == 'Y' ? "STITCHING REQUIRED" : ""
          - if @designer.name == 'Aapno Rajasthan' && item.design.addon_types.collect(&:name).include?('Rakhi Gifts')
            - vendor_sku = item.line_item_addons.present? ? item.line_item_addons.first.addon_type_value.description.to_s : item.design.design_code.to_s + '_only'
          - else
            - vendor_sku = item.design.design_code.present? ? item.design.design_code : ''
          %tr{style: "padding-top:10px;"}
            %td{colspan: "5", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal
                %span= item.design.title + " - " + item.design.categories.first.name.singularize + " - " + "# M" + item.design.id.to_s + ", Vendor[SKU] - #" + vendor_sku
                %b=qc_not_required == true ? "" : 'ALPHA'
                %b=item.stitching_required == 'Y' ? "STITCHING REQUIRED" : ""
                - if (variant = item.variant).present? && (option_type = variant.option_type_values).present?
                  %b="[size : #{option_type.collect(&:name).last}]"
            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal= item.quantity

            -if @order.international? || @designer_order.ship_to == 'mirraw'
              %td{colspan: "1", style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                -if @rtv
                  %p.MsoNormal=(item_snapshot_price = (item.vendor_selling_price.present? ? item.vendor_selling_price : item.snapshot_price(RETURN_NORMAL)))
                -else
                  %p.MsoNormal= (item_snapshot_price = (item.snapshot_price(RETURN_NORMAL) * @designer_order.get_payout_ratio).round(2))
            -else
              %td{colspan: "1", style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                -if @rtv
                  %p.MsoNormal=(item_snapshot_price = (item.vendor_selling_price.present? ? item.vendor_selling_price : item.snapshot_price(RETURN_NORMAL)))
                -elsif @order.currency_rate_market_value.present? && @designer_order.ship_to != "mirraw"
                  %p.MsoNormal= (item_snapshot_price = @order.international_cod_price(item.snapshot_price))
                -else
                  %p.MsoNormal= (item_snapshot_price = item.snapshot_price(RETURN_NORMAL))
            %td{colspan: "1", style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top", :width => "64"}
              -total_plus_shipping += (item_snapshot_price * item.quantity).round(2) if @rtv
              %p.MsoNormal= (item_snapshot_price * item.quantity).round(2)

              - if (lta = item.line_item_addons)[0].present? && !@rtv
                - lta.each do |addon|
                  - if addon.snapshot_price != 0
                    %tr{style: "padding-top:10px;"}
                      %td{colspan: "5", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                        %p.MsoNormal= addon.addon_type_value.name
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                        %p.MsoNormal= item.quantity
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                        -if @order.currency_rate_market_value.present? && @designer_order.ship_to != "mirraw"
                          %p.MsoNormal= (addon_snapshot_price = @order.international_cod_price(addon.snapshot_price))
                        -else
                          %p.MsoNormal= (addon_snapshot_price = addon.snapshot_price)
                      %td{colspan: "1", style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                        %p.MsoNormal= (addon_snapshot_price * item.quantity)

        - if @designer_order.discount? && !@rtv
          %tr{style: "padding-top:10px;"}
            %td{colspan: "5", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal Discounts
            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -

            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -

            %td{colspan: "1", style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top", :width => "64"}
              -if !@order.international? && @designer_order.ship_to != "mirraw"
                - if @order.currency_rate_market_value.present?
                  %p.MsoNormal= "-" +@order.international_cod_price(@designer_order.discount).to_s
                -else
                  %p.MsoNormal= "-" + @designer_order.discount.to_s


        - if @designer_order.shipping != 0
          %tr{style: "padding-top:10px;"}
            %td{colspan: "5", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal Shipping Charges
            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -

            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -

            %td{colspan: "1", style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top", :width => "64"}
              - if @order.currency_rate_market_value.present? && @designer_order.ship_to != "mirraw" && !@rtv
                %p.MsoNormal= @order.international_cod_price(@designer_order.shipping)
              -else
                %p.MsoNormal= @designer_order.shipping

        - all_line_items.count.upto(5) do
          %tr{style: "height:20pt;border:none"}
            %td{colspan: "5", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}


        %tr{style: "height:3.5pt"}
          %td{style: "padding:0in 5.4pt 0in 5.4pt;height:3.5pt;", valign: "top", colspan: 7}
          %td{style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;height:3.5pt;", valign: "top"}
            %p.MsoNormal
              -if (@order.international? || @designer_order.ship_to == "mirraw") && !@rtv
                %b= total_vendor_payout
              -else
                %b= total_plus_shipping.round(2)
        %tr{style: "height:48.45pt"}
          %td{colspan: "8", style: "border-top:none;border-left:solid windowtext .5pt;border-bottom:none;border-right:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt;height:48.45pt"}
            %p.MsoNormal
              -if (@order.international? || @designer_order.ship_to == "mirraw") && !@rtv
                %b= "Amount Chargeable: " + DesignerOrdersHelper.number_to_words(total_vendor_payout).to_s
              -else
                %b= "Amount Chargeable: " + DesignerOrdersHelper.number_to_words(total_plus_shipping).to_s
        %tr{style: "height:35.6pt"}
          %td{colspan: "8", style: "border-top:none;border-left:solid windowtext .5pt;border-bottom:none;border-right:solid windowtext .5pt;padding:0in 5.4pt 0in 5.4pt;height:35.6pt", valign: "top", :width => "497"}
            -if @order.pay_type.downcase == COD.downcase && !@rtv
              %span.MsoNormal Return Process:
              %p.MsoNormal
                If you want to return above product please visit www.mirraw.com/pages/faq
            %span.MsoNormal Declaration:
            %p.MsoNormal
              We declare that this
              Performa invoice show the actual price of the goods Described and that all
              particulars are true & correct
        %tr{style: "height:31.2pt"}
          %td{colspan: "4", style: "border:solid windowtext .5pt;border-top:none;padding:0in 5.4pt 0in 5.4pt;height:31.2pt", valign: "top"}
          %td{colspan: "4", style: "border:solid windowtext .5pt;border-left:none;padding:0in 5.4pt 0in 5.4pt;height:31.2pt", valign: "top"}
            %p.MsoNormal
              -if @rtv
                %u{style: "text-underline:double"}="For #{company_name},"
              -else
                %u{style: "text-underline:double"}= "For " +  @designer.name
            %br
            %p.MsoNormal
              %u{style: "text-underline:double"} Signature
          %td{:height => "31", style: "height:31.2pt;border:none", :width => "0"}

  - if @rtv.blank? && (@invoice_number.present? || (@designer_order.ship_to.blank? && @order.international?) || @designer_order.ship_to == 'mirraw' || checking_for_addons?(@order))
    - barcode_number = @order.present? ? "#{@order.number}|-|#{@designer_order.id}" : "#{@invoice_number}"
    - barcode_img = Shipment.generate_barcode(barcode_number)
    - order_address = "#{company_name},\n #{shipping_address_1}, #{shipping_address_2}, #{shipping_city}, #{shipping_state},#{shipping_pincode} \nPhone : +91 #{shipping_telephone}"
    - designer_address = "#{@designer.name}\n Phone: #{@designer.phone},\n#{@designer.street.gsub(/\,+$/,'')}, \n#{@designer.city} - #{@designer.pincode}, \n#{@designer.state},\n#{@designer.country}"
    %table{border: '2', align: 'center', style:'height:10px;width:80%;height:20%;page-break-before:always;'}
      %tr
        %td.td_barcode
          %img.bar_code{src: barcode_img, width: '45%',height:'70%',style:'margin-top:2px;'}
            %h3= @order.present? ? barcode_number : "#{@line_items.collect(&:order).flatten.collect(&:number).compact.uniq.join('-')}"
      %tr
        %td{colspan: 2}
          %table{border: "2",align: 'center', style: 'border-collapse:collapse;', width: '100%'}
            %tr
              %td.td_style{width: '50%'}
                %h3  TO
                = simple_format(order_address)
              %td.td_style{width: '50%'}
                %h3  FROM
                = simple_format(designer_address)
      %tr
        %td{colspan: 2}
          %p{style:'text-align:center;'}
            "**** If undelivered then return it to shipper *****"
  -if @rtv && total_plus_shipping >= 50000 && jewellery_flag
    - bill_data = {'GSTIN of Recipient' => @designer.gst_no || 'URD', 'Place of Delivery' => @designer.pincode, 'Invoice or Challan Number' => "MIR/#{@designer_order.invoice_number.to_s}", 'Invoice or Challan Date' => (@designer_order.pickup.presence || @designer_order.completed_at.presence || @designer_order.created_at).strftime("%d/ %m/ %Y"), 'Value of Goods' => value_of_good, 'HSN Code' => hsn_codes.join(', '),'Transport Document Number' => @awb_no, 'Vehicle Number' => ''}
    =render partial: '/shipments/e_way_bill', locals: {bill_data: bill_data}


- if @edit_credit_note
  %link{rel: "stylesheet", href: "https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css"}
  %script{src: "https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"}
  %script{src: "https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"}
  :javascript
    $(function() {
      if (typeof ($().popover) === 'function') {
        return $('[data-toggle="popover"]').popover().popover('show');
      }
    });
  %center
    .jumbotron
      %b.text-info Please enter your own credit note number or leave as it is for system generated number
      %br/
      %br/
      = form_tag edit_credit_note_path, :method => "post", class: "form-inline" do
        %label Credit Note Number:
        = text_field_tag 'credit_note_number', @credit_note_number.presence || @designer_order.try(:credit_note_number)
        = submit_tag 'Download', class: "btn btn-sm btn-primary"

- if @show_credit_note_pdf
  - w_order_invoice = WarehouseOrderInvoice.new(@designer_order)
  - company_name, shipping_telephone, shipping_address_1,shipping_address_2, shipping_city, shipping_pincode, shipping_state, _ = DesignerOrder.get_warehouse_billing_address([@designer_order.warehouse_address_id])
  -if @rtv
    -if params.present?
      -all_line_items = @designer_order.line_items.joins(:rtv_shipments).where(rtv_shipments: {number: params[:rtv]}).group('line_items.id').to_a
    -else
      -all_line_items = @designer_order.line_items.where(id: line_items_id)
  -else
    -all_line_items = @line_items.presence || @designer_order.line_items.sane_items.preload(design: [:categories,property_values: :property],variant: :option_type_values)
  .section
    -if true#(@designer_order.ship_to.blank? && @order.international?) || (@designer_order.ship_to == 'mirraw')
      %table{border: "1", :cellpadding => "0", :cellspacing => "0", style: "border-collapse:collapse;border:none", :width => "100%"}
        - total_taxable_value = 0.0
        - value_of_good = ''
        - hsn_codes = []
        - gst_total = 0.0
        - igst_flag = @designer.gst_no.present? && ((@invoice_number.present? && @designer.state.try(:upcase) == SHIPPER_STATE.upcase) || (((@designer_order.present? && @designer_order.ship_to == 'mirraw') || @rtv) && @designer.state.try(:upcase) == SHIPPER_STATE.upcase) || (@order.present? && @designer_order.present? && @designer_order.ship_to == 'customer' && @designer.state.try(:upcase) == @order.buyer_state.try(:upcase)))
        %tr
          %td{colspan: "22", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %p.MsoNormal
              %center
                -if @rtv #|| @designer_order.try(:domestic_sor?, @order)
                  %br
                  =w_order_invoice.ship_to.html_safe
                -else
                  = "Name: " + (@designer.business_name.presence || @designer.name)
                  %br
                  = "Address: " + (@designer.business_street.presence || @designer.street) + ", " + (@designer.business_city.presence || @designer.city) + " - " + (@designer.business_pincode.presence || @designer.pincode) + ", " + (@designer.business_state.presence || @designer.state) + ", " + (@designer.business_country.presence || @designer.country)
                  %br
                  = "Phone: #{@designer.phone}"
                  %br
                  = "GST no: " + @designer.gst_no.to_s

        %tr
          %td{colspan: "22", valign: "top"}
            %p.MsoNormal
              %center= "Credit Note"

        %tr
          %td{colspan: "14", style: "padding:0in 5.4pt 0in 5.4pt;height:18.7pt", valign: "top"}
            %span.MsoNormal{style: "font-weight:bold"} Recipient :
            %p.MsoNormal
              -if @rtv
                = "Name: #{@designer.name}"
                %br
                = "Address: #{@designer.street}, #{@designer.city} - #{@designer.pincode} , #{@designer.state} ,  #{@designer.country}"
                %br
                = "Phone: #{MIRRAW_CONTACT_INFO}"
                %br
                = "PAN no: " + @designer.try(:pan_no).to_s
                %br
                = "GST no: " + @designer.gst_no.to_s
              -elsif (@invoice_number.present? && @order.blank?) || (@designer_order.ship_to.blank? && @order.international?) || (@designer_order.ship_to == 'mirraw')
                %br
                =w_order_invoice.ship_to.html_safe
              -else
                = "Name: " + @order.name
                %br
                = "Address: " + @order.street + ", " + @order.city + " - " + @order.pincode + ", " + @order.buyer_state + ", " + @order.country
                %br
                = "Phone: " + @order.phone


            %td{colspan: "8", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %div
                %span.MsoNormal{style: "font-weight:bold"} Credit Note No :
                %span.MsoNormal
                  = @designer_order.credit_note_number
                %br
                %span.MsoNormal{style: "font-weight:bold"} Credit Note Date :
                %span.MsoNormal= @designer_order.credit_note_date
                %br
              %br
              %div
                %span.MsoNormal{style: "font-weight:bold"} Ref Purchase Order No :
                %span.MsoNormal= @order.number

          -if @rtv
            -total_plus_shipping = 0
          -elsif @invoice_number.present?
            - total_vendor_payout = 0
          -elsif @order.international? || @designer_order.ship_to == 'mirraw'
            - total_vendor_payout = @designer_order.payout
          -elsif @order.currency_rate_market_value.present? && @designer_order.ship_to != "mirraw"
            - total_plus_shipping = @order.international_cod_price(@designer_order.scaled_total + @designer_order.shipping)
          -else
            - total_plus_shipping = @designer_order.total + @designer_order.shipping

          %tr
            %td{colspan: "11", rowspan: 2, style: "padding:0in 5.4pt 0in 5.4pt;border-right: 1px solid;", valign: "top"}
              %p.MsoNormal
                %b Description of  goods

            %td{colspan: "1", rowspan: 2, style: "border:1px solid;padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %p.MsoNormal
                %b HSN CODE

            %td{colspan: "1", rowspan: 2, style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %p.MsoNormal
                %b Quantity

            %td{colspan: "1", rowspan: 2, style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %p.MsoNormal
                %b Value (INR)

            %td{colspan: "1", rowspan: 2, style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %p.MsoNormal
                %b Discount (INR)

            %td{colspan: "1", rowspan: 2, style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %p.MsoNormal
                %b Net Value (INR)

            %td{colspan: "1", rowspan: 2, style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %p.MsoNormal
                %b Taxable Value

            -(titles = igst_flag ?  ['CGST', 'SGST'] : ['IGST']).each do |title|
              %td{colspan: ((3 - titles.length) * 2).to_s, style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
                %p.MsoNormal
                  %b=title


            %td{colspan: "1", rowspan: 2, style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
              %p.MsoNormal
                %b Amount (INR)

          %tr
            -(titles = ('Rate,Amount (INR),' * (igst_flag ? 2 : 1)).split(',')).each do |title|
              %td{colspan: (4 - titles.length).to_s, style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
                %p.MsoNormal
                  %b=title

        - payable_gst = 0.0
        - jewellery_flag = false  #if all line_items designable type is jewellery then false else true
        - billing_status = !@rtv && !@invoice_number && @designer_order.ship_to != 'mirraw' && @order.cod?
        - if billing_status
          - dos_total  = all_line_items.to_a.sum{|li| (li.snapshot_price * li.quantity)}.to_f
          - order_total_price = @order.line_items.to_a.sum{|li| li.price_with_addons * li.quantity}
          - designer_order_discount = @designer_order.discount.to_i
          - total_plus_shipping += designer_order_discount if designer_order_discount > 0
          - order_total_discount = @order.cod? ? 0 : @order.get_total_discount.to_i
        -designer_order_discount, order_total_discount, order_total_price = if billing_status && @order.currency_rate_market_value.present?
          -[designer_order_discount, order_total_discount, order_total_price].map{|i| @order.international_cod_price(i)}
        - all_line_items.each do |item|
          / - if item.design.sell_count == 0
          /   - qc_not_required = false
          / - elsif (item.design.return_count >= 10 && (item.design.return_count*100)/item.design.sell_count <= 5)
          /   - qc_not_required = true
          / - elsif (item.design.return_count < 10 && (item.design.return_count*100)/item.design.sell_count <= 1)
          /   - qc_not_required = true
          / - else
          /   - qc_not_required = false
          -if item.design.skip_qc
            -qc_not_required = true
          -else
            -qc_not_required = false

          - stitching_status = item.stitching_required == 'Y' ? "STITCHING REQUIRED" : ""
          - if @designer.name == 'Aapno Rajasthan' && item.design.addon_types.collect(&:name).include?('Rakhi Gifts')
            - vendor_sku = item.line_item_addons.present? ? item.line_item_addons.first.addon_type_value.description.to_s : item.design.design_code.to_s + '_only'
          - else
            - vendor_sku = item.design.design_code.present? ? item.design.design_code : ''
          - addon_price = item.vendor_addon_items.to_a.sum{|addon|addon.snapshot_price(RETURN_NORMAL)}
          - addon_text = item.vendor_addon_items.collect{|item| "[#{item.addon_type_value.name}]" }.join
          %tr{style: "padding-top:10px;"}
            %td{colspan: "11", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal
                %span= item.design.title + " - " + item.design.categories.first.name.singularize + " - " + "# M" + item.design.id.to_s
                %p{style: "background-color: #000000;"}
                  %b{style: "color: #ffffff; font-weight: bold; font-size: larger; padding: 8px; line-height: 1.7"}
                    ="Vendor[SKU] - #" + vendor_sku
                    =qc_not_required == true ? "" : 'ALPHA'
                    =item.stitching_required == 'Y' ? "STITCHING REQUIRED" : ""
                    - if (variant = item.variant).present? && (option_type = variant.option_type_values).present?
                      ="[size : #{option_type.collect(&:name).last}]"
                    - if @invoice_number.present?
                      = " : #{item.order.number}"
                    - if addon_text.present?
                      =addon_text
                    - if item.available_in_warehouse
                      [DNS]
            %img{src: design_image_data(item), width: '170px', height:'250px',style:'margin-top:2px;'}
            - design = item.design
            - product_name = design.invoice_category_name('dhl').titleize
            - hsn_code = (@designer.hsn_approved && design.gst_rate.present? && design.hsn_code.present?) ? design.hsn_code.to_s : design.categories.hsn_code.to_s
            - hsn_codes << hsn_code
            - jewellery_flag = design.designable_type.try(:downcase) != 'jewellery'
            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal= hsn_code

            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal= item.quantity

            -if @rtv
              -item_snapshot_price = (item.vendor_selling_price.present? ? (item.vendor_selling_price * (100-@designer_order.transaction_rate)/100.0).to_i : item.snapshot_price(RETURN_NORMAL))
            -elsif @invoice_number.present?
              -item_snapshot_price = (item.snapshot_price(RETURN_NORMAL) * item.designer_order.get_payout_ratio).round(2)
              -item_snapshot_price += addon_price
            -elsif @order.international? || @designer_order.ship_to == 'mirraw'
              -item_snapshot_price = ((item.snapshot_price(RETURN_NORMAL) + addon_price) * @designer_order.get_payout_ratio).round(2)
            -elsif @order.currency_rate_market_value.present? && @designer_order.ship_to != "mirraw"
              -item_snapshot_price = @order.international_cod_price(item.snapshot_price + addon_price)
            -else
              -item_snapshot_price = item.snapshot_price(RETURN_NORMAL) + addon_price
            - designer_discount, discount, discounted_rate = 0, 0, item_snapshot_price
            - if billing_status
              - designer_discount = (item_snapshot_price * item.quantity * designer_order_discount/dos_total).round(2)
              - discount = ((item_snapshot_price / order_total_price.to_f) * order_total_discount) + designer_discount
              - discount = 0 if discount > item_snapshot_price
              - total_plus_shipping -= (discount * item.quantity) if billing_status
              - discounted_rate = (item_snapshot_price - discount)
            - _,gst_rate = item.find_hscode_gst(item_snapshot_price, hsn_code, false)
            -if @designer.gst_no.present?
              - gst_total += gst_tax = ((discounted_rate - discounted_rate/(1+gst_rate/100.0))*item.quantity).round(2)
              - taxable_value = (discounted_rate/(1+gst_rate/100.0)).round(2)
              - total_taxable_value += (taxable_value * item.quantity).round(2)
            -total_vendor_payout += (item_snapshot_price * item.quantity) if @invoice_number.present?
            %td{colspan: "1", style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal=item_snapshot_price #@designer.gst_no.present? ? taxable_value : item_snapshot_price

            %td{colspan: "1", style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal= discount.nonzero?.try(:round,2) || '-'

            %td{colspan: "1", style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal= discounted_rate.round(2)

            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal= @designer.gst_no.present? ? (taxable_value * item.quantity).round(2) : "-"

            -(gst_div = igst_flag ? 2 : 1).times do
              %td{colspan: (3 - gst_div).to_s, style: "padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                %p.MsoNormal= @designer.gst_no.present? ? ((gst_rate.to_f / gst_div.to_f).round(2).nonzero? || '-') : '-'

              %td{colspan: (3 - gst_div).to_s, style: "padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                %p.MsoNormal= (gst_tax.to_f / gst_div.to_f).round(2).nonzero? || '-'

            %td{colspan: "1", style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top", :width => "64"}
              -if @rtv
                -total_plus_shipping += (discounted_rate * item.quantity).round(2)
              -elsif @designer.gst_no.blank?
                -payable_gst += ((discounted_rate - (discounted_rate/(1+gst_rate/100.0)).round(2)) * item.quantity).round(2)
              %p.MsoNormal= (discounted_rate * item.quantity).round(2)

              - if false#(lta = item.line_item_addons)[0].present? && !@rtv
                - lta.each do |addon|
                  - if addon.snapshot_price != 0
                    %tr{style: "padding-top:10px;"}
                      %td{colspan: "11", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                        %p.MsoNormal= addon.addon_type_value.name
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                        %p.MsoNormal= item.quantity
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                        -if @order.currency_rate_market_value.present? && @designer_order.ship_to != "mirraw"
                          %p.MsoNormal= (addon_snapshot_price = @order.international_cod_price(addon.snapshot_price))
                        -else
                          %p.MsoNormal= (addon_snapshot_price = addon.snapshot_price)
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                      %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                      %td{colspan: "1", style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
                        %p.MsoNormal= (addon_snapshot_price * item.quantity)

        - if @designer_order.present? && @designer_order.shipping != 0
          %tr{style: "padding-top:10px;"}
            %td{colspan: "11", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal Shipping Charges
            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -

            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -


            %td{colspan: "1", style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top", :width => "64"}
              - if @order.present? && @order.currency_rate_market_value.present? && @designer_order.ship_to != "mirraw" && !@rtv
                %p.MsoNormal= @order.international_cod_price(@designer_order.shipping)
              -elsif @designer_order.present?
                %p.MsoNormal= @designer_order.shipping

            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -


            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -


            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -


            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -


            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -


            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -


            %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal -

            %td{colspan: "1", style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;", valign: "top", :width => "64"}
              - if @order.present? && @order.currency_rate_market_value.present? && @designer_order.ship_to != "mirraw" && !@rtv
                %p.MsoNormal= @order.international_cod_price(@designer_order.shipping)
              -elsif @designer_order.present?
                %p.MsoNormal= @designer_order.shipping

        - all_line_items.count.upto(5) do
          %tr{style: "height:20pt;border:none"}
            %td{colspan: "11", style: "padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
            -6.times do
              %td{colspan: "1", style: "padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
            -(gst_div = igst_flag ? 4 : 2).times do
              %td{colspan: ((6 - gst_div)/2).to_s, style: "padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
            %td{colspan: "1", style: "padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top"}


        %tr{style: "height:3.5pt"}
          %td{style: "padding:0in 5.4pt 0in 5.4pt;height:3.5pt;", valign: "top", colspan: 21}
            %p.MsoNormal Total
          %td{style: "text-align:right;padding:0in 5.4pt 0in 5.4pt;height:3.5pt;", valign: "top",colspan: 1}
            %p.MsoNormal
              -if (@invoice_number.present? || @order.international? || @designer_order.ship_to == "mirraw") && !@rtv
                %b= total_vendor_payout.round(2)
              -else
                %b= total_plus_shipping.round(2)
        %tr
          %td{colspan: "14",rowspan: (igst_flag ? 6 : 5).to_s, style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %p.MsoNormal
              -if (@invoice_number.present? || @order.international? || @designer_order.ship_to == "mirraw") && !@rtv
                %b= "Amount Chargeable: " + DesignerOrdersHelper.number_to_words(total_vendor_payout).to_s
              -else
                %b= "Amount Chargeable: " + DesignerOrdersHelper.number_to_words(total_plus_shipping).to_s
            %p.MsoNormal
              = "Details of Transportation"
              %br
              = "E-way bill no:"
              %br
              = "Date of E-way Bill:"
              %br
              = "Name of Transporter"
              %br
              = "Mode:"
              %br
              = "Vehicle No:"
              %br
              = "Date & Time:"
          %td{colspan: "4", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %p.MsoNormal Total Amount Before Tax
          %td{colspan: "4", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %p.MsoNormal= @designer.gst_no.present? ? total_taxable_value.round(2) : ((@invoice_number.present? || @order.international? || @designer_order.ship_to == "mirraw") && !@rtv ? total_vendor_payout : total_plus_shipping.round(2))
          -(titles = igst_flag ? ['CGST', 'SGST'] : ['IGST']).each do |title|
            %tr
              %td{colspan: "4", style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
                %p.MsoNormal= "Add: #{title}"
              %td{colspan: "4", style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
                %p.MsoNormal= (gst_total / titles.length).round(2)
        %tr
          %td{colspan: "4", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %p.MsoNormal Total Amount
          %td{colspan: "4", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %p.MsoNormal
              -if (@invoice_number.present? || @order.international? || @designer_order.ship_to == "mirraw") && !@rtv
                %b= total_vendor_payout
              -else
                %b= total_plus_shipping.round(2)
                - value_of_good = "#{(total_plus_shipping).round(2)} INR"
        %tr
          %td{colspan: "4", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %p.MsoNormal
              GST payable on Reverse Charge
              -if payable_gst > 0
                %br
                Tax Amount
          %td{colspan: "4", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %p.MsoNormal
            -if payable_gst > 0
              YES
              %br
              =payable_gst.round(2)
        %tr
          %td{colspan: "22", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
            %p.MsoNormal
        %tr{style: "height:35.6pt"}
          %td{colspan: "18", style: "padding:0in 5.4pt 0in 5.4pt;height:35.6pt", valign: "top", :width => "497"}
            -if @order.present? && @order.pay_type.downcase == COD.downcase && !@rtv
              %span.MsoNormal Return Process:
              %p.MsoNormal
                If you want to return above product please visit www.mirraw.com/pages/faq
            %span.MsoNormal Declaration:
            %p.MsoNormal We declare that this invoice shows the actual price of the goods described and that all particulars are true and correct.
          %td{colspan: "4", style: "padding:0in 5.4pt 0in 5.4pt;height:31.2pt", valign: "top"}
            %p.MsoNormal
              -if @rtv
                %u{style: "text-underline:double"} For Mirraw
              -else
                %u{style: "text-underline:double"}= "For " +  @designer.name
            %br
            %p.MsoNormal
              %u{style: "text-underline:double"} Signature
  

:css
  .td_style {
    font-size:12px;
    padding:10px;
    vertical-align:top;
  }
  .td_barcode
  {
    font-size:12px;
    text-align:center;
    padding:10px;
  }

- if @edit_invoice
  :css
    table{
      border: 1px solid black !important;
    }
    td {
      border-right: 1px solid black !important;
    }
    body{
      background-color: #eee;
      padding: 50px
    }

- if @edit_credit_note
  :css
    table{
      border: 1px solid black !important;
    }
    td {
      border-right: 1px solid black !important;
    }
    body{
      background-color: #eee;
      padding: 50px
    }