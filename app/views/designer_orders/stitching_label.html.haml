:css  
  .page-break { 
    display:block;
    clear:both;
    page-break-after:always; 
  }
  
- all_measurements = delayed_attempt ? [@stitching_measurement] : line_item.stitching_measurements.to_a 
- all_measurements.each do |stitching_measurement|
  -designable = stitching_measurement.line_item.design.designable_type
  -type = 'D'
  -type = 'B' if designable == 'Saree' || designable == 'Lehenga'
  -type = 'F' if stitching_measurement.product_designable_type == 'fnp'
  -barcode_number = "#{stitching_measurement.id}-#{stitching_measurement.order_id}-#{stitching_measurement.line_item_id}-#{stitching_measurement.line_item.quantity}-#{type}"
  -in_line_barcode_img = Shipment.generate_barcode(barcode_number)
  -lower_barcode_number = "#{stitching_measurement.order.number}-#{stitching_measurement.design_id}-#{stitching_measurement.id}-#{stitching_measurement.line_item_id}"
  -lower_in_line_barcode_img = Shipment.generate_barcode(lower_barcode_number)
  -vertical_barcode_img = Shipment.generate_barcode(lower_barcode_number, 50, true)
  %meta{content: "text/html; charset = UTF-8", "http-equiv" => "Content-Type"}
  .Section1
    - order = stitching_measurement.order
    %table{border: "1", cellpadding: "0", cellspacing: "0", style: "font-size:18px;border-collapse:collapse;border:none", width: "100%"}
      - if stitching_measurement.code.present?
        %tr
          %td
            Code:
          %td
            = stitching_measurement.code
          - 2.times do |i|
            %td{rowspan: '50'}          
              %img.bar_code{src: vertical_barcode_img, style: 'padding:10px;'}        
      %tr
        %td{colspan: "2"}
          Product: 
          = PRODUCT_DESIGNABLE_TYPES.key(stitching_measurement.product_designable_type)
        - unless stitching_measurement.code.present?
          - 2.times do |i|
            %td{rowspan: '50'}          
              %img.bar_code{src: vertical_barcode_img, style: 'padding:10px;'}        
      %tr
        %td{colspan: '2'}
          Stylist:
          = order.try(:stylist).try(:name)
      %tr
        %td{colspan: "2"}        
          %img.bar_code{:src=> in_line_barcode_img,style:"margin: 1px 1px 0px 0px;width:98%"}
          %b{style:"margin-left: 52px"}=barcode_number
      - if stitching_measurement.product_designable_type == 'fnp'
        %tr
          %td
            Order No:
            = order.number
          %td
            Product Id:
            = stitching_measurement.design_id
      - else

        - is_custom = stitching_measurement.line_item.line_item_addons.to_a.find{|b| b.addon_type_value.addon_type_value_group.name.downcase == 'custom_stitching'}.present?
        - prestitched_saree_size = stitching_measurement.line_item.get_prestitch_saree_size(type: :size)
        =render partial: 'stitching_measurement/stitching_data_view',locals: {hide_top_bottom: nil, custom: is_custom, lower_barcode_number: lower_barcode_number, lower_in_line_barcode_img: lower_in_line_barcode_img, prestitched_saree_size: prestitched_saree_size, :@stitching_measurement => stitching_measurement}
      %tr
        %td{colspan: "2"}
          = "Package Details: #{stitching_measurement.line_item.design.package_details}"
      %tr
        %td
          -@addons = stitching_measurement.line_item.get_addons
            -@addons.each do |addon|
            - addon_name = addon.try(:addon_type_value).try(:name)
            -if["Petticoat Stitching","No Petticoat","Shapewear"].include?(addon_name)
              %tr  
                %td{colspan: "2"}
                  = "#{addon_name} | #{DesignerOrder.get_name_for_stitching_addon_note_value(addon)} "
            -if["Fall and Pico","No Fall and Pico"].include?(addon_name) 
              %tr    
                %td{colspan: "2"}
                  = "#{addon_name}"
      %tr 
        %td{colspan: "2"}
          = "Date: #{Time.zone.now.strftime('%Y-%m-%d %H:%M')}"  
      - if (mes_confirmed_date = stitching_measurement.line_item.try(:measuremnet_received_on)).present?
        %tr
          %td{colspan: "2"}
            = "To Be Received By Date: #{(mes_confirmed_date + OperationProcess::PROCESS_IDS['Tailor Receiving'][1].to_i.day).strftime('%Y-%m-%d %H:%M')}"  
    - unless (stitching_measurement == all_measurements.last)
      .page-break

