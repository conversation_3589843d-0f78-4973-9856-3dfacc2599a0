.designer-designs-table
	.rows
		= form_tag form_path, :method => 'get' do
			.row
				.col-md-6= text_field_tag :search, params[:search],placeholder: 'SEARCH',class: 'form-control'
				.col-md-4= select_tag :sort,options_for_select(%w(published unpublished reject processing review all), @sort),class: 'form-control'
				.col-md-2= submit_tag 'List Product',class: 'btn'

	= form_tag discontinue_designs_path, method: :put do
		%table.table
			%tr
				%th 
					= link_to 'All | ', 'javascript:void(0)', :class => "unpublish_designs_checkbox_all"
					= link_to 'None', 'javascript:void(0)', :class => "unpublish_designs_checkbox_none"
				%th Mirraw ID
				%th Internal Code
				%th Photo
				%th Name
				-if @designer.is_transfer_model?
					%th Transfer Price
				-else
					%th Displayed Price
					%th Price
					%th Discount Percent
				%th Quantity
				%th Status
				- if @sort == 'reject'
					%th Note
				%th{:colspan => 4} Actions
			- @designs.each do |design|
				- if design.present? && design.master_image && design.master_image.photo?
					%tr
						%td= check_box_tag "design_ids[]", design.id
						%td= design.id
						%td.col-md-1= design.design_code ? design.design_code : ''
						%td= image_tag(design.master_image.photo.url(:thumb), :width => '50', :height => '50') 
						%td.col-md-1= link_to design.title , designer_design_url(@designer, design)
						-if @designer.is_transfer_model?
							%td
								-unless design.variants.present?
									= text_field_tag 'price', design['transfer_price'], size: 10, :class => "enabled_design_price form-control", :id => "price_" + design.id.to_s
									%div= link_to 'update', 'javascript:void(0)', :class => "update_design_price", :id => "update_price_" + design.id.to_s
						-else
							%td{:id => design.id.to_s+'_effective_price'}= design.effective_price
							%td
								-unless design.variants.present?
									= text_field_tag 'price', design['price'], size: 10, :class => "enabled_design_price form-control", :id => "price_" + design.id.to_s
									%div= link_to 'update', 'javascript:void(0)', :class => "update_design_price", :id => "update_price_" + design.id.to_s
							%td
								= text_field_tag 'discount_percent', design['discount_percent'].to_i, size: 3, :class => "enabled_design_discount_percent form-control", :id => "discount_percent_" + design.id.to_s
								%div= link_to 'update', 'javascript:void(0)', :class => "update_design_discount_percent", :id => "update_discount_percent_" + design.id.to_s
						- if design.variants.present?
							%td
								/= link_to 'edit', edit_designer_design_path(design.designer, design), :target => '_blank'
						- else
							%td
								= text_field_tag 'quantity', design.designer_quantity, size: 3, :class => "enabled_design_quantity form-control", :id => "quantity_" + design.id.to_s
								- unless @designer.is_unicommerce_vendor
									%div= link_to 'update', 'javascript:void(0)', :class => "update_design_quantity", :id => "update_" + design.id.to_s
						= hidden_field_tag 'manage_boutique', true
						%td{:id => design.id.to_s+'_state'}= design.human_state_name
						- if @sort == 'reject'
							%td= design.notes
						%td= link_to 'edit', edit_designer_design_path(@designer, design), :target => '_blank',class: 'btn'
						%td
							- if ['review','reject','processing'].exclude?(design.state) && design.in_stock_warehouse.to_i <= 0
								= button_tag 'OutOfStock', :value => 'seller_out_of_stock', :data => {:id => design.id, :refresh => 0}, :class => 'btn state-change'
						%td              
							= button_tag 'Delete', :value => 'delete', :data => {:id => design.id, :refresh => 0}, :class => 'btn btn-danger state-change'
						- if design.state == 'banned' and current_account.admin? 
							%td
								= button_tag 'RemoveBan', :value => 'in_stock', :data => {:id => design.id, :refresh => 0}, :class => 'btn state-change'
						- if design.variants_available
							%td= button_tag 'DeleteSizeOptions', :value => 'delete-variants', :data => {:id => design.id, :designer_id => @designer.id}, class: 'btn remove-variants'
		= hidden_field_tag :designer_id, @designer.id
		= hidden_field_tag :sort, @sort
		- if params[:search].blank?
			- if @sort == "unpublished"
				// Do Nothing
			- else
				- if ['review','reject','processing'].exclude?(@sort)
					= submit_tag 'OutOfStock',class: 'btn'
		- if current_account.admin?
			= submit_tag 'Delete',class: 'btn'
			= submit_tag 'Reject',class: 'btn'
			- unless @designer.review_pending?
				= submit_tag 'InStock',class: 'btn'

= will_paginate @designs