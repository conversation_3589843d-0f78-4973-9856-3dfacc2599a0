= javascript_include_tag 'seller_campaign'
.row
  = form_tag campaign_portal_mirraw_admin_seller_campaigns_path, method: :get, class: 'form-inline' do
    .form-group
      = label_tag :start_date, 'Start Date:'
      = text_field_tag :start_date, params[:start_date], class: 'form-control datepicker start_date', placeholder: 'YYYY-MM-DD'
    .form-group
      = label_tag :end_date, 'End Date:'
      = text_field_tag :end_date, params[:end_date], class: 'form-control datepicker end_date', placeholder: 'YYYY-MM-DD'
    = submit_tag 'Filter', class: 'btn btn-primary'
:javascript
  $(document).ready(function() {
      MR.sellerCampaign.datePicker()
  })        