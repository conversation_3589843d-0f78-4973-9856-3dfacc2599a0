=content_for :page_title, 'Designer Additional Discount'

.pane{:style => "margin-bottom:30px;padding:10px"}
  = render :partial => 'designers/additional_discount_form'
  %table#help{style:"border-spacing: 15px; border-collapse: separate;width:100%"}
    %tr
      %td{:colspan=> '100%' }
        %h5
          %b
            NOTE:
          This discount is applicable on all of your product. This additional discount will be active from start and end date specified. Once additional discount is active only end date will change. Additional discount will automatically disable once end date passes. 
    %tr
      %td{:colspan => 2}
        %h4
          %i.fa.fa-chevron-right
          %u To activate additional discount :
        %ul.list
          %li= "Enter the additional discount in percentage"
          %li= "Select start and end date"
          %li= "Click Update"
          %li= "This will activate additional discount on all of your product from start date."
      %td{:rowspan=> 2}
        %h3
          %em= "Example:"
          For Design ID
          = link_to @design_data[:design_id], designer_design_path(@designer,@design_data[:design_cached_slug])
        %ul.list
          %li
            Current MRP =
            %em= @design_data[:price]
          %li
            Existing discount =
            %em= @design_data[:discount_percent]
          %li
            Previous Selling Price =
            %em= @design_data[:discount_price]
          %li
            Additional Discount =
            %em= @design_data[:additional_discount]
          %li
            Total Discount =
            %em=  @design_data[:effective_discount]
          %li
            Final Selling Price =
            %em= @design_data[:new_discount_price]
          %li
            Your Payout amount =
            %em= @design_data[:new_payout]
    %tr
      %td{:colspan => 2}
        %h4
          %i.fa.fa-chevron-right
          %u To deactivate additional discount :
        %ul.list
          %li= "Select end date as today"
          %li= "Click Update"
          %li= "This will deactivate additional discount on all of your product from next day."
    %tr
      %td{:colspan => 2}
        %h4
          %i.fa.fa-chevron-right
          %u To cancel upcoming additinal discount :
        %ul.list
          %li= "Select additional discount percent as zero(For upcoming additional discount)"
          %li= "Click Update"
          %li= "This will cancel upcoming additional discount."
