=content_for :page_title, "Dispatch Bulk Orders"
.row
  = form_tag designer_bulk_dispatch_path(@designer), method: 'get', class: 'form-horizontal form-label-left' do
    .row.designer_order_index_filters
      .col-md-12.col-sm-12.col-xs-12
        .x_panel
          .x_title.form-group
            .col-md-1.col-sm-1.col-xs-1=label_tag :order_status, 'Order Status : '
            .col-md-2.col-sm-2.col-xs-2=select_tag :options_for_filter,options_for_select(['pending','dispatched'], params['options_for_filter']),class: 'form-control'

            #reportrange.col-md-3.col-sm-6.col-xs-6
              %i.glyphicon.glyphicon-calendar.fa.fa-calendar
              =hidden_field_tag :daterange_period,(params[:daterange_period].presence || 'Last 3 Months')
              =text_field_tag :daterange_selector,params[:daterange_selector], style: 'border: 0px;width: 85%'
              %b.caret

            .col-md-1.col-sm-6.col-sx-6
              = submit_tag "Search",class: 'btn btn-primary btn-sm'
          .x_content
            .pull-right
              Total #{params[:options_for_filter]} Orders :
              %b #{@designer_orders.try(:total_entries) || @shipments.try(:total_entries)}
-if @state == 'pending'
  -if @designer_orders.present?
    .row
      %table.table
        %tr
          %th.col-md-1.text-center Select Orders
          %th.col-md-1.text-center Order Number
          %th.col-md-1.text-center Item
          %th.col-md-1.text-center Image
          %th.col-md-1.text-center Quantity
          %th.col-md-1.text-center Transfer Price
          %th.col-md-2.text-center Total Transfer Price
          %th.col-md-1.text-center Status
          %th.col-md-3.text-center Action
    .row.designer_orders.states_css
      = form_tag designer_order_dispatch_orders_in_bulk_path(@designer), class: "form form-horizontal" do
        .col-md-9.col-sm-9.col-xs-9
          .panel.panel-default.border-panel
            %table.table.table-bordered
              -@designer_orders.each do |des_order|
                -order = des_order.order
                -order_date = (order.confirmed_at.presence || order.created_at)
                %tr
                  -need_to_disable = des_order.warehouse_address_id.to_i != DEFAULT_WAREHOUSE_ADDRESS_ID.to_i
                  %td.col-md-1.col-sm-1.col-xs-1=check_box_tag "designer_order_id[]", des_order.id, false, class: "select_orders form-control",disabled: need_to_disable
                  %td.col-md-1.col-sm-1.col-xs-1
                    %b #{order.number}
                    %br #{order_date.strftime('%a, %d %B %Y')}
                    %hr
                    %b Dispatch By
                    %br
                      - if order.express_delivery.present?
                        #{order_date.advance(days: 1).strftime('%d %B %Y')}
                      - else
                        #{order_date.advance(days: 2).strftime('%d %B %Y')}

                  %td.col-md-9.col-sm-9.col-xs-9
                    %table.table
                      = render partial: 'designer_order_items', :locals => {designer_order: des_order, bulk: true}
                  %td.col-md-1.col-sm-1.col-xs-1
                    .row.text-center
                      %b.label{class: "#{des_order.state}"} #{des_order.state}
                      - if order.express_delivery.present?
                        %br
                        = image_tag('express_delivery.png', alt: 'Express Delivery')
                      %br
                      %br
                      %b.red
                        ="#{(Date.today - (des_order.confirmed_at || des_order.created_at).to_date).to_i} Days Passed" if des_order.state == "pending"
        .col-md-3.col-sm-3.col-xs-3
          .x_panel.tile
            .x_title
              %h2 Dispatch By
              %ul.nav.navbar-right.panel_toolbox
                %li.pull-right
                  %a.collapse-link
                    %i.fa.fa-chevron-up
              .clearfix
            .x_content
              .row
                .col-md-2 Address:
                .col-md-10
                  = "#{COMPANY_NAME},"
                  %br
                  = "#{SHIPPING_ADDRESS},"
                  %br
                  = SHIPPING_ADDRESS_LINE_2
                  %br
                  = SHIPPING_PINCODE
                  %br
                  = "+91 #{SHIPPING_TELEPHONE}"
              %hr
              .row
                .form-group.text-center.get_invoice
                  = link_to 'Get Serviceable Shippers', 'javascript:void(0)', id: 'get-shippers', class: 'btn btn-success'
                  = hidden_field_tag :state, 'dispatched'
                  = hidden_field_tag :dispatch_through_clickpost,false
                .form-group.shipper_names{style: 'display: none;'}
                  = label_tag :shipping_company, 'Shipping Company:', class: 'control-label col-md-3'
                  .col-md-8.col-md-offset-1= select_tag :shipper_id, options_from_collection_for_select(Shipper::ALL_SHIPPERS, :last, :first), id: 'shipping_company', class: 'form-control'
                .shipment_number{style: 'display: none;'}
                  .form-group
                    = label_tag :tracking_number_label, 'Tracking Number:', class: 'control-label col-md-3'
                    .col-md-8.col-md-offset-1= text_field_tag :tracking_number, nil, class: 'form-control'
                .dispatch_shipment{style: 'display: none;'}
                  .form-group.text-center
                    = link_to "[Edit / Generate Invoice]", '', type: 'button', class: 'btn btn-info btn-sm', :target => '_blank', id: 'edit_inv_link'
                    = submit_tag 'Dispatch', class: 'btn btn-small btn-success'
    .row.text-center
      %h4= will_paginate @designer_orders
  -else
    %h5 No New Orders Present !
-else
  .row
    %table.table
      %tr
        %th.col-md-1.text-center Order Number
        %th.col-md-1.text-center Payout
        %th.col-md-1.text-center Item
        %th.col-md-1.text-center Image
        %th.col-md-1.text-center Quantity
        %th.col-md-1.text-center Transfer Price
        %th.col-md-2.text-center Total Transfer Price
        %th.col-md-1.text-center Status
        %th.col-md-3.text-center Action
  .row.designer_orders.states_css
    -@shipments.each do |shipment|
      = form_tag designer_order_dispatch_orders_in_bulk_path(@designer), class: "form form-horizontal",enctype: "multipart/form-data" do
        .row.panel.panel-default
          .col-md-9
            %table.table.table-bordered
              -dos = shipment.bulk_designer_orders.find{|i| i.state == 'dispatched'} || shipment.bulk_designer_orders.first
              = hidden_field_tag 'designer_order_id', dos.id, class: 'designer_order_id'
              -shipment.bulk_designer_orders.each do |des_order|
                -order = des_order.payment_order
                %tr
                  %td.col-md-1.col-sm-1.col-xs-1= order.number
                  %td.col-md-1.col-sm-1.col-xs-1
                    Rs. #{des_order.payout}
                    -if @state == 'completed'
                      %p= "Payout Status = #{des_order.designer_payout_status}" if des_order.designer_payout_status.present?
                      %p= "Payout Date = #{des_order.designer_payout_date.strftime('%d/%m/%Y')}" if des_order.designer_payout_date.present?
                      %p= "Payout Notes = #{des_order.designer_payout_notes}" if des_order.designer_payout_notes.present?
                  %td.col-md-9.col-sm-9.col-xs-9
                    %table.table
                      = render partial: 'designer_order_items', :locals => {designer_order: des_order, bulk: true}
                  %td.col-md-1.col-sm-1.col-xs-1
                    .row.text-center
                      %b.label{class: "#{des_order.state}"} #{des_order.state}
          .col-md-3.text-center
            %br
            - if shipment.invoice_updated_at.present?
              %a{:href => shipment.invoice} [Invoice]
            - if shipment.label_updated_at.present?
              %a{:href => shipment.label} [Label]
            %br
            - if dos.tracking_partner.present?
              Shipped By : 
              %b #{dos.tracking_partner}
              %br
              Tracking Number : 
              %b #{shipment.number}
              %hr
            -if @state == 'dispatched' && ['dispatched', 'completed'].include?(dos.state)
              -if dos.invoice_state == 'not_uploaded'
                %b= 'Upload Signed Invoice here'
                =file_field_tag :file,class: 'form-control col-md-12',accept: 'application/pdf',required: true
                =hidden_field_tag :state, 'invoice_uploaded'
                %br
                = submit_tag 'Upload Invoice', class: 'btn btn-small btn-success',style: 'margin-top:10px;'
              -elsif dos.invoice_state == 'invoice_uploaded'
                =hidden_field_tag :state, 'completed'
                = submit_tag 'Mark Complete', class: 'btn btn-small btn-success'
  .row.text-center
    %h4= will_paginate @shipments

= javascript_include_tag 'designer_orders'