=content_for :page_title, 'Bulk Upload XLSX'
.row
  .col-md-12
    %h5 Please download the respective XLSX Format and use only that XLSX Sheets while uploading

%br

- @version_bulk_upload.each do |version|
  / %h2= version[:name].to_s.humanize
  = form_tag designers_bulk_upload_path, method: :post, multipart: true do
    .row
      .col-md-3
        -if version[:new_url].present?
          %h5=link_to "#{version[:name]} XLSX format download", version[:new_url]
        -else
          %h5=link_to "#{version[:name]} XLSX format download", version[:url]
      .col-md-6= file_field_tag :file, accept: 'text/xlsx' , required: true,id: :bulk_upload_xl,class: 'form-control'
      = hidden_field_tag :version, version[:name]
      .col-md-3= submit_tag 'Upload',class: 'btn'
    %hr
= form_tag designers_bulk_upload_path, method: :post, multipart: true, class: 'form-horizontal', id: 'home_decor_form' do
  .row
    .form-group
      =label_tag :home_decor, 'Home Decor :', class: 'control-label col-md-1'
      .col-md-2
        =select_tag :home_decor_types, options_for_select(DesignBulk::HOMEDECOR.keys), class: 'form-control'
        %br
        %h5=link_to 'Bed Sheets XLSX format download', DesignBulk::HOMEDECOR['bed-sheets'], id: 'dropbox_link'
      .col-md-6= file_field_tag :file, accept: 'text/xlsx' , required: true,id: :bulk_upload_xl,class: 'form-control'
      = hidden_field_tag :version, :HomeDecor
      .col-md-3= submit_tag 'Upload',class: 'btn'
  %hr
.row
  .col-md-12
    %h6= "You can check your failed designs per batch from dropdown as Product Upload >> Failed Designs or #{link_to 'Here',failed_designs_path(@designer)}".html_safe
.row
  .col-md-12
    %h6= "To increase user experience we have seperated image processing. You can check your image being processed at Product Upload >> Image processing or #{link_to 'Here',failed_images_path(@designer)}".html_safe
