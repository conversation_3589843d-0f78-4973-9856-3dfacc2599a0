- @og = true
- if @seo.present?
  - title_text = @seo.title
  - if @seo.description
    - description @seo.description
  - else
    - description @designer.description
  - if @seo.keyword
    - keywords @seo.keyword
- else
  - designer_name = @designer.name.to_s.titleize
  - title_text = "#{designer_name} Designs Online Store - Shop latest #{designer_name} collections @ Best Price"
  - description "Trending #{designer_name} best selling products online at Mirraw, we offer wide range of #{designer_name} designs at exciting discounted rates."
  - keywords "#{designer_name},#{designer_name} online store"

- if params[:page].present?
  - title_text = "Page #{params[:page]} of #{@designer.name.to_s.upcase}"


- title title_text

- og_title @designer.name
- og_type Mirraw::Application.config.fb_namespace + ":designer"
- og_image @designer.photo.url(:original)
- og_url designer_url(@designer)
- account_risk_warning = @reviews_count_last_90_day > VENDOR_SUSPENSION_METRIC['warning_review_threshold'].to_i && @designer.check_for_banned_warning
- total_pending_products_count = (@total_orders['pending'].to_i + @total_orders['replacement_pending'].to_i)
-unless true || DESIGNER_NOTICE == 'none'
  .row
    .col-md-12.col-sm-12.col-xs-12
      :javascript
        new PNotify({title: 'Notice', text: '#{DESIGNER_MESSAGES["#{DESIGNER_NOTICE}"].tr("\r\n","")}', type: 'error', styling: 'bootstrap3'})
      #fake_review_alert.hide.alert.alert-danger.alert-dismissible.fade.in{:role => "alert"}
        %button.close{"aria-label" => "Close", "data-dismiss" => "alert", :type => "button"}
          %span{"aria-hidden" => "true"} ×
        %h5
          %i.fa.fa-exclamation-circle
          - if DESIGNER_NOTICE == 'fake_reviewers' && @designer.warning_notification.to_i >= 3
            =DESIGNER_MESSAGES["#{DESIGNER_NOTICE}"]
          -else
            =DESIGNER_MESSAGES["#{DESIGNER_NOTICE}"]
.clearfix
.row
  .col-md-7.col-sm-12.col-xs-12
    -if (@designer.inactive_designer_state? || total_pending_products_count > 0)
      .alert.alert-info.alert-dismissible.fade.in{:role => "alert"}
        %button.close{"aria-label" => "Close", "data-dismiss" => "alert", :type => "button"}
          %span{"aria-hidden" => "true"} ×
        %h5
          %i.fa.fa-warning
          -if @designer.inactive_designer_state?
            Your upload access is temporarily disabled. <NAME_EMAIL> to know more.
          -else
            You have
            %strong #{total_pending_products_count}
            orders to be dispatched
            - if @express_delivery_count > 0
              %em{style: 'color:red'}= "including #{@express_delivery_count} express delivery orders"
            !
    - if !@designer.inactive_designer_state?
      - if @warehouse_orders['pending'].to_i > 0
        .alert.alert-info.alert-dismissible.fade.in{:role => "alert"}
          %button.close{"aria-label" => "Close", "data-dismiss" => "alert", :type => "button"}
            %span{"aria-hidden" => "true"} ×
          %h5
            %i.fa.fa-warning
            You have
            %strong #{@warehouse_orders['pending']}
            Warehouse Orders to be dispatched !
      - if @sla_critical_orders_count.to_i > 0
        .alert.alert-warning.alert-dismissible.fade.in{:role => "alert"}
          %button.close{"aria-label" => "Close", "data-dismiss" => "alert", :type => "button"}
            %span{"aria-hidden" => "true"} ×
          %h5
            %i.fa.fa-warning
            You have
            %strong #{@sla_critical_orders_count}
            Orders that will violate service level agreement if not dispatched in #{Designer::SLA_CONFIG[:sla_critical_alert_days]} days !
            %br
            =link_to 'view orders', designer_designer_orders_path(@designer, type_of_filter: 'SLA critical')

    - if has_high_rate?(@designer, rate_type: "cancellation", consequence: "suspension") && @designer.on_hold? && @designer.banned_reason == 'cr_exceeded'
      = render "designers/rate_criteria/cancellation_suspension"
    - elsif has_high_rate?(@designer, rate_type: "cancellation", consequence: "warning") && !has_high_rate?(@designer, rate_type: "late_shipment", consequence: "suspension")
      = render "designers/rate_criteria/cancellation_warning"

    - if has_high_rate?(@designer, rate_type: "late_shipment", consequence: "suspension") && @designer.on_hold? && @designer.banned_reason == 'lsr_exceeded'
      = render "designers/rate_criteria/late_shipment_suspension"
    - elsif has_high_rate?(@designer, rate_type: "late_shipment", consequence: "warning") && !has_high_rate?(@designer, rate_type: "cancellation", consequence: "suspension")
      = render "designers/rate_criteria/late_shipment_warning"

  .col-md-4.col-md-offset-1.col-sm-11.col-xs-11
    - account_status_panel_style = @designer.inactive_designer_state? ? 'border-color: red;border-width: 3px;' : (account_risk_warning ? 'border-color: #8a6d3b;border-width: 3px;' : '')
    .x_panel{style: account_status_panel_style}
      .x_content
        -case @designer.state_machine
        -when 'approved','review'
          .col-md-7
            %h4 Account Status :
          .success-icon.col-md-5
            %h4 Approved
        -when 'banned'
          .col-md-10
            %h4 Account Status : Banned
          .fail-icon.col-md-2
            %i.fa.fa-times.status
        -when 'vacation'
          .col-md-10
            %h4 Account Status : On Vacation
        -when 'on_hold'
          .col-md-10
            %h4 Account Status : On Hold
        -when 'inactive'
          .col-md-5
            %h4 Account Status :
          .warning-icon.col-md-7
            %h4
              Inactive
              &nbsp;
              %b= link_to '[Activate Account ?]', 'javascript:void(0)', class: 'set_inactive_link'
          %br
          %br
          Please note that in inactive mode you will not be receiving any orders until you change the status from panel
        -else
          .col-md-10
            %h4 Account Status : Under Review
          .success-icon.col-md-2
            %i.fa.fa-search.status
    - if (@designer.on_hold? || @designer.banned?) || account_risk_warning
      .alert.alert-dismissible.fade.in{:role => "alert", class: "#{@designer.banned? ? 'alert-danger' : 'alert-warning'}"}
        %button.close{"aria-label" => "Close", "data-dismiss" => "alert", :type => "button"}
          %span{"aria-hidden" => "true"} ×
        %h5
          %i.fa.fa-warning
          - if (@designer.on_hold? || @designer.banned?)
            - case @designer.banned_reason
            - when 'banned'
              Your account has been banned.
            - when 'high_odr'
              = "Your account has been SUSPENDED due to ODR rate greater than accepted levels of #{VENDOR_SUSPENSION_METRIC['max_odr_threshold']}\%. <NAME_EMAIL> for further clarifications. You can check the negative reviews"
              = link_to 'here', designer_feedback_url(@designer.id)
            - when 'on_hold'
              Your account has been put on hold. <NAME_EMAIL> for further clarifications.
              = link_to 'here', designer_feedback_url(@designer.id)
            - when 'inactive'
              Your account has been put on hold due to inactivity for a long time. <NAME_EMAIL> for further clarifications.
              = link_to 'here', designer_feedback_url(@designer.id)
          - else
            = "Your ODR Rate is currently high and may result in suspension if it exceeds #{VENDOR_SUSPENSION_METRIC['max_odr_threshold']}\%. <NAME_EMAIL> for further clarifications."


/ top tiles
.row.tile_count
  .animated.flipInY.order_count_width.col-sm-4.col-xs-4.tile_stats_count
    .left
    .right
      %span.count_top
        %i.fa.fa-folder
        Total Orders
      .count= @total_orders.values.compact.sum - @total_orders['new'].to_i - @total_orders['canceled'].to_i - @total_orders['vendor_canceled'].to_i + @cancel_orders
      %span.count_bottom
        Last #{Designer::NO_MONTHS} months
  .animated.flipInY.order_count_width.col-sm-4.col-xs-4.tile_stats_count
    .left
    =link_to designer_designer_orders_path(@designer, type_of_filter: 'Order Status', options_for_filter: 'dispatched') do
      .right
        %span.count_top
          %i.fa.fa-truck
          Dispatched Orders
        .count.green= @total_orders['dispatched'].to_i
        %span.count_bottom
          Last #{Designer::NO_MONTHS} months
  .animated.flipInY.order_count_width.col-sm-4.col-xs-4.tile_stats_count
    .left
    =link_to designer_designer_orders_path(@designer, type_of_filter: 'Order Status', options_for_filter: 'pending') do
      .right
        %span.count_top
          %i.fa.fa-file-text-o
          Pending Orders
        .count.red= total_pending_products_count
        %span.count_bottom
          Last #{Designer::NO_MONTHS} months
  .animated.flipInY.order_count_width.col-sm-4.col-xs-4.tile_stats_count
    .left
    =link_to designer_designer_orders_path(@designer, type_of_filter: 'Order Status',options_for_filter: 'buyer_returned') do
      .right
        %span.count_top
          %i.fa.fa-mail-reply
          Returned Orders
        .count= @total_orders['buyer_returned'].to_i
        %span.count_bottom
          Last #{Designer::NO_MONTHS} months
  .animated.flipInY.order_count_width.col-sm-4.col-xs-4.tile_stats_count
    .left
    =link_to designer_designer_orders_path(@designer, type_of_filter: 'Order Status',options_for_filter: 'canceled') do
      .right
        %span.count_top
          %i.fa.fa-times
          Canceled Orders
        .count= @cancel_orders.to_i
        %span.count_bottom
          Last #{Designer::NO_MONTHS} months
  .animated.flipInY.order_count_width.col-sm-4.col-xs-4.tile_stats_count
    .left
    =link_to designer_designer_orders_path(@designer, type_of_filter: 'Order Status',options_for_filter: 'critical') do
      .right
        %span.count_top
          %i.fa.fa-exclamation-triangle
          Critical Orders
        .count.red= @total_orders['critical'].to_i
        %span.count_bottom
          Last #{Designer::NO_MONTHS} months
  .animated.flipInY.order_count_width.col-sm-4.col-xs-4.tile_stats_count
    .left
    .right
      %span.count_top
        %i.fa.fa-user
        Orders this month
      - expected_designer_orders = (@designer_orders[1.month.ago.strftime('%Y%m')].to_i / 1.month.ago.end_of_month.day.to_f) * Time.now.day
      - actual_designer_orders = @designer_orders[Time.now.strftime('%Y%m')].to_i
      .count= actual_designer_orders
      - if expected_designer_orders > 0
        - deviation = (((actual_designer_orders - expected_designer_orders) / expected_designer_orders.to_f) * 100).to_i
        %span.count_bottom{"data-toggle" => "tooltip", "title" => "#{deviation.abs} % Avg From Last Month"}
          -if deviation > 0
            %i.green
              %i.fa.fa-sort-asc>
              #{deviation.abs} %
          -else
            %i.red
              %i.fa.fa-sort-desc>
              #{deviation.abs} %
          Avg From last Month
  .animated.flipInY.order_count_width.col-sm-4.col-xs-4.col-lg-1.tile_stats_count
    - oos_bestseller_count = @designer.get_oos_bestseller_designs.length
    .left
    =link_to oos_bestseller_designs_path(@designer) do
      .right
        %span.count_top
          %i.fa.fa-exclamation-triangle
          OOS Bestsellers
          -if oos_bestseller_count > 0
            .count.red
              #{oos_bestseller_count}
          -else
            .count.light-green
              #{oos_bestseller_count}

.clearfix
.row
  .col-lg-12.col-md-12.col-sm-12.col-xs-12
    .x_panel
      .x_title
        %h2 Performance Metrics
        %ul.nav.navbar-right.panel_toolbox
          %li.pull-right
            %a.collapse-link
              %i.fa.fa-chevron-up
        .clearfix
      .x_content
        .dashboard-widget-content
          -all_metrics = @designer.current_metric_values.designer_visible.dashboard.as_json
          -if all_metrics.present?
            -all_metrics.each do |metric|
              .col-sm-12.col-xs-12.tile_stats_count{class: "#{all_metrics.size >= 5 ? 'col-md-55' : 'col-md-3'}"}
                .left_metric
                %h5=link_to metric['name'], designers_account_health_path(@designer), class: 'link'
                .right
                  -if metric['threshold_achieved']
                    -class_type = 'success-icon'
                    -icon_type  = 'fa-check-circle'
                  -else
                    -class_type = 'fail-icon'
                    -icon_type  = 'fa-times-circle'
                  %div{class: class_type}
                    %i.fa.metrics{class: icon_type}
                    .target
                      %h3 #{metric['value']}
                      .days
                        -# show breakdown for odr, visible only to super admins
                        -if metric['metric_definition_id']==13 and current_account.role? :super_admin
                          -metric_numerator = (metric['denominator_count'].to_f*metric['value'].to_f/100).round
                          (#{metric_numerator.round} of #{metric['denominator_count'].round} in last #{metric['duration']})
                        -else
                          (#{metric['duration']})
                        %br
                        - if metric['name'] == "Order Defect Rate"
                          ="Target < #{VENDOR_SUSPENSION_METRIC['min_odr_threshold'].to_f}%"
                        - else
                          Target #{metric['threshold_text']}
          -else
            No recent data available. Please check your
            =link_to 'Historical Data',designers_historical_data_path(@designer,1)

%br
.clearfix
.row
  .col-md-4.col-sm-12.col-xs-12
    .x_panel
      .x_title
        %h2
          %a.collapse-link Announcements
        %ul.nav.navbar-right.panel_toolbox
          %li.pull-right
            %a.collapse-link
              %i.fa.fa-chevron-up
        .clearfix
      .x_content
        .dashboard-widget-content
          #announcement-accordion.accordion{role: 'tablist'}
            - @announcements.each do |announcement|
              - collapse_id = "announcement_collapse_#{announcement.id}"
              .panel
                %a.panel-heading.collapsed{data:{toggle:'collapse',parent: '#announcement-accordion',target: '#' + collapse_id }}
                  .row
                    .panel-title.pull-left= announcement.note.truncate(25)
                    .small.pull-right= [announcement.start_date,announcement.created_at].max.to_date
                .panel-collapse.collapse{id: collapse_id}
                  .panel-body
                    = announcement.note.to_s.html_safe
            -unless @announcements.present?
              %h4 -- No new Alerts --
  .col-md-4.col-sm-12.col-xs-12
    .x_panel
      .x_title
        %h2
          %a.collapse-link Campaign
        %ul.nav.navbar-right.panel_toolbox
          %li.pull-right
            %a.collapse-link
              %i.fa.fa-chevron-up
        .clearfix
      .x_content
        .dashboard-widget-content
          #announcement-accordion.accordion{role: 'tablist'}
            - @seller_campaigns.each do |campaign|
              - collapse_id = "announcement_collapse_#{campaign.id}"
              .panel
                %a.panel-heading.collapsed.campaign-panel{data:{toggle:'collapse',parent: '#announcement-accordion',target: '#' + collapse_id }}
                  .row.campaign-board
                    .panel-title.pull-left= campaign.name
                    = link_to "Learn More", all_campaign_path, class: "btn btn-secondary pull-right"
            -unless @seller_campaigns.present?
              %h4 -- No new Campaign --
  .col-md-4.col-sm-12.col-xs-12.profile-wiget{style: 'display:none;'}
    .x_panel.tile
      .x_title
        %h2= link_to  'Profile Completion',edit_designer_path(@designer)
        %ul.nav.navbar-right.panel_toolbox
          %li.pull-right
            %a.collapse-link
              %i.fa.fa-chevron-up
        .clearfix
      .x_content
        .dashboard-widget-content
          %ul.quick-list
            -completion_percent = 0
            -{'name'=>'Name','street'=>'Address','email'=>'Email Id','phone'=>'Contact no.','photo'=>'Profile Picture','pan_card'=>'Pan card','cst_certificate'=>'CST Certificate','gst_no'=>'GST Number','gst_certificate'=>'GST Certificate'}.each do |attribute,value|
              %li
                = link_to edit_designer_path(@designer,anchor: "designer_#{attribute}", partial_form: false) do
                  -if (@designer.respond_to?(attribute) ? @designer.send(attribute) : [attribute]).present?
                    -completion_percent += 1
                    %i.fa.fa-check-square-o
                  -else
                    %i.fa.fa-square-o
                  =value
          .sidebar-widget
            = link_to edit_designer_path(@designer) do
              %h4 Profile Completion
              %canvas#designer_profile_gauge{:height => "80", :style => "width: 160px; height: 100px;", :width => "150"}
              .goal-wrapper
                %span#gauge-text.gauge-value.pull-left.completion_percent=((completion_percent * 100 )/9.0).round
                %span.gauge-value.pull-left %
                %span#goal-text.goal-value.pull-right 100%


=javascript_include_tag 'gauge/gauge.min','gauge/gauge_demo'
