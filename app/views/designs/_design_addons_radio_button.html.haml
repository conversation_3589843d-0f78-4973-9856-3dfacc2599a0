:javascript
  callbackArr.push(["$('[data-toggle=\"tooltip\"]').tooltip()", "#{asset_path('application_new.js')}"]);

#stitching_radio_ui.select_div.nopadding
  - addon_types.select{|i| ['color', 'length'].exclude?(i.type_of_addon) }.each_with_index do |at, i|
    - is_blouse_not_available = (blouse_available.try(:downcase) == 'no')
    - blouse_availability_check = BLOUSE_AVAILABILITY_CHECK_VENDORS.include?(@design.designer_id)
    %fieldset.radio-stitching-panel
      - if i == 0 && @design.free_stitching_available?
        %legend.free_stitching_legend{ align: 'right'}
          Free Stitching
      - if !blouse_availability_check || at.name != 'Blouse' || @design.designable_type != 'Saree' || !is_blouse_not_available
        .addon-type-heading{data: {addon_type: at.camelcase_name}}
          - addon_type_name = at.name.titleize
          = "#{addon_type_name} Option"
          -# - if Design::STITCHING_ALLOWED_DESIGNABLE_TYPES.include?(@design.designable_type)
          -#   %a.stitching_info{href:"/pages/stitching_information##{at.name.parameterize}_stitching", title: "See How #{addon_type_name} Stitching Works", target:'_blank', data: {toggle:'tooltip', placement: 'right'}} i
      - unstitch = true
      -non_rts_edt = @design.not_rts_shipping_time(@actual_country.try(:downcase))
      -if Design::UNSTITCH_POPUP_ENABLE && ['Blouse','Lehengas','Salwar Kameez'].include?(addon_type_name)
        - at.addon_type_values.each do |atv|
          - unless atv.blank?
            -if ['Standard Stitching','Regular Blouse Stitching'].include?(atv.name)
              -unstitch = atv.effective_price(@design).zero?      
      - at.addon_type_values.each do |atv|
        - unless atv.blank?
          -next if Design::STITCHING_WITH_VARIANT_DESIGNABLE_TYPES.include?(@design.designable_type) && ((!unstitch_variant.present? && atv.description == 'addons_with_variants') || (unstitch_variant.present? && atv.description != 'addons_with_variants'))          
          -next if blouse_availability_check && is_blouse_not_available && @design.designable_type == 'Saree' && ['Custom Blouse Stitching', 'Regular Blouse Stitching', 'Unstitched Blouse'].include?(atv.name)
          -rts = @design.sor_available?
          -unstitched_in_warehouse = @design.size_in_warehouse? if rts

          -if (@design.ready_to_ship? || unstitched_in_warehouse)
            - delivery_date = edt.advance(days: atv.prod_time).try(:strftime,'%d %b %Y')
            - rts_date      = delivery_date
            - ready_to_ship = atv.prod_time == 0 ? 'true' : 'false'
          -else
            - delivery_date = Time.now.advance(days: non_rts_edt + atv.prod_time).in_time_zone.strftime('%d %b %Y')
            - rts_date      = rts ? edt.advance(days: atv.prod_time).try(:strftime,'%d %b %Y') : Time.now.advance(days: non_rts_edt + atv.prod_time).in_time_zone.strftime('%d %b %Y')
            - ready_to_ship = 'false'
          .div.row{style: 'margin-bottom:5px;'}
            - if atv.prod_time == 0 && atv.price == 0 && unstitch
              %input.Mcustom-radio.with-gap.radio_addons{type: 'radio', name: "at-#{at.id}",value: atv.id, checked: 'checked' ,id: "atv_#{atv.id}", class: [('standard-stitch-variant' if unstitch_variant.present? && atv.name.downcase.include?('standard'))], data:{prod_time: atv.prod_time, delivery_date: delivery_date, rts_date: rts_date, ready_to_ship: ready_to_ship, addon_type_value: atv.name.titleize.gsub(' ', '')}}
            - else
              %input.Mcustom-radio.with-gap.radio_addons{type: 'radio', name: "at-#{at.id}",value: atv.id, id: "atv_#{atv.id}", class: [('custom-rd-stitching' if atv.name.downcase.include?('custom')), ('unstitch' if ['Unstitched Blouse','Dress Material Only'].include?(atv.name))  , ('standard' if ['Standard Stitching','Regular Blouse Stitching'].include?(atv.name))] ,checked: ('checked' if ['Standard Stitching','Regular Blouse Stitching','No Petticoat', 'No Fall and Pico'].include?(atv.name) && Design::UNSTITCH_POPUP_ENABLE && !unstitch),data:{prod_time: atv.prod_time, delivery_date: delivery_date, rts_date: rts_date, ready_to_ship: ready_to_ship, variant_id: unstitch_variant.try(:id), old_price: (unstitch_variant.present? ? get_price_in_currency_with_symbol(unstitch_variant.price) : nil),price: (unstitch_variant.present? ? get_price_in_currency_with_symbol(unstitch_variant.effective_price) : nil), addon_type_value: atv.name.titleize.gsub(' ', '')}}
            %label.atv-name{for: "atv_#{atv.id}"}
              .atv-name-value
                = atv.name
                -categories_id = @design.categories.collect(&:id)
                -is_anarkali = (@design.designable_type == 'SalwarKameez' && (ANARKALI_CATEGORY_FOR_STANDARD_ADDON & categories_id).present?)
                -if check_for_message(atv.name,is_anarkali)
                  =link_to 'i','javascript:void(0)',title: render(partial: '/shared/message_for_stitched_unstitched',:locals => {:addon_option_type_name => atv.name,:designable_type => is_anarkali ? 'Salwar-Anarkali' : @design.designable_type}) , data: {toggle: 'tooltip',html: true, placement: 'right',container: 'body'},class: 'info_message'
                -if (value = check_for_label(atv.name.downcase))
                  %span.label.label-danger #{value}
              - if atv.effective_price(@design) == 0
                %span= get_price_in_currency_with_symbol(0)
              - else
                %span.price-s= "#{get_price_in_currency_with_symbol(atv.effective_price(@design))}"
            - if atv.name.downcase.include?('custom') && atv.addon_option_values.blank?
              .custom-stitching-notice{style:'padding-left: 24px;'}
                * Stitching Measurement form will be emailed to you once order is placed.
                -if @design.system_plus_size
                  %br
                  * Sizes are supported upto Max Bust Size #{@design.designable.get_max_bust_size} Inch
            - addon_option_types = atv.addon_option_values.to_a.select{|aov| aov.addon_option_type.published}.group_by(&:addon_option_type_id)
            - if atv.addon_option_values.present?
              .select_div_addons.col-sm-11{class:[('atv_' + atv.addon_option_values.first.addon_type_value_id.to_s+'_option_types atv_option_types'),('atv_' + atv.addon_option_values.first.addon_type_value_id.to_s+'_option_types atv_option_types stitched' if ['Standard Stitching','Regular Blouse Stitching'].include?(atv.name))]  ,style: ('display:none' if atv.name.downcase.include?('petticoat') || !Design::UNSTITCH_POPUP_ENABLE || unstitch)}
                - if availabel_sizes.present? && (atv.name.downcase.include?('standard') || atv.name.downcase.include?('regular'))
                  .size-chart-text 
                    %span.size-text= (@design.designable_type == 'SalwarKameez') ? 'Select Bust Size' : 'Select Size'
                    %span
                      %button.btn-view-size{"data-target" => "#modal-size-chart", "data-toggle" => "modal", type: "button"} Size Chart
                      = render partial: '/designs/design_size_chart', locals: {availabel_sizes: availabel_sizes}
                  .size-chart-div.col-sm-12.nopadding.plus_size_icon
                    .size-chart
                      -rts_sizes = []
                      - availabel_sizes.each do |id,size|
                        -next unless @design.check_if_plus_size_serviceable(size.size)
                        -if rts && @design.size_in_warehouse?(size.size_bucket_id)
                          -size_data = {size_id: id, delivery_date: edt.strftime('%d %b %Y'), ready_to_ship: 'true', prod_time: 0}
                          -rts_sizes.push("size-#{size.size}")
                        -elsif rts && unstitched_in_warehouse
                          -size_data = {size_id: id, delivery_date: edt.advance(days: atv.prod_time).strftime('%d %b %Y'), ready_to_ship: 'false', prod_time: atv.prod_time}
                        -else
                          -size_data = {size_id: id, delivery_date: Time.now.advance(days: non_rts_edt + atv.prod_time).in_time_zone.strftime('%d %b %Y'), ready_to_ship: 'false', prod_time: atv.prod_time}
                        %button.size{id: "size-#{size.size}", data: size_data}= size.size
                      .size_error.col-sm-12 * Please select one size from above
                    .plus_size
                      -if @design['designable_type'].to_s.downcase == 'saree' && atv['description'].to_s == 'addons_saree' && atv['name'].to_s== 'Regular Blouse Stitching'
                        %button.button
                          %a{:href => "#", :onclick => "return false;"} Plus Size
                    = plus_size_info_message("i","plus_size_icon_info",atv.name)
                    -if rts_sizes.present?
                      .show_rts_sizes
                        %input.Mcustom-checkbox.filled-in#show_rts_products{type: 'checkbox', data: {rts_sizes: rts_sizes}}
                        %label.atv-name{for: 'show_rts_products'}
                          Show me Ready To Ship Sizes
                          =link_to 'i', 'javascript:void(0)', title: 'Following checkbox allows you to highlight the sizes which are avilable to be shipped directly with no extra processing time.', data: {toggle: 'tooltip',html: true, placement: 'right',container: 'body'},class: 'info_message'
                  -if @design.designable_type == 'SalwarKameez'
                    %span#salwar_kameez_default
                      *Size that you are selecting is body measurement Bust Size. 2-3 inch loosing will be added further.
                      %br
                      Example: If you select Bust Size: 34, Garment that will be made will have a Bust Size of 36 inches.
                    %span#salwar_kameez_specific{style: 'display:none;'}
                      The garment that will be made will have a bust size of
                      %var
                      to
                      %var
                      inches as 2-3 inches loosing will be provided pertaining to the body measurement you selected.
                  %fieldset.atv_option_type.salwar_standard_atv{style: 'display:none;'}
                    - if addon_option_types.present?
                      - unless atv.addon_option_types.where(published: true).all?{|aot| ['select plus size','select fabric color'].include?(aot.p_name.to_s.downcase)}
                        %legend{style: 'width:auto;'} Select Your Measurement
                      .select_div.col-sm-12
                        - addon_option_types.each do |addon_option_type_id, addon_option_values|
                          -addon_option_type = addon_option_values.first.addon_option_type
                          -addon_option_type_name = addon_option_type.name
                          - next if ["select plus size","select fabric color"].include?(addon_option_type.p_name.to_s.downcase) && ENV['PLUS_SIZE_SHOW'] == 'false'
                          - next if addon_option_type_name == "select blouse size"
                          .form-groups
                            %span.atov-name
                            - if addon_option_type['option_type'].to_s == 'radio'  
                              = plus_size_color_options(addon_option_values,'render_plus_size_color',"aov_atv_#{addon_option_values.first.addon_type_value_id.to_s}")
                              .info_message_for_icon
                                = plus_size_info_message("i","plus_size_icon_info",atv.name)
                            -else  
                              = addon_option_type['p_name']
                              %br
                              %br
                              = generate_drop_down(addon_option_values)
                            -if addon_option_type_name.downcase == 'select your height' && @design.designable_type.downcase == 'salwarkameez'
                              #std-stitching.std_addon_info{title: get_standard_images(@is_anarkali),style: 'display:none', data: {toggle:'tooltip',html: 'true', placement: 'right',container: 'body'}}
                                .glyphicon.glyphicon-info-sign{style: 'top: 3px;float: right;'}
                            .row.atv_error * Please select one option from above
                -elsif (is_anarkali || @design.designable_type != 'SalwarKameez')
                  %fieldset.atv_option_type.salwar_standard_atv
                    -if atv.name.downcase.exclude?('custom') && atv.name != 'Petticoat Stitching'
                      %legend{style: 'width:auto;'} Select Waist Measurements
                    -if atv['name'] == "Custom Blouse Stitching" && @design['designable_type'].to_s.downcase == 'saree' && atv['description'].to_s == 'addons_saree'
                      .custom_select_size.plus_size_blouse_custom
                        %li Select Size
                      .row.plus_size_buttons
                        .small-5.large-5.columns.plus_size_btn_info.plus_size_custom_regular_align
                          %a.plus_size_blouse_custom.plus_size_custom_regular.selected_custom_plus_size{:href => "#", :onclick => "return false;"} Regular (#{PLUS_SIZE['PLUS_SIZE_RANGE']['regular']['min']}-#{PLUS_SIZE['PLUS_SIZE_RANGE']['regular']['max']})
                        .small-7.large-7.columns.plus_size_btn_info.plus_size_icon
                          %a.plus_size_blouse_custom.plus_size_custom.other_custom_plus_size{:href => "#", :onclick => "return false;"} Plus Size (#{PLUS_SIZE['PLUS_SIZE_RANGE']['custom']['min']}-#{PLUS_SIZE['PLUS_SIZE_RANGE']['custom']['max']})
                        = plus_size_info_message("i","plus_size_icon_data",atv.name)
                    - addon_option_types.each do |addon_option_type_id, addon_option_values|
                      -option_type = addon_option_values.first.addon_option_type
                      -next unless option_type.published
                      - next if ["select plus size","select fabric color"].include?(option_type.p_name.to_s.downcase) && ENV['PLUS_SIZE_SHOW'] == 'false'
                      -if option_type.option_type == 'select'
                        .dropdown-div{:data =>{"name": option_type['p_name']}} 
                          =option_type['p_name']
                          %br
                          %br
                          =generate_drop_down(addon_option_values)
                          .row.atv_error * Please select one option from above
                      -if option_type.option_type == 'radio' && option_type.p_name=='Select Fabric Color'
                        .form-group2
                          = plus_size_color_options(addon_option_values,'render_plus_size_color',"aov_atv_#{addon_option_values.first.addon_type_value_id.to_s}")
                          .info_message_for_icon
                            = plus_size_info_message("i","plus_size_icon_info",atv.name)
                          .row.atv_error * Please select one option from above
                      -elsif option_type.option_type == 'radio'
                        - if atv.name.downcase == 'shapewear' && option_type.p_name.to_s.downcase == 'select color'
                          %legend{style: 'width:auto;'} Select Color
                          .shapewear_color{class: "aov_atv_#{addon_option_values.first.addon_type_value_id.to_s}", id: "shapewear_color_parent_div"}
                            = render :partial => '/designs/shapewear_color', locals: {addon_option_values: addon_option_values}
                          .row.atv_error * Please select one option from above    
                      -elsif option_type.option_type == 'checkbox'
                        -addon_option_values.each do |addon_option_value|
                          %input.Mcustom-checkbox.filled-in{type: 'checkbox', value: addon_option_value.id, id: 'atv_' + addon_option_values.first.addon_type_value_id.to_s + '_option_values_' + addon_option_type_id.to_s, class: "aov_atv_#{addon_option_values.first.addon_type_value_id.to_s}"}
                          %label.atv-name{for: "atv_#{addon_option_values.first.addon_type_value_id}_option_values_#{addon_option_type_id}"}
                            %span.atv-name-value{style: 'width: 75%'} #{addon_option_value.p_name} 
                            %span.price-s #{get_price_in_currency_with_symbol(option_type.price)}
                - if atv.name.downcase.include?('custom')
                  -margin_top = is_anarkali || @design.designable_type != 'SalwarKameez' ? 'margin-top: -5px;' : ''
                  .custom-stitching-notice{style: margin_top}
                    * Stitching Measurement form will be emailed to you once order is placed.
                    -if @design.system_plus_size
                      %br
                      * Sizes are supported upto Max Bust Size #{@design.designable.get_max_bust_size} Inch        
                -elsif atv.name.downcase.include?('shapewear') && @design.designable_type == 'Saree'
                  = render partial: '/designs/shapewear_size_chart' 
            -elsif unstitch_variant.present? && @design.variants.present? && 'Standard Stitching' == atv.name
              .select_div_addons.col-sm-11.variant_addons
                - if dynamic_size_chart = @design.dynamic_size_chart
                  .size-chart-text
                    %span.size-text Select Size
                    %span
                      %button.btn-view-size{"data-target" => "#modal-dynamic-size-chart", "data-toggle" => "modal", type: "button"} Size Chart
                      #modal-dynamic-size-chart.modal.fade{role: "dialog"}
                        .modal-dialog
                          .modal-content
                            .modal-header
                              %button.close{"aria-label" => "Close", "data-dismiss" => "modal", type: "button"}
                                %span{"aria-hidden" => "true"} &times;
                              %h4#exampleModalLabel.modal-title
                                Sizing Guide
                            .modal-body
                              = render dynamic_size_chart
                .size-chart-div.col-sm-12.nopadding
                  .size-chart
                    -all_variants = @design.variants.select{|v| v.stitched == true}.to_a.sort_by!{|v| v.option_type_values[0].position}
                    -all_variants.each do |variant|
                      - if variant.quantity.present? && variant.quantity > 0
                        %button.size.variant_stitch{id: variant.id, data: {old_price: get_price_in_currency_with_symbol(variant.price),price: get_price_in_currency_with_symbol(variant.effective_price)}}= variant.option_type_values[0].p_name
                    .size_error.col-sm-12 * Please select one size from above
  - if (length_option = addon_types.select{|i| ['color', 'length'].include?(i.type_of_addon)}).present?
    - length_option.each do |addon|
      %fieldset.radio-stitching-panel
        .addon-type-heading= "Select #{addon.name} Option"
        %ul
          - length_options = addon.addon_type_values.map do |atv|
            - unless atv.price == 0
              - ["#{atv.name} #{get_price_in_currency_with_symbol(atv.effective_price(@design))}", atv.id]
            - else
              -[atv.name, atv.id]
          .nopadding.col-md-8=select_tag :product_length, options_for_select(length_options), prompt: "select #{addon.type_of_addon}", class: 'form-control length_option', required: true


