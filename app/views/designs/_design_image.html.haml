.col-sm-12.col-md-9.col-lg-6.col-xs-12.nopadding.large_device_img_div
  .large_device_img
    .col-sm-2.col-md-2.col-lg-2.thumb_image_div
      #design_gallery
        #gal1.thumb_images
          - video_links = "https://assets0.mirraw.com/product-video/#{@design.id}.mp4" if @design.has_video
          - images = @design.images
          - total_images_and_videos = images.to_a.insert(1,video_links) 
          - total_images_and_videos.each_with_index do |object , index|
            - if object.instance_of?(Image)
              = link_to 'javascript:void(0)', :data => {:image => object.photo.url(:zoom),'image-id'=>object.id,'zoom-image' => object.zoom_display, 'fancybox-group' => 'thumb'}, :class => 'fancybox-thumbs' do
                = image_tag(object.photo.url(:thumb), :alt => @design.title, :id => 'image_thumbnail_' + object.id.to_s)
            - elsif <PERSON>SIGN_VIDEO && object.instance_of?(String)
              #play_video.play_button.fancybox-thumbs{style: "position: relative"}
                = image_tag(@design.images.first.photo.url(:thumb), :alt => @design.title, :id => 'image_thumbnail_' + @design.images.first.id.to_s, class: 'videos',:remote => true)
                .play_button{style: "bottom: 0px"}
                  %span.glyphicon.glyphicon-play
              = render partial: '/designs/product_page_video_modal', locals: {youtube_link: object}               

    .col-sm-10.col-md-9.col-lg-10.large_img_div{style: "padding: 0px;"}
      .large_img
        -if show_wishlist?
          = render partial: 'wishlists/wishlist_forms', locals: {design: @design}
        - if STITCHING_LABEL_DESIGNABLE_TYPE.include?(@design.designable_type) && stitching_label.present?
          .label_for_image_box
            .label_text= stitching_label.titleize
        - elsif @design.designable_type == 'Kurti' && @design.package_details.try(:gsub, /[^\d]/, '').try(:length) == 1
          .label_for_image_box
            .label_text Top Only
        - if @master_image.present?
          = image_tag(@master_image.photo.url(:zoom), :alt => @design.title, :itemprop => "image",:id => 'master', :data => {'zoom-image' => @master_image.zoom_display, :id => @master_image.id})
          %ul
            %li{unbxdattr: "product", unbxdparam_sku: @design.id, unbxdparam_prank: "1"}
          .clr
    .img_text.col-sm-12.col-md-9.col-lg-10 Disclaimer: Slight variation in actual color vs. image is possible due to the screen resolution.
  - if false
    .small_device_img.visible-xs
      .design_images_div{:style =>'margin-top: 5px'}
        .wrapper
          .carousel{'data-mixed' => ''}
            %a.prev{"data-prev" => ""}
            %ul.design_images
              - @design.images.each do |image|
                %li
                  .wrap
                    %figure
                      = link_to image.photo.url(:small), :rel => 'group' do
                        = image_tag(image.photo.url(:small), :alt => @design.title, :'data-src' => image.photo.url(:small))
            %a.next{"data-next" => ""}
            %a.badge
  - if @privileged_user
    .design_counts.col-sm-12.col-md-9.col-lg-10.col-xs-12
      = button_tag 'Make Master', :value => 'Make Master', :class => 'button-medium make-master' if current_account.present? && %w(senior_vendor_team vendor_team admin super_admin).include?(current_account.role.try(:name))
      - if @super_user
        -if grading_access
          = nested_form_for [@designer, @design], :remote => true do |f|
            - if ['inr', 'rs'].include?(@symbol.downcase) || current_account.role.try(:name) == 'senior_vendor_team'
              = f.label :grade, 'Grade:'
              = f.text_field :grade, size: 8
              = f.submit 'save'
              %br
              = f.label :grade_mobile, 'Grade Mobile:'
              = f.text_field :grade_mobile, size: 8
              = f.submit 'save'
            - else
              = f.label :international_grade, 'International Grade:'
              = f.text_field :international_grade, size: 8
              = f.submit 'save'
              %br
              = f.label :international_grade_mobile, 'International Grade Mobile:'
              = f.text_field :international_grade_mobile, size: 8
              = f.submit 'save'
        %br
        -if EXPERT_QC.include?(current_account.try(:email))
          %button#qcbutton.btn.btn-default.btn-sm.button-medium.make-master{"data-target": "#Qcmodel_qc_#{@design.id}", "data-toggle": "modal", type: "button", style: "margin-left: 2%;"}
            ='Expert QC'
          #Qcmodel.modal.fade{id: "qc_#{@design.id}","aria-labelledby": "QcmodelLabel", role: "dialog", tabindex: "-1"}
            .modal-dialog{role: "document" , style: "color: black;"}
              #Qc-model-content.modal-content{style:"margin-left:32% !important; width:545px !important; background: white !important"}
                .modal-header
                  %button.close{"aria-label": "Close", "data-dismiss": "modal", type: "button"}
                    %span{"aria-hidden": "true"} &#215;
                  %h4#QcmodelLabel.modal-title Expert Quality Check
                .modal-body
                  %h4#exampleModalLabel.modal-title
                    Rate the Product
                  %br
                  - for i in 1..5
                    %button#dLabel{"aria-expanded": "false", "aria-haspopup": "true", type: "button", class:"btn-success btn-lg custom_qc_model_button", data: { design_id: @design.id, rating: "#{i}"}}
                      #{i}
                .modal-footer
                  %button#dLabel.btn.btn-danger{"data-dismiss": "modal", type: "button", style: "left:0px !important;", data: { design_id: @design.id, rating: 1}}
                    Reject
.nopadding{:style => 'display:none'}
  - @design.images.each do |image|
    = link_to @design.title, image.zoom_display, :class => 'product_gallery', :id => image.id, :title => @design.title