- can_stitch = STITCHING_ENABLE == 'true' && (addon_values = @design.get_design_addons(@actual_country, @symbol)).to_a.present?
- if @design.in_stock? && !@designer.inactive_designer_state?
  - variants_quantity = @design.variants_quantity
  - design_quantity = @design.quantity
  - if variants_quantity == 1
    - str = 'Only 1 piece left.'
  - elsif variants_quantity == 0 && design_quantity == 1
    - str = 'Only 1 piece left.'
  - else
    - str = ''
  - if RAKHI_PRE_ORDER[0] == 'true' && @design.category_parents_name.include?('rakhi-online')
    .estimated_delivery.col-lg-12.col-md-12.col-sm-12.col-xs-12.floatl.nopadding
      .pre-order
        = "Schedule Delivery"
        .pre-order-date
          = check_box_tag 'pre-order-check', RAKHI_PRE_ORDER[1], false, class: 'accept_tos Mcustom-checkbox filled-in'
          %label{for: 'pre-order-check'}= RAKHI_PRE_ORDER[1]
  - if country_or_currency_indian?
    .row
      %br      
      = hidden_field_tag 'product_id', @design.id
      .effect-1
        %span.fa.fa-map-marker.location_logo{style: 'font-size:16px'}
        %input.pincode_input{placeholder: "Pincode", type: "text", id: 'pin_code', required: true,maxlength: 6, pattern: "[0-9]{6}"}
        %a.cod_pdd_check_button
          %button.get-eta-form-link{style: 'outline:none; border:0px; border: none; background-color:white; font-size:18px;',id: 'check_for_pdd',type: 'button'}check
      %br
  .estimated_delivery.col-lg-12.col-md-12.col-sm-12.col-xs-12.floatl.nopadding
    %span Estimated Delivery :
    -# Ready to ship label is being hidden due to express delivery feature
    - if !country_or_currency_indian? && (@design.sor_available_for_customer? || (variant_available = @design.variants.any?{|v| v.sor_available_for_customer?})) #rts_available_country?
      -non_rts_time = Time.now.advance(days: @design.not_rts_shipping_time(@actual_country.try(:downcase))).in_time_zone.strftime('%d %b %Y').to_s
      -if addon_types.blank? || @design.size_in_warehouse? || variant_available
        - stringify_edt_date = edt.try(:strftime,'%d %b %Y').to_s
        - if non_rts_time != stringify_edt_date  
          %del.strike_old_date= non_rts_time
        .prod_time{:data => {:ready_to_ship => "true"}}
          = stringify_edt_date
      -else
        %del.strike_old_date{style: 'display: none;'}= non_rts_time
        .prod_time= non_rts_time
    - elsif (!country_or_currency_indian? && @design.ready_to_ship?)
      .prod_time{:data => {:ready_to_ship => "true"}}
        = Time.now.advance(:days => @design.delivery_date_for_rts(@actual_country)).in_time_zone.strftime('%d %b %Y').to_s
    - else
      .prod_time=country_or_currency_indian? ? '' : edt.try(:strftime,'%d %b %Y').to_s
  .cod_availability_message

    - if can_stitch
      .notice_class#stitching_delay_message=""

  .col-lg-12.col-md-12.col-sm-11.col-xs-12.button-container.nopadding
    %a{href: "javascript:void(0)", id: @design.id, class: "add_to_cart_link", unbxdattr:"AddToCart", unbxdparam_sku: @design.id}
      -# .buy_this_now_button
      -#   %span.buy_now_cart_image
      -#   %span.buy-this-now-text BUY NOW
      .buy_this_now_button
        %span.buy_now_cart_image
        %span.buy-this-now-text
          BUY
          %span.item-count
          %span NOW
  .col-lg-12.col-md-12.col-sm-11.col-xs-12.nopadding.span.floatl.design_msg= str
  - if @design.designer.name == 'Aapno Rajasthan' && @design.categories.pluck(:name).include?('rakhi-international')
    %br
    .span.floatl.notice_class{style: 'font-size:14px;'}="* Product cannot be shipped to India."
  - if RAKHI_DELAY_MSG != 'false' && @design.category_parents_name.include?('rakhi-online')
    %br
    .span.floatl.notice_class{style: 'font-size:14px;font-style: normal;'}
      = RAKHI_DELAY_MSG.html_safe
  %br 
  - if cod_available_for_country? && !QUICK_COD_DISABLED && (@designer.cod || @design.sor_available? || @design.is_classiques?)     
    .cod_feature.col-lg-12.col-md-12.col-sm-12.col-xs-12.nopadding
      .col-lg-9.nopadding
        #quick-cod-form-link-text
          To place quick COD order
          %button{id: 'quick-cod-form-link',type: 'button', data: {'design_id'=>@design.id}}Click here
  %br

- else    
  .design_quantity.col-lg-12.col-md-12.col-sm-11.col-xs-12.nopadding
    .sold_div
      %h5
        - if @super_user
          = @design.human_state_name
        - else
          Sold out
    - if @design.state != 'delete' and @design.state != 'banned' and !@designer.inactive_designer_state?
      .request_item
        = form_tag('/design/add_to_reqlist', {:method => "post", :remote => true, :id => 'add-to-reqlist'}) do
          = hidden_field_tag 'design_id', @design.id
          .email_div
            = label_tag 'Email:'
            =email_field_tag 'email', nil,:class => 'black_text', required: true
          .request_item_button.tertiary_button
            = submit_tag 'Request This Item'
#product_desc.design_id.col-lg-10.col-md-12.nopadding.text-justify{style: 'line-height: 16px; margin: 10px 0px 0px;'}
-if @design.package_details.present? && (design_key_spec_hash = get_key_spec_data(design_spec_hash)).present?
  .estimated_delivery.col-lg-12.col-md-12.col-sm-12.col-xs-12.nopadding
    .key_specifications
      .text
        Key Specifications :
      %table
        %tr
          %td &bull;
          %td Package details
          %td :
          %td #{@design.package_details.titleize.html_safe}
        - design_key_spec_hash.each do |highlight, values|
          - if values.present?
            - if highlight == 'other_data'
              - values.each do |property,value|
                %tr
                  %td &bull;
                  %td= property.titleize
                  %td :
                  %td= value.titleize
            - else
              %tr
                %td &bull;
                %td= highlight.to_s.titleize
                %td :
                %td= values.map{|k,v| "#{k.titleize}- #{v.titleize}"}.join(' / ')
        - if design_spec_hash[:stitching]
          %tr
            %td &bull;
            %td Stitching Type
            %td :
            %td #{design_spec_hash[:stitching]}
        %tr
          %td &bull;
          %td Product ID
          %td :
          %td #{@design.id}
      .more_specifications_link= link_to '+ More Specifications', '#specs'
- else
  .estimated_delivery.design_id.col-lg-7.col-md-7.col-sm-8.col-xs-7.nopadding{:align => 'left'}= "Product ID : "+ @design.id.to_s
- if (collection = @design.designer_collection).present?
  #collection_block.col-lg-7.col-md-7.col-sm-8.col-xs-7.nopadding{align: 'left'}
    = link_to "View more from #{collection.name.to_s.humanize} collection", designer_collection_path(@designer,collection)
- if can_stitch && addon_values.collect(&:name).exclude?('Rakhi Gifts') && STITCHING_RADIO_BUTTON_UI != 'true'
  .col-lg-12.col-md-12.col-sm-12.col-xs-12.nopadding.design_state_block
    - if Design::STITCHING_ALLOWED_DESIGNABLE_TYPES.include?(@design.designable_type)
      = "Note : For Custom Stitching Measurement form will be emailed to you once order is placed."

.col-lg-10.col-md-10.col-sm-10.col-xs-10.nopadding.design_state_block
  %br
  - check_for_warehouse = (@design.in_stock_warehouse.to_i <= 0) || @super_user
  - if @privileged_user
    - if @design.state == 'reject' or @design.state == 'review'

    - elsif check_for_warehouse
      = button_tag 'Mark Out Stock', :value => 'seller_out_of_stock', :data => {:id => @design.id, :refresh => 1}, :class => 'button-medium state-change'
    = button_tag 'Delete', :value => 'delete', :data => {:id => @design.id, :refresh => 1}, :class => 'button-medium state-change' if check_for_warehouse
    - if @design.variants_available && @design.variants.none?{|v| v.in_stock_warehouse.to_i > 0}
      = button_tag 'Delete size options', :data => {:id => @design.id, :designer_id => @designer.id}, :class => 'button-medium remove-variants'
  - if @super_user
    = button_tag 'Reject', :value => 'reject', :data => {:id => @design.id, :refresh => 1}, :class => 'button-medium state-change'
    = button_tag 'Remove Catalog', :class => 'button-medium remove-int-catalog', data: {link: designer_design_remove_all_catalog_item_path(@designer, @design),type: 'all'} if current_account.present? && %w(super_admin admin).include?(current_account.role.try(:name))
    = button_tag 'Remove From Domestic Catalog', :class => 'button-medium remove-int-catalog', data: {link: designer_design_remove_dom_catalog_item_path(@designer, @design),type: 'domestic'} if current_account.present? && %w(super_admin admin).include?(current_account.role.try(:name))
    = button_tag 'Remove From International Catalog', :class => 'button-medium remove-int-catalog', data: {link: designer_design_remove_int_catalog_item_path(@designer, @design),type: 'international'} if current_account.present? && %w(super_admin admin).include?(current_account.role.try(:name))

  
