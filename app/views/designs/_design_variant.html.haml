- if false #(freesize = design.variant_freesize).present?
  .variant_div.col-lg-10.col-md-10.col-sm-10.col-xs-11.nopadding
    %ul
      %li.variant.bg_red{:id => freesize.id, :class => 'selected'}= freesize.option_type_values[0].p_name
- elsif (design.variants).present?
  .variant_div.col-lg-10.col-md-10.col-sm-10.col-xs-11.nopadding
    -# - variants_options = design.variants.reject {|v| v.option_type_values[0].p_name == FREESIZE}
    - variants_options = design.variants.select{|v| v.stitched == true}.group_by{|v| v.option_types[0].try(:p_name)}
    - class_name = parent_design_variant ? 'variant' : 'variant_modal'
    - rts_date = design.delivery_date_for_rts(@actual_country)
    - variants_options.each do |option_type_name, variants|
      - if variants.present? and option_type_name.present?
        -if (FOOTWEAR_CATEGORY_PARENT_IDS.include?(design.categories.first.parent_id) || FOOTWEAR_CATEGORY_IDS.include?(design.categories.first.id))
          - variant_available_flag = false
          %b.variant_text='Select ' + option_type_name 
          %b.uk-text='(UK Size)'
        -else
          - variant_available_flag = false
          %b.variant_text='Select ' + option_type_name
      -if design.category_ids == [127]
        %select.chosen-select#variantselect
          %option.variant_select --select--
          - variants.sort_by!{|v| v.option_type_values[0].position}
          - change_delivery_date = variants.collect{|v| v.id if v.sor_available?}.uniq.compact if parent_design_variant # && rts_available_country?
          - variants.each do |variant|
            - if variant.quantity.present? && variant.quantity > 0
              - variant_available_flag = true
              -if change_delivery_date.present?
                - if change_delivery_date.include?(variant.id)
                  - delivery_date = Time.now.advance(days: rts_date).strftime('%d %b %Y')
                  - ready_to_ship = 'true'
                - else
                  - delivery_date = edt.strftime('%d %b %Y')
                  - ready_to_ship = 'false'
              %option{id: variant.id, value: variant.id,class: class_name,data: {category_id: design.category_ids, design_id: variant.design_id, old_price: get_price_in_currency_with_symbol(variant.price),price: get_price_in_currency_with_symbol(variant.effective_price), delivery_date: delivery_date, ready_to_ship: ready_to_ship}}= variant.option_type_values[0].p_name
          - unless variant_available_flag
            %li Out of Stock
      -else
        %ul
          - variants.sort_by!{|v| v.option_type_values[0].try(:position).to_i}
          - change_delivery_date = variants.collect{|v| v.id if v.sor_available?}.uniq.compact if parent_design_variant # && rts_available_country?
          - variants.each do |variant|
            - if variant.quantity.present? && variant.quantity > 0
              - variant_available_flag = true
              -if change_delivery_date.present?
                - if change_delivery_date.include?(variant.id)
                  - delivery_date = Time.now.advance(days: rts_date).strftime('%d %b %Y')
                  - ready_to_ship = 'true'
                - else
                  - delivery_date = edt.strftime('%d %b %Y')
                  - ready_to_ship = 'false'
              %li{id: variant.id, value: variant.id,class: class_name,data: {design_id: variant.design_id, old_price: get_price_in_currency_with_symbol(variant.price),price: get_price_in_currency_with_symbol(variant.effective_price), delivery_date: delivery_date, ready_to_ship: ready_to_ship}}= variant.option_type_values[0].try(:p_name).to_s
          - unless variant_available_flag
            %li Out of Stock
    - if parent_design_variant && dynamic_size_chart = design.dynamic_size_chart
      %button#dynamic-size-chart.btn-view-size{"data-target" => "#modal-dynamic-size-chart", "data-toggle" => "modal", type: "button"} Size Chart
      #modal-dynamic-size-chart.modal.fade{role: "dialog"}
        .modal-dialog
          .modal-content
            .modal-header
              %button.close{"aria-label" => "Close", "data-dismiss" => "modal", type: "button"}
                %span{"aria-hidden" => "true"} &times;
              %h4#exampleModalLabel.modal-title
                Sizing Guide
            .modal-body
              = render dynamic_size_chart
