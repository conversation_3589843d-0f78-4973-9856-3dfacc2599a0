- content_for :footer_js do
  = javascript_include_tag 'designs'
- role_name = current_account.role.try(:name)
- is_new_create_action = ['new','create'].include?(action_name)
- designable = @design.designable_type.presence || @designable
- excluded_properties = ['petticoat_color','fabric_of_petticoat','celebrity','petticoat_size','blouse_length'].to_set
.alert.alert-info.col-md-6
  %a.close{'aria-label' => 'close', 'data-dismiss' => 'alert', :href => '#', :title => 'close'} ×
  %strong Info!
  * for mandatory fields

= nested_form_for [@designer, @design], :validate => true, :html => {:multipart => true} do |f|
  =hidden_field_tag :type , params[:type]
  -if @design.errors.any?
    #error_explanation
      %h2= "#{pluralize(@design.errors.count, "error")} prohibited this design from being saved:"
      %ul
        - @design.errors.full_messages.each do |msg|
          %li= msg
  .span-20= render :partial => 'design_notice'
  .row.form-horiziontal
    .col-md-7
      .row.form-group.required
        .col-md-2= f.label :title
        .col-md-8= f.text_field :title,class: 'form-control', required: true
      .row.form-group
        .col-md-2= f.label :specification
        .col-md-8= f.text_area :specification, rows: 4, class: 'form-control'
      - if ALLOWED_VENDOR_FOR_PREPARATION.include?(@designer.id) && !@design.ready_to_ship && !@design.sor_available? && !@design.variants.any?{|v| v.sor_available?} || @design.premium==true
        .row.form-group
          .col-md-2= f.label :eta, "Additional Prep Time"
          .col-md-8
            = f.text_field :eta, class: 'form-control', readonly: current_account.designer? || ['senior_vendor_team', 'super_admin'].exclude?(role_name)
            * (Please enter additional dispatch time, by default 2 days is considered)
      -if @designer.is_transfer_model?
        .row.form-group.required
          .col-md-2= f.label :transfer_price
          .col-md-8= f.text_field :transfer_price, value: @design.transfer_price.to_i,class: 'form-control'
        -if current_account.admin?
          .row.form-group
            .col-md-2= f.label :display_price
            .col-md-8= f.label :price, value: @design[:price]
          .row.form-group
            .col-md-2= f.label :discount_percent, "% discount", required: true
            .col-md-8= f.label :discount_percent, @design[:discount_percent].to_i, required: true
      -else
        .row.form-group.required
          .col-md-2= f.label :price
          .col-md-8= f.text_field :price, value: @design[:price],class: 'form-control', required: true
        .row.form-group.required
          .col-md-2= f.label :discount_percent, "% discount"
          - discount_percent = @design[:discount_percent].to_i
          .col-md-8= f.number_field :discount_percent,value: discount_percent, min: 0, max:95, step:1, class: 'form-control'
      - if @designer.wholesaler?
        .row.form-group
          .col-md-2= f.label :min_wholesale_quantity
          .col-md-8= f.text_field :min_wholesale_quantity,class: 'form-control'
        .row.form-group
          .col-md-2= f.label :min_wholesale_price, 'Wholesale Price per piece'
          .col-md-8= f.text_field :min_wholesale_price,class: 'form-control'
        .row.form-group
          .col-md-2= f.label :retail, 'Available for retail ?'
          .col-md-8= f.check_box :retail, :style => "width:15px; margin:5px; vertical-align:bottom;",class: 'form-control'
      - unless @design.variants && @design.variants.size > 0
        .row.form-group.required
          .col-md-2= f.label :designer_quantity
          .col-md-8= f.text_field :designer_quantity,class: 'form-control', readonly: @designer.is_unicommerce_vendor
      .row.form-group
        .col-md-2= f.label :description
        .col-md-8= f.text_area :description, rows: 4, class: 'form-control'
      .row.form-group.required
        .col-md-2= f.label :package_details, 'Package Details'
        .col-md-8
          = f.text_field :package_details    ,class: 'form-control'
          * (Data should be double colon (::) seperated like 1 saree::1 Blouse::1 Petticoat)
      -unless is_new_create_action
        .row.form-group
          .col-md-2= f.label :tag_list, 'Tag list'
          .col-md-8= f.text_field :tag_list    ,class: 'form-control'
      .row.form-group.required
        .col-md-2= f.label :design_code, 'SKU code'
        .col-md-8= f.text_field :design_code,class: 'form-control'
      .row.form-group.required
        .col-md-2= f.label :gst_rate, 'GST Rate'
        .col-md-8= f.text_field :gst_rate,class: 'form-control'
      .row.form-group.required
        .col-md-2= f.label :hsn_code, 'HSN Code'
        .col-md-8= f.text_field :hsn_code,class: 'form-control'
      - if ENABLE_DESIGNER_DESIGNABLE_EDIT || current_account.admin? || ['processing', 'review', 'reject'].include?(@design.state) || (IMAGE_EDIT_CATEGORY_IDS.split(',').collect(&:to_i) & @design.category_ids).present? || is_new_create_action
        = f.fields_for :images do |i|
          .row.form-group.required
            .col-md-2= i.label :photo, "Image"
            - if @design.new_record? 
              .col-md-8= i.file_field :photo
            - else
              .col-md-8.show_product_image
                = image_tag i.object.photo.url(:small), :style => "float:left"
                = i.check_box :_destroy, :style => "display:none;" ,class: 'form-control'
                -if current_account.admin? && (['super_admin'].include?(role_name) || ACCESSIBLE_EMAIL_ID['image_update_access'].to_a.include?(current_account.email))
                  = link_to "X", "javascript:void(0)", :class => "delete_design_image"
              .show_file_field{style: "display:none"}
                %br
                %br
                %p Image guideline
                %p 1. Image size to be less than 5 MB
                %p 2. Image resolution to be more than 800 x 1100 (w x h)
                %p 3. Image should be in JPG format
                .col-md-6= i.file_field :photo ,class: 'form-control'
                .col-md-2= i.link_to_remove "[-] remove"
        -if @design.images.count < 10
          = f.link_to_add "[+] More Images", :images,id: ('increment_image_count' unless current_account.admin?),data: {count: @design.images.count}
    .col-md-5
      - if (current_account.present? && current_account.admin?) || is_new_create_action
        .row.form-group
          = label_tag "Category (Luxe categories marked as *)"
          %ul#selected-categories            
          %div.search-input-div
            %input.form-control{ placeholder: "Search categories ...", type: "text", id: "search-input" }
            %span.search-input-addon
              %i.glyphicon.glyphicon-search
          %div.dropdown-menu-categories{id: "design_category_ids"}
            - f.object.get_valid_categories.each do |category|
              %div.category-item
                = check_box_tag "design[category_ids][]", category.id, @design.category_ids.include?(category.id), {id: "category_#{category.id}" }
                = label_tag "category_#{category.id}", category.lineage
      -# / - elsif @design.available_category_ids?
      -# /   = f.select :category_ids, @designer.categories.where(id: @design.available_category_ids).preload(:parent).leaves.collect{|c| [c.lineage, c.id] }, { :selected => @design.category_ids }, { :multiple => false, :name => 'design[category_ids][]', :size => "20", :class => "required form-control", "data-placeholder" => "Start typing categories"}
      -# / - else
      -# /   = f.select :category_ids, @designer.categories.preload(:parent).leaves.collect{|c| [c.lineage, c.id] }, { :selected => @design.category_ids }, { :multiple => false, :name => 'design[category_ids][]', :size => "20", :class => "required form-control", "data-placeholder" => "Start typing categories"}

    .row.form-group.required
      .col-md-5
        - if current_account.admin? && @design.variants.blank? && @design.is_stitched? && @option_types.present? #(params[:action] == 'update' && @design.variants.blank?)
          - @design.variants.build(design_id: @design.id, price: @design[:price])
          %br
          = button_tag type: 'button', value: 'add-new-variants', class: 'btn btn-primary add-new-variants' do
            %i.fa.fa-plus &nbsp;&nbsp;Apply Size Options
          .span-11.last#variants-panel= render :partial => 'designs/mini_form_variants', :locals => {:form => f}
        - elsif @design.variants.size > 0 || params[:action] == 'update'
          .span-11.last= render :partial => 'designs/form_variants', :locals => {:form => f}
        - else
          #variants-panel
        - if @design.variants_available && @design.variants.none?{|v| v.in_stock_warehouse.to_i > 0} && params[:action] != 'update'
          = button_tag 'Delete size options', :type => 'button', :value => 'delete-variants', :data => {:id => @design.id, :designer_id => @design.designer.id}, :class => 'button-medium btn btn-danger remove-variants'

    -if role_name == 'listing' || (@design.reject? && current_account.designer?) || is_new_create_action
      -existing_properties = []
      .col-md-7
        -@property_values.each do |pv|
          -if (property = pv.property).present?
            - property_bulk_upload_name = property.bulk_upload_name.gsub(' ','_')
            - existing_properties.push(property_bulk_upload_name.to_sym)
            .row.form-group
              .col-md-2= f.label property.name.to_sym, property.name.titleize
              .col-md-8= f.select :property_value_ids, options_for_select(@all_property_value_list[property_bulk_upload_name].to_a.sort, [pv.bulk_upload_name, pv.id]), {prompt: "-- Select #{property.name} --"}, class: "form-control #{'stitched' if property.name == 'stitching'}", name: 'design[property_value_ids][]'
        -designable = @design.designable_type.presence || @designable
        -if designable.present? && (design_fields = BulkUpload.meta_data[designable.to_sym]).present?
          - remaining_property_bulk_upload_name = (design_fields[:designs_property_values] - existing_properties).collect(&:to_s)
          - @all_property_value_list.slice(*remaining_property_bulk_upload_name).each do |pv_name, options|
            -next if excluded_properties.include?(pv_name)
            .row.form-group{class: "#{'required' if @design.get_property_names.try(:keys).to_a.include?(pv_name)}"}
              .col-md-2= f.label pv_name, pv_name.to_s.titleize
              -selected_id = (options.to_a.map{|i| i[1]} & params[:design].try(:[],:property_value_ids).to_a.map(&:to_i)).presence || nil
              .col-md-8=select_tag :property_value_ids, options_for_select(options.to_a.sort, selected_id),prompt: "-- Select #{pv_name} --", class: "form-control #{'stitched' if pv_name.try(:downcase).to_s == 'stitching'}", name: 'design[property_value_ids][]'
        -if (designable = @design.designable).present? && is_new_create_action
          - attributes = designable.attribute_names.map(&:to_sym)-[:id, :created_at, :updated_at]
          = f.fields_for :designable, designable do |ds|
            -(attributes - DUPLICATE_DESIGNABLE[designable.class.to_s].try(:keys).to_a.map(&:to_sym)).each do |attr|
              -next if excluded_properties.include?(attr.to_s)
              .row.form-group
                .col-md-2= ds.label attr
                - if (designable_values = designable.send("#{attr}_values")).present?
                  .col-md-8= ds.select attr,options_for_select(designable_values, designable.send(attr)),{prompt: "-- Select #{attr} --" }, class: 'form-control'
                - else
                  .col-md-8= ds.text_field attr ,class: 'form-control'
    .span-11.last
      - if @master_addons.present?
        - @master_addons.each do |addon_type_name, addon_type_values|
          %fieldset{:style => "border:1px solid #dedede;display:none;width:400px;padding:0px", :class => "#{addon_type_values[0][:category_name]} addon"}
            %table{:align => 'center'}
              %legend{:style => 'text-align:center'}=addon_type_name
              - if addon_type_values.present?
                - addon_type_values.each do |addon_type_value|
                  %tr{:bgcolor => '#181818'}
                    %td
                      = check_box_tag "addon[#{addon_type_value[:atv_id]}][apply]", 't', addon_type_value[:published]
                    %td{:style => 'width:200px;'}
                      = "#{addon_type_value[:atv_name]}"
                      = hidden_field_tag "addon[#{addon_type_value[:atv_id]}][at_id]", "#{addon_type_value[:at_id]}"
                      = hidden_field_tag "addon[#{addon_type_value[:atv_id]}][name]", "#{addon_type_value[:atv_name]}"
                      = hidden_field_tag "addon[#{addon_type_value[:atv_id]}][position]", "#{addon_type_value[:atv_position]}"
                      = hidden_field_tag "addon[#{addon_type_value[:atv_id]}][atv_id]", "#{addon_type_value[:atv_id]}"
                      = hidden_field_tag "addon[#{addon_type_value[:atv_id]}][prod_time]", "#{addon_type_value[:prod_time]}"
                      - if addon_type_value[:state] == true
                        = hidden_field_tag "addon[#{addon_type_value[:atv_id]}][exist]", 't'
                      - else
                        = hidden_field_tag "addon[#{addon_type_value[:atv_id]}][exist]", 'f'
                    %td Price
                    %td
                      = text_field_tag "addon[#{addon_type_value[:atv_id]}][price]", "#{addon_type_value[:price]}", size: 6
  .row.form-group.required
    .button-large.create-design-button
      .actions
        = f.submit 'Save Item', class: 'btn'
    OR
    = link_to 'Back to profile', designer_path(@designer), :class => "small", :style => "color:#0192B5"   
    .progress{style: "display:none; color:yellow;"}
      =image_tag('progress.gif')
      %h2{style: "display:inline"}  Saving design...
