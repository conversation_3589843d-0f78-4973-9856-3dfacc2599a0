- if @hide_menu.blank?
  %ul.menu.menu_specific
    .inner-menu
      - render :partial => '/layouts/static_menu'
      = raw @static_menu      
      %li.redhover#facebook_auth_url.last.left-border{:style => "height:100%"}
        - if !account_signed_in?
          - if (params[:controller] == 'pages' && params[:action] == 'sell')                                                    
            = link_to 'Designer Login', sellers_sign_in_path, :style => "background-color:#4c4c4c;"
          - else
            = link_to account_omniauth_authorize_path(:facebook), :style => "margin:auto;height:100%;" do
              = image_tag('flogin.jpg', :style => 'padding-top:15px;background:none;')   

        - else
          - if current_account.designer?
            %a.drop{:href => designer_url(current_account.designer)}= truncate(current_account.designer.name, :length => 15) 
          - elsif current_account.user? && current_account.user.name.present?
            %a.drop{:href => user_path(current_account.user)}= truncate(current_account.user.name, :length => 15)                    
          - else
            %a.drop{:href => '#'} My Account
          .dropdown_1column.align_right
            .col_1
              %ul.simple
                - if current_account.designer?
                  %li= link_to 'View Orders', designer_designer_orders_path(current_account.designer)
                  %li= link_to 'Manage Boutique', manage_boutique_path(current_account.designer, :sort => 'published')
                  %li= link_to 'OOS Bestseller Designs', oos_bestseller_designs_path(current_account.designer)
                  %li= link_to 'View Payments', designers_payments_path(current_account.designer)
                  %li= link_to 'Help Center', pages_faq_path
                  %li= link_to 'Link FB Page', manage_pages_path(current_account.designer)
                  %li= link_to 'Schedule FB Albums', designer_fb_publish_new_path(current_account.designer)
                  %li= link_to 'Additional Discount', additional_discount_path(current_account.designer)
                  %li= link_to 'Create Coupons', new_designer_coupon_path(current_account.designer)
                  %li= link_to 'Edit Profile', edit_designer_path(current_account.designer)
                  %li= link_to 'My Boutique', designer_url(current_account.designer)
                - if current_account.user?
                  %li= link_to 'My Board', user_path(current_account.user)
                  %li= link_to 'My Addresses', addresses_path
                  %li= link_to 'Invite Friends', invite_path
                  %li= link_to 'This / That', plays_tot_url 
                  %li= link_to 'Style Quiz', plays_quiz_url 

                - if current_account.admin?
                  %li= link_to 'Inventory by order', admin_inventory_by_order_url(:by_order => 1)
                  %li= link_to 'Inventory by designer', admin_inventory_by_order_url
                  %li= link_to 'View Orders', orders_path
                  %li= link_to 'View Coupons', coupons_path
                  %li= link_to 'Publish on FB', admin_fb_publish_new_path
                  %li= link_to 'Manage FB Pages', admin_manage_pages_path
                  %li= link_to 'Add FB Pages', account_omniauth_authorize_path(:facebook, :scope => "manage_pages,publish_pages")
                  %li= link_to 'Manage Stock', admin_availability_update_new_path
                %li
                  = link_to 'sign out', destroy_account_session_path, :method => :delete
