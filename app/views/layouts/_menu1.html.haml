- if @hide_menu.blank?
  %nav#megamenu-m.hidden-xs
    / BEGIN MENU WRAPPER
    .container-fluid
      / BEGIN MENU CONTAINER
      %ul.menu-wrapper
        = render :partial => '/layouts/static_menu'
        - if account_signed_in? && current_account.admin?
          %li#currency-menu.menu-list
            = render :partial => '/layouts/currency_menu'

- if params[:controller] == 'designs' && params[:action] == "show"

- else
  .pagecontentclear
