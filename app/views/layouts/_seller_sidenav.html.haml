- @og = true
- if @seo.present?
  - title_text = @seo.title
  - if @seo.description
    - description @seo.description
  - else
    - description @designer.description
  - if @seo.keyword
    - keywords @seo.keyword
- else
  - title_text = "#{@designer.name.to_s.upcase} Online Boutique - Official"
  - description @designer.description
  - keywords @designer.name.to_s.camelize + ',' + @designer.name.to_s.camelize + " online"

- if params[:page].present?
  - title_text = "Page #{params[:page]} of #{@designer.name.to_s.upcase}"

- title title_text
- og_title @designer.name
- og_type Mirraw::Application.config.fb_namespace + ":designer"
- og_image @designer.photo.url(:small)
- og_url designer_url(@designer)

.col-md-3.left_col
  .left_col.scroll-view
    .navbar.nav_title{style: "border: 0;"}
      %a.site_title{href: designer_path(@designer)}
        %span
          #header_logo
    .clearfix
    / menu profile quick info
    .profile
      .profile_pic
        =link_to designer_path(@designer) do
          -if @designer.photo?
            =image_tag(@designer.photo.url(:small), alt: @designer.name.to_s, class: 'img-circle profile_img')
          -else
            .img-circle.profile_img.text-center.no-image=@designer.name.to_s[0]
      .profile_info
        %span Welcome,
        %h2
          =link_to @designer.name.to_s, designer_path(@designer), style: 'color: white;'
          -if current_account.admin?
            %br
            ID - #{@designer.id}
          =link_to designer_feedback_path(@designer) do
            #rating_div
              %br
              #stars-existing.starrr{"data-rating" => "#{@designer.average_rating.to_i}"}
              %span#count-existing #{@designer.average_rating.to_f} | 5.0
    / /menu profile quick info
    %br/
    / sidebar menu
    #sidebar-menu.main_menu_side.hidden-print.main_menu
      .menu_section
        %h6{style: 'margin-bottom: -18px;'} &nbsp;
        %ul.nav.side-menu
          %li
            %a
              %i.fa.fa-line-chart
              Performance
              %span.fa.fa-chevron-down
            %ul.nav.child_menu{:style => "display: none"}
              %li=link_to 'Account Health', designers_account_health_path(@designer)
              %li=link_to 'Customer Feedback', designer_feedback_path(@designer)
              %li=link_to 'Performance Over Time', designers_historical_data_path(@designer)
          %li
            %a
              %i.fa.fa-home
              Orders
              %span.fa.fa-chevron-down
            %ul.nav.child_menu{:style => "display: none"}
              %li=link_to 'Manage Orders', designer_designer_orders_path(@designer)
              -if @designer.bulk_dispatch
                %li=link_to 'Dispatch Bulk Orders', designer_bulk_dispatch_path(@designer)
              -if !@designer.inactive_designer_state?
                %li=link_to 'Check Claim Request',claim_page_path(@designer)
              %li=link_to 'International Orders', designers_export_orders_path(@designer)
              %li=link_to 'Manage Warehouse Orders', designer_warehouse_orders_path(@designer)
              %li=link_to 'Manage Pickups', designer_pickup_path(@designer)
              %li=link_to 'Prepare Manifest', designer_prepare_manifest_path(@designer)
          %li
            %a
              %i.fa.fa-edit
              Inventory
              %span.fa.fa-chevron-down
            %ul.nav.child_menu{:style => "display: none"}
              %li= link_to 'Manage Inventory', manage_boutique_path(@designer, :sort => 'published')
              %li= link_to 'Bestsellers Out of Stock', oos_bestseller_designs_path(@designer)
              %li= link_to 'Published - download', download_csv_path(@designer, :state => 'published'), class: 'delayed_file_link'
              %li= link_to 'Unpublished - download', download_csv_path(@designer, :state => 'unpublished'), class: 'delayed_file_link'
              / %li= link_to 'Collections', designer_collections_path(@designer)
              -if defined? RailsAdmin
                %li=link_to 'View Boutique',designer_boutique_path(@designer)
          %li
            %a
              %i.fa.fa-upload
              Product Upload
              %span.fa.fa-chevron-down
            %ul.nav.child_menu{:style => "display: none"}
              -if @designer.upload_panel? && !(@designer.review_pending? || @designer.inactive_designer_state?)
                %li= link_to 'Add a product', 'javascript:void(0)',data: {toggle: 'modal',target: '#new_design'}
                -unless ENV['MAINTAINANCE_ON'].to_i == 2
                  %li= link_to 'Bulk Product Upload', designers_bulk_upload_path(@designer)
                %li= link_to 'Listings Report', failed_designs_path(@designer)
                / %li= link_to 'Image Processing', failed_images_path(@designer)
                - unless ['designer'].include?(get_current_role)
                  %li= link_to 'Addon Product', designer_addons_path(@designer)
              %li= link_to 'Upload Size Chart', dynamic_size_charts_path
          %li
            %a
              %i.fa.fa-comments
              Promote
              %span.fa.fa-chevron-down
            %ul.nav.child_menu{:style => "display: none"}
              -#%li= link_to 'Ads',ads_designer_path(@designer)
              -#%li= link_to 'Install Facebook App', 'https://www.facebook.com/dialog/pagetab?app_id=***************&redirect_uri=http://mirraw-facebook.herokuapp.com/', :target => '_blank'
              -#%li= link_to 'Manage Facebook Page', account_omniauth_authorize_path(:facebook, :scope => "manage_pages,publish_pages")
              -#%li= link_to 'Schedule FB Album', designer_fb_publish_new_path(@designer)
              -unless @designer.is_transfer_model?
                %li= link_to 'Additional Discounts',additional_discount_path(@designer)
              %li= link_to 'Create Coupons', new_designer_coupon_path(@designer)
              %li= link_to 'Campaign', all_campaign_path(@designer)
          %li
            %a
              %i.fa.fa-rupee
              Payments
              %span.fa.fa-chevron-down
            %ul.nav.child_menu{:style => "display: none"}
              %li= link_to 'B2C Invoice & Report', payout_invoices_path(@designer)
              %li= link_to 'B2C Payments Reports', payout_invoices_path(@designer, payout: true)
              %li= link_to 'B2B Invoice & Report', payout_invoices_path(@designer,purchase: true)
              %li= link_to 'B2B Payments Reports', payout_invoices_path(@designer,purchase: true, payout: true)
              %li= link_to 'Upload Invoices', designer_upload_invoices_path(@designer)
              %li=link_to  'Payments Summary',designers_payments_path(@designer)

          %li
            %a
              %i.fa.fa-table
              Reports
              %span.fa.fa-chevron-down
            %ul.nav.child_menu{:style => "display: none"}
              %li= link_to 'Review Report', review_report_path(@designer)
              - #Temp Disable this page to query issue
              - #%li= link_to 'Order Quality Report', designers_order_quality_report_path(@designer)
              %li= link_to 'My Report', admin_designer_report_path(@designer)
          %li
            %a
              %i.fa.fa-ticket
              Ticket
              %span.fa.fa-chevron-down
            %ul.nav.child_menu{:style => "display: none"}
              %li= link_to 'Create Ticket' , create_ticket_path(@designer.cached_slug || @designer.id)
          %li
            %a
              %i.fa.fa-envelope
              Contact Us
              %span.fa.fa-chevron-down
            %ul.nav.child_menu{:style => "display: none"}
              %li= link_to 'Seller Support Team' , mirraw_contact_details_path(@designer.cached_slug || @designer.id)
              %li= link_to 'Operations Team', mirraw_contact_details_path(@designer.cached_slug || @designer.id)
              %li= link_to 'Listings Team' , mirraw_contact_details_path(@designer.cached_slug || @designer.id)
    / /sidebar menu
    / /menu footer buttons
    .sidebar-footer.hidden-small
      = link_to edit_designer_path(@designer), "data-toggle" => "tooltip", :title => "Profile" do
        %span.glyphicon.glyphicon-user{"aria-hidden" => "true"}
      = link_to designer_messages_path(@designer), "data-placement" => "top", "data-toggle" => "tooltip", :title => "Messages" do
        %span.glyphicon.glyphicon-envelope{"aria-hidden" => "true"}
      = link_to designers_help_center_path, "data-toggle" => "tooltip", :title => "Help" do
        %span.glyphicon.glyphicon-info-sign{"aria-hidden" => "true"}
      = link_to destroy_account_session_path(designer: true), :method => :delete, "data-toggle" => "tooltip", :title => "Logout" do
        %span.glyphicon.glyphicon-off{"aria-hidden" => "true"}
    / /menu footer buttons
#new_design.modal.fade{:role => "dialog"}
  .modal-dialog
    .modal-content
      .modal-header
        %button.close{"data-dismiss" => "modal", :type => "button"} &#215;
        %h4.modal-title Select Design Type
      .modal-body
        =form_tag new_designer_design_path(@designer), method: :get, class: '' do
          .form-group.row
            .col-md-8=select_tag 'type', options_for_select(MANUL_LIST_DESIGNABLE.map{|i| [i.titleize,i.underscore]}),class: 'form-control'
            .col-md-4=submit_tag 'New Design',class: 'btn btn-primary btn-block', style: 'margin-bottom: 0px;'
