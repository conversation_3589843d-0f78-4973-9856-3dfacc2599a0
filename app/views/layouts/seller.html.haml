!!!
%html{:xmlns => "//www.w3.org/1999/xhtml", "xml:lang" => "en", :lang => "en", "xmlns:fb" => "//ogp.me/ns/fb#"}
  %head
    %title= content_for?(:title) ? content_for(:title) : 'Sell Online In India - Mirraw.com'
    - keywords = content_for?(:keywords) ? content_for(:keywords) : 'Mirraw, Mirraw online shopping'
    - description =  content_for?(:description) ? content_for(:description) : "Find wide range of fashion jewellery, imitation, bridal, artificial, beaded and antique jewellery online. Buy Designer Sarees & Bags. Buy imitation jewellery online from designers across India. Call us on #{MIRRAW_CONTACT_INFO} now to resolve your queries."
    %meta{:content => "text/html; charset=utf-8", "http-equiv" => "Content-Type"}
    %meta{content: "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0", name: "viewport"}
    %meta{:name => 'keywords', :content => keywords }
    %meta{:name => 'description', :content => description }
    %meta{:name => 'google-site-verification', :content => 'agapK5s18B0nYsx87rslHH_kTY2Uxa_WuGeZ8uGtxas'}
    %meta{:content => "ae162c57511278e951259fb168ee2257", :name => "p:domain_verify"}
    %meta{:content => "2a662054611db26020810ba3279e4d02", :name => "p:domain_verify"}
    %meta{:content => "C73FA91FB27EB19AF53A38C69211F328", :name => "msvalidate.01"}
    %meta{content: "NOINDEX,NOFOLLOW", name: "robots"}

    = favicon_link_tag '/apple-touch-icon-144x144.png', rel: 'apple-touch-icon', type: 'image/png', sizes: '144x144'
    = favicon_link_tag '/apple-touch-icon-114x114.png', rel: 'apple-touch-icon', type: 'image/png', sizes: '114x114'
    = favicon_link_tag '/apple-touch-icon-72x72.png', rel: 'apple-touch-icon', type: 'image/png', sizes: '72x72'
    = favicon_link_tag '/apple-touch-icon.png', rel: 'apple-touch-icon', type: 'image/png'
    = include_gon
    =stylesheet_link_tag    'seller'#,'seller_style'#, media: 'all', 'data-turbolinks-track' => true
    / = render "layouts/ga"
    = csrf_meta_tags

  %body.nav-md
    =javascript_include_tag 'seller'
    .container.body
      -# %span{style: 'color: white'} #{current_account.accountabl}
      -if account_signed_in?
        .main_container
          - if current_account.accountable_type == "Affiliate"
            = render "affiliate/sidenav"
            = render "affiliate/topnav"
          - else
            = render "layouts/seller_sidenav"
            = render "layouts/seller_topnav"
          .right_col{role: "main"}
            %script
              - flash.each do |type, message|
              - type = (type == 'notice' ? 'success' : 'error')
                new PNotify({text: "#{message}", type: '#{type}', styling: 'bootstrap3'});
            .row
              .page-title
                .title_left
                  %h4
                    = content_for?(:page_title) ? content_for(:page_title) : ''
              .clearfix
            %br
            = yield
          = render "layouts/seller_footer"
      -else
        .main_container
          - if account_signed_in? && current_account.accountable_type == "Affiliate"
            = render "affiliates/topnav"
          - else
            = render "layouts/seller_topnav"
          = yield
          = render "layouts/seller_footer"
  =javascript_include_tag 'seller_style'
  = yield :footer_js
