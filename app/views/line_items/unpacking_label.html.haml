- barcode_text = "Unpack-#{line_item_id}"
- barcode_img = Shipment.generate_barcode(barcode_text)
- barcode_width, barcode_text_size = (from_order_page == 'true' ? [85, 'font-size:30pt'] : [55, ''])
- font_size_for_unpack = (from_order_page == 'true' ? 'font-size: 30px' : '')
- item =LineItem.find line_item_id
- designer_order = item.designer_order
- item_quantity = designer_order.replacement_pending? ? item.rtv_quantity : item.quantity
.row
  .col-md-4.col-md-4{style: 'border: 2px solid black;padding: 20px;'}
    %center
      %div
        %table{width: "100%"}
          %tr
            %td{style: "font-size:20pt;"}
              %span{style: barcode_text_size}= "#{designable_type}"
      .barcode{style: "width: #{barcode_width}%;text-align:center;font-size:15pt;"}
        %b{style: barcode_text_size}="#{order_number} - #{design_id}"
        %img.bar_code{src: barcode_img, width: '100%'}
        %b{style: "font-size: #{barcode_text_size}",style: "#{font_size_for_unpack}"}= barcode_text
        %br
        - if (dns = order.delivery_nps_info).present? && dns.promised_delivery_date.present?
          %b{style: "#{font_size_for_unpack}"} EDD: #{dns.promised_delivery_date.to_date}
        %br
        - if !from_order_page.present?
          %b SKU: #{item.design.design_code}
        %br
        - if item.note.present? && !from_order_page.present?
          %b{style: 'margin: auto;'} Size: #{item.note}
        %br
        -if item_quantity.present?
          %b Quantity: #{item_quantity}
