= stylesheet_link_tag 'mirraw_admin'
.container.text-center
  %h3 For Design
  = form_tag update_design_discount_mirraw_admin_design_index_path, method: 'post', multipart: true do
    .form-group
      = label_tag :csv_file, 'Upload CSV File'
      %br
      %br
      = file_field_tag :csv_file, accept: 'text/csv', required: true, class: 'csv_upload'
    .form-group
      %p
        Not sure how to structure your CSV? Download our demo CSV to see an example file.
        = link_to "Download Demo CSV", 'https://ticket-images-new.s3.amazonaws.com/design_update-02-12-2024-ashwini.bavdane%40mirraw.com-designs3.csv', class: "demo-link"
    = submit_tag "Upload", class: "btn btn-primary"
  %br
  %br
  %br
  %h3 For Child
  = form_tag update_variant_price_mirraw_admin_design_index_path, method: 'post', multipart: true do
    .form-group
      = label_tag :csv_file, 'Upload CSV File'
      %br
      %br
      = file_field_tag :csv_file, accept: 'text/csv', required: true, class: 'csv_upload'
    .form-group
      %p
        Not sure how to structure your CSV? Download our demo CSV to see an example file.
        = link_to "Download Demo CSV", 'https://ticket-images-new.s3.amazonaws.com/variant_update-02-21-2024-ashwini.bavdane%40mirraw.com-variant.csv', class: "demo-link"
    = submit_tag "Upload", class: "btn btn-primary"
