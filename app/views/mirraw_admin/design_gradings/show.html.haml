= stylesheet_link_tag 'admin_style'

%h4.grading-tag-title.text-center.font-weight-bold.mb-4
  = "DESIGN ID'S & RANKS FOR GRADING TAG"

#grading_tag_details_panel
  %table.table.table-bordered
    %thead.thead-light
      %tr
        %th Grading Tag Name
        %th Country Code
        %th Platform
        %th Details
    %tbody
      %tr
        %td= @grading_tag.name
        %td= @grading_tag.country_code
        %td= @grading_tag.platform
        %td= "#{@grading_tag.grading_taggable_type} - #{@grading_tag.grading_taggable_id}"

%br
#design_grading_search
  = form_tag mirraw_admin_design_grading_path(@grading_tag), method: :get, class: 'form-inline' do
    .form-group
      = text_field_tag :design_id, params[:design_id], placeholder: 'Search by Design ID', class: 'form-control mr-2 search-field', aria: { label: "Search by Design ID" }
    .form-group
      = submit_tag 'Search', class: 'btn btn-primary'
    .form-group
    = link_to 'Download CSV', export_csv_mirraw_admin_design_grading_path(@grading_tag), class: 'btn btn-primary mb-4'
#design_grading_details
  %table.table.table-bordered.mt-4
    %thead.thead-light
      %tr
        %th ID
        %th Design ID
        %th Rank
        %th Master Image
    %tbody
      - @design_gradings.each do |grading|
        %tr
          %td= grading.id
          %td= grading.design_id
          %td= grading.rank
          %td
            - if grading.design.present? && grading.design.master_image.present? && grading.design.master_image.photo.present?
              = image_tag grading.design.master_image.photo.url, alt: "Master Image", class: "img-thumbnail", width: 100
            - else
              = "No image available"

#pagination
%div.d-flex.justify-content-center.mt-4
  = will_paginate @design_gradings, previous_label: "<i class='fas fa-chevron-left'></i> Previous", next_label: "Next <i class='fas fa-chevron-right'></i>", class: "pagination pagination-sm"

= link_to 'Back to Grading Tag Panel', mirraw_admin_design_gradings_path, class: 'btn btn-primary mb-4'
