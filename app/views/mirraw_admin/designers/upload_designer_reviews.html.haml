= stylesheet_link_tag 'mirraw_admin'
.container.text-center
  %h3 Designer Reviews Management
  %p Manage review visibility for specific designers' products
  
  = form_tag update_designer_reviews_mirraw_admin_designers_path, method: 'post' do
    .form-group
      = label_tag :designer_ids, 'Designer IDs (comma-separated)'
      %br
      %br
      = text_area_tag :designer_ids, nil, 
        placeholder: 'Enter comma separated designer IDs (e.g. 1,2,3,4)', 
        class: 'form-control', 
        rows: 4,
        required: true,
        style: 'color: black; width: 100%; max-width: 500px; margin: 0 auto;'
    
    .form-group
      %br
      = label_tag nil, 'Enable Reviews:'
      %br
      %br
      .radio-group{style: 'display: inline-block; text-align: left;'}
        .radio-option{style: 'margin-bottom: 10px;'}
          = radio_button_tag :enable_reviews, 'true', false, id: 'enable-reviews-true', required: true
          = label_tag 'enable-reviews-true', 'Enable Reviews', style: 'margin-left: 5px; font-weight: normal;'
        .radio-option{style: 'margin-bottom: 10px;'}
          = radio_button_tag :enable_reviews, 'false', false, id: 'enable-reviews-false', required: true
          = label_tag 'enable-reviews-false', 'Disable Reviews', style: 'margin-left: 5px; font-weight: normal;'
    
    .form-group
      %br
      = submit_tag "Update Designer Reviews", class: "btn btn-primary"

  %br
  %br
  
  .form-group
    %p
      %strong Instructions:
    %ul{style: 'text-align: left; display: inline-block; max-width: 600px;'}
      %li Enter designer IDs separated by commas (e.g., 1, 2, 3, 4)
      %li Select whether to enable or disable reviews for the specified designers
      %li Changes will be processed in the background and may take a few minutes to reflect
      %li You will receive an email notification once the update is complete
      %li When reviews are disabled, customers will not see reviews, ratings, or review submission forms for that designer's products
