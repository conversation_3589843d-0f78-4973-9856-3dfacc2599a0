!!!
%html{:xmlns => "http://www.w3.org/1999/xhtml"}
  %head
  %body
    %p= "Hello,"
    %p 
      -unless @line_item
        = "This is a notification email to alert you that Order Number #{@order.number} has been canceled by the buyer. We are processing the refunds currently. Please _DO NOT_ Ship the product."
      -else
        = "This is a notification email to alert you that  In Order Number #{@order.number}, the product #{@line_item.design_id} has been canceled by the buyer. We are processing the refunds currently. Please _DO NOT_ Ship the product #{@line_item.design_id}."
      %br
      
      You can view complete order here:
      = link_to @order.number, order_url(:number => @order.number)
      
      - if @gharpay
        This was gharpay (Cash Before Delivery) order.

      %br
      
      = "If you have any questions about this order, feel free to get back. Thanks."