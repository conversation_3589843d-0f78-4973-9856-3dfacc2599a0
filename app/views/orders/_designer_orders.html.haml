.modal.fade{id: "modal_cancel_desorder", role: "dialog", tabindex: "-1", style: 'color:black!important;',data: {backdrop: "static",keyboard:"false" }}
  .modal-dialog{role: "document"}
    .modal-content
      .modal-header
        %button.close{"aria-label": "Close", "data-dismiss": "modal", type: "button"}
          %span{"aria-hidden": "true"} &times;
        %h4#exampleModalLabel.modal-title
          Select Reason for cancelation
      .modal-body
        .form.form-inline
          .text-center
            #cancel_reason_alert.alert-danger
            %br
            = select_tag 'reason_for_cancel', options_for_select(DesignerOrder::CANCEL_ORDER_REASONS),prompt: "--select reason--",class: 'form-control'
            = hidden_field_tag :desorder_id
            = hidden_field_tag :cancel_design_id
            = hidden_field_tag :order_no_for_cancel
            = hidden_field_tag :cancel_type
            #marking_seller_oos.hide
              %br
              = label_tag 'Do you also want to mark item seller out of stock ?'
              = select_tag 'mark_seller_oos', options_for_select(['NO','YES']),class: 'form-control'
              %br
            #line_item_oos_count.hide 
              %br
              = label_tag 'Apply negative adjustment to the line item?'
              = select_tag 'apply_negative_adjustment', options_for_select(['YES','NO']),class: 'form-control'
              = number_field_tag 'line_item_oos_quantity', nil, class: 'form-control', min: 1, placeholder: 'Enter quantity', data: { required_on_visible: true }
              %br
              %br
            = submit_tag   'Submit',class: 'btn btn-primary form-control',id: 'cancel_desorder_with_reason'
-show_email = ACCESSIBLE_EMAIL_ID['designer_email_show_role_ids'].to_a.include?(current_account.role_id)  
- mes_suggestions = get_orders_measurements_suggestions(@order.stitching_measurements)
- des_order_state_class = { 1 =>'btn-danger', 2 => 'btn-warning', 3 => 'btn-success'}
- prioritized_designer_orders.sort_by{|k,v| k}.to_h.each do |position, designer_orders|
  - designer_orders = @designer_orders if @designer_orders.present?
  - designer_orders.each do |designer_order|
    - if designer_order.present? && designer_order.designer.present?
      -d_order_line_items = designer_order.line_items
      -item_received = designer_order.is_all_items_received
      -domestic_sor_designer_order = designer_order.domestic_sor?(@order)
      -if item_received && designer_order.rack_list.present?
        -sticker_style = 'background:none'
      -else
        -sticker_style = 'display:none'
      .designer_orders{:class => 'designer_order_' + designer_order.id.to_s}
        %tr
          %td{:width => "20%"}
            - designer = designer_order.designer
            .label-wrapper
              .btn.btn-sm.status{ class: "#{des_order_state_class[position]}", style: 'color: black;font-weight:bold;'}= designer_order.state.try(:humanize)
              -if designer.is_one_tire_designer? && d_order_line_items.any? { |line_item| line_item.design.premium? }
                .label.label-info.luxe-label= "Luxe"
            %br
            %br
            - (@email_ids['Designer']||=[]) << ["Designer : #{designer.name.titleize}", designer.email] if show_email
            - dotext = "Designer order # " + designer_order.id.to_s 
            %span= link_to dotext, "#{root_url}"'admin/designer_order/' + designer_order.id.to_s
            %br
            - if %w(operations).exclude?(current_account.role.try(:name))
              %span= link_to "[edit]", "#{root_url}"'admin/designer_order/' + designer_order.id.to_s + "/edit"
            %span= link_to "[show]", designer_designer_order_path(designer, designer_order, :format => "pdf"), :target => "_blank"
            %span= link_to "[rtv_invoice]", designers_rtv_invoice_path(designer_order), :target => "_blank"
            -if (credit_note = designer_order.credit_note_url).present?
              %span= link_to "[credit note]", credit_note, target: :_blank
            = link_to designer.name, designer_path(designer), :style => "text-decoration:underline;"
            %br
            = "Name: " + designer.name.to_s
            %br
            = "ID: " + designer.id.to_s
            %br
            -if %w(accounts accounts_admin).include?(current_account.role.try(:name))
              -%w(designer_payout_status designer_payout_date designer_payout_notes).each do |payout_data|
                -if designer_order[payout_data].present?
                  = "#{payout_data.titleize}: #{designer_order[payout_data]}"
                  %br
            = "Address: " + designer.street.to_s + ", " + designer.city.to_s + " - " + designer.pincode.to_s + ", " + designer.state.to_s + ", " + designer.country.to_s
            %br
            - if ['operations'].include?(current_account.role.try(:name))
              = "Phone: " + designer.phone.to_s
              - if designer.alt_phone.present?
                %br
                = "Alt Phone: " + designer.alt_phone.to_s
            %br
            = "Cost : " + get_price_in_currency_for_roles(designer_order.total)
            - if designer_order.discount?
              %br
              = "Discounts = " + designer_order.discount.to_s
            - if designer_order.scaled_discount?
              %br
              = "Scaled Discounts = " + designer_order.scaled_discount.to_s
            %br
            = "Total : " + get_price_in_currency_for_roles(designer_order.total + designer_order.shipping)
            %br
            = "Scaled Total : " + get_price_in_currency_for_roles(designer_order.scaled_total + designer_order.shipping)
            %br 
            = "Quantity Count = " + designer_order.total_count.to_s
            %br
            = "Line Item Count = " + designer_order.line_items_count.to_s
            %br
            -  if designer_order.pickup?  
              = "Pickup = " + designer_order.pickup.strftime('%d-%b-%Y %I:%M %p')
            - else
              = "Pickup = Date Missing"
            -  if designer_order.in_critical_at?  
              %br
              = "Critical = " + designer_order.in_critical_at.strftime('%d %b %Y')
            - if designer_order.delivered_at?
              %br
              = "Delivered on = " + designer_order.delivered_at.strftime('%d %b %Y')   
            - if designer_order.completed_at?
              %br
              = "Completed on = #{designer_order.completed_at.strftime('%d %b %Y')}"
            - if (latest_replcement_dispatched_date = designer_order.line_items.to_a.map(&:latest_replacement_dispatched_scan).flatten.compact.map(&:scanned_at).max).present?
              %br
              = "Latest Replacement Dispatched on = #{latest_replcement_dispatched_date.strftime('%d %b %Y')}"
            -if (shipment = designer_order.shipment).present? && shipment.out_for_delivery_on.present? && shipment.invoice_data['out_for_delivery_attempt_count'].present?
              = 'Out For Delivery on: ' +  shipment.out_for_delivery_on.strftime('%d %b %Y')
              = 'Delivery  Attempts : ' + shipment.invoice_data['out_for_delivery_attempt_count']

            -if (shipment = designer_order.shipment).present? && shipment.out_for_pickup_on.present? && shipment.invoice_data['out_for_pickup_attempt_count'].present?
              = 'Out For Pickup on: ' +  shipment.out_for_pickup_on.strftime('%d %b %Y')
              = 'Pickup Attempts : ' + shipment.invoice_data['out_for_pickup_attempt_count']

            %br
            %hr
            = form_tag designer_order_add_notes_path, :class => "designer_order_add_notes", :style => "padding:0px" do
              = hidden_field_tag 'designer_order_id', designer_order.id, :class => 'designer_order_id_add_notes'
              = text_field_tag 'add_notes', '', :class => "form-control add_notes"
              %br
              = submit_tag 'Add Designer Order notes', :class => "btn btn-default btn-sm"
          %td{:width => "75%"}
            %table
              %tr
                %td
                  - stitching_enabled = designer_order.line_items.any?{|li| li.stitching_required == 'Y'}
                  - if item_received && designer_order.rack_code.present?
                    .well.well-md
                      .white= designer_order.rack_code.to_s
                %td
                  = render :partial => 'designer_orders/tracking', :locals => {:designer_order => designer_order}
                  - if item_received && designer_order.rack_list.present? && designer_order.rack_code.present?
                    %td
                      .well.well-md
                        .white= "RACK #{designer_order.rack_list.code}"
                    %td{style: 'text-align:right;width:40%;'}
                      %div{id: "rack_sticker_link_#{designer_order.id}",style:sticker_style}
                        %span= link_to 'Rack Code Sticker', rack_code_sticker_url(designer_order.id, format: 'pdf'), :target => '_blank'
                  -total_qty = d_order_line_items.collect(&:quantity).inject('+')
                  -des_order_type = designer_order.check_stitching_or_jewellery_des_order
                  -stitching_done_flag = d_order_line_items.collect(&:stitching_done).compact.present? ? true : false
                  -if designer_order.rack_list_id.blank? && d_order_line_items.find{|li| li.qc_done_on.present?}
                    %b= link_to 'Change Rack', 'javascript:void(0)', data: {designer_order_id: designer_order.id, stitching_flag: stitching_enabled, stitching_done_flag: stitching_done_flag, total_qty: total_qty, des_order_type: des_order_type}, id: "change_rack_code_#{designer_order.id}", class: [('change-rack-code'), ('hide' unless item_received)]
                  -elsif designer_order.rack_list_id.present? && designer_order.rack_code.blank?
                    %b= link_to 'Assign Rack Code', 'javascript:void(0)', data: {designer_order_id: designer_order.id, rack_list_code: designer_order.rack_list.code}, id: "assign_rack_code_#{designer_order.id}", class: [('assign-rack-code'), ('hide' unless item_received)]
            = hidden_field_tag "tailoring_receive_pending_#{designer_order.id}", designer_order.line_items.find{|item| item.tailoring_info.present? && item.tailoring_info.collect(&:material_received_status).include?(nil)}.present?
            = render :partial => '/orders/designer_order', :locals => {designer_order: designer_order, stitching_flag: stitching_enabled, mes_suggestions: mes_suggestions}
          %td{:width => "5%"}
            - if !domestic_sor_designer_order || (domestic_sor_designer_order && !designer_order.pending?)
              = form_tag designer_order_event_trigger_path, id: 'change_state' do
                = hidden_field_tag 'designer_order_id', designer_order.id, :class => 'designer_order_id'
                = select_tag "state", options_for_select(DesignerOrder.state_machine.states.map(&:name),selected: designer_order.state,disabled: (((@order.international? || designer_order.ship_to == 'mirraw') && designer_order.invoice_state == 'not_uploaded') ? ['completed'] : [])), :class => 'form-control state'
                %br
                .button-medium
                  = submit_tag 'Submit', :class => "btn btn-default btn-sm designer_order_event"
              %hr
            - if domestic_sor_designer_order || (designer_order.pickedup? && designer_order.ship_to == 'customer' && designer_order.notes.exclude?('dispatched by unicommerce'))
              = render partial: '/orders/designer_order_dispatch', locals: {designer_order: designer_order}
            - else
              = form_tag designer_order_add_secondary_tracking_path, :class => "designer_order_add_secondary_tracking form", :style => "padding:0px" do
                = hidden_field_tag 'designer_order_id', designer_order.id, :class => 'designer_order_id_add_secondary_tracking'
                .form-group
                  = label_tag 'Reason for tracking update :'
                  = select_tag "tracking_reason", options_for_select(['Due to Inscan Failure', 'Due to Replacement']), class: 'form-control'
                .form-group
                  = text_field_tag 'secondary_tracking', '', :class => "form-control add_notes", :placeholder => "Recent Tracking Number",:required => true
                .form-group
                  = submit_tag 'Update Secondary Tracking', :class => "btn btn-default btn-sm"
            %p{:id => 'designer_notes_'+designer_order.id.to_s }= (notes = designer_order.notes).present? ? "Notes: #{notes}" : "Notes: none"
            - if designer_order.package_received_on.present?
              %hr
                = "Package Received On : #{designer_order.package_received_on.strftime('%Y-%m-%d %H:%M')}"
                %br
                - if designer_order.inscan_tracking_num.present?
                  = "Package Received by : #{designer_order.package_received_by} with tracking number : #{designer_order.inscan_tracking_num}"
                - else
                  = "Package Received by : #{designer_order.package_received_by}"

  -break if @designer_orders