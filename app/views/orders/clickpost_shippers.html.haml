= javascript_include_tag 'clickpost_shipment'
%table.table{:border => 1}
  %caption{:style => 'background-color:white;color:black;'}
    Clickpost Dispatch - 
    = link_to @order.number, orders_url(:number => @order.number)
  %thead
    %tr
      %td Include
      %td Design
      %td Image
  %tbody
    - @all_items.each_with_index do |item_data,index|
      - design_link_text= (item_data.design.title + '- #' + item_data.design.id.to_s)
      - image_link = item_data.design.master_image.photo(:small)
      - array_name = 'item['+ index.to_s + ']'
      - design_link = designer_design_url(item_data.design.designer_id, item_data.design.id)
      %tr
        %td= check_box_tag array_name + '[]', '', ready_for_deliver_condition(item_data)
        %td= link_to design_link_text, design_link
        %td
          = link_to design_link do
            = image_tag(image_link, :size => '50x50')
        %td
          = hidden_field_tag  array_name+'[item_id]', item_data.id
  %tfooter
    %tr
      %td{:colspan => '100%'}
        = select_tag :shipper_id, options_from_collection_for_select(@shippers, :last, :first), :class => 'shipper_id admin_shipper_id form-control col-md-12 shipper_int_dom', :data => {:order_id => @order.number},style: 'width: 150px;'
        %br
        %br
        = submit_tag 'Clickpost Dispatch', class: 'btn btn-success pickup_done_button'
