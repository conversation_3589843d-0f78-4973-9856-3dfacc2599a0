- @og = true
- order_line_items = @order.line_items.not_canceled
- purchase_ga_data_array = []
- if @trackGA || (Rails.env.development? && true)
  - @prodid = raw ga_product_array(@order)
  - @pagetype = "purchase"
  - @totalvalue = raw ga_product_price(@order)
  - coupon = nil
  - coupon = @order.coupon.code if @order.coupon.present?
  - affiliate = @order.utm_source.present? ? @order.utm_source : 'Mirraw'
  %script{:type => 'text/javascript'}
    window.addEventListener('load', function(){
    - order_line_items.each do |item|
      Unbxd.track("order",{"pid":"#{item.design_id}","qty":"#{item.quantity}","price":"#{item.price}"});
    })
    - order_line_items.each do |item|
      - experiment_id = item.design.experiment_id(@tester, :grading)
      - item_ga_data = item.ga_data(dimension2: experiment_id.to_s).to_json.html_safe
      - purchase_ga_data_array << item.line_item_wise_ga_data(0, item.get_breadcrumb, @country_code)
      -# ga('ec:addProduct', JSON.parse('#{item_ga_data}'));
    - order_ga_data = @order.ga_data.to_json.html_safe
    -# ga('ec:setAction', 'purchase', JSON.parse('#{order_ga_data}'));
    window.uetq = window.uetq || [];
    window.uetq.push({ 'gv': '#{@order.total}', 'gc': 'INR'});

  - if @order.utm_source.present? && @order.utm_source.include?("perfect") 
    :javascript
      window._pa = window._pa || {};
      _pa.orderId = "#{@order.number}";
      _pa.revenue = "#{@order.total / 60}";

      window._pq = window._pq || [];
      _pq.push(['track', 'convert']); 

  - result = ''
  - order_line_items.each do|item|
    - result = result + ',' if result.present?
    - result += "{" + "id:" + item.design.id.to_s + "," + "price:" + item.price.to_s + "," + "quantity:" + item.quantity.to_s + "}" 

  -# - if Rails.env.production?
    %script{:type => 'text/javascript'}
      !function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
      n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
      n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
      t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
      document,'script','https://connect.facebook.net/en_US/fbevents.js');
      
      fbq('init', '691366507586398');
      fbq('track', "PageView");
      - fb_design_id = []
      - order_line_items.each do |item|
        - fb_design_id.push(item.design.id)
      fbq('track', 'Purchase', {value: '#{@total}', currency: 'INR', content_ids: #{fb_design_id}, content_type : 'product', phone: '#{@phone_number}', city: '#{@city}', country: '#{@country}', email: '#{@email}'});
    %noscript
      %img{:height => "1", :src => "https://www.facebook.com/tr?id=691366507586398&ev=PageView&noscript=1", :style => "display:none", :width => "1"}
    
  = render :partial => '/orders/thankyou/adwords', locals:{adwords_total: @adwords_total}

  - order_number = ''
  - pay_type = 'NULL' 
  - if @order.pay_type == Order::PAYMENT_GATEWAY
    - pay_type = "CREDITDEBITNET"
  - elsif @order.pay_type == Order::GHARPAY
    - pay_type = "CBD"
  - elsif @order.pay_type == Order::PAYPAL
    - pay_type = "PAYPAL"
  - elsif @order.pay_type == Order::WALLET
    - pay_type = "WALLET"


  - if Rails.env.production?
    - order_number = @order.number
  - else
    - order_number = "MirrawTEST"

  - @cooliyo_order_number = order_number
  - @icubes_order_number = order_number

  - if @order.utm_source.present? && (@order.utm_source == "Komli" || @order.utm_source == "komli")
    %iframe{frameborder: "0", height: "1", src: "https://secure.komli.com/p.ashx?o=81&p=#{@order.total.to_s + ".00"}&e=144&t=#{order_number}", width: "1"}
  - if @order.utm_source.present? && @order.utm_source == "giveter"
    %iframe{frameborder: "0", height: "1", src: "http://www.giveter.com/AffiliateTracker/update?transaction=#{order_number}--#{@order.total.to_s}", width: "1"}
  - if @order.utm_source.present? && @order.utm_source == "roposo"
    %iframe{frameborder: "0", height: "1", src: "http://www.roposo.com/AffiliateTracker/update?transaction=#{order_number}--#{@order.total.to_s}", width: "1"}
  - if @order.utm_source.present? && @order.utm_source.include?("vcommission")
    %iframe{:frameborder => "0", :height => "1", :scrolling => "no", :src => "https://tracking.vcommission.com/SL1wa?adv_sub=#{order_number}_#{@order.pay_type.strip}_lifestyle&amount=#{@order.total.to_s}", :width => "1"}

  / - if @order.utm_source.present? && @order.utm_source.include?('sweetcouch')
  /   :javascript
  /     window._SC_tracker = {AFF_ID : 'mirraw.com', DATA: "TransactionID=#{order_number}&TotalCost=#{@order.total.to_s}"};
  /     (function() {var s = document.createElement('script'); s.type = 'text/javascript'; s.src = 'https://b8a730e06a174562a9b7-51037f5144a0b5b3bdfe3b7e83d3a916.ssl.cf2.rackcdn.com/sc_tracker.js'; s.id = '_sc_tracker'; document.getElementsByTagName('head')[0].appendChild(s);})();

  - if @order.utm_source.present? && @order.utm_source.include?('cooliyo')
    = render :partial => '/orders/thankyou/cooliyo'

  - if @order.utm_source.present? && @order.utm_source.include?('junglee')
    :javascript
      (function() {                 
        var jc = document.createElement('script');
        jc.type = 'text/javascript';
        jc.async = 'true';
        jc.src = 'https://www.junglee.com/ec/js?mId=AZZG1R9KKG8ZN';
        document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(jc, s);
      })();

  - if @order.utm_source.present? && @order.utm_source.include?('icubes')
    %iframe{:frameborder => "0", :height => "1", :scrolling => "no", :src => "http://tracking.icubeswire.com/SL1Em?adv_sub=#{@icubes_order_number}&amount=#{@order.total - @order.shipping}", :width => "1"}

  :javascript
    if (!window.mstag) mstag = {loadTag : function(){},time : (new Date()).getTime()};
  -# %script#mstag_tops{src: "//flex.msn.com/mstag/site/5dc6bd12-e5a2-46a7-9dff-e4b191647277/mstag.js", type: "text/javascript"}
  :javascript
    mstag.loadTag("analytics", {dedup:"1",domainId:"1367753",type:"1",revenue:"#{@order.total.to_s}",actionid:"246432"})
  %noscript
    %iframe{frameborder: "0", height: "1", scrolling: "no", src: "//flex.msn.com/mstag/tag/5dc6bd12-e5a2-46a7-9dff-e4b191647277/analytics.html?dedup=1&domainId=1367753&type=1&revenue=#{@order.total.to_s}&actionid=246432", style: "visibility:hidden;display:none", width: "1"}
  
  %img{src: "https://sp.analytics.yahoo.com/spp.pl?a=10001279463990&.yp=428972&gv=#{@order.total}&ea=#{@order.number}"}

.container
  - if @order.satisfies_cashback_conditions? && @order.cashback_available? && !@order.cashback_rewarded?
    %p.cashback-reminder= t('cashback.reminder.html', price: get_actual_price_in_currency_with_symbol(@order.other_details['loyalty_rewards_credit'].to_f, @order.currency_rate, @order.currency_code))

  .row{style: "background: #eee;padding-top: 20px"}
    - if @order.cod? && @order.zipdial? && false
      - if @order.pending?
        .notice
          ="Thank you for placing your order. Please give a missed call on #{@order.zipdial_number} within next 1 hour to confirm your COD order."
    - elsif @order.pending? && @order.bank_deposit?
      .notice
        = "Thank you for placing your order. Please note that the Payment must be made to the account within 3 days of placing the order. Our bank details have been emailed to you."
 
    - elsif @order.pay_type == PAYMENT_GATEWAY
      - if @order.ccavenue_order_status.present? && @order.ccavenue_order_status.downcase != 'success' && session[:order_number].present? && session[:order_number] == @order.number
        .notice
          = "Your order has been cancelled. Please click on the below link to retry "
          =link_to 'Retry Order', order_retry_url(:id => @order.number), :style => "color:red"
          %br
          = ".If you need any help, please get in touch at #{MIRRAW_CONTACT_INFO} #{MIRRAW_OFFICE_TIME}."
    .col-xs-12
      .col-lg-6
        -if @order.international?
          Order Acknowledgment
        -else
          -if (['pending','fraud','new','confirmed'].include? @order.state)
            Order Placed
          -else
            Order Confirmed
      -if false
        .span-6{style: "text-align: center; margin-top: 10px;"}
          = link_to 'Continue Shopping', design_category_url(@order.line_items.last.design) ,class: 'track_return_button', style: 'font-size:medium;'
      -# .col-lg-6
      -#   %span(style="font-style:italic;") Share with friends :)
      -#   %g:plusone{:size => "medium", :href => "https://www.mirraw.com"}
      -#   = facebook_like_url("https://www.mirraw.com")
    %br
    %br
    %br
    .col-lg-12
      .col-lg-6.order_number
        = "Order Number: #{@order.number}"
      .col-lg-3
        Ordered on:
        = @order.created_at.in_time_zone.strftime('%d %b %Y')
      - if @order.confirmed_at.present? && @rakhi_pre_delivery.blank?
        .col-lg-3.nopadding
          Expected Delivery Date
          -if @essentials_delivery_show == true
            = @essentials_delivery_date
          -else
            = @delivery_date.to_date
        -if @revised_delivery_date.present? && (@order.international? || @all_line_items.map(&:stitching_required).include?('Y'))
          .col-lg-12.floatr
            Revised Delivery Date
            &nbsp
            = @revised_delivery_date.to_date
    %hr.hr_line
    -@show_domestic_pipeline = true
    - if @order.international? || @all_line_items.map(&:stitching_required).include?('Y')
      / -if @order.confirmed_at.present?
      /   .prepend-2.span-6.last{style: 'padding-left:66%;width:27%;margin-top:-2%'}
      /     Expected Dispatch Date
      /     = @expected_dispatch_date.to_date
      = render :partial => '/orders/international_order_status'
      -@show_domestic_pipeline = false
      %br
    .survey-form-block
      = link_to 'Survey Form: Help Mirraw to serve you better', "https://indianethnicwear.typeform.com/to/RKyQOK", :target => "_blank", :rel => 'nofollow', class: 'survey-form'
    - if @trackGA || (current_account.present? && (current_account.accountable_id == @order.user_id || ACCESSIBLE_EMAIL_ID['users_phone_access'].try(:include?, current_account.email)))
      %span.address1(style="font-size:1.2em;font-weight:bold;text-decoration:underline;line-height: 45px;margin-left: 10%") Shipping Address:
      %span
        = @order.name
        = @order.shipping_address + ', '
        = "#{@order.phone}, #{@order.email}"
      - if @order.international? && ['sane','pending','followup'].include?(@order.state) && @order.tags.collect(&:name).include?('Invalid Address') 
        .row
          .notice#address-warning{style: "text-align:center;"}
            Your address seems to be incorrect or little short. Please provide us correct address for faster service.
          %button.btn.btn-default.btn-xs{"data-target" => "#shipping_address_modal", "data-toggle" => "modal", :type => "button"} Edit Address
          .modal.fade{:id => "shipping_address_modal", :role => "dialog", :tabindex => "-1", style: 'color:black !important;'}
            .modal-dialog{:role => "document", style: 'width:600px; margin: 30px auto;'}
              .modal-content{style: "width: 720px"}
                .modal-header
                  %button.close{"aria-label" => "Close", "data-dismiss" => "modal", :type => "button"}
                    %span{"aria-hidden" => "true"} &times;
                  %h4#exampleModalLabel.modal-title Update Shipping Address
                .modal-body
                  = form_tag 'shipping_address_form', onsubmit: "UpdateUserShippingAddress(#{@order.id})" do
                    = hidden_field_tag 'order',@order.id
                    = "Flat No. Floor Building Name, Street:"
                    = text_field_tag 'Street',@order.street, placeholder: "enter street",  :class => "form-control",required:true
                    = "City:"
                    = text_field_tag 'City',@order.city, placeholder: "enter city",  :class => "form-control",required:true
                    = "Pincode/ZipCode/Postal Code:"
                    = text_field_tag 'Pincode',@order.pincode.to_s, placeholder: "enter pincode",  :class => "form-control",required:true
                    = 'State:'
                    = text_field_tag 'BuyerState',@order.buyer_state, placeholder: 'enter buyer state', class: 'form-control',required:true
                    = submit_tag 'Submit',id: 'shipping_submit', style:'margin-top:10px', class: "btn btn-success"

    -if (ENABLE_MAP_LANDMARK && @limit_not_exceeded && @order.cod? && !@order.international? && @trackGA && (MAX_CHAR_LIMIT_FOR_MAP <= 0 || @order.street.length < MAX_CHAR_LIMIT_FOR_MAP) && ['sane','dispatched'].exclude?(@order.state))
      .panel#update_landmark_container
        .panel-heading
          LANDMARK
        .panel-content
          %div{style: 'background-color: #232023;border-radius: 5px;padding: 10px;'}  
            %div.google_map{style: 'width: 60%;height:500px;display: inline-block'}
              = render partial: '/orders/google_map_landmark'
            %div.info_pac{style: 'width: 40%;height:100%;display: inline-block;float:left;'}
              %div
                #address-warning
                  Your address seems to be little short. Please provide us nearest Landmark for faster service.
                %table
                  %tr.user_details
                    %td Name
                    %td 
                      %b=@order.name
                  %tr.user_details
                    %td Address
                    %td
                      =@order.street
                      %br
                        ="#{@order.city} - #{@order.pincode}"
                  %tr.address_buttons
                    %td{colspan: '2'}
                      %button#correct_address My Address Is Correct
                  %tr.address_buttons
                    %td{colspan: '2'}
                      OR      
                  %tr.land-mark-details
                    %td Landmark
                    %td
                      #landmark_text 
                      %input#pac-input{placeholder: "Enter a location", type: "text"}
                      = hidden_field_tag :user_location
                  %tr.address_buttons
                    %td{colspan: '2'}
                      %button#add_landmark Update Landmark           
                  %tr
                    %td{colspan: '2'}
                      #landmark-ack
    %div
      - cod_order_check = @order.present? && @order.id.present? && @order.cod? && @order.currency_rate_market_value.present? && @order.currency_code.present? &&[@order.currency_code].exclude?(@symbol)
      = render :partial => '/orders/designer_order_items', locals: {cod_check: cod_order_check}    
    .row{style: "width: 75%;margin-left: 19rem"}
      .cart_content
        #cart
          #order_show_info
            .col-md-offset-3.col-lg-6.nopadding
              - shipping = 0
              - product_total = get_item_total_price_in_currency_from_order(true)
              - if (discounts = @order.total_discounts) > 0  
                .discounts
                  .col-lg-6
                    Discounts
                  .col-lg-6
                    - if cod_order_check
                      - discounts = get_actual_price_in_currency(discounts = @order.international_cod_price(discounts), @order.currency_rate)
                    - else
                      - discounts = get_actual_price_in_currency(discounts, @order.currency_rate)
                    #{get_price_in_currency_with_symbol(discounts,true)}
              .total
                .col-lg-6
                  Product Total
                .col-lg-6
                  #{get_price_in_currency_with_symbol(product_total,true)}
              - addon_charges = get_item_addons_price_in_currency_from_order
              - if addon_charges > 0
                .total
                  .col-lg-6
                    Customisation
                  .col-lg-6
                    #{get_price_in_currency_with_symbol(addon_charges,true)}
              .total
                .col-lg-6
                  Shipping
                .col-lg-6
                  - if @order.shipping > 0
                    - if cod_order_check
                      - shipping = get_actual_price_in_currency(@order.international_cod_price(@order.shipping), @order.currency_rate)
                    - else
                      - shipping = get_actual_price_in_currency(@order.shipping, @order.currency_rate)
                    #{get_price_in_currency_with_symbol(shipping,true)}
                  - else
                    FREE
              - cod = 0
              - if @order.cod? && @order.cod_charge.present? && @order.cod_charge > 0 
                .total
                  .col-lg-6
                    COD Charges
                  .col-lg-6
                    - if cod_order_check
                      - cod = get_price_in_currency(@order.international_cod_price(@order.cod_charge))
                    - else
                      - cod = get_price_in_currency(@order.cod_charge)
                    #{get_price_in_currency_with_symbol(cod,true)}
              - unless GIFT_WRAP_PRICE.to_f < 0
                - if (order_addon = @order.order_addon).present?
                  - if order_addon.gift_wrap_price?
                    - product_total += get_price_in_currency(order_addon.gift_wrap_price)
                    .total
                      .col-lg-6
                        Gift Wrap
                      .col-lg-6
                        #{get_price_in_currency_with_symbol(order_addon.gift_wrap_price)}
              - grandtotal = (product_total - discounts + shipping + cod + addon_charges)
              - if @order.total_tax
                - tax_amount = get_actual_price_in_currency(@order.total_tax,@order.currency_rate)    
                - grandtotal += tax_amount  
                .tax_amount.total
                  .col-lg-6
                    Total Tax 
                  .col-lg-6
                    #{get_price_in_currency_with_symbol(tax_amount,true)}
              .total
                - if grandtotal < 0
                  - grandtotal = 0
                .col-lg-6
                  Grand Total 
                .col-lg-6
                  #{get_price_in_currency_with_symbol(grandtotal,true)}
                -if ENABLE_CUSTOMS_MESSAGE && @order.in_eu_zone? && session[:show_customs_message] == 'customs_message_visible'
                  .span-6{style: 'font-size: 0.8em;color: orange;'}= CUSTOMS_CHARGE_MESSAGE
    .row
      .col-sm-12.button-block
        //- invoice_url = @order.other_details[@order.other_details.keys.keep_if{|i| i.include?('proforma_invoice_MOSPL')}.first]
        //- url = current_account.present? ? invoice_url : account_session_path
        //-if invoice_url
        //  = link_to "Download Invoice", url ,class: 'button-block-info btn btn-success', target: '_blank'
        - if @order.cancellable?
          %button.btn.btn-primary.cancel-cod-order-trigger{ type: "button" } Cancel Order
          .cancel-cod-order{ data: { order: @order.number } }
            %span.cancel-cod-order-close.cancel-cod-order-confirmation-cancel &times;
            .cancel-cod-order-confirmation
              %h5 Are you sure you want to cancel your order?
              %p
                If you click on "Yes", an OTP will be sent to the mobile which was used
                to place the order. You have to enter the OTP in the next step.

              %ul.selectize
                - t('order.cancellation.reasons').each_with_index do |reason, index|
                  %li.selectize-option
                    = radio_button_tag "cancellation-reason-option", reason
                    = label_tag "cancellation-reason-option_#{reason}", reason
                %li.selectize-option.selectize-option-last
                  = radio_button_tag "cancellation-reason-option", "Other"
                  = label_tag "cancellation-reason-option_Other", "Other", class: 'selectize-other-option-toggle'
                  = text_field_tag "cancellation-reason-other-input", '', class: 'selectize-other-option-toggle is-hidden'

              %div.cancel-cod-order-reason-select-warning.text-danger.is-hidden
                %em Please select one of the reasons for cancelling.

              %button.btn.cancel-cod-order-confirmation-accept{ type: 'button' } Yes
              %button.btn.cancel-cod-order-confirmation-cancel{ type: 'button' } No
            .cancel-cod-order-otp-form
              %h5 Please enter the OTP that was sent to your mobile.
              %p
                Did not receive an OTP?
                %a.cancel-cod-order-resend{ href: '#' } Resend it.
              %form
                = text_field_tag :otp, '', class: 'cancel-cod-order-otp-input', autocomplete: 'off', required: true, type: 'rel', maxlength: 5, placeholder: 'OTP'
                = submit_tag 'Confirm', class: 'cancel-cod-order-otp-submit'
        - elsif (['cancel','new','pending','confirmed','cancel_complete'].exclude? @order.state)
          = link_to "Return", 'javascript:void(0)', id: 'return_panel',data: {user_id: current_account.try(:user).try(:id).to_s, number: @order.number},class: 'btn btn-success', target: '_blank'
          #show_return_policy{:title => "Return Policy", :style => 'display:none'}
            = render :partial => '/orders/return_policy'

      - if @country_code == 'IN' && @order.cod? && COD_NOTE_MESSAGE != 'false'
        %hr
        .alert.alert-info{style: "margin-top:10px;color: black;"}= "#{COD_NOTE_MESSAGE}"
      - if RAKHI_PRE_ORDER[0] == 'true' && @rakhi_pre_delivery.present?
        %hr
        .notice_class{style: "color: #c5bb1c; font-style: italic; padding: 5px 20px; font-weight: 600;"}
          - if @order.international?
            = "Order will be delivered #{RAKHI_PRE_ORDER[1]} (Schedule Delivery)."
          - else
            = "If your order contains multiple items, only Rakhi will be delivered #{RAKHI_PRE_ORDER[1]}"
    %hr.hr_line
      / - if @order.stitching_addon?
      /   .notice_class{style: "color: orange; font-style: italic; padding-bottom: 5px;"}
      /     = "Due to Festival Rush, estimated time to deliver stitched product is 25 days from date of order."
      /   %hr
    .row
      .survey-form-block
        = link_to 'Survey Form: Help Mirraw to serve you better', "https://indianethnicwear.typeform.com/to/RKyQOK", :target => "_blank", :rel => 'nofollow', class: 'survey-form'
      %br
      %br
      %br
      .col-lg-6
        .question When will I get my items?
        %div{:style => "font-size:11px;line-height:1.37em;padding:5px 25px 0 0px"}
          You will see a 'Expected Date' estimate for each item, which reflects when your items are expected to arrive at your shipping address.
          Once your item ships from respective designer, you will receive a Shipment Notification email with carrier and tracking information.
          
      .col-lg-6
        How do I get in touch with mirraw ?
        %div{:style => "font-size:11px;line-height:1.37em;padding:5px 20px 0 0px"}
          You can send us an email at
          = ['inr','rs'].include?(@symbol.downcase) ? ' <EMAIL>' : ' <EMAIL>'
          with your Order Number and question.
          You can also call Mirraw Customer Service at 
          %span= "#{mirraw_contact_number}."
          Please have your Order Numbers available.
- cc = CurrencyConvert.find_by(country_code: @country_code)
- market_rate = cc.present? ? cc.market_rate : 1
- rate = cc.present? ? cc.rate : 1
- gift_wrap = (order_addon.present? && order_addon.gift_wrap_price.present?) ? (get_price_in_currency(order_addon.gift_wrap_price) * market_rate).to_f : 0 
- addon_charges = addon_charges.present? ? (addon_charges * market_rate).round(2) : 0
- bmgn_discount = (get_price_in_currency(@order.additional_discount) * market_rate)
- item_total_price =  ((product_total * market_rate) - bmgn_discount)
- bmgnx_hash = PromotionPipeLine.bmgnx_hash
- bmgnx_free_items = @order.get_free_items_bmgnx(bmgnx_hash) if bmgnx_hash.present?
- total_payout = 0
- @order.designer_orders.each do |dso|
  - total_payout += dso.payout
- purchase_ga_data_array.each do |item|
  - @order.designer_orders.each do |dso|
    -dso.line_items.each do |line_item|
      - if line_item.design.id == item[:item_id].to_i
        -item[:item_payout] = line_item .get_vendor_payout_for_item
  - if bmgnx_free_items.present?  && bmgnx_free_items.keys.include?((item[:item_id]).to_i) 
    //- if bmgnx_free_items[(item[:item_id]).to_i][1] == item[:quantity]
    //  - item[:item_offer] = "b1g1"
    //- else 
    //  - item[:item_offer] = "b1g1"
    -item[:item_offer] = "B1G1"
-if @order.coupon.present?
  - item_total_without_gift_wrap = (item_total_price - gift_wrap).round(2)
  - coupon_discount = @order.discount.present? ? (get_price_in_currency(@order.discount) * market_rate).round(2) : 0
  - coupon_percent =  ((coupon_discount / (item_total_without_gift_wrap)) * 100).round(2)
  - coupon_code = @order.coupon.code if @order.coupon.present?
  - purchase_ga_data_array.each do |item|
    - item_price_without_cust = item[:price] - item[:item_customization]
    - item_price_with_coupon = (item_price_without_cust - (item_price_without_cust * coupon_percent/100)).round(2)
    - item[:price] = (item_price_with_coupon + item[:item_customization]).round(2)
  - item_total_price_wit_coupon =  (item_total_without_gift_wrap - (item_total_without_gift_wrap * coupon_percent)/100).round(2)
  - item_total_price = (item_total_price_wit_coupon + gift_wrap).round(2)
- item_total_price = (item_total_price + addon_charges).round(2)
- purchase_event_trigger = trigger_ga4_purchase_event(@order)
- item_ids = []
- contents = []
- content_ids = []
- num_items = [purchase_ga_data_array.length]
- purchase_ga_data_array.each do |item|
  - item_ids << { id: item[:item_id] , google_business_vertical: 'retail' }
  - content_ids << item[:item_id]
  - contents << {id: item[:item_id], quantity: item[:quantity]}
- if purchase_event_trigger
  - gads_params = {item_ids: item_ids,contents: contents,content_ids: content_ids,num_items: num_items}
  - purchase_ga_data = {event: 'ga4_purchase', ecommerce: {country_code: @country_code,transaction_id: "#{@order.number}", value: item_total_price, payout: total_payout.round(2), tax: (tax_amount * market_rate).round(2) || 0,coupon: coupon_code || "", coupon_discounts: coupon_discount || 0,customization: addon_charges,gift_wrap: gift_wrap.round(2),offers_discount: bmgn_discount.round(2), shipping: (shipping * market_rate).round(2), currency: "INR", items: purchase_ga_data_array,item_ids: item_ids}}
  :javascript
    window.dataLayer = window.dataLayer || [];
    var gads_items_id = #{gads_params.to_json.html_safe}
    var customer_details = {
      name: "#{@name}",
      email: "#{@email}",
      city: "#{@city}",
      country: "#{@country}",
      state: "#{@state}",
      zipcode: "#{@pincode}",
      phonenumber: "#{@phone_number}"
    }
    var ga4_purchase_params = #{purchase_ga_data.to_json.html_safe}
    dataLayer.push({ ecommerce: null });
    dataLayer.push(ga4_purchase_params);

= javascript_include_tag 'cancel_cod_order'
= javascript_include_tag 'store'

- if @trackGA || (current_account.present? && (current_account.accountable_id == @order.user_id))
  -# - if JABBER_REVIEW_ENABLED
    #InstantFeedbackWidget
      #InstantFeedbackWidgetInner.stjr-widget
    :javascript
      (function (src, callback) {

        var s, r, t; r = false;
        s = document.createElement('script');
        s.type = 'text/javascript';
        s.src = src;
        s.onload = s.onreadystatechange = function () {
          if (!r && (!this.readyState || this.readyState == 'complete')) {
            r = true; 
            callback();
          }
        };

        t = document.getElementsByTagName('script')[0];
        t.parentNode.insertBefore(s, t);
      }
      ('https://biz.sitejabber.com/js/widget.**********.js', function () {

        new STJR.InstantFeedbackWidget(
          {
            id: 'InstantFeedbackWidget',
            url: 'mirraw.com',
            user: {
              first_name: "#{@order.name.split(' ')[0]}",
              last_name:  "#{@order.name.split(' ')[1]}"
            },
            order_id: "#{@order.number}"
          }).render();
      }));

  :javascript
    UpdateUserShippingAddress = function(order_id){
      event.preventDefault()
      street = $('#Street').val()
      city = $('#City').val()
      pincode = $('#Pincode').val()
      state = $('#BuyerState').val()
      $.ajax(paramUpdateUserShippingAddress(order_id,street,city,pincode,state));
      $('.close').trigger('click')
    };

    paramUpdateUserShippingAddress = function(order_id,street,city,pincode,state){
      return{
        type: 'POST',
        data: {
          order_id: order_id,
          new_street: street,
          new_city: city,
          new_pincode: pincode,
          new_state: state,
          source: 'By User From Order Show'
        },
        url: '/orders/update_shipping_address',
        datatype: 'json',
        success: function(data,status,jqXHR){
          if (data.error !== void 0){
            return alert(data.error);
          }
          else {
            alert("Updating your shipping address, Please refresh your page after some time.")
          }
        }
      }
    };