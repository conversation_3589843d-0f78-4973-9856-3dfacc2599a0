=form_tag pages_post_freshdesk_ticket_path, id:'post_freshdesk_ticket' do
  =hidden_field_tag :designer_ticket, true if designer_faq
  =hidden_field_tag :country,nil
  =hidden_field_tag :options
  =hidden_field_tag :sub_option_1
  =hidden_field_tag :sub_option_2
  =hidden_field_tag :support_text_id
  =hidden_field_tag :from_mobile, false
  .row
    .col-md-2
      =label_tag :issue,"Issue:",class: 'control-label'
    .col-md-9
      -if designer_faq
        =text_field_tag :issue_text_help_center, nil,class: 'form form-control',autofocus: true
      -else
        =text_field_tag :issue_text_help_center, nil,class: 'form form-control',readonly: true, maxlength: '100'
        =hidden_field_tag :type_of_issue_help_center,nil,readonly: true, maxlength: '100'
  .row#order_number_label
    .col-md-2
      =label_tag :order,"Order No:",class: 'control-label'
    .col-md-9
      -if designer_faq
        =text_field_tag :order_number_text,nil,class: 'form form-control',maxlength: '100'
      -else
        =text_field_tag :order_number_text,nil,class: 'form form-control',readonly: true, maxlength: '100'
  .row
    .col-md-2
      =label_tag :email_id, "Email:", class: 'control-label'
    .col-md-9
      -if designer_faq && @designer.present?
        =email_field_tag :email_id_help_center,@designer.email,class: 'form form-control',required: true, maxlength: '100', placeholder: "Enter your email"
      -else
        =email_field_tag :email_id_help_center,nil,class: 'form form-control',required: true, maxlength: '100', placeholder: "Enter your email"
  .row#coupon_id_tag{style: 'display:none'}
    .col-md-2
      =label_tag :coupon_id,"Coupon Id*:",class: 'control-label'
    .col-md-9
      =text_field_tag :coupon_id_field,nil,class: 'form form-control',disabled: true, maxlength: '100',required: true
  .row
    .col-md-2
      =label_tag :message, "Message*:",class: 'control-label'
    .col-md-9
      =text_area :message_content, nil, size: "5x5",class:'form form-control',maxlength: "1000",required: true
  .row
    .col-md-offset-8.col-md-3
      1000 characters only
  .row
    .col-md-3.col-md-offset-3
      %button{"aria-label" => "Close", "data-dismiss" => "modal", type: "button",class: 'form form-control btn btn-warning'}Close
    .col-md-3
      =submit_tag "Submit",class:'form form-control btn btn-success',data: {disable_with: 'Please Wait..'}
