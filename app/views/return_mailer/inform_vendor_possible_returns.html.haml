= render :partial => 'header', :locals => {buyer: false}
%p
  = "This is a notification regarding Order "
  = link_to @order.number, order_url(number: @order.number)
  =" . Buyer has indicated that they would like to return below item"
  %br
  =" Line Item Cancelled:"
  %ol
    - @items.each do |item|
      %li= link_to item.design.title, designer_design_url(item.design.designer_id, item.design_id)
  %br
  Customer Details:
  %ol
    Name: #{@order.name}
    %br
    Email: #{@order.email}
    %br
    Phone: #{@order.phone}
  %br
  Pictures collected in the form:
  %ol
    - @items.each do |item|
      - if item.return_image_file_name.present?
        = image_tag item.return_image.try(:url), class: 'img-thumbnail', target: '_blank', alt: item.return_image_file_name, width: '250', height: '200'
        %br
        %br
        %br
      - if item.return_image2_file_name.present?
        = image_tag item.return_image2.try(:url), class: 'img-thumbnail', target: '_blank', alt: item.return_image2_file_name, width: '250', height: '200'

%p 
  Thank you,
  %br
  Team Mirraw  
