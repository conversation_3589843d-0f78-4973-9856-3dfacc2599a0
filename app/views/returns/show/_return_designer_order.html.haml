-if @rdo_discounts.table_name == 'return_designer_orders' && @rdo_discounts.present?
  %table.table.table-bordered
    %tr
      %th
      %th Designer
      %th Design
      %th Action
      -order = @return.order
      -order_discount,order_total,order_total_only_bmgn_products = (order.discount.to_i + order.additional_discount.to_i + (order.referral_discount.to_f * order.currency_rate.to_f)), 0, 0 
    - @rdo_discounts.each do |rdo|
      - unless rdo.line_items.blank?
        -designer = rdo.designer
        %tr
          %td
          %td
            =designer.name
            %br
            - if rdo.designer.alt_phone
              = ' / ' + rdo.designer.alt_phone
            %hr
            %b='State: '+ rdo.state
            %hr
            ='Total: '+ rdo.total.to_s
            -order=@return.try(:order)
            -if order.present? && order.currency_rate.present? && order.currency_rate != 1.0
              (#{order.currency_code} #{(rdo.total/order.currency_rate).round(2)})
            %hr
            - if (tracking = rdo.tracking).present?
              %b= tracking
              -if (shipment = rdo.reverse_shipment).present?
                %br
                %b Shipment Status - #{shipment.shipment_state}
                %br
                =shipment.last_event_timestamp.try(:strftime, '%a, %d %b')
              - if rdo.dispatched_date.present?
                %hr
                %b="Disp:"+ rdo.dispatched_date.strftime('%a, %d %b')
              - if rdo.received_date.present?
                %hr
                %b="Rcvd:"+ rdo.received_date.strftime('%a, %d %b')
              -if (return_type = rdo.return_type)
                %hr
                %b="Return Type:"+ return_type
            - elsif order.domestic?
              = link_to 'Create Shipment', returns_fetch_couriers_from_clickpost_path(return_id: @return.id, order_id: order.id, create_manual_shipment: true), class: 'btn btn-primary'
          %td
            %table.table
              %tr
                %td Title
                %td Image
                %td Price
                %td Qty
                %td
              - rdo.line_items.each do |item|
                -price = 0
                -price = item.snapshot_price
                -return_items,designer_order_total = {},0
                -design = item.design
                -designer = design.designer
                -designer_order = item.designer_order
                -if designer_order.discount.to_i > 0
                  -designer_order_total = designer_order.line_items.to_a.sum{|li| (li.snapshot_price * li.quantity)}.to_f
                -item_price,total_discount,support_discount,tax_amount = item.get_return_amount(order_total,designer_order_total,designer_order.discount.to_i,order_discount,order,order_total_only_bmgn_products )
                %tr
                  %td.col-md-5
                    = link_to item.title + " - #" + design.id.to_s, designer_design_path(designer, design)
                    - if (addons = item.line_item_addons).present?
                      %br
                      -addons.each do |addon|
                        ='+ ' + addon.addon_type_value.name.camelize
                        - if addon.snapshot_price != 0
                          - price += addon.snapshot_price
                          ="Rs. #{addon.snapshot_price}"
                        - else
                          FREE
                        %br
                  %td.col-md-2= link_to image_tag(item.image(:thumb)),  designer_design_path(designer, design)
                  %td.col-md-2
                    -current_qty = (item.return_quantity.present? ? item.return_quantity : 1)
                    = price
                    %br
                    -if tax_amount > 0 
                      + #{tax_amount / current_qty} (tax)
                  %td.col-md-2
                    %span= select_tag 'rtn_quantity_' + item.id.to_s, options_for_select((1..item.quantity).to_a,current_qty), class: "form-control"
                    = link_to 'update', 'javascript:void(0)', class: 'update_return_qty', data: {item: item.id.to_s, current_qty: current_qty} if ['canceled','payment_complete'].exclude?(@return.state) && item.quantity > 1
                  -if ['canceled','payment_complete','pending_payment'].exclude?(@return.state) && (rdo.shipment_id.blank? || rdo.line_items.size == 1)
                    %td
                      = link_to "javascript:void(0)", :class => "remove_line_item_return", :"data-item-id" => item.id.to_s do
                        = image_tag('cross.png', :alt => 'remove', :style => 'background:none', :size => '15x15')
          %td
            -if ['new','buyer_dispatched'].include?(rdo.state) && ((['new','refund_details_pending','images_approved','require_image_approval'].include?(@return.state) && !tracking) || @return.wrong_track_details?)
              = form_tag return_designer_order_event_trigger_path, :class => 'return_designer_order_event form-horizontal' do
                =hidden_field_tag 'return_designer_order_id', rdo.id, :class => 'return_designer_order_id'
                =hidden_field_tag 'state','buyer_dispatched'
                -if !@return.reverse_pickup?
                  .form-group
                    .col-sm-3=label_tag 'Shipping'
                    .col-sm-8=text_field_tag 'tracking_company',nil, class: 'form-control',required: true,autofocus: true
                  .form-group
                    .col-sm-3=label_tag 'Tracking'
                    .col-sm-8=text_field_tag 'tracking_number',nil, class: 'form-control',required: true,minlength: 5
                  .form-group.text-center
                    = submit_tag 'Submit', class: 'btn btn-primary'
              %hr
            = form_tag '#' , class: 'return_designer_order_add_notes', remote: true do
              = hidden_field_tag 'return_designer_order_id', rdo.id, :class => 'return_designer_order_id'
              .col-sm-8=text_area_tag 'add_notes','', class: 'add_notes form-control',required: true
              =submit_tag 'Add Notes',class: 'btn btn-default btn-sm'
            %br
            %hr
            .span{:id => rdo.id.to_s+'_notes'}
              = rdo.notes
-elsif @rdo_discounts.table_name == 'discount_line_items' && @rdo_discounts.present?
  %table.table.table-bordered
    %tr
      %th
      %th.col-md-3 Title
      %th Design Id
      %th Image
      %th Reason
      %th Total Item Price
      %th Addon
      %th Quantity
      %th Discount (%)
      %th Discount On
      %th Price
    -@rdo_discounts.each_with_index do |dli,index|
      -line_item = dli.line_item
      -design = line_item.design
      -total_price   = line_item.snapshot_price
      %tr
        %td=index + 1
        %td.col-md-3=link_to line_item.title, designer_design_path(design.designer, design)
        %td=design.id
        %td=link_to image_tag(line_item.image(:thumb)), designer_design_path(design.designer, design)
        %td=dli.return_reason
        %td
          #{total_price}
        %td
          -if dli.discount_on != 'Only Product' && (addons = line_item.line_item_addons).present?
            -addons.each do |addon|
              ='+ ' + addon.addon_type_value.name.camelize
              - if addon.snapshot_price != 0
                ="Rs. #{addon.snapshot_price}"
              - else
                FREE
              %br
          -else
            NONE
        %td=dli.quantity
        %td="#{dli.discount_percent} %"
        %td=dli.discount_on
        %td
          ="Rs. #{dli.price.to_i}"
          -if order.present? && order.currency_rate.to_i != 1
            (#{order.paid_currency_code || order.currency_code} #{(dli.price.to_f/(order.paid_currency_rate || order.currency_rate)).round(2)})