- if design && design.images.present? && design.categories.present? && (designer = design.designer).present?
  -is_domestic = (['india', 'n/a'].include?(@actual_country.downcase)) ||  (['inr', 'rs'].include?(@symbol.downcase))
  - is_sibling = is_sibling.presence || false
  -category_name = design.categories.first.name
  -singularize_category_name = category_name.singularize
  - if designer && (d_end_date = designer.additional_discount_end_date).present? && designer.additional_discount_start_date <= Date.today && Time.zone.now < d_end_date.end_of_day && d_end_date.end_of_day < (15.days.from_now)
    - end_date = (d_end_date.end_of_day.getlocal.utc).strftime("%Y/%m/%d %H:%M:%S")
  -#Removing as currently not required also removing 'dimension2' : '#{grading_experiment_id}' from product impression also setting galist unknow currently
  -#grading_experiment_id = design.experiment_id(@tester, :grading)
  .design_div.col-lg-12.col-md-12.col-sm-12.nopadding{class: (design.id if !is_sibling)}
    - if !@design_ga_data_array.nil?
      - if @breadcrumb.present? && @breadcrumb.breadcrumb.present?
        -  @design_ga_data_array << design.generate_design_hash(@index, @breadcrumb.breadcrumb.push(@final_breadcrumb), true, @country_code) unless is_sibling
    - effective_discount_percent = design.effective_discount_percent
    - is_not_exclude_free_shipping = @category.present? && EXCLUDE_FREE_SHIPPING_CATEGORIES.exclude?(@category.id)
    - if design.state == 'in_stock' && !designer.inactive_designer_state?
      - if PromotionPipeLine.bmgnx_hash.present? && design.buy_get_free == 1
        .b1g1.label-ribbon= Promotions.bmgnx_offer_message
      - elsif design.free_stitching_available?
        .free_stitching_label.label-ribbon Free Stitching
      - elsif ENABLE_ALMOST_GONE && (1...3).include?(design.actual_max_quantity)
        .almost_gone_label.label-ribbon Almost Gone!
      -elsif design.free_shipping_eligible? && !(!is_domestic && PromotionPipeLine.bmgnx_hash.present? && design.buy_get_free == 1) && is_not_exclude_free_shipping
        .b1g1.label-ribbon Free Shipping
    - if !no_design_found_page
      = render :partial => 'designs/microdata', :locals => {design: design, designer: designer, :design_name => design.title.downcase.camelize, no_design_found_page: no_design_found_page}
    -image_height,image_type,long_div = [338, :long, true]
    %li.listing-card{class: ( 'height_long_div' if long_div)}
      .add_border
        - add_class = "opacity_5" unless design.in_stock?
        - #@ga_list = 'unknown' unless @ga_list.present? ## was used in impressions and click action
        -# - if design.mirraw_certified?
        -#   = image_tag('mirraw-cert1.jpg', class: 'catalog-cert', style: ("top:5px;" if end_date.present?))
        -# # - if designer.cod && is_domestic
        -# #   %div{id: "cod_#{design.id}"}
        -# #     = image_tag('cod_tag.jpg', class: 'cod_tag', height: 22, width: 26)
        :javascript
          (product_sku_list = (typeof(product_sku_list) != 'undefined') ? product_sku_list : []).push('#{design.id}');
        - ga_design_data = design.ga_data
        - ga4_catalog_select_params = {}
        - @breadcrumb_array = @breadcrumb.present? && @breadcrumb.breadcrumb.present? ? @breadcrumb.breadcrumb.push(@final_breadcrumb) : []
        - design_ga_hash = design.generate_design_hash(@index, @breadcrumb_array, false, @country_code) unless is_sibling
        - ga4_catalog_select_params = {event: "ga4_select_item", ecommerce: {item_list_id: @category.id.to_s, country_code: @country_code, item_list_name: @category.title, items: [design_ga_hash]}} if @category.present?

        = link_to designer_design_path(designer, design), onclick: "dataLayer.push({ ecommerce: null }); dataLayer.push(#{ga4_catalog_select_params.to_json}); console.log(#{ga4_catalog_select_params.to_json});", target: "_blank",:class => 'listing-thumb row design-impression-container', unbxdattr: "product", unbxdparam_sku: design.id, unbxdparam_prank: (1), data: {ga_design_data: ga_design_data.to_json} do
          - if (design.sor_available? || design.variants.any?{|v| v.sor_available?})
            .rts_plp_logo
          - design_master_image = design.master_image.photo
          - if design.master_image.photo_processing?
            = image_tag(design_master_image.url(:original), :alt => "#{design.title}", :class => add_class, :width => 225, height: image_height)
          - elsif LAZY_LOAD_IMAGE
            - lazy_class = is_sibling ? 'lazy-hover' : 'lazy-detect'
            = webp_picture_tag(design_master_image, p_style: image_type, lazy: {class: lazy_class}, :alt => "#{design.title}", :width => 225, height: image_height)
          - else
            = webp_picture_tag(design_master_image, p_style: image_type, :alt => "#{design.title}", :class => add_class, :width => 225, height: image_height)
          -#- else
          -#  - design_row_number = design_counter / 4 + 1
          -#  - if (1..2) === design_row_number
          -#    = image_tag('11.png', :alt => "#{design.title}", :class => "#{add_class}", :itemprop => "image", 'data-custom' => design.master_image.photo.url(:small), :width => 225, height: 257)
          -#  - elsif (3..6) === design_row_number
          -#    = image_tag('11.png', :alt => design.title, :class => "lazy-custom #{add_class}", :itemprop => "image", 'data-src' => design.master_image.photo.url(:small), :width => 225, height: 257)
          -#  - else
          -#    = image_tag('11.png', :alt => design.title, :class => "lazy #{add_class}", :itemprop => "image", 'data-original' => design.master_image.photo.url(:small), :width => 225, height: 257)
        .listing-social.row
          -if can? :manage, design
            .other_designer_button
              =link_to "show other designers", designer_design_other_designers_path(designer, design, id: design.id, ici:'similar-designs',  icn_term: "similar-designs-discount-update")
          -if !(account_signed_in? && current_account.role.present? && defined?(RailsAdmin))
            %div{class: designer.inactive_designer_state? || design.state != 'in_stock' ? "soldOutProducts" : "add_to_cart" }
              - if !designer.inactive_designer_state? && design.check_quantity
                - if design.variants_available
                  = link_to designer_design_path(designer, design), data: {ga_design_data: ga_design_data.to_json}, id: design.id do
                    .buy_outer_link
                      %span.buy_small_link{style: 'display:block'} Shop Now
                - elsif design.state == 'in_stock'
                  = link_to "javascript:void(0)", :id => design.id, :class => "add_to_cart_link",  unbxdattr:"AddToCart", unbxdparam_sku: design.id, data: {ga_design_data: ga_design_data.to_json} do
                    .buy_outer_link
                      %span.buy_small_link{style: 'display:block'} Shop Now
                - else 
                  %span.soldOut
                    SOLD OUT
              - else
                %span.soldOut
                  SOLD OUT

          - if effective_discount_percent.present? && effective_discount_percent > 0 && designer.name != "Manyavar"
            %span.discount
              %span.discount_percent(align="center")= "-" + effective_discount_percent.to_s + "%"
        - if can?(:update, design) && designer.facebook_page_linked?
          .post-to-facebook.row
            = form_tag post_to_facebook_path(designer), :method => :post do
              = hidden_field_tag :design_id, design.id
              = image_submit_tag 'post_to_fb.jpg'
        - if current_account.present? && current_account.admin?
          .post-to-facebook.row
            -if current_account.present? && %w(super_admin admin).include?(current_account.role.try(:name))
              = button_tag 'Remove Catalog', :class => 'remove-catalog', :data => {:link => designer_design_remove_all_catalog_item_path(designer, design),type: 'all'} 
              = button_tag 'Remove From int catalog', :class => 'remove-int-catalog', :data => {:link => designer_design_remove_int_catalog_item_path(designer, design),type: 'international'} 
              = button_tag 'Remove From dom catalog', :class => 'remove-dom-catalog', :data => {:link => designer_design_remove_dom_catalog_item_path(designer, design),type: 'domestic'} 
        .listing-detail.row
          .col-lg-12.col-md-12.col-sm-12.col-xs-12.nopadding
            %p.listing-title.row
              = link_to designer_design_path(designer, design), data: {ga_design_data: ga_design_data.to_json}, :title => design.title.downcase.camelize do
                %span
                  = truncate(design.title.downcase.camelize, :length => 30, :omission => "", :separator => ' ')
              - if can? :publish, design
                - if design.in_stock?
                  = image_tag('tick_mark.png', :alt => 'published', :class => 'display_none', :size => '20x10')
                - else
                  = image_tag('cross.png', :alt => 'unpublished', :class => 'display_none', :size => '10x10')
            - if can? :update, design
              %p.listing-maker.row
                = link_to truncate(designer.name, :length => 15), designer_path(designer)
                = link_to "edit", edit_designer_design_path(designer, design), :class => 'edit_link'
          - effective_price_in_currency = get_price_in_currency(design.effective_price)
          - effective_price_currency_symbol = get_price_in_currency_with_symbol(effective_price_in_currency, true)
          -if current_account.present? && current_account.admin?
            - rpv_data = RevenuePerView.where(design_id: design.id).first
            - if rpv_data.present?
              rpv: #{rpv_data.rpv}
          .col-lg-12.col-md-12.col-sm-12.col-xs-12.nopadding
            - if effective_discount_percent.present? && effective_discount_percent > 0 && designer.name != "Manyavar"
              %div.discount-listing-price
                .row.col-lg-12.col-md-12.col-sm-12.col-xs-12.nopadding
                  %span.effective-price= effective_price_currency_symbol
                  %span.old-listing-price
                    %span.old-price= get_price_in_currency_with_symbol(get_price_in_currency(design.price), true)
                  %span.red-discount-percentage= "(#{effective_discount_percent}% Off)"
            - else
              %span.discount-listing1-price
                %span.listing-price
                  %span.effective-price= effective_price_currency_symbol
                  
                  %span.small_new_rating{class: 'green-rating', style: 'margin-top: -14px'}
                    New
                -# -if total_review > 0
                -#   .total_rating
                -#     ="(#{total_review.to_i})"
            .col-lg-8.col-md-8.col-sm-8.nopadding
              - if end_date.present? && @integration_status == 'new'
                .designer_timers{data:{countdown: end_date}}
              - if show_wishlist?
                = render partial: 'wishlists/wishlist_forms', locals: {design: design}

        - if can_show_design_details? && (can? :update, design)
          .admin_block.col-lg-12.col-md-12.col-sm-12.col-xs-12.nopadding
            .pudct_id.col-md-12.col-sm-12.col-xs-12
              =design.id
            .col-lg-12.col-md-12.col-sm-12.col-xs-12
              = nested_form_for [designer, design], :remote => true do |f|
                -# -if grading_access
                -#   - if ['inr', 'rs'].include?(@symbol.downcase) || current_account.role.try(:name) == 'senior_vendor_team'
                -#     = f.text_field :grade, size: 5
                -#   - else
                -#     = f.text_field :international_grade, size: 5
                = f.text_field :collection_list,value: design.collection_list_text, :size => 10
                = f.submit 'save'
            %b.col-lg-1212.col-md-12.col-sm-12.col-xs-12.counts_block
              ="Clks:#{design.clicks}"
              .label.label-danger="L-Sell:#{design.sell_count}"
              - if params[:sell_at].present?
                .label.label-success="R-Sell:#{design.sell_count}"
              ="rtn:#{design.return_count}"
              %br
              ="Qty:#{design.quantity}"
              = "L:#{time_ago_in_words(design.created_at)} ago"
              - if grading_access
                .label.label-danger
                  Sell-30days: #{design.metric_sales_30}
