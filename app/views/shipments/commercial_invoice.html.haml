- gst_invoice = @order.international?
- jewellery_product, apparels_product = false,false
- no_igst_couriers ||= IGST_PAYMENT_COMMERCIAL.split(',')
- total_quantity = 0
- igst_total = 0
- company_name, _, shipping_address_1, shipping_address_2, shipping_city, shipping_pincode,shipping_state,_ = @order.get_warehouse_shipping_address
.Section1
  %table{border: "1", cellpadding: "0", cellspacing: "0", style: "border-collapse:collapse;border:none;font-size: 10pt;", width: "100%"}
    -if ['atlantic'].include?(@shipment_detail['shipper_name'])
      - barcode_content = (@shipment_detail['shipper_name'] == 'dhl' ? @date : @shipment_detail['awb_number'])
      %tr
        %td{colspan: "6", style: "text-align:right;"}
        %td{colspan: "6", style: "text-align:right;"}
          %img{:src=> Shipment.generate_barcode(barcode_content,20),style:"margin: 1px 1px 0px 0px;width:98%"}
    %tr
      %td{colspan: "12", style: "text-align:center;"}
        %b= @date.to_s.include?('MOSPL') ? 'PROFORMA INVOICE' : 'COMMERCIAL INVOICE CUM PACKING LIST'

    %tr
      %td{colspan: "12", style: "text-align:center;"}
        %b
          -if !@shipment_detail['dhl_csb'] && no_igst_couriers.include?(@shipment_detail['shipper_name'])
            SUPPLY MEANT FOR EXPORT UNDER LETTER OF UNDERTAKING  WITHOUT PAYMENT OF IGST
          -else
            SUPPLY MEANT FOR EXPORT ON PAYMENT OF IGST

    -if @irn_number.present? && @gst_barcode.present?
      %tr
        %td{colspan: "6", valign: "top"}
          %p.MsoNormal{style: "font-weight:bold;"} IRN Number: 
          %p.MsoNormal=@irn_number
        %td{colspan: "6", style: "text-align:center;"}
          - qr_code = "data:image/svg+xml;base64,#{Base64.encode64(RQRCode::QRCode.new(@gst_barcode).as_svg)}"
          %center{style: "padding:5pt 5pt 5pt 5pt"}
            %img{src: qr_code, width: '180'}

    %tr
      %td{colspan: "6", rowspan: "3", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
        %span.MsoNormal{style: "font-weight:bold"} Supplier :
        %p.MsoNormal
          = "#{company_name},"
          %br
          = "#{shipping_address_1},"
          %br
          = "#{shipping_address_2} - #{shipping_pincode},"
          %br
          = @order.contact_number
        - if gst_invoice
          %p.MsoNormal
            Statecode:- 27
            %br
            Reverse Charge(Y/N):- N

      %td{:colspan => "2", :style => "padding:0in 5.4pt 0in 5.4pt;", :valign => "top"}
        %span.MsoNormal{:style => "font-weight:bold"} Invoice No
        %div.MsoNormal=@date

      %td{:colspan => "2", :style => "padding:0in 5.4pt 0in 5.4pt;", :valign => "top"}
        %span.MsoNormal{:style => "font-weight:bold"} Date
        %div.MsoNormal=Time.zone.now.strftime("%d/ %m/ %Y")
      %td{colspan: "2", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
        %span.MsoNormal{style: "font-weight:bold"} Exporter's Ref

    %tr
      %td{colspan: "3", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
        %span.MsoNormal{style: "font-weight:bold"} Buyer's Order No & Date
        %div.MsoNormal= @order.number + " " + @order.created_at.strftime("%d/ %m/ %Y")
      %td{:colspan => "3", :style => "padding:0in 5.4pt 0in 5.4pt;", :valign => "top"}
        %span.MsoNormal{:style => "font-weight:bold"} IEC Number :
        %div.MsoNormal 0313074984

    %tr
      %td{colspan: "2", style: "padding0:in 5.4pt 0in 5.4pt;height:17.45pt", valign: "top"}
        %span.MsoNormal{style: "font-weight:bold"} Other Reference(s)
        %div.MsoNormal= @order.number
      %td{:colspan => "2", :style => "padding:0in 5.4pt 0in 5.4pt;", :valign => "top"}
        %span.MsoNormal{:style => "font-weight:bold"} ADCODE :
        %div.MsoNormal= @order.get_ad_code
      - if gst_invoice
        %td{:colspan => "2", :style => "padding:0in 5.4pt 0in 5.4pt;", :valign => "top"}
          %span.MsoNormal{:style => "font-weight:bold"} GST Number :
          %div.MsoNormal 27AAHCM4763C1ZH
      -else
        %td{:colspan => "2", :style => "padding:0in 5.4pt 0in 5.4pt;", :valign => "top"}
    -if gst_invoice
      %tr
        %td{:colspan => "6", :rowspan => "3", :style => "padding:0in 5.4pt 0in 5.4pt;height:18.7pt", :valign => "top"}
          %span.MsoNormal{:style => "font-weight:bold"} Recipient :
          %p.MsoNormal
            = "Name: " + @order.name
            %br
            = "Address: " + @order.street + ", " + @order.city + " - " + @order.pincode + ", " + @order.buyer_state + ", " + @order.country  
            %br
            = "Telephone no: " + @order.phone


        %td{:colspan => "6", :style => "padding:0in 5.4pt 0in 5.4pt;", :valign => "top"}
          %p.MsoNormal Buyer ( If other than consignee)

      %tr
        %td{:colspan => "3", :style => "padding:0in 5.4pt 0in 5.4pt;", :valign => "top"}
          %span.MsoNormal Country of origin of goods
          %div.MsoNormal{:style => "font-weight:bold"}= "#{shipping_city}, #{shipping_state}"

        %td{:colspan => "3", rowspan: 2,:style => "padding:0in 5.4pt 0in 5.4pt", :valign => "top"}
          %span.MsoNormal Country of final destination
          %div.MsoNormal{:style => "font-weight:bold"}="#{@order.city.presence || @order.buyer_state},#{@order.country}"
      %tr
        %td{colspan: "3", style: "padding:0in 5.4pt 0in 5.4pt", valign: "top"}
          %span.MsoNormal Place of Supply
          %div.MsoNormal{style: "font-weight:bold"}= @order.country
    -else
      %tr
        %td{:colspan => "6", :rowspan => "2", :style => "padding:0in 5.4pt 0in 5.4pt;height:18.7pt", :valign => "top"}
          %span.MsoNormal{:style => "font-weight:bold"} Consignee :
          %p.MsoNormal
            = "Name: " + @order.name
            %br
            = "Address: " + @order.street + ", " + @order.city + " - " + @order.pincode + ", " + @order.buyer_state + ", " + @order.country  
            %br
            = "Telephone no: " + @order.phone


        %td{:colspan => "6", :style => "padding:0in 5.4pt 0in 5.4pt;", :valign => "top"}
          %p.MsoNormal Buyer ( If other than consignee)

      %tr
        %td{:colspan => "3", :style => "padding:0in 5.4pt 0in 5.4pt;", :valign => "top"}
          %span.MsoNormal Country of origin of goods
          %div.MsoNormal{:style => "font-weight:bold"}= "#{shipping_city}, #{shipping_state}"

        %td{:colspan => "3", :style => "padding:0in 5.4pt 0in 5.4pt", :valign => "top"}
          %span.MsoNormal Country of final destination
          %div.MsoNormal{:style => "font-weight:bold"}="#{@order.city.presence || @order.buyer_state},#{@order.country}"

    %tr
      %td{style: "padding:0in 5.4pt 0in 5.4pt;", colspan: "2"}
        %p.MsoNormal Pre-Carriage by
        %p.MsoNormal By Air

      %td{style: "padding:0in 5.4pt 0in 5.4pt;height:12.65pt", colspan: "4"}
        %p.MsoNormal Place of receipt by pre Carrier
        %p.MsoNormal

      -if 'fedex' == @shipment_detail['shipper_name']
        %td{colspan: "3", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
          %p.MsoNormal Advance Payment Received
          %p.MsoNormal UT/Bond : Yes
          %p.MsoNormal AD270320002582O
        %td{colspan: "3", valign: "top"}
          %p.MsoNormal E-commerce : Yes
          %hr{style:"border-top: thin solid #000000;"}
            %p.MsoNormal Transaction id : #{@order.paypal_txn_id}
      -else
        %td{colspan: "6", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
          %p.MsoNormal Advance Payment Received
    %tr
      %td{style: "padding:0in 5.4pt 0in 5.4pt;height:15.55pt", valign: "top", colspan: "2"}
        %span.MsoNormal Vessel / Flight No
        %p.MsoNormal{style: "font-weight:bold"} By Air

      %td{style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top", colspan: "2"}
        %p.MsoNormal  Port of Loading
        %p.MsoNormal{style: "font-weight:bold"}= COMPANY_LOCATIONS[:user][:city]
      %td{:colspan => "2", :style => "padding:0in 5.4pt 0in 5.4pt", :valign => "top"}
        %p.MsoNormal Carrier
        %p.MsoNormal{:style => "font-weight:bold"}= @order.courier_company.presence || 'NA'

      %td{colspan: "6",style: "text-align:center;"}
        -if @shipment_detail['shipper_name'] == 'dhl' && @shipment_detail['awb_number'].present?
          %img{src: Shipment.generate_barcode(@shipment_detail['awb_number'].to_s,10),style:"margin: 1px 0px 0px 0px;width:98%"}
        %h4{:style => "margin-top:-7px;"}
          AWB Number -
          =@shipment_detail['awb_number'].presence || 'NA'

    %tr
      %td{style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top", colspan: "3"}
        %span.MsoNormal Port of Discharge
        %p.MsoNormal{style: "font-weight:bold"}="#{@order.city.presence || @order.buyer_state},#{@order.country}"

      %td{style: "padding:0in 5.4pt 0in 5.4pt;height:15.55pt", valign: "top", colspan: "3"}
        %span.MsoNormal Final Destination
        %p.MsoNormal{style: "font-weight:bold"}="#{@order.city.presence || @order.buyer_state},#{@order.country}"
      -col_span = (@shipment_detail['shipper_name'] == 'fedex' ? '1' : '3' )
      %td{:colspan => col_span, :style => "padding:0in 5.4pt 0in 5.4pt", :valign => "top"}
        %p.MsoNormal Gross Weight
        %p.MsoNormal{:style => "font-weight:bold"}= @order.actual_weight
      -if @shipment_detail['shipper_name'] == 'fedex'
        %td{:colspan => "2", :style => "padding:0in 5.4pt 0in 5.4pt", :valign => "top"}
          %p.MsoNormal Net Weight
          %p.MsoNormal{:style => "font-weight:bold"}= @items.collect{|item| item[:weight]}.compact.sum.round(2)

      %td{:colspan => "3", :style => "padding:0in 5.4pt 0in 5.4pt", :valign => "top"}
        %p.MsoNormal Dimentional Weight
        %p.MsoNormal{:style => "font-weight:bold"}= @order.volumetric_weight

    - unless gst_invoice
      %tr
        %td{colspan: "8", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
          %p.MsoNormal
            %b Description of  goods

        %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
          %p.MsoNormal
            %b Quantity

        %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
          %p.MsoNormal
            %b= "Rate (#{@shipment_detail['currency_code']})"

        %td{colspan: "2", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
          %p.MsoNormal
            %b="Amount (#{@shipment_detail['currency_code']})"

      -fob_total = 0
      - @items.each do |item|
        %tr{style: "padding-top:10px;"}
          %td{colspan: "8", class: 'table-row-padding', valign: "top"}
            %p.MsoNormal= item[:name].gsub(/_.*/,'').gsub('-', ' ').camelize
          %td{colspan: "1", class: 'table-row-padding', valign: "top"}
            %p.MsoNormal= item[:quantity]

          %td{colspan: "1", class: 'table-row-padding', valign: "top"}
            %p.MsoNormal= item[:price]

          %td{colspan: "2", class: 'table-row-padding', valign: "top", width: "64"}
            -fob_total += item[:total_price]
            %p.MsoNormal= item[:total_price]
      - 3.times do
        %tr{style: "height:20pt;border:none"}
          %td{colspan: "8", class: 'table-row-padding', valign: "top"}
          %td{colspan: "1", class: 'table-row-padding', valign: "top"}
          %td{colspan: "1", class: 'table-row-padding', valign: "top"}
          %td{colspan: "2", class: 'table-row-padding', valign: "top"}
    - else
      %tr
        %td{colspan: "2",rowspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-right:1px solid black;", valign: "top"}
          %p.MsoNormal
            %b Description of  goods

        %td{colspan: "1",rowspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
          %p.MsoNormal
            %b HSN CODE

        %td{colspan: "2",rowspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
          %p.MsoNormal
            %b Quantity

        %td{colspan: "1",rowspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
          %p.MsoNormal
            %b Weight
        
        %td{colspan: "2",rowspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
          %p.MsoNormal
            %b= "Net Value (#{@shipment_detail['currency_code']})"

        %td{colspan: "1",rowspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
          %p.MsoNormal
            %b= "Taxable Value (INR)"


        %td{colspan: "2", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
          %p.MsoNormal
            %b IGST
            %br
            %span
              %div{style: 'text-align:left;width:40%;padding:0px;float:left;'}
                %b %
              %div{style: 'text-align:right;padding:0px;float:right;'}
                %b Rate(INR)

        %td{colspan: "1",rowspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;", valign: "top"}
          %p.MsoNormal
            %b="Amount (#{@shipment_detail['currency_code']})"

      - @items.each do |item|
        -total_quantity += item[:quantity]
        -jewellery_product = true if (!jewellery_product && item[:designable_type].to_s.downcase == 'jewellery')
        -apparels_product = true if (!apparels_product && ['saree','kurta','kurti','salwarkameez','lehenga'].include?(item[:designable_type].to_s.downcase))
        %tr{style: "padding-top:10px;"}
          %td{colspan: "2", class: 'table-row-padding', valign: "top"}
            %p.MsoNormal
              = item[:name].gsub(/_.*/,'').gsub('-', ' ').camelize
              -if item[:meis_scheme]
                %span.MsoNormal
                  ="- Customized Fashion Garment"

          %td{colspan: "1", class: 'table-row-padding', valign: "top"}
            %p.MsoNormal= item[:hsn_code]

          %td{colspan: "2", class: 'table-row-padding', valign: "top"}
            %p.MsoNormal= item[:quantity]

          %td{colspan: "1", class: 'table-row-padding', valign: "top"}
            %p.MsoNormal= item[:weight].to_f.round(3)
            
          %td{colspan: "2", class: 'table-row-padding', valign: "top"}
            %p.MsoNormal= item[:item_discounted_price]

          %td{colspan: "1", class: 'table-row-padding', valign: "top"}
            %p.MsoNormal= item[:taxable_value] if @shipment_detail['dhl_csb'] || no_igst_couriers.exclude?(@shipment_detail['shipper_name'])

          %td{colspan: "1", class: 'table-row-padding', valign: "top"}
            %p.MsoNormal= item[:gst_rate] if @shipment_detail['dhl_csb'] || no_igst_couriers.exclude?(@shipment_detail['shipper_name'])

          %td{colspan: "1", style: "padding:0in 5.4pt 0in 5.4pt;border-bottom:none;border-top:none;margin:0in 5.4pt", valign: "top"}
            %p.MsoNormal= item[:gst_tax] if @shipment_detail['dhl_csb'] || no_igst_couriers.exclude?(@shipment_detail['shipper_name'])

          %td{colspan: "1", class: 'table-row-padding', valign: "top", width: "64"}
            %p.MsoNormal= item[:total_price]


      - 3.times do
        %tr{style: "height:20pt;border:none"}
          %td{colspan: "2", class: 'table-row-padding', valign: "top"}
          %td{colspan: "1", class: 'table-row-padding', valign: "top"}
          %td{colspan: "2", class: 'table-row-padding', valign: "top"}
          %td{colspan: "1", class: 'table-row-padding', valign: "top"}
          %td{colspan: "2", class: 'table-row-padding', valign: "top"}
          %td{colspan: "1", class: 'table-row-padding', valign: "top"}
          %td{colspan: "1", class: 'table-row-padding', valign: "top"}
          %td{colspan: "1", class: 'table-row-padding', valign: "top"}
          %td{colspan: "1", class: 'table-row-padding', valign: "top"}

    %tr{style: "height:3.5pt"}
      %td{colspan: "3", class: 'table-total left-padding', valign: "top"}
        = "TOTAL"
      %td{colspan: "2", class: 'table-total left-padding', valign: "top"}
        = total_quantity
      %td{colspan: "6", class: 'table-total right-padding', valign: "top"}
        = @shipment_detail['shipper_name'] == 'fedex' ? 'FOB' : 'C&F / FOB / Total'
      %td{style: "padding:0in 5.4pt 0in 5.4pt;height:3.5pt;", valign: "top"}
        %p.MsoNormal= '%.2f' % @shipment_detail['total_price']
    %tr{style: "height:31.2pt"}
      %td{colspan: "12", style: "border-bottom:none;height:35.6pt", valign: "top", width: "497"}
        %span.MsoNormal Declaration:
        -if gst_invoice && jewellery_product
          %span.MsoNormal Jewellery products does not contain any precious or semi-precious metal or stone or Diamond and the base material used is Alloy.
        -# -if gst_invoice && apparels_product
        -#   %span.MsoNormal We intend to claim benefit under Meis scheme.
        %span.MsoNormal
          We declare that this
          invoice shows the actual price of the goods Described and that all
          particulars are true & correct
    %tr{style: "height:31.2pt"}
      %td{colspan: "6", style: "border-top:none;", valign: "top"}
      %td{colspan: "6", style: "border-left:none;", valign: "top"}
        %u{style: "text-underline:double"} Signature / Date / Co. stamp
        %br
    %tr
      %td{colspan: '11', style: 'border:none;', valign: 'top'}
        %p.MsoNormal This is a computer generated invoice no signature is required
    %tr
      %td{colspan: '11', style: 'border:none;', valign: 'top'}
        %p.MsoNormal We intend to claim rewards under the Remission of Duties and Taxes on Exported Products (RoDTEP) scheme and Rebate of State and Central Taxes and levies (RoSCTL).

:css 
  .table-row-padding {
    padding:0in 5.4pt 0in 5.4pt;
    border-bottom:none;
    border-top:none;
  }

  .table-total {
    padding:0in 5.4pt 0in 5.4pt;
    height:3.5pt;
  }

  .left-padding {
    text-align:left
  }

  .right-padding {
    text-align:right
  }