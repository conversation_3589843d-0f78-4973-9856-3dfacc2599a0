%table.table{:border => 1}
  %thead
    %tr
      %td Tracking Number
      %td Company
      %td Weight
      %td Under Valued Price
      %td Invoicer
      %td Packager
      %td State
      %td Delivered
      %td Delivered To
      %td Packaging Type
      %td Total Items
      %td Label
      %td Invoice
      %td Return Label
      %td Return Tracking
      %td Charges
  %tbody
    %tr
      %td= @s.number
      %td= @s.shipper.name
      %td= @s.weight
      %td= @s.price
      %td= @s.invoicer.email.split('@').first
      %td= @s.packer.email.split('@').first
      %td= @s.human_shipment_state_name
      %td= @s.delivered_on
      %td= @s.delivered_to
      %td= @s.packaging_type
      %td= @s.line_items.count
      %td
        -if !@s.order.international?
          %a{:href => @s.courier_label_url} Open Label
        - else
          %a{:href => @s.label} Open Label
        - if @s.courier_label_url.present?
          %hr
          %a{:href => @s.courier_label_url} Secondary link
      %td
        -if ['fedex','dhl','skynet','dhl ecom'].include? @s.shipper.name.try(:downcase)
          %p= link_to 'Open Invoice', combined_sticker_url(@s.order.id,shipment_id: @s.id),target: '_blank'
        -else
          %a{:href => @s.invoice} Open Invoice
        -if @jwellery_invoice == true
          %hr
          %a{:href => @s.jewellery_invoice.url}jewellery invoice
        -if @s.credit_note_invoice.present?
          %hr
          %a{:href => @s.credit_note_invoice}CN Invoice
        -if @s.web_pdf_file_name.present? 
          %hr
          %a{:href => @s.web_pdf.url}Web Pdfs
      %td
        - if @s.return_label_updated_at.present?
          %a{:href => @s.return_label} Open return Label
        - else
          return label pending
      %td
        - if @s.return_tracking_no.present?
          = @s.return_tracking_no
      %td
        - if @s.shipment_charges.to_f != 0.0
          = @s.shipment_charges.to_f
