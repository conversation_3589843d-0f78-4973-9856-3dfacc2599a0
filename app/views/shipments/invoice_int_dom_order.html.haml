.Section1
  -if @order.present?
    -# -designer_order = @shipment.designer_order
    -# -designer = designer_order.designer
  %table{:border => "1", :cellpadding => "0", :cellspacing => "0", style: "font-size:14px;border-collapse:collapse;border:none", :width => "100%"}
    %tr
      %td{colspan: "24", style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
        %p.MsoNormal
          %center
            -if (@order.present?)
              -company_name, shipping_telephone, shipping_address_1,shipping_address_2,_, shipping_pincode,_,_= @order.get_warehouse_shipping_address
              %br
              = "#{company_name},"
              %br
              = "#{shipping_address_1},"
              %br
              = "#{shipping_address_2} - #{shipping_pincode}"
              %br
              = "+91 #{shipping_telephone}"
              %br
              GST no : #{MIRRAW_GST_NUMBER}
      %tr
        %td{colspan: "24", valign: "top"}
          %p.<PERSON><PERSON>al
            "Invoice"
      
      %tr
        %td{colspan: "16", style: "padding:1pt 5.4pt 1pt 5.4pt;height:18.7pt", valign: "top"}
          %span.MsoNormal{style: "font-weight:bold"} Recipient :
          %p.Mso<PERSON>ormal
            = "Name: " + @order.name
            = "Address: " + @order.street + ", " + @order.city + " - " + @order.pincode + ", " + @order.buyer_state + ", " + @order.country
            %br
            = "Phone: " + @order.phone

        %td{colspan: "8", style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
          %div
            %span.MsoNormal{style: "font-weight:bold"} Invoice No
            %span.MsoNormal= @order.get_invoice_number
            %br
            %span.MsoNormal{style: "font-weight:bold"} Invoice Date
            %span.MsoNormal= Date.today.strftime('%Y-%m-%d')
          %div
            %span.MsoNormal{style: "font-weight:bold"} Purchase Order No
            %span.MsoNormal= @order.number
            %br
            %span.MsoNormal{style: "font-weight:bold"} State Code :
            %span.MsoNormal= IN_STATE_CODE[@order.state_code]
            %br
            %span.MsoNormal{style: "font-weight:bold"} Place of Supply :
            %span.MsoNormal= @order.buyer_state

      %tr
        %td{colspan: "13", rowspan: 2, style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
          %p.MsoNormal
            %b Description of  goods

        - ['HSN_CODE', 'Quantity', 'Value (INR)', 'Discount (INR)', 'Net Value (INR)', 'Taxable Value'].each do |title|
          %td{colspan: "1", rowspan: 2, style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
            %p.MsoNormal
              %b=title

        -(titles = invoice_data[:igst_flag] ? ['IGST'] : ['CGST', 'SGST']).each do |title|
          %td{colspan: ((3 - titles.length) * 2).to_s, style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
            %p.MsoNormal
              %b=title

        %td{colspan: "1", rowspan: 2, style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
          %p.MsoNormal
            %b Amount (INR)

      %tr
        -(titles = ('Rate,Amount (INR),' * (invoice_data[:igst_flag] ? 1 : 2)).split(',')).each do |title|
          %td{colspan: (4 - titles.length).to_s, style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
            %p.MsoNormal
              %b=title

      - @items.each do |item|
        %tr{style: "padding-top:10px;"}
          %td{colspan: "13", style: "padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
            %p.MsoNormal= item[:name]

          -[:hsn_code, :quantity, :price, :item_discount, :item_discounted_price, :taxable_value].each do |key|
            %td{colspan: "1", style: "padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal= item[key] || '-'

          -(gst_div = invoice_data[:igst_flag] ? 1 : 2).times do
            %td{colspan: (3 - gst_div).to_s, style: "padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal= (item[:gst_rate] / gst_div.to_f).round(2).nonzero? || '-'

            %td{colspan: (3 - gst_div).to_s, style: "padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
              %p.MsoNormal= (item[:gst_tax] / gst_div.to_f).round(2).nonzero? || '-'

          %td{colspan: "1", style: "text-align:right;padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top", :width => "64"}
            %p.MsoNormal= item[:total_price]

      - 5.times do
        %tr{style: "height:20pt;border:none"}
          %td{colspan: "13", style: "padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
          -6.times do
            %td{colspan: "1", style: "padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
          -(gst_div = invoice_data[:igst_flag] ? 2 : 4).times do
            %td{colspan: ((6 - gst_div)/2).to_s, style: "padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top"}
          %td{colspan: "1", style: "padding:1pt 5.4pt 1pt 5.4pt;border-bottom:none;border-top:none;", valign: "top"}

      %tr{style: "height:3.5pt"}
        %td{style: "padding:1pt 5.4pt 1pt 5.4pt;height:3.5pt;", valign: "top", colspan: 23}
          %p.MsoNormal Total
        %td{style: "text-align:right;padding:1pt 5.4pt 1pt 5.4pt;height:3.5pt;", valign: "top"}
          %p.MsoNormal= invoice_data[:item_total] 

      %tr
        %td{colspan: "17",rowspan: (invoice_data[:igst_flag] ? 5 : 6).to_s, style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
          %p.MsoNormal= "Amount In Words: " + DesignerOrdersHelper.number_to_words(invoice_data[:item_total])
          %p.MsoNormal
            = "Details of Transportation"
            %br
            = "E-way bill no:"
            %br
            = "Date of E-way Bill:"
            %br
            = "Name of Transporter"
            %br
            = "Mode:"
            %br
            = "Vehicle No:"
            %br
            = "Date & Time:"
        %td{colspan: "4", style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
          %p.MsoNormal Total Amount Before Tax
        %td{colspan: "3", style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
          %p.MsoNormal= invoice_data[:item_total] - invoice_data[:gst_total]

      -(titles = invoice_data[:igst_flag] ? ['IGST'] : ['CGST', 'SGST']).each do |title|
        %tr
          %td{colspan: "4", style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
            %p.MsoNormal= "Add: #{title}"
          %td{colspan: "3", style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
            %p.MsoNormal= (invoice_data[:gst_total].to_f / titles.length).round(2)
      %tr
        %td{colspan: "4", style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
          %p.MsoNormal Total Amount
        %td{colspan: "3", style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
          %p.MsoNormal= invoice_data[:item_total]
      %tr
        %td{colspan: "4", style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
          %p.MsoNormal
            GST payable on Reverse Charge
            -if invoice_data[:payable_gst].to_i > 0
              %br
              Tax Amount
        %td{colspan: "3", style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
          %p.MsoNormal
          -if invoice_data[:payable_gst].to_i > 0
            YES
            %br
            =invoice_data[:payable_gst].round(2)
      %tr
        %td{colspan: "24", style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
          %p.MsoNormal
      %tr
        %td{colspan: "16", style: "padding:1pt 5.4pt 1pt 5.4pt;", valign: "top"}
          %p.MsoNormal Declaration:
          %p.MsoNormal We declare that this invoice shows the actual price of the goods described and that all particulars are true and correct.
        %td{colspan: "8", style: "padding:1pt 5.4pt 1pt 5.4pt;height:31.2pt", valign: "top"}
          %p.MsoNormal
            %u{style: "text-underline:double"}= "For #{company_name}"
          %br
          %p.MsoNormal
            %u{style: "text-underline:double"} Signature
        %td{:height => "31", style: "height:31.2pt;border:none", :width => "0"}

-if @order.present?
  - barcode_number = "#{@shipment.number}"
  - barcode_img = Shipment.generate_barcode(barcode_number)
  - company_name, shipping_telephone, shipping_address_1,shipping_address_2, shipping_city, shipping_pincode, shipping_state, _ = @order.get_warehouse_shipping_address
  - order_address = "#{company_name},\n#{shipping_address_1}, #{shipping_address_2}, #{shipping_city}, #{shipping_state}, #{shipping_pincode},\n Phone : +91 #{shipping_telephone}"
  - customer_address = "#{@order.name}\n Phone: #{@order.phone},\n#{@order.street.gsub(/\,+$/,'')}, \n#{@order.city} - #{@order.pincode}, \n#{@order.buyer_state},\n#{@order.country}"
  %table{border: '2', align: 'center', style:'height:10px;width:80%;height:20%;page-break-before:always;'}
    %tr
      %td.td_barcode
        %img.bar_code{src: barcode_img, width: '45%',height:'70%',style:'margin-top:2px;'}
          %h3= barcode_number
    %tr
      %td{colspan: 2}
        %table{border: "2",align: 'center', style: 'border-collapse:collapse;', width: '100%'}
          %tr
            %td.td_style{width: '50%'}
              %h3  TO
              = simple_format(customer_address)
            %td.td_style{width: '50%'}
              %h3  FROM
              = simple_format(order_address)
    %tr
      %td{colspan: 2}
        %p{style:'text-align:center;'}
          "**** If undelivered then return it to shipper *****"


:css
  .td_style {
    font-size:12px;
    padding:10px;
    vertical-align:top;
  }
  .td_barcode
  {
    font-size:12px;
    text-align:center;
    padding:10px;
  }
