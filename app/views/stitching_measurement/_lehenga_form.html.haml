#update_form_stitching
  .main_stitching_form.col-md-12
    #stitching_measurement_details{data: @selected_stitching_measurement.try(:attributes)}
    -name = (@design.try(:designable_type) || params[:type_of_suit]) == 'Lehenga' ? 'Lehenga Choli' : 'Blouse' 
    = hidden_field_tag 'measurement_tag',@selected_stitching_measurement.try(:user_review).try(:[],'measurement_tag')
    = hidden_field_tag 'user_review_id',@selected_stitching_measurement.try(:user_review).try(:[],'review_id')
    #accordion.panel-group
      .panel.panel-default#top_measurements{style: 'width: 100%; display: none;'}
        .panel-heading
          %center
            %h4{style: 'font-weight:bold !important;'}
              Top Measurements : step 2
        %h5
          %center{style: 'color: black;'}
            Click On Image For Help
        .panel-collapse.collapse.in{style: 'margin-top: 20px;'}
          =form_tag nil,id:'top_measurements' do
            .col-md-4.col-md-offset-3
              - front_style_no = @styles_mapping.keys.include?(@selected_stitching_measurement.try(:style_no).to_s) ? @selected_stitching_measurement.try(:style_no) : ''
              =hidden_field_tag :style_no, front_style_no
              =label_tag :style_select, 'Select A Front Style', class: 'stitching_labels', style: 'color: black;'
              %br
              %button.btn.btn-default.btn-xs#style_modal_btn{"data-target" => "#modal_style_no", "data-toggle" => "modal", type: "button",style: 'font-size: 1.5em;margin-bottom:30px;'}
                -if front_style_no.present?
                  = image_tag("stitching/blousefront/#{front_style_no}.jpg", id:"select_style_no", class: "blouse_image blouse_types_#{front_style_no}")
                -else
                  #select_style_no Front Style No For Blouse
              .modal.fade{id: "modal_style_no", role: "dialog", tabindex: "-1", style: 'color:black!important;'}
                .modal-dialog{role: "document",style: 'width:82%;'}
                  .modal-content{style: 'background-color:#faf8f8;'}
                    .modal-header
                      %button.close{"aria-label" => "Close", "data-dismiss" => "modal", type: "button"}
                        %span{"aria-hidden" => "true",style: 'font-size:1.3em;'} &times;
                      %h4.modal-title
                        ="Select Front Style No for #{name} *"
                      %hr
                      .modal-content{style: 'background-color: #918585;'}
                        -@styles_mapping.keys.map(&:to_i).sort.each do |img|
                          %button.style_number_btn{class: ('active_btn' if @selected_stitching_measurement.try(:style_no).to_i == img), "aria-label" => "Close", "data-dismiss" => "modal",type: 'button',data: {img_id: img}}
                            = image_tag("stitching/blousefront/#{img}.jpg", class: "step_image blouse_image", alt: "Blouse Front Style #{img}")
            .col-md-5#back_style_block{style: 'display: none;'}
              =hidden_field_tag :back_style_no,@selected_stitching_measurement.try(:back_style_no)
              =label_tag :style_select, 'Select A Back Style', class: 'stitching_labels', style: 'color: black;'
              %br
              %button.btn.btn-default.btn-xs#style_modal_btn{"data-target" => "#back_modal_style_no", "data-toggle" => "modal", type: "button",style: 'font-size: 1.5em;margin-bottom:30px;'}
                -if @selected_stitching_measurement.try(:back_style_no)
                  = image_tag("stitching/blouseback/#{@selected_stitching_measurement.try(:back_style_no)}.jpg", id: "back_select_style_no", class: "back_blouse_image blouse_back_types_#{@selected_stitching_measurement.try(:back_style_no)}")
                -else
                  #back_select_style_no Back Style No For Blouse
              .modal.fade{id: "back_modal_style_no", role: "dialog", tabindex: "-1", style: 'color:black!important;'}
                .modal-dialog{role: "document",style: 'width:82%;'}
                  .modal-content{style: 'background-color:#faf8f8;'}
                    .modal-header
                      %button.close{"aria-label" => "Close", "data-dismiss" => "modal", type: "button"}
                        %span{"aria-hidden" => "true",style: 'font-size:1.3em;'} &times;
                      %h4.modal-title
                        ="Select Back Style No for #{name} *"
                      %hr
                      .modal-content{style: 'background-color: #918585;'}
                        -(0..21).each do |img|
                          %button.style_number_btn.back_style_number_btn{class: ('active_btn' if @selected_stitching_measurement.try(:back_style_no).to_i == img), id: "back_style_button_#{img}", "aria-label" => "Close", "data-dismiss" => "modal",type: 'button',data: {img_id: img, type: 'back'}}
                            = image_tag("stitching/blouseback/#{img}.jpg", class: "step_image back_blouse_image blouse_back_types_#{img}")
            .card.col-md-12.measurement-experience-toggle{data: {min_measurement_experience: 1}}
              .chest#data_entry_1{data: {entry_id: 1}}
                =hidden_field_tag :chest_size
                .row
                  .col-md-5
                    =label_tag :chest_size_label, "Select Bust Size", class: 'stitching_labels'
                    %span.help_notes Measure the bust size by placing the measurement tape straight on the apex points
                    .chest_size_ul_select.stitching_questions
                  .col-md-5.col-md-offset-1.align_center
                    %a.fancybox{href: image_path('stitching_new_images/bust.jpg')} 
                      =image_tag("stitching_new_images/bust.jpg", :width => "100%", title: 'See How to measure bust size', class: 'stitching_image')

            .card.col-md-12.measurement-experience-toggle{data: {min_measurement_experience: 3}}
              .blouse_length#data_entry_2{data: {entry_id: 2}}
                =hidden_field_tag :blouse_length_size,@selected_stitching_measurement.try(:length)
                .row
                  .col-md-5
                    =label_tag :blouse_length_label, 'Select Blouse Length', class: 'stitching_labels'
                    %span.help_notes Measure length from to point of your shoulder to desired length like shown in the how to measure image.Subject to fabric constraint, we might have to attach another fabric to meet your desired length.
                    .blouse_length_size_ul_select.stitching_questions
                      %ul.list-group
                        -MEASUREMENTS['blouse_length'].each do |blouse_length|
                          %li.blouse_length_size_li.list-group-item{class: ('active_li' if @selected_stitching_measurement.try(:length) == blouse_length[0]),data: {size: blouse_length[0]}}
                            =blouse_length[1]
                  .col-md-5.col-md-offset-1.align_center
                    %a.fancybox{href: image_path('stitching_new_images/blouse_length.jpg')} 
                      =image_tag("stitching_new_images/blouse_length.jpg", :width => "100%", title: 'See How to measure blouse length', class: 'stitching_image')


            .card.col-md-12.measurement-experience-toggle{data: {min_measurement_experience: 2}}
              .under_bust#data_entry_3{data: {entry_id: 3}}
                =hidden_field_tag :under_bust_size
                .row
                  .col-md-5
                    =label_tag :under_bust_label, 'Select Under Bust Size', class: 'stitching_labels'
                    %span.help_notes Measure the bust around by placing the measuring tape below the chest and above the natural waist
                    .under_bust_size_ul_select.stitching_questions
                  .col-md-5.col-md-offset-1.align_center
                    %a.fancybox{href: image_path('stitching_new_images/under_bust.jpg')} 
                      =image_tag("stitching_new_images/under_bust.jpg", :width => "100%", title: 'See How to under bust size', class: 'stitching_image')

            .card.col-md-12.measurement-experience-toggle{data: {min_measurement_experience: 3}}
              .shoulder_to_apex#data_entry_4{data: {entry_id: 4}}
                =hidden_field_tag :shoulder_to_apex_size,@selected_stitching_measurement.try(:shoulder_to_apex)
                .row
                  .col-md-5
                    =label_tag :shoulder_to_apex_label, 'Select Shoulder To Apex Length', class: 'stitching_labels'
                    .shoulder_to_apex_size_ul_select.stitching_questions
                      %ul.list-group
                        -(8..12).each do |shoulder|
                          %li.shoulder_to_apex_size_li.list-group-item{class: ('active_li' if @selected_stitching_measurement.try(:shoulder_to_apex) == shoulder.to_s),data: {size: shoulder}}
                            ="#{shoulder} Inch"
                  .col-lg-5.col-lg-offset-1.align_center
                    %a.fancybox{href: image_path('stitching_new_images/apex.jpg')} 
                      =image_tag("stitching_new_images/apex.jpg", :width => "100%", title: 'See How to measure shoulder to apex size', class: 'stitching_image')

            .card.col-md-12.measurement-experience-toggle{data: {min_measurement_experience: 2}}
              .front_neck#data_entry_5{data: {entry_id: 5}}
                =hidden_field_tag :front_neck_size,@selected_stitching_measurement.try(:front_neck)
                .row
                  .col-md-5
                    =label_tag :front_neck_label, 'Select Front Neck Depth', class: 'stitching_labels'
                    %span.help_notes Measure the front neck by placing the starting point of the measuring tape on one side of the shoulder near the neck till the deep you want
                    .front_neck_size_ul_select.stitching_questions
                      %ul.list-group
                        -MEASUREMENTS['front_neck'].each do |front_neck|
                          %li.front_neck_size_li.list-group-item{class: ('active_li' if @selected_stitching_measurement.try(:front_neck) == front_neck[0]),data: {size: front_neck[0]}}
                            =front_neck[1]
                  .col-md-5.col-md-offset-1.align_center
                    %a.fancybox{href: image_path('stitching_new_images/front_neck.jpg')} 
                      =image_tag("stitching_new_images/front_neck.jpg", :width => "100%", title: 'See How to measure front neck size', class: 'stitching_image')

            .card.col-md-12.measurement-experience-toggle{data: {min_measurement_experience: 2}}
              .back_neck#data_entry_6{data: {entry_id: 6}}
                =hidden_field_tag :back_neck_size,@selected_stitching_measurement.try(:back_neck)
                .row
                  .col-md-5
                    =label_tag :back_neck_label, 'Select Back Neck Depth', class: 'stitching_labels'
                    %span.help_notes Measure the back neck by placing the starting point of the measuring tape on one side of the shoulder near the neck till the deep you want
                    .back_neck_size_ul_select.stitching_questions
                      %ul.list-group
                        -MEASUREMENTS['back_neck'].each do |back_neck|
                          %li.back_neck_size_li.list-group-item{class: ('active_li' if @selected_stitching_measurement.try(:back_neck) == back_neck[0]),data: {size: back_neck[0]}}
                            =back_neck[1]
                  .col-md-5.col-md-offset-1.align_center
                    %a.fancybox{href: image_path('stitching_new_images/back_neck.jpg')} 
                      =image_tag("stitching_new_images/back_neck.jpg", :width => "100%", title: 'See How to measure back neck size', class: 'stitching_image')
            .card.col-md-12.measurement-experience-toggle{data: {min_measurement_experience: 2}}
              .shoulder#data_entry_7{data: {entry_id: 7}}
                =hidden_field_tag :shoulder_size
                .row
                  .col-md-5
                    =label_tag :shoulder_label, 'Select Shoulder Size', class: 'stitching_labels'
                    %span.help_notes Measure the shoulder width by placing the starting point of the measurement tape at the one shoulder bone to the other.
                    .shoulder_size_ul_select.stitching_questions
                  .col-md-5.col-md-offset-1.align_center
                    %a.fancybox{href: image_path('stitching_new_images/shoulder_size.jpg')} 
                      =image_tag("stitching_new_images/shoulder_size.jpg", :width => "100%", title: 'See How to measure shoulder size', class: 'stitching_image')
            .card.col-md-12.measurement-experience-toggle{data: {min_measurement_experience: 3}}
              .size_around_arm_hole#data_entry_8{data: {entry_id: 8}}
                =hidden_field_tag :size_around_arm_hole_size
                .row
                  .col-md-5
                    =label_tag :size_around_arm_hole_label, 'Select Armhole (All Around) length', class: 'stitching_labels'
                    %span.help_notes Measure the armhole around by placing the starting point of the measuring tape on the shoulder bone and round back to the same shoulder bone point. 
                    .size_around_arm_hole_size_ul_select.stitching_questions                
                  .col-md-5.col-md-offset-1.align_center
                    %a.fancybox{href: image_path('stitching_new_images/arm_hole.jpg')} 
                      =image_tag("stitching_new_images/arm_hole.jpg", :width => "100%", title: 'See How to measure size around armhole', class: 'stitching_image')

            .card.col-md-12.measurement-experience-toggle{data: {min_measurement_experience: 3}}
              .sleeves_length#data_entry_9{data: {entry_id: 9}}
                =hidden_field_tag :sleeves_length_size,@selected_stitching_measurement.try(:sleeves_length)
                .row
                  .col-md-5
                    =label_tag :sleeves_length_label, 'Select Sleeves Length', class: 'stitching_labels'
                    %span.help_notes Measure the sleevs length by placing the starting point of the measuring tape on the one shoulder bone and to the wrist of the hands.
                    .sleeves_length_size_ul_select.stitching_questions
                      %ul.list-group
                        -MEASUREMENTS['sleeves_length'].each do |sleeves_length|
                          - class_name = sleeves_length[0] == 'Sleeveless' ? 'sleeveless' : 'sleeves'
                          %li.sleeves_length_size_li.list-group-item{class: [('active_li' if @selected_stitching_measurement.try(:sleeves_length) == sleeves_length[0]), (class_name)] ,data: {size: sleeves_length[0]}}
                            =sleeves_length[1]
                  .col-md-5.col-md-offset-1.align_center
                    %a.fancybox{href: image_path('stitching_new_images/sleeves.jpg')} 
                      =image_tag("stitching_new_images/sleeves.jpg", :width => "100%", title: 'See How to measure sleeves length', class: 'stitching_image')

            .card.col-md-12.measurement-experience-toggle{data: {min_measurement_experience: 3}}
              .sleeves_around#data_entry_10{data: {entry_id: 10}}
                =hidden_field_tag :sleeves_around_size,@selected_stitching_measurement.try(:sleeves_around)
                .row
                  .col-md-5
                    =label_tag :sleeves_around_label, 'Select Sleeve Circumference (All Around) length', class: 'stitching_labels'
                    %span.help_notes Measure the bicep around by placing the measuring tape on the bicep circumference.
                    .sleeves_around_size_ul_select.stitching_questions
                      %ul.list-group
                        -found=false
                        -MEASUREMENTS['sleeves_around'].each do |sleeves_around|
                          %li.sleeves_around_size_li.list-group-item{class: ('active_li' if @selected_stitching_measurement.try(:sleeves_around) == sleeves_around[0] && found=true) ,data: {size: sleeves_around[0]}}
                            =sleeves_around[1]
                        -if @selected_stitching_measurement.present? && found == false
                          %li.sleeves_around_size_li.list-group-item.active_li{data: {size: @selected_stitching_measurement.try(:sleeves_around) }}
                            ="#{@selected_stitching_measurement.try(:sleeves_around)} Inch"
                        %li.list-group-item.sleeves_around_select_other
                          %span.sleeves_around_label_other Other
                          %input.sleeves_around_other-field.other-field.form.form-control{type: 'number',pattern: "\d*", autocomplete: 'off',placeholder: 'Other'}
                          %button.btn.sleeves_around_other-btn.other-btn.btn-success{type: 'button'} &#10004;
                  .col-md-5.col-md-offset-1.align_center
                    %a.fancybox{href: image_path('stitching_new_images/sleeves_around.jpg')} 
                      =image_tag("stitching_new_images/sleeves_around.jpg", :width => "100%", title: 'See How to measure sleeves around size', class: 'stitching_image')
            / -if name == 'Blouse'
            /   .card.col-md-12
            /     .padded#data_entry_11{data: {entry_id: 11}}
            /       =hidden_field_tag :padded_size,@selected_stitching_measurement.try(:padded)
            /       .row
            /         .col-md-5
            /           =label_tag :padded_label, 'Select Yes If Required Padded', class: 'stitching_labels'
            /           .padded_size_ul_select.stitching_questions
            /             %ul.list-group
            /               -%w(Yes No).each do |padded|
            /                 %li.padded_size_li.list-group-item{ class: ('active_li' if @selected_stitching_measurement.try(:padded) == padded) ,data: {size: padded}}= padded
            .card.col-md-12.measurement-experience-toggle{data: {min_measurement_experience: 3}}
              .blouse_opening#data_entry_12{data: {entry_id: 12}}
                =hidden_field_tag :blouse_opening_size,@selected_stitching_measurement.try(:hook)
                .row
                  .col-md-5
                    =label_tag :blouse_opening_label, 'Select Blouse Opening Style', class: 'stitching_labels'
                    %span.help_notes 
                      %br
                      If you go for zip on side then in future you will have problem for alteration.
                      %br
                      We suggest you to go for hooks in front or back.
                      %br
                      Note: For P.C Style Choli(Style No. 15) only Dori Style is possible.
                    .blouse_opening_size_ul_select.stitching_questions
                      %ul.list-group
                        -['Zip On Side','Back Hook','Front Hook', 'String At The Back'].each do |opening_style|
                          - id_name = opening_style == 'Zip On Side' ? 'side' : opening_style.split.first.downcase
                          %li.blouse_opening_size_li.list-group-item{id: id_name + '_link', class: ('active_li' if @selected_stitching_measurement.try(:hook) == opening_style),data: {size: opening_style}}=opening_style      
      
      %button.btn.btn-primary.change_top_measurements{type: 'button'} Go Back
       
      .panel.panel-default#bottom_measurements{style: 'width: 100%; display: none;'}
        .panel-heading
          %center
            %h4{style: 'font-weight:bold !important;'}
              Step 3 : Bottom Measurement
          %h5
            %center
              Click On Image For Help
        .panel-collapse.collapse.in
          .card.col-md-12.measurement-experience-toggle{data: {min_measurement_experience: 3}}
            .denim_waist#data_entry_14{data: {entry_id: 14}}
              =hidden_field_tag :denim_waist_size
              .row
                .col-md-5
                  =label_tag :denim_waist_label, 'Select Denim Waist Size', class: 'stitching_labels'
                  .denim_waist_size_ul_select.stitching_questions
                .col-md-5.col-md-offset-1.align_center
                  %a.fancybox{href: image_path('stitching_new_images/denim_waist.jpg')} 
                    =image_tag("stitching_new_images/denim_waist.jpg", :width => "100%", title: 'See How to measure denim waist size', class: 'stitching_image')

          .card.col-md-12.measurement-experience-toggle{data: {min_measurement_experience: 1}}
            .hip#data_entry_15{data: {entry_id: 15}}
              =hidden_field_tag :hip_size
              .row
                .col-md-5
                  =label_tag :hip_label, 'Select Hip Size', class: 'stitching_labels'
                  %span.help_notes Measure the hip around by placing the measuring tape above the crotch point just on the half of hip.
                  .hip_size_ul_select.stitching_questions
                .col-md-5.col-md-offset-1.align_center
                  %a.fancybox{href: image_path('stitching_new_images/hip.jpg')} 
                    =image_tag("stitching_new_images/hip.jpg", :width => "100%", title: 'See How to measure hip size', class: 'stitching_image')
          
          .card.col-md-12.measurement-experience-toggle{data: {min_measurement_experience: 2}}
            .bottom#data_entry_16{data: {entry_id: 16}}
              =hidden_field_tag :bottom_size
              .row
                .col-md-5
                  =label_tag :bottom_size_label, "Select Bottom Length", class: 'stitching_labels'
                  %span.help_notes Measure the bottom length by placing the starting point of the measuring tape on denim waist straight to the ankle.
                  .bottom_size_ul_select.stitching_questions
                .col-md-5.col-md-offset-1.align_center
                  %a.fancybox{href: image_path('stitching_new_images/bottom_length.jpg')} 
                    =image_tag("stitching_new_images/bottom_length.jpg", :width => "100%", title: 'See How to measure bottom length', class: 'stitching_image')
        
    .row#message_content{style: 'margin-left:-10px;display:none'}
      .col-md-12
        =hidden_field_tag :measurement_repeat_size,'No'
        -if (@quantity - @stitching_measurements.size) != 1
          .measurement_repeat
            .row=label_tag :measurements_count, "You Bought #{@quantity} Quantities of this design. Would you like to use the same measurement for remaining quantity?"
            .row
              .measurement_repeat_size_ul_select.col-md-6
                %ul.list-group
                  %li.measurement_repeat_size_li.list-group-item{data: {size: 'Yes'}} Yes
                  %li.measurement_repeat_size_li.list-group-item.active_li{data: {size: 'No'}} No
        %br
          - display_prop = @quantity == 1 ? '' : 'display:none;color:white;'
          .similar_products{style:display_prop}
            - if @similar_bought_products.present? && @stitching_measurements.size == 0
              = render partial: 'similar_products'
        =label_tag :message_label, 'Enter Notes For Tailor(Max Length 200)'
        =text_area :message_content, nil, size: "5x5",class:'form form-control',maxlength: "200",required: true
      %br
    .row{style: 'margin-top: 20px;'}
      %button#submit_top_measurements.btn.btn-primary.submit_btn Submit Top Measurements
      %br
      %button#submit_all_measurements.btn.btn-primary.submit_btn Submit Measurements
      %br
      %button.change_top_measurements.btn.btn-primary.submit_btn{type: 'button'} Change Top Measurements
    .modal.fade{id: "modal_review", role: "dialog", tabindex: "-1", style: 'color:black!important;'}
      .modal-dialog#review_modal{role: "document"}
        .modal-content{style: 'background-color:white;'}
          .modal-header
            %button.close{"aria-label" => "Close", "data-dismiss" => "modal", type: "button",style: 'color: black;'}
              %span{"aria-hidden" => "true",style: 'color:black;font-size:1.1em;'} &times;
          .modal-body
            .col-md-6.col-md-offset-2
              %h3 Review Your Measurements
              #modal_details
              -if current_account.present?
                %table.table-stripped.table
                  %tr
                    %td Measurement Name*
                    %td= text_field_tag :measurement_name,nil,class: 'form form-control',maxlength: 30
              #submit_measurements_after_review.btn.btn-success.submit_btn{type: 'button'} Submit Measurements
              #change_measurments.btn.btn-success.submit_btn{"aria-label" => "Close", "data-dismiss" => "modal", type: "button"} Go Back
:javascript
  $(document).ready(function(){
    $('[data-toggle="tooltip"]').tooltip(); 
  });
