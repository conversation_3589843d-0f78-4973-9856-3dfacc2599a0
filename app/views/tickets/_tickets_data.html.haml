= javascript_include_tag 'common'
= javascript_include_tag 'tickets'
= stylesheet_link_tag 'tickets'
%table.table.table-bordered{style: 'width:15%;'}
  .search
    .serach_panel
      -questions = Ticket.get_issues('seller')
      - state = ['open','working','reopen','reassign','reject','close']
      -if action_check
        =form_tag tickets_seller_ticket_path, method: :get do
        
          = select_tag :issue_tickets, options_for_select(questions, params[:issue_tickets]), class: 'form-control col-lg-3', prompt: 'Select issue', style:'margin-right: 10px;width:15%;'
          = select_tag :state, options_for_select(state, params[:state]), class: 'form-control col-lg-3', prompt: 'State', style:'margin-right: 10px;width:15%;'
          
          = label_tag :start_date, "Start Date"
          = text_field_tag :start_date, params[:start_date], class: "datepicker start_date", style: 'color:black', autocomplete: 'off'
        
          = label_tag :end_date, "End Date"
          = text_field_tag :end_date, params[:end_date], class: "datepicker end_date", style: 'color:black', autocomplete: 'off'
          = submit_tag 'Submit', class: "btn btn-primary btn-sm submit-data",style:'margin-right: 10px;width:15%;'
          = hidden_field_tag :download_report, nil, id: 'download_report_field'
          %button.btn.export-btn.btn-primary
            %i.fa.fa-chevron-down
            Export Data
      -else
        =form_tag create_ticket_path, method: :get do
          
          = select_tag :state, options_for_select(state, params[:state]), class: 'form-control col-lg-3', prompt: 'State', style:'margin-right: 10px;width:15%;'
          
          = label_tag :start_date, "Start Date"
          = text_field_tag :start_date, params[:start_date], class: "datepicker start_date", style: 'color:black', autocomplete: 'off'
        
          = label_tag :end_date, "End Date"
          = text_field_tag :end_date, params[:end_date], class: "datepicker end_date", style: 'color:black', autocomplete: 'off'
   
          = submit_tag 'Submit', class: "btn btn-primary btn-sm",style:'margin-right: 10px;width:10%;'

  %table.table-bordered.custom-ticket-table
    -if action_check
      %b Open count: #{@open_count}
      %br
      %b Close count: #{@closed_count}
    %thead
      %tr
        %th ID
        %th Created By
        %th Created At
        %th Issue
        %th Message
        %th Order Number
        %th Department
        %th Worked On By
        %th Resolved At
        %th State
        %th TAT
        %th Images
        %th Video
        %th Ageing
        %th Action
  
    -if @tickets.present?
      -@tickets.each do |ticket|
        - account = Account.find ticket.created_by_id
        -order = Order.find ticket.order_id if ticket.order_id.present?
        -tr_class = '#363a09' if ticket.tickets_raised.to_i > 5 || ticket.calls_received.to_i > 5
        -tr_class = '#3a0101' if (ticket.reopened_on.present? ? ((ticket.reopened_on + 2.days) < Time.now)  : ((ticket.created_at + 3.days) < Time.now))
        %tr
          %td= ticket.id
          - if account.designer.present?
            %td= link_to account.designer.name, designer_path(account.designer)
          - else
            %td= account.email
          %td= ticket.created_at.strftime('%Y-%m-%d')
          %td= ticket.issue
          %td.ticket-message= ticket.message
          %td= link_to order.number, order_order_detail_path(order) if order.present?
          %td= ticket.department
          %td= Account.account_name(ticket.worked_by_id) if ticket.worked_by_id.present?
          %td= ticket.resolved_at.strftime('%Y-%m-%d')if ticket.resolved_at.present?
          %td= ticket.state 
          %td= Ticket::TICKET_ISSUES_WITH_TAT[ticket.issue]
          %td.ticket-message
            - if ticket.images.present?
              -urls = ticket.images.gsub('"','')
              -cleaned_data = urls[1..-2].split(', ')
              - cleaned_data.each do |image|
                %a{href: image, target: "_blank"} #{image}
                %br
                %br
          %td.ticket-message
            %a{href: ticket.video} #{ticket.video}
          %td= (Date.current - ticket.created_at.to_date).to_i
          -if action_check
            %td
              %button.btn.btn-default.btn-xs{"data-target" => "#ticket_resolve_#{ticket.id}_#{tab}", "data-toggle" => 'modal', style: 'margin-top:10px;padding:5px;'} Resolve/Reject Issue
              .modal.fade{id: "ticket_resolve_#{ticket.id}_#{tab}", role: "dialog", tabindex: "-1", style: 'color:black!important;'}
                .modal-dialog{role: 'document'}
                  .modal-content{style: 'width: 720px'}
                    .modal-header
                      %button.close{"aria-label" => "Close", "data-dismiss" => "modal"}
                        %span{"aria-hidden" => "true"} &times;
                      %h4.modal-title Resolve/Reject Issue
                    .modal-body
                      =form_tag tickets_ticket_resolve_path do
                        =hidden_field_tag :ticket_id,ticket.id
                        .row{style: 'margin-top: 20px;'}
                          .col-md-6.col-md-offset-3
                            .form-group.panel{style:'background-color: darkgrey'}
                              .panel-body
                                = hidden_field :ticket_identifier, value: 'seller'
                                = hidden_field :department, value: 'seller'
                                %br
                                =label_tag :select_action
                                %br
                                =radio_button_tag :action_ticket, 'Reject',class: 'form form-control',checked: 'false'
                                =label_tag :action_reject, 'Reject'
                                %br
                                =radio_button_tag :action_ticket, 'Resolve',class: 'form form-control',checked: 'checked'
                                =label_tag :action_resolve, 'Resolve'
                                %br
                            =label_tag :message
                            =text_area :message,nil, size: '5x5',class:'form form-control',maxlength: '1000',required: true
                            %br
                            =submit_tag :submit,disable_with: 'Please Wait....',class: 'form form-control btn btn-success'
              -unless ticket.working?
                =link_to 'Working On',tickets_ticket_working_on_path(ticket_id: ticket.id),class: 'btn btn-sm btn-success',style: 'margin: 10px 0px;'
              %br
              %button.btn.btn-default.btn-xs{"data-target" => "#ticket_reassign_#{ticket.id}_#{tab}", "data-toggle" => 'modal', style: 'margin-top:10px;padding:5px;'} Reassign
              .modal.fade{id: "ticket_reassign_#{ticket.id}_#{tab}", role: "dialog", tabindex: "-1", style: 'color:black!important;'}
                .modal-dialog{role: 'document'}
                  .modal-content{style: 'width: 720px'}
                    .modal-header
                      %button.close{"aria-label" => "Close", "data-dismiss" => "modal"}
                        %span{"aria-hidden" => "true"} &times;
                      %h4.modal-title Reassign
                    .modal-body
                      =form_tag tickets_reassign_path do
                        =hidden_field_tag :ticket_id,ticket.id
                        =label_tag :message
                        =text_area :message,nil, size: '2x2',class:'form form-control',maxlength: '1000',required: true
                        %br
                        = label_tag :reassign_to
                        = text_field_tag :reassign_to, nil, class: 'form form-control', required: true, placeholder: "<EMAIL>, <EMAIL>", pattern: "^([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})(, *[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})*$"
                        %br
                        =submit_tag :submit,disable_with: 'Please Wait....',class: 'form form-control btn btn-success'
          -else
            %td 
              -if (ticket.state == 'close' || ticket.state == 'reject')  && Time.now <= ticket.resolved_at + 48.hours
                =link_to 'Reopen Ticket','#',class: 'btn btn-warning ticket_id',id: "ticket_reopen_#{ticket.id}", data: {id: ticket.id}, "data-target" => "#ticket_notes_modal", "data-toggle" => "modal"

    %nav.fix_paginate{style: "text-align: center;"}
      = will_paginate @tickets

.modal.fade{id: "ticket_notes_modal", role: "dialog", tabindex: "-1", style: 'color:black!important;', 'data-keyboard' => 'false' , 'data-backdrop' => 'static'}
  .modal-dialog{:role => "document",style: 'width:70%'}
    .modal-content{style: "width: 50%;margin-left: 25%;"}
      .modal-header
        %button.close{"aria-label" => "Close", "data-dismiss" => "modal", :type => "button"}
          %span{"aria-hidden" => "true"} &times;
        %h4#ticketModalLabel.modal-title Ticket Notes 
      .modal-body
        #ticket_notice_message.alert.alert-info{style:'display:none'}
        %span.glyphicon.glyphicon-arrow-left.pull-left.back_arrow{style: 'font-size: 30px;margin-bottom: 20px; display:none; color: #39b3d7'}
        = form_tag '', class: 'ticket_notes_form form-group' do
          = hidden_field_tag 'ticket_id', ''
          %br
          = text_area_tag 'add_notes', '', class: 'form-control'
          %br
          = submit_tag 'Add note and reopen ticket',id: 'add_new_notes', style: 'color:black;', class: 'form-control btn btn-info'

:javascript
  $(document).ready(function() {
    MR.tickets.init();
  });
