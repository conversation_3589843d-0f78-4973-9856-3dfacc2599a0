= javascript_include_tag 'rtv_shipment'
#rtv_shipment.modal.fade{:role => "dialog"}
  .modal-dialog
    .modal-content{style: 'color:black!important;width: 600px;margin-left: 0px;'}
      .modal-header
        %button.close{:type => 'button',data: {dismiss: 'modal'}} &#215;
        %h4.modal-title Create Warehouse RTV
      .modal-body
        =form_tag create_rtv_path(@warehouse_order.id), method: :post, class: 'form-horizontal', remote: true, id: 'rtv_form' do
          = hidden_field_tag :designer_pincode, @warehouse_order.try(:designer).try(:pincode)
          #notice.alert.alert-info{style: 'display:none;'}
          - @warehouse_order.warehouse_line_items.sort_by(&:id).each do |warehouse_line_item|
            .form-group
              = label_tag "rtv_quantity_#{warehouse_line_item.id}", rtv_data[warehouse_line_item.id][:title], class: 'control-label col-sm-5'
              .col-sm-7
                = number_field_tag "rtv_quantity_#{warehouse_line_item.id}", nil, id: "warehouse_rtv_#{warehouse_line_item.id}", class: 'form-control rtv-quantity', placeholder: 'RTV quantity', max: warehouse_line_item.present_quantity, min: 1, autofocus: true

          .form-group.row
            =label_tag :shipper_name, nil, class: 'control-label col-sm-5'
            .col-sm-7= select_tag :shipper_name, options_for_select(@shippers.keys, params[:shipper_name]), class: 'form-control courier_name_select'

          .form-group.row.courier_tracking_details{style: 'display:none'}
            =label_tag :awb_number, 'AWB number', class: 'control-label col-sm-5'
            .col-sm-7=text_field_tag :number, '', class: 'form-control awb_number', min: 1

          .form-group.row.courier_tracking_details{style: 'display:none'}
            =label_tag :courier_name, 'courier Name', class: 'control-label col-sm-5'
            .col-sm-7=text_field_tag :courier_name, '', class: 'form-control courier_name'

          .form-group.row
            =label_tag :weight, nil, class: 'control-label col-sm-5'
            .col-sm-7=number_field_tag :weight, 1, class: 'form-control', min: 1
          
          .form-group.row
            =label_tag :reason, nil, class: 'control-label col-sm-5'
            .col-sm-7=select_tag :reason, options_for_select(WarehouseOrder::RTV_REASONS), class: 'form-control'
          
          .form-group.row
            =label_tag :weight, 'Reference No', class: 'control-label col-sm-2'
            .col-sm-10= text_field_tag :reference_number, nil, placeholder: 'Enter Reference Number :',required: true, class: 'form-control'
          
          .form-group
            .col-sm-5
            .col-sm-7=submit_tag 'Submit', class: 'btn btn-primary', data:{disable_with: 'please wait'}
      .modal-footer
        %button.btn.btn-default{"data-dismiss" => "modal", :type => "button"} Close