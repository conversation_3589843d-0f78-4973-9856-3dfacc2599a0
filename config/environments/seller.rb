#require 'rack/ssl'
#require 'rack/ssl-enforcer'
Rails.application.configure do
  # Settings specified here will take precedence over those in config/application.rb

  # Code is not reloaded between requests
  config.cache_classes = true

  # Do not eager load code on boot. This avoids loading your whole application
  # just for the purpose of running a single test. If you are using a tool that
  # preloads Rails for running tests, you may have to set it to true.
  config.eager_load = true

  # Full error reports are disabled and caching is turned on
  config.consider_all_requests_local       = false
  config.action_controller.perform_caching = true

  # Disable Rails's static asset server (Apache or nginx will already do this)
  config.serve_static_files = true

  # Compress JavaScripts and CSS
  # config.assets.compress = true
  # Specify the default JavaScript compressor
  config.assets.js_compressor = :uglifier
  config.assets.css_compressor = :sass

  # Do not fallback to assets pipeline if a precompiled asset is missed.
  # config.assets.compile = false

  # Asset digests allow you to set far-future HTTP expiration dates on all assets,
  # yet still be able to expire them through the digest params.
  config.assets.digest = true
  
  # Add HTTP headers to cache static assets for an hour
  config.static_cache_control = "public, max-age=315576000"

  # avoid redirect from http to https
  config.partial_protocol = 'https'

  # config.pay_with_amazon.merge!(widget_url: "https://static-eu.payments-amazon.com/cba/js/in/PaymentWidgets.js",return_url: "http://www.mirraw.com/order/amazon_success",cancel_url: "http://www.mirraw.com/orders/new")

  config.action_mailer.delivery_method = :smtp
  
  # Specifies the header that your server uses for sending files
  # (comment out if your front-end server doesn't support this)
  config.action_dispatch.x_sendfile_header = nil # Use 'X-Accel-Redirect' for nginx
  config.action_mailer.default_url_options = { :host => "seller.mirraw.com" }
 
  if ENV['AWS_SES'].to_i == 1
    ActionMailer::Base.smtp_settings = {
      :address        => "email-smtp.us-east-1.amazonaws.com",
      :port           => 587,
      :authentication => :plain,
      :user_name      => ENV['SES_USERNAME'],
      :password       => ENV['SES_PASSWORD'],
      :domain         => ENV['SES_DOMAIN'],
      :enable_starttls_auto => true
    }
  else
    ActionMailer::Base.smtp_settings = {
      :address              => "smtp.sendgrid.net",
      :port                 => 587,
      :domain               => "mirraw.com",
      :user_name            => ENV['SENDGRID_USERNAME'],
      :password             => ENV['SENDGRID_PASSWORD'],
      :authentication       => "plain",
      :enable_starttls_auto => true
    }
  end
  # Force all access to the app over SSL, use Strict-Transport-Security, and use secure cookies.
  # config.force_ssl = true

  # See everything in the log (default is :info)
  # config.log_level = :debug

  # Use a different logger for distributed setups
  # config.logger = SyslogLogger.new

  # Use a different cache store in production
  # RACK CACHE
  # REF https://devcenter.heroku.com/articles/rack-cache-memcached-rails31#configure-rails-cache-store
  client = Dalli::Client.new((ENV["MEMCACHIER_SERVERS"] || "").split(","),
                              :username => ENV["MEMCACHIER_USERNAME"],
                              :password => ENV["MEMCACHIER_PASSWORD"],
                              :failover => true,
                              :socket_timeout => 1.5,
                              :socket_failure_delay => 0.2,
                              :value_max_bytes => 10485760)

  config.action_dispatch.rack_cache = {
    :metastore    => client,
    :entitystore  => client
  }

  # DO NOT TOUCH THIS
  config.cache_store = :dalli_store,
                    (ENV["MEMCACHIER_SERVERS"] || "").split(","),
                    {:username => ENV["MEMCACHIER_USERNAME"],
                     :password => ENV["MEMCACHIER_PASSWORD"],
                     :failover => true,
                     :socket_timeout => 1.5,
                     :socket_failure_delay => 0.2,
                     :pool_size => ENV["MEMCACHIER_POOL_SIZE"] || 5
                    }  

  # Enable serving of images, stylesheets, and JavaScripts from an asset server
  config.action_controller.asset_host = Proc.new { |source, request = nil|
    if ENV['ASSETS_HOST'].present?
      ENV['ASSETS_HOST']
    elsif request.blank?
      ASSETS_PROTOCOL + '//deqjvj8xmhubl.cloudfront.net'
    elsif request.ssl?
      'https://deqjvj8xmhubl.cloudfront.net'
    else
      'http://static1.mirraw.com'
    end
  }

  # Precompile additional assets (application.js, application.css, and all non-JS/CSS are already added)
  # config.assets.precompile += Ckeditor.assets
  config.assets.precompile += %w( seller.css seller.js seller_style.js dynamic_size_charts.js coupon.js designs.js designers_show_edit.js chartkick.js  designer_collection.css designer_orders.js gauge/gauge.min.js gauge/gauge_demo.js)
  # config.autoload_paths += %W(#{config.root}/app/models/ckeditor)
  config.assets.digest = true
  config.assets.initialize_on_precompile = false 
  Sunspot.config.solr.url = ENV['WEBSOLR_URL']
  # Disable delivery errors, bad email addresses will be ignored
  # config.action_mailer.raise_delivery_errors = false
  # config.action_mailer.delivery_method = :smtp

  # Enable threaded mode
  # config.threadsafe!

  # Enable locale fallbacks for I18n (makes lookups for any locale fall back to
  # the I18n.default_locale when a translation can not be found)
  config.i18n.fallbacks = true

  # Send deprecation notices to registered listeners
  config.active_support.deprecation = :notify 
  
  config.fb_app_id = '234215623324080'
  config.fb_app_secret = '6a68fe85eef383d3131f2651fb3776db'
  config.fb_namespace = 'mirraw-com'
  config.fb_channel_url_base = 'www.mirraw.com'

  #google analytics and optimize credentials
  config.google_optimize = 'GTM-5B939TJ'
  config.google_analytics_url = 'staging-mirraw.herokuapp.com'

  config.mixpanel = '12a9a15d5c86594ee2c6cad9c66086a4'
  config.bcc_email = '<EMAIL>'
  
  config.gharpay_username = 'mirraw_api'
  config.gharpay_password = '0u4x3hnb'
  config.gharpay_baseurl = 'http://webservices.gharpay.in'
  
  config.delhivery_baseurl = 'https://track.delhivery.com'
  config.delhivery_token = 'ee64db0a4b9c2db22e1751f4f4b22204e0649028'
  config.delhivery_surface_token ='de733df2c6dd3c8a0f16fef509607a55951fe795'
  config.delhivery_surface_client_name = 'Mirraw Surface-BOM-Mirraw Online Services Pvt Ltd'

  config.googlemap_api = ENV['GOOGLEMAP_API']

  config.google_authsub_secure = true
  config.google_authsub_session = false

  config.zipdial_cod_z2vtoken = '6f5336b6187683ed68b5ad04b3a691ffc4b5b28f'
  config.delhivery_cod_token = '7859be64a22d165525787183bb4e0794a647cd7d'

  config.ccavenue_mpcg_url = 'https://secure.ccavenue.com'
  config.ccavenue_mpcg_access_code = 'AVIL01BF69AS89LISA'
  config.ccavenue_mpcg_enc_key = '8EC1A28BA6B615A94C7117912C10959B'

  #config.middleware.insert_before 0, Rack::SSL, :exclude => proc { |env| env['HTTPS'] != 'on' }
  #config.middleware.insert_before 0, Rack::SslEnforcer, :only => ['/orders/new', '/accounts/sign_in', '/accounts/sign_up', '/accounts','/accounts/forgot_password','/sellers/sign_in'], :ignore => ['/order/ccavenue_mpcg_response', '/order/paytm_response', '/order/response', '/order/paypal_response', '/order/cod_verify', '/orders', '/pages/checkgharpay', '/api/cod', '/api/get_cod_charge' ,'/api/cod_cbd', '/api/check_free_shipping', '/carts/save_email', '/carts/generate_otp', '/carts/verify_otp', %r{/country}, %r{/assets}, '/order/payu_response','/order/amazon_response'], :only_hosts => ['seller.mirraw.com', 'sellercentral.mirraw.com', 'secure.ccavenue.com', 'ajax.googleapis.com'], :strict => true, :force_secure_cookies => false
  #config.middleware.insert_before 0, Rack::SslEnforcer,
  #  strict: true,
  #  force_secure_cookies: false
  # config.dhl = {
  #   :site_id=> 'mirrawonline',
  #   :password=> 'yPcIi0fFXY',
  #   :account_number=> '*********'
  # }
  # config.skynet_baseurl = 'http://www.skynetworldwide.in/api'
  # config.skynet_credentials = {
  #   'username' => '504mir',
  #   'password' => Base64.encode64('Mirraw123!@#')
  # }

  # #paytm configuration
  # config.paytm = {
  #   paytm_merchant_key: ENV['PAYTM_MERCHANT_KEY'],
  #   website: 'MirrawonlineWEB',
  #   mid: ENV['PAYTM_MID'],
  #   industry_type_id: 'Retail105',
  #   channel_id: 'WEB',
  #   payment_url: 'https://secure.paytm.in/oltp-web/processTransaction',
  #   callback_url: 'https://www.mirraw.com/order/paytm_response'
  # }

  #vendor App Cors allowed origins
  config.middleware.insert_before 0, "Rack::Cors" do
    allow do
      origins 'http://localhost'
      resource '*', headers: :any, methods: [:get, :post, :put, :patch],
      expose: ['access-token', 'expiry', 'token-type', 'uid', 'client']
    end
  end

  config.lograge.enabled = true
  config.lograge.formatter = Lograge::Formatters::Json.new
  config.lograge.custom_options = lambda do |event|
    {
      host: event.payload[:host],
      remote_ip: event.payload[:remote_ip],
      ip: event.payload[:ip],
      x_forwarded_for: event.payload[:x_forwarded_for],
      true_client_ip: event.payload[:true_client_ip],
      remote_addr: event.payload[:remote_addr],
      http_true_client_ip: event.payload[:http_true_client_ip],
      user_agent: event.payload[:user_agent],    
    }.compact
  end
end
