#require 'rack/ssl'
require 'rack/ssl-enforcer'
Rails.application.configure do
  # Settings specified here will take precedence over those in config/application.rb

  # Code is not reloaded between requests
  config.cache_classes = true

  # Do not eager load code on boot. This avoids loading your whole application
  # just for the purpose of running a single test. If you are using a tool that
  # preloads Rails for running tests, you may have to set it to true.
  config.eager_load = true

  # Full error reports are disabled and caching is turned on
  config.consider_all_requests_local       = false
  config.action_controller.perform_caching = true

  # Disable Rails's static asset server (Apache or nginx will already do this)
  config.serve_static_files = true

  # Compress JavaScripts and CSS
  # config.assets.compress = true
  # Specify the default JavaScript compressor
  config.assets.js_compressor  = :uglifier
  config.assets.css_compressor = :sass

  # Enable serving of images, stylesheets, and JavaScripts from an asset server
  config.action_mailer.asset_host = 'https://beta.mirraw.com'

  # avoid redirect from http to https
  config.partial_protocol = 'https'

  # Add HTTP headers to cache static assets for an hour
  config.static_cache_control = "public, max-age=604800"


  # Do not fallback to assets pipeline if a precompiled asset is missed.
  # config.assets.compile = false
  
  config.action_mailer.delivery_method = :smtp
  # Specifies the header that your server uses for sending files
  # (comment out if your front-end server doesn't support this)
  config.action_dispatch.x_sendfile_header = nil # Use 'X-Accel-Redirect' for nginx
  config.action_mailer.default_url_options = { :host => "staging-mirraw.herokuapp.com" }
  # ActionMailer::Base.smtp_settings = {
  #   :address        => "smtp.sendgrid.net",
  #   :port           => "25",
  #   :authentication => :plain,
  #   :user_name      => ENV['SENDGRID_USERNAME'],
  #   :password       => ENV['SENDGRID_PASSWORD'],
  #   :domain         => ENV['SENDGRID_DOMAIN']
  # }
  if ENV['AWS_SES'].to_i == 1
    ActionMailer::Base.smtp_settings = {
      :address        => "email-smtp.us-east-1.amazonaws.com",
      :port           => 587,
      :authentication => :plain,
      :user_name      => ENV['SES_USERNAME'],
      :password       => ENV['SES_PASSWORD'],
      :domain         => ENV['SES_DOMAIN'],
      :enable_starttls_auto => true
    }
  else
    ActionMailer::Base.smtp_settings = {
      :address              => "smtp.gmail.com",
      :port                 => 587,
      :domain               => "mirraw.com",
      :user_name            => "aneasy2forgetid",
      :password             => "shail123",
      :authentication       => "plain",
      :enable_starttls_auto => true
    }
  end
  # Force all access to the app over SSL, use Strict-Transport-Security, and use secure cookies.
  config.force_ssl = false
  # config.use_ssl = true

  # See everything in the log (default is :info)
  config.log_level = :debug

  # Use a different logger for distributed setups
  # config.logger = SyslogLogger.new
  config.log_formatter = ::Logger::Formatter.new

  # Use a different cache store in production
  config.cache_store = :dalli_store,{:pool_size => 5}

  $cache = Dalli::Client.new

  # Enable Rack::Cache
  config.middleware.use Rack::Cache,
     :metastore => $cache,
     :entitystore => 'file:tmp/cache/entity'


  # Enable serving of images, stylesheets, and JavaScripts from an asset server
  # config.action_controller.asset_host = 'https://static1.mirraw.com'
  config.action_controller.asset_host = Proc.new { |source, request = nil|
    if ENV['ASSETS_HOST'].present?
      ENV['ASSETS_HOST']
    elsif request.blank?
      ASSETS_PROTOCOL + '//d3oh1boanmoilz.cloudfront.net'
    elsif request.ssl?
      'https://d3oh1boanmoilz.cloudfront.net'
    else
      'http://static1.mirraw.com'
    end
  }

  # Precompile additional assets (application.js, application.css, and all non-JS/CSS are already added)
  # config.assets.precompile += %w( search.js )
  config.assets.precompile += Ckeditor.assets if CKEDITOR_ENABLED
  config.assets.precompile += %w(blueprint/ie.css material-custom.css jquery.fancybox.css jquery.fancybox-buttons.css jquery.fancybox-thumbs.css admin.css sessions.css admin_designers.css flatpickr.min.css coupons.css designer_collections.css product_analytics.css designers.css review-page.css 404.css fashion_updates.css horoscope.css admin_style.css mobile_menu.css rack_list.css eid_page.css pages.css awesomplete.css landing.css banner-slider.css infinite_scroll.css returns.css reviews.css stitching.css surveys.css carousel.css intlTelInput.css alertify.core.css alertify.bootstrap3.css alertify.default.css rakhi.css sitemaps.css)
  config.assets.precompile += %w(flatpickr.min.js coupons.js qc_fail_images.js jquery.raty.js fashion_updates.js app_admin.js alertify.min.js admin.js order_qc_check.js rack_check.js order_unpacking.js fancyzoom.min.js jquery.fancybox.js jquery.fancybox-buttons.js jquery.fancybox-thumbs.js awesomplete.js jquery-rakhi.js custom-rakhi.js pages.js jquery.bxslider.min.js banner-slider.js infinite_scroll.js carousel.js intlTelInput.min.js rack_list.js returns.js jquery.validate.min.js stitching.js designs.js jquery.sticky-kit.min.js store.js catalog_view.js store_full_image.js surveys.js tailoring_info.js bootstrap.js store_new.js)
  config.assets.precompile += %w(ckeditor_js.js chartkick.js FileSaver.min.js tableexport.min.js web_cart.js cart_mobile.js designer_orders.js designers_show_edit.js designers.js product_view.js design_resize.js invitation.js jquery-critical.js jquery.min.js application_new.js subscriptions.js application.js order_reports.js googlemaps_places.js shipments.js orders.js package_management.js image_slider.js respond.min.js banner-1.3.min.js reviews.js survey_question.js user.js )
  config.assets.precompile += %w(critcal_inline.css application_critical.css designs1.css application_new.css application_new.js ckeditor_js.js bootstrap-select.min.js bootstrap-select.min.css dynamic_landing_pages.css dynamic_landing_pages.js )
  config.assets.precompile += %w(seller.css seller.js seller_style.js dynamic_size_charts.js coupon.js gauge/gauge.min.js gauge/gauge_demo.js datatables/jquery.dataTables.min.js datatables/dataTables.bootstrap.min.js dataTables.bootstrap.min.css vendor_performance.js vendor_performance.css)
  config.autoload_paths += %W(#{config.root}/app/models/ckeditor) if CKEDITOR_ENABLED
  config.assets.digest = true
  config.assets.initialize_on_precompile = false
  Rails.application.config.assets.precompile += %w( order.js )

  # Disable delivery errors, bad email addresses will be ignored
  # config.action_mailer.raise_delivery_errors = false
  # config.action_mailer.delivery_method = :smtp

  # Enable threaded mode
  # config.threadsafe!

  # Enable locale fallbacks for I18n (makes lookups for any locale fall back to
  # the I18n.default_locale when a translation can not be found)
  config.i18n.fallbacks = true

  # Send deprecation notices to registered listeners
  config.active_support.deprecation = :notify

  config.fb_app_id = '411869898824383'
  config.fb_app_secret = '70845d1291c62c6f1e36cf09dce65c36'
  config.fb_namespace = 'mirraw-staging'
  config.fb_channel_url_base = 'staging-mirraw.herokuapp.com'

  #google analytics and optimize credentials
  config.google_optimize = 'GTM-5B939TJ'
  config.google_analytics_url = 'staging-mirraw.herokuapp.com'
  
  config.mixpanel = '********************************'
  config.bcc_email = '<EMAIL>'

  config.google_authsub_secure = true
  config.delhivery_baseurl = 'https://test.delhivery.com'
  config.delhivery_cod_token = ENV["DELHIVERY_COD_TOKEN"] || '7859be64a22d165525787183bb4e0794a647cd7d'
  config.delhivery_surface_token = 'f80ffd17df2870e7df98b9725e6f918b2e242fef'
  config.delhivery_surface_client_name = 'MIRRAWSURFACE'
  config.google_authsub_session = true

  config.googlemap_api = ENV['GOOGLEMAP_API'] || 'AIzaSyBGZLQ1ZdG2oUdR9EZzoXPOOL-m_DmhyEQ'
  #config.middleware.insert_before 0, Rack::SSL, :exclude => proc { |env| env['HTTPS'] != 'on' }
  #config.middleware.insert_before 0, Rack::SslEnforcer, :only => ['/orders/new', '/accounts/sign_in', '/accounts/sign_up','/accounts','/accounts/forgot_password','/sellers/sign_in'], :ignore => ['/order/ccavenue_mpcg_response', '/order/paytm_response', '/order/response', '/order/paypal_response', '/order/amazon_response', '/order/cod_verify', '/orders', '/pages/checkgharpay', '/api/cod','/api/get_cod_charge' , '/api/pincode_info', '/api/cod_cbd', '/api/check_free_shipping', '/api/international_min_cart_value', '/carts/save_email', '/carts/generate_otp', '/carts/verify_otp', %r{/country}, %r{/assets}, '/carts/apply_wallet_discount'], :only_hosts => ['staging-mirraw.herokuapp.com', 'secure.ccavenue.com', 'ajax.googleapis.com', 'tochigi-5848.herokussl.com'], :strict => true, :force_secure_cookies => false
  config.middleware.insert_before 0, Rack::SslEnforcer,
    strict: true,
    force_secure_cookies: false

  config.middleware.insert_before 0, "Rack::Cors" do
    allow do
      origins '*'
      resource '*', headers: :any, methods: [:get, :post, :put, :patch],
      expose: ['access-token', 'expiry', 'token-type', 'uid', 'client']
    end
  end

  config.dhl = {
    :site_id=> 'Shubham',
    :password=> 'Kzx2ORrveI',
    :account_number=> '*********'
  }
  config.image_fingerprint_app = {url: ENV.fetch('IMAGE_SEARCH_URL')  }
  config.skynet_baseurl = 'http://www.skynet.dev.canbrand.in/api'
  config.skynet_credentials = {
    'username' => 'testac',
    'password' => Base64.encode64('test123')
  }
  if ENV["RAILS_LOG_TO_STDOUT"].present?
    logger           = ActiveSupport::Logger.new(STDOUT)
    logger.formatter = config.log_formatter
    config.logger = ActiveSupport::TaggedLogging.new(logger)
  end
  MailSafe::Config.internal_address_definition = /.*mirraw\.com/i
  MailSafe::Config.replacement_address = ENV['STAGING_EMAIL_ADDRESS']
  MIRRAW_DESKTOP_DOMAIN = 'https://staging-mirraw.herokuapp.com'

  config.lograge.enabled = true
  config.lograge.formatter = Lograge::Formatters::Json.new
  config.lograge.custom_options = lambda do |event|
    {
        host: event.payload[:host],
        remote_ip: event.payload[:remote_ip],
        ip: event.payload[:ip],
        x_forwarded_for: event.payload[:x_forwarded_for],
        true_client_ip: event.payload[:true_client_ip],
        remote_addr: event.payload[:remote_addr],
        http_true_client_ip: event.payload[:http_true_client_ip],
        user_agent: event.payload[:user_agent],
    }.compact
  end

end
