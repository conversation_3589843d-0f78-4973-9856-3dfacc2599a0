# config/initializers/exception_notification.rb

require 'exception_notification'


Rails.application.config.middleware.use ExceptionNotification::Rack,
  ignore_crawlers: %w{Googlebot bingbot},
  error_grouping: ENV['GROUP_NOTIFIER_ERROR'] == 'true',                    # Enable error grouping
  error_grouping_period: ENV['NOTIFIER_GROUP_PERIOD'].to_i.minutes || 15.minutes,  # Default to 15 minutes if env var isn't set
  error_grouping_cache: Rails.cache,         # Use Rails.cache (e.g., Memcached, Redis)
  email: {
    email_prefix: "Prod Exception - Desktop - #{Rails.env.upcase} ",
    sender_address: %{"notifier" <<EMAIL>>},
    exception_recipients: %w{<EMAIL>}
  }