if Rails.env.production? || Rails.env.admin?
    class OverrideMailRecipient
      def self.delivering_email(mail)                
        mail.perform_deliveries = [mail.to].flatten.select{| email | email.include?("@mailbus.ml")}.blank?
        mail.perform_deliveries = [mail.to].flatten.select{| email | email.include?("@privaterelay.appleid.com")}.blank?
      end
    end
    ActionMailer::Base.register_interceptor(OverrideMailRecipient)
end