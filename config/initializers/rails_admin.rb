RailsAdmin.config do |config|
  config.current_user_method &:current_account
  config.parent_controller = 'ApplicationController'
  config.actions do
    dashboard do
      statistics false
    end

    index
    new do
      except ['PublicSystemConstant','VersionFailedDesign','Stylist']
    end
    export

    show
    edit do
      except  ['VersionFailedDesign', 'FashionUpdate','AdditionalPayment','Wallet', 'LineItem', 'User']
    end
    delete do
      except ['Adjustment', 'Order', 'Design', 'DesignerOrder', 'LineItem', 'PublicSystemConstant','VersionFailedDesign', "Account", "Designer", "User", "Image","Coupon", "Wallet",'Return','AdditionalPayment','Stylist', 'AutomatedNotificationAudience', 'Variant', 'Shipper', 'Shipment', 'RackList', 'DesignerIssue', 'Courier', 'PaymentOption']
    end
  end
  ActiveSupport::Inflector.inflections(:en) do |inflect|
    inflect.acronym 'FAQ'
  end
  config.included_models = ["FAQ", "PopularLink",'PerformanceMetricValue', "Adjustment",
  "Announcement","Coupon", "User", "Account","SeoList", "LuxeSeoList","Frontpage", "Feature","Order",
  "DesignerOrder", "Designer", "Design", "Image", "Category", "LineItem", "CurrencyConvert",
  "Cart", "Account", "Property", "PropertyValue", "Shipment", "SuperMenu", "Menu", "MenuItem",
  "MenuColumn", "Saree", "SalwarKameez", "Lehenga", "Jewellery", "Bag", "Pickup", "Courier",
  "OptionType", "OptionTypeValue", "Variant", "Country", "State", "Collection", 
  "DesignerIssue", "Return", "LineItemAddon", "AddonTypeValue", 'AddonOptionType', 
  "BannerSlider", "Landing", "Block", "DesignerShipper", "Shipper", "Blacklist", "Board", 
  "AddonTypeValueGroup", "RackList", "Error", "SellerFollowup", "OosAlert", "RedirectRule",
  'VendorPayout','VendorPayoutBatch','Subscription', 'UnbxdContainer', 'UnbxdWidget', 
  'ShipmentCharge','ShipperFuelPrice','FailedDesign','VersionFailedDesign','DesignerBatch','AwbNumber',
  'CategoryBanner','Notification','CategoryNewArrival', 'Referral', 'Referrer', 
  'ReferredPerson', 'TailoringInfo', 'TailorDailyMetric' ,'Survey', 'PublicSystemConstant', 'TagSlider', 
  'StoryCollection', 'CategoryConnection', 'Tailor','SupportText', 'DesignerCollection',
  'OptionalPickup', 'NavTab', 'NavBlock', 'Widget', 'CategoryAlbum', 'FbPage', 
  'NewsletterBanner', 'FashionUpdate', 'FashionUpdateCategory','Ticket','AdditionalPayment', 
  'SurveyQuestion','Consumable', 'Other', 'Kurta', 'HomeDecor', 'Jacket', 'Kurti', 'Turban', 
  'MetricDefinition', 'DynamicTemplate', 'FilterNotifierScheduler', 'SitemapItem', 'Feed',
  'Stylist', 'AutomatedNotificationAudience','DesignableValue', 'GiftCard', 'OperationProcess', 
  'ProcessDate', 'PurchaseOrder', 'NotificationEvent', 'SubscriberDefinition', 'ProductFeed::Feed',
  'TableauReport', 'HelpCenterHeader', 'Tab', 'PaymentOption', 'Horoscope', 'OfferPanel', 'VideoListing', 'DesignsVideoListing', 'WarehouseAddress', 'Log', 'Lane', 'WarehouseOrderLog', 'RackListsWarehouseLineItem', 'DesignShipperWeight']

  config.authorize_with :cancan  do 
    unless Account.allowed_roles.include?(current_account.role.name)
      redirect_to main_app.new_account_session_path 
      flash[:error] = "You are not an admin"
    end
  end

  config.compact_show_view = false
  #config.total_columns_width = 1200
  config.model Announcement do
  end

  config.model AdditionalPayment do
    list do
      include_all_fields
      filters [:id]
    end
  end

  config.model Order do
    weight -99
    # field :courier_company do
    #   searchable false
    # end
    # field :tracking_number do
    #   searchable false
    # end
    visible do
      bindings[:controller].current_account.is_authorized_for_rails_admin
    end

    list do
      field :id
      field :number
      # field :email
      # field :phone
      field :city
      field :created_at
      field :state
      include_all_fields
      exclude_fields :shipments, :export_shipments, :designer_issues, :designer_invoices, :line_items, :designer_orders, :designs, :outbound_coupons, :tickets, :adjustments, :sales_registers, :additional_payments, :returns, :return_designer_orders, :survey_answers, :email_logs, :stitching_measurements, :referral, :survey, :order_issue, :order_addon, :element, :email, :billing_email, :phone, :billing_phone
      filters [:number]
    end
    configure :designs do
      hide
      filterable false
      searchable false
    end

    edit do
     [:number,:state,:total,:discount,:paid_amount,:payment_state,:paypal_payer_id,:paypal_txn_id,
      :paypal_payment_type,:paypal_ipn_track_id,:paypal_mc_gross,:paypal_mc_currency,:paypal_num_cart_items,
      :mirraw_addon_charges,:currency_rate,:currency_code,:ip_address,:paid_currency_rate,:paid_currency_code,
      :app_source,:token,:geo,:currency_rate_market_value,:amazon_order_id,:razorpay_id,:razorpay_status,
      :paytm_txn_id,:paytm_txn_status,:bank_deposit_txn_id,:g2a_txn_id,:g2a_txn_status,:payu_mihpayid,
      :payu_payment_category_mode,:payu_status,:payu_error,:payu_bankcode,:payu_bank_ref,:payu_unmapped_status,
      :payu_error_message,:payu_name_on_card,:payu_card_num,:payu_payment_issuing_bank,:payu_card_type].each do |prop|
        configure prop do
          read_only true
          help false
        end
      end
      [:pay_type,:buyer_id,:created_at,:updated_at,:total,:shipping,:state,:name,:email,:phone,:street,:city,:buyer_state,:country,:pincode,:number,:notes,:billing_name,:billing_street,:billing_email,:billing_phone,:billing_city,:billing_state,:billing_country, :billing_pincode, :ccavenue_authdesc, :ccavenue_nb_order_no,:ccavenue_nb_bid, :ccavenue_card_category,:ccavenue_bank_name,:track,:visit_mail_sent,:gharpay_order_id,:gharpay_status,:discount,:coupon_id,:courier_company, :tracking_number, :confirmed_at,:pickup,:paid_amount,:credit_amount,:items_received_status,:items_received_on,:user_id,:payment_state,:state_code,:country_code,:utm_source,:utm_medium,:utm_campaign,:referral_url,:utm_term,:utm_content,:paypal_payer_id, :paypal_txn_id,:paypal_payment_type, :paypal_ipn_track_id, :paypal_mc_gross, :paypal_mc_currency,:paypal_num_cart_items,:fedex_shipment_error,:cod_charge,:zipdial_transaction_token,:zipdial_verify_image,:zipdial_number,:cod_verify_msg_count,:mirraw_addon_charges,:pending_at,:ready_for_dispatch_at,:cancel_mail_sent_at,:cancel_mail_sent_count,:ccavenue_order_status,:ccavenue_failure_message,:ccavenue_payment_mode,:ccavenue_bank_status_code,:ccavenue_bank_status_message,:ccavenue_customer_identifier,:ccavenue_currency, :ccavenue_merchant_data,:cart_id,:ccavenue_payment_link,:ccavenue_api_error,:ccavenue_invoice_id, :ccavenue_payment_link_qr_code,:ccavenue_payment_link_valid_for,:ccavenue_payment_link_status,:payu_mihpayid,:payu_payment_category_mode,:payu_status,:payu_error,:payu_bankcode,:payu_bank_ref,:payu_unmapped_status,:payu_error_message,:payu_name_on_card, :payu_card_num,:payu_payment_issuing_bank,:payu_card_type,:payment_gateway, :attempted_payment_gateway,:ip_address,:currency_rate,:currency_code,:paid_currency_rate,:paid_currency_code,:transaction_fee,:transaction_fee_currency_code,:amount_sent_payment_gateway,:payu_money_id,:app_source,:additional_discount,:shipping_discount,:cod_available,:token,:geo, :guest_mode,:currency_rate_market_value,:deleted_at,:icn_term,:feedback_flag, :actual_country_code, :referral_discount,:refund_discount,:actual_weight,:volumetric_weight,:best_shipper,:multi_channel_marketing,:notification_count, :out_of_mirraw_warehouse,:out_scan_notes,:paypal_error,:order_notification,:payment_gateway_details,:razorpay_id,:razorpay_status, :amazon_order_id, :out_scan_batch,:paytm_txn_id,:paytm_txn_status,:bank_deposit_txn_id, :g2a_txn_id,:additional_payments,:g2a_txn_status,:utm_exp_id,:other_details,:assigned_to_stylist_at,:stylist_id,:express_delivery,:duplicated_from_id,:cancel_reason,:juspay_txn_id,:juspay_order_status,:app_name].each do |prop|
        field prop do
          visible do
            current_account = bindings[:controller].current_account
            ADMIN_PANEL_ACCESS["admin_order_access"].to_a.include?(current_account.email) ? true : false
          end
        end
      end
      exclude_fields :notes, :line_items, :designer_issues, :designer_orders, :returns, :outbound_coupons, :shipments, :cart, :coupon, :shipments, :export_shipments, :designer_issues, :designer_invoices, :line_items, :designer_orders, :designs, :coupon, :outbound_coupons, :buyer, :user, :cart, :referral, :survey, :order_issue, :order_addon, :tickets, :adjustments, :sales_registers, :element, :additional_payments, :stylist, :reviews, :returns, :return_designer_orders, :surveyable, :loggable, :stitching_measurements, :email, :billing_email, :phone, :billing_phone
    end
    show do
      [:pay_type,:buyer_id,:created_at,:updated_at,:total,:shipping,:state,:name,:email,:phone,:street,:city,:buyer_state,:country,:pincode,:number,:notes,:billing_name,:billing_street,:billing_email,:billing_phone,:billing_city,:billing_state,:billing_country, :billing_pincode, :ccavenue_authdesc, :ccavenue_nb_order_no,:ccavenue_nb_bid, :ccavenue_card_category,:ccavenue_bank_name,:track,:visit_mail_sent,:gharpay_order_id,:gharpay_status,:discount,:coupon_id,:courier_company, :tracking_number, :confirmed_at,:pickup,:paid_amount,:credit_amount,:items_received_status,:items_received_on,:user_id,:payment_state,:state_code,:country_code,:utm_source,:utm_medium,:utm_campaign,:referral_url,:utm_term,:utm_content,:paypal_payer_id, :paypal_txn_id,:paypal_payment_type, :paypal_ipn_track_id, :paypal_mc_gross, :paypal_mc_currency,:paypal_num_cart_items,:fedex_shipment_error,:cod_charge,:zipdial_transaction_token,:zipdial_verify_image,:zipdial_number,:cod_verify_msg_count,:mirraw_addon_charges,:pending_at,:ready_for_dispatch_at,:cancel_mail_sent_at,:cancel_mail_sent_count,:ccavenue_order_status,:ccavenue_failure_message,:ccavenue_payment_mode,:ccavenue_bank_status_code,:ccavenue_bank_status_message,:ccavenue_customer_identifier,:ccavenue_currency, :ccavenue_merchant_data,:cart_id,:ccavenue_payment_link,:ccavenue_api_error,:ccavenue_invoice_id, :ccavenue_payment_link_qr_code,:ccavenue_payment_link_valid_for,:ccavenue_payment_link_status,:payu_mihpayid,:payu_payment_category_mode,:payu_status,:payu_error,:payu_bankcode,:payu_bank_ref,:payu_unmapped_status,:payu_error_message,:payu_name_on_card, :payu_card_num,:payu_payment_issuing_bank,:payu_card_type,:payment_gateway, :attempted_payment_gateway,:ip_address,:currency_rate,:currency_code,:paid_currency_rate,:paid_currency_code,:transaction_fee,:transaction_fee_currency_code,:amount_sent_payment_gateway,:payu_money_id,:app_source,:additional_discount,:shipping_discount,:cod_available,:token,:geo, :guest_mode,:currency_rate_market_value,:deleted_at,:icn_term,:feedback_flag, :actual_country_code, :referral_discount,:refund_discount,:actual_weight,:volumetric_weight,:best_shipper,:multi_channel_marketing,:notification_count, :out_of_mirraw_warehouse,:out_scan_notes,:paypal_error,:order_notification,:payment_gateway_details,:razorpay_id,:razorpay_status, :amazon_order_id, :out_scan_batch,:paytm_txn_id,:paytm_txn_status,:bank_deposit_txn_id, :g2a_txn_id,:additional_payments,:g2a_txn_status,:utm_exp_id,:other_details,:assigned_to_stylist_at,:stylist_id,:express_delivery,:duplicated_from_id,:cancel_reason,:juspay_txn_id,:juspay_order_status,:app_name].each do |prop|
        field prop do
          visible do
            current_account = bindings[:controller].current_account
            ADMIN_PANEL_ACCESS["admin_order_access"].to_a.include?(current_account.email) ? true : false
          end
        end
      end
      exclude_fields :email, :billing_email, :phone, :billing_phone
    end

  end

  config.model Tab do
    list do
      include_all_fields
    end
  end

  config.model PaymentOption do
    list do
      include_all_fields
      exclude_fields :lft, :rgt
    end

    edit do
      exclude_fields :lft, :rgt
    end
  end

  config.model Horoscope do
    list do
      include_all_fields
      exclude_fields :meta_tag, :keyword_header, :description_header, :horoscope_title, :photo_processing, :original_photo_processing
    end
      
    show do
      include_all_fields
      exclude_fields :meta_tag, :keyword_header, :description_header, :horoscope_title
    end

    edit do
      include_all_fields
      exclude_fields :meta_tag, :keyword_header, :description_header, :horoscope_title, :photo_processing, :original_photo_processing
    end    
  end

  config.model Return do
    list do
      field :state
      include_all_fields
      exclude_fields :return_designer_orders, :tickets, :discount_line_items
      filters [:order_number, :id, :state]
    end

    edit do
      %w{total discount agent_info user_paypal_email product_id designer_name completed_on coupon_code coupon_gen_date app_source ticket_id}.each do |col_name|
        configure col_name.to_sym do
          help false
          read_only true
        end
      end
      exclude_fields :adjustment, :order, :user, :line_items, :coupon
    end
  end

  config.model SurveyQuestion do
    show do
      include_all_fields
      field :positive_events
      field :negative_events
    end
    edit do
      include_all_fields
      field :positive_events
      field :negative_events
    end
  end

  config.model StoryCollection do
    show do
      include_all_fields
      field :design_id_list
    end
    edit do
      include_all_fields
      exclude_fields :designs
      field :design_id_list do
        help "Provide comma seperated design ids. eg. 1,21,33"
      end
    end
  end

  config.model Survey do
    exclude_fields :email
  end

  config.model MetricDefinition do
    list do
      exclude_fields :numerator_scope_event_definition, :denominator_scope_event_definition, :numerator_term_events, :denominator_term_events, :metric_values
    end

    show do
      include_all_fields
      field :numerator_term_events
      field :denominator_term_events
    end
    edit do
      include_all_fields
      field :numerator_term_events
      field :denominator_term_events
    end
  end

  config.model DynamicTemplate do
    show do
      include_all_fields
      field :sample_title
      field :sample_body
    end
    edit do
      include_all_fields
      field :body, :ck_editor
    end
  end

  config.model FilterNotifierScheduler do
    show do 
      include_all_fields
      field :filter_group
    end
    edit do 
      include_all_fields
      field :filter_group
    end
  end


  config.model Coupon do
    visible do
      bindings[:controller].current_account.is_authorized_for_rails_admin
    end
    exclude_fields :source_order, :designer, :coupon_used_on, :used_on_orders
    list do
      filters [:code]
    end

    edit do
      field :geo, :enum do
        label 'Geo'
        enum do
          ['International', 'Domestic', 'All']
        end
        default_value 'All'
      end
      field :app_name, :enum do
        label 'App Name'
        enum do
          ['mirraw','luxe']
        end
        default_value 'mirraw'
      end
      field :app_source, :enum do
        label 'App Source'
        enum do 
          ['all','website','android','ios']
        end
        multiple true
        default_value 'all'
      end
      %w{converted_to_refund created_by return_id source_order_id designer_id}.each do |col_name|
        configure col_name.to_sym do
          help false
          read_only true
        end
      end
      exclude_fields :designs, :source_order, :designer, :coupon_used_on, :used_on_orders
    end
    
    show do
      field :geo
      field :app_source
    end 
  end

  config.model DesignShipperWeight do
    list do
      include_all_fields
    end
    edit do
      exclude_fields :created_at, :updated_at
    end
  end

  config.model WarehouseAddress do
    list do
      include_all_fields
    end
    edit do
      include_all_fields
      exclude_fields :created_at, :created_by, :company_name
    end
  end

  config.model Designer do
    visible do
      bindings[:controller].current_account.is_authorized_for_rails_admin
    end
    weight -50
    list do
      field :name
      field :email
      field :phone
      field :alt_phone
      field :street
      field :city
      field :state
      field :pincode
      field :photo
      #include_all_fields
    end

    edit do
      exclude_fields :state_machine, :designs, :designer_issues, :variants, :designer_collections, :designer_orders, :coupons, :adjustments, :categories, :design_categories, :designer_shippers, :pickups, :designer_batches, :carts, :optional_pickups
    end

    configure :followings do
      hide
      # for list view
      filterable false
      searchable false
    end

    configure :designer_orders do
      hide
      # for list view
      filterable false
      searchable false
    end

    configure :designs do
      hide
      # for list view
      filterable false
      searchable false
    end
  end

  config.model Design do
    visible do
      bindings[:controller].current_account.is_authorized_for_rails_admin
    end
    weight -49
    list do
      field :id
      field :designer
      field :grade
      field :title
      field :price
      field :eta
      field :ignore_in_google_feed
      field :quantity
      #include_all_fields
      exclude_fields :line_items, :reviews, :design_performance, :designer_issues, :images, :delay_images, :delay_designables, :categories_designs, :approved_reviews, :addon_type_values, :addon_types, :variants, :variants_with_option_type_id, :option_type_values, :customized_option_type_values, :claim_requests, :follows, :stitching_measurements, :dynamic_prices, :dynamic_price_for_current_country, :design_score
    end

    edit do
      configure :grade do
        read_only do
          !bindings[:view].grading_access
        end
        help false
      end
      configure :international_grade do
        read_only do
          !bindings[:view].grading_access
        end
        help false
      end
      configure :price do
        read_only true
        help false
      end
      configure :discount_percent do
        read_only true
        help false
      end
      configure :discount_price do
        read_only true
        help false
      end

      field :tag_list
      field :collection_list
      include_all_fields
      exclude_fields :line_items, :designer_issues, :featured_product
    end

    show do
      field :tag_list
      include_all_fields
      exclude_fields :line_items
    end
  end

  config.model Image do
    weight -48
    list do
      field :design
      field :kind
      field :photo do
        thumb_method :thumb
      end
      field :created_at
      field :updated_at
      include_all_fields
    end
  end

  config.model DesignerOrder do
    weight -98
    list do
      field :order
      field :designer
      field :pickup
      field :tracking
      field :created_at
      include_all_fields
      exclude_fields :shipment, :designer_invoice, :line_items, :return_designer_orders, :delivery_nps_info, :reverse_commissions, :priority_shipper, :priority_shipper_cod
    end

    edit do
      fields do
        read_only true
      end
      [:fedex_serviceable,:shipment_status,:delhivery_serviceable,:ecomexpress_serviceable,:city_courier_serviceable,:bluedart_serviceable,:dtdc_serviceable,:speedpost_serviceable,:fedex_mirraw_serviceable,:aramex_serviceable,:tirupati_courier_serviceable,:gati_serviceable,:ship_delight_serviceable,:shadowfax_serviceable,:rapid_delivery_serviceable,:xpress_bees_serviceable,:clickpost_serviceable,:wowexpress_serviceable,:ekart_serviceable,:kerryindev_serviceable, :smartr_serviceable, :first_call_serviceable, :ats_serviceable, :dak_india_serviceable, :abhilaya_serviceable, :sme_cargo_serviceable, :velocity_serviceable].each do |prop|
        configure prop do
          read_only false
          # help true
        end
      end
      exclude_fields :line_items, :designer, :order, :coupon, :shipment, :designer_invoice, :return_designer_orders, :delivery_nps_info, :reverse_commissions, :shipper, :priority_shipper, :priority_shipper_cod
    end
  end

  config.model Cart do
    exclude_fields :email
    list do
      exclude_fields :line_items, :designs
    end
  end

  config.model Category do
    list do
      exclude_fields :category_banners, :category_new_arrivals, :category_connections, :categories, :category_album, :lft, :rgt, :designs, :designers, :design_node, :addon_types, :master_addons, :option_types, :google_analytics_data, :time_series_datasets , :dynamic_size_chart_state

      field :dynamic_size_chart_state do
        searchable false
      end
      field :category_type do
        searchable false
      end
    end

    show do
      exclude_fields :lft, :rgt, :designs, :designers, :design_node, :addon_types, :master_addons, :option_types, :dynamic_size_charts, :default_size_chart, :google_analytics_data, :time_series_datasets 
    end

    edit do
      field :lft do
        visible do
          false
        end
      end
      field :rgt do
        visible do
          false
        end
      end
      field :name
      field :parent
      field :title
      field :luxe_title
      field :weight
      field :dynamic_size_chart_state
      field :category_type
      field :searchable
      field :international
      field :invoice_price
      field :invoice_name
      field :super_child
      field :super_child_label
      field :photo
      field :hsn_code
      field :breadcrumb_path
      field :google_product_category
      field :mirraw_product_category
      field :popular_links
      field :faqs
      field :show_to_vendor
      field :app_name
      # field :photo_file_name do
      #   visible do
      #     true if bindings[:object].photo_file_name.present?
      #   end
      # end
    end
  end

  config.model PopularLink do
    object_label_method :keyword
  end

  config.model FAQ do
    object_label_method :question
    edit do
    end
  end

  config.model FashionUpdateCategory do
    list do
      field :lft do
        visible do
          false
        end
      end
      field :rgt do
        visible do
          false
        end
      end
      include_all_fields
    end

    edit do
      field :lft do
        visible do
          false
        end
      end
      field :rgt do
        visible do
          false
        end
      end
      include_all_fields
    end
  end

  config.model LineItem do
    list do
      exclude_fields :designer, :order, :order_delivery_nps_info, :order, :payment_order, :line_item_addons, :discount_line_items, :designer_issues, :reverse_commissions, :rtv_shipment_line_items, :rtv_shipments, :tailoring_info, :stitching_measurements, :categories
    end
    edit do
     field :note
    end
    show do
      include_all_fields
      field :unscaled_price
      exclude_fields :scaling_factor
    end
    weight 98
  end

  config.model CurrencyConvert do
    weight 102
  end

  config.model Adjustment do
    visible do
      bindings[:controller].current_account.is_authorized_for_rails_admin
    end
    edit do
      field :notes
    end
  end

  config.model SellerFollowup do
    exclude_fields :email_logs, :email, :contact_number
    edit do
      field :note
    end
  end

  config.model PublicSystemConstant do
    edit do
      field :value
    end
  end

  config.model Frontpage do
    edit do

    end
  end

  config.model BannerSlider do
    exclude_fields :boards
    edit do
    end
  end

  config.model Error do
    edit do
    end
  end

  config.model Feature do
    edit do
      field :design_id
      field :start_date
      field :end_date
      field :grade
    end
  end

  config.model AddonTypeValueGroup do
    edit do
      field :p_name
    end
    exclude_fields :addon_type_values
  end

  config.model Feed do
    list do
      field :name
      field :active
      field :feed_type
      field :next_run_at
      field :last_attempted_at
      field :last_completed_at
      field :processing
      include_all_fields
    end
  end

  config.model ProductFeed::Feed do |feed|
    list do
      field :name
      field :active
      field :feed_type
      field :state
      field :last_attempted_at
      field :last_completed_at
      field :last_crashed_at
      field :next_run_at
      include_all_fields
      exclude_fields :processing
    end
    edit do
      field :name
      field :feed_type
      field :next_run_at do
        visible do
          !bindings[:object].sub_feed?
        end
        help 'Expect delay of 20 minutes to start'
      end
      field :configuration
      field :active
      field :sub_feeds
      field :parent_feed
    end
    show do
      field :name
      field :download_url_links
      field :active
      field :feed_type
      field :state
      field :last_attempted_at
      field :last_completed_at
      field :last_crashed_at
      field :next_run_at
      include_all_fields
      exclude_fields :processing
    end
  end

  config.model AddonTypeValue do
    # edit do
    #   exclude_fields :line_item_addons, :addon_type_value_group_id, :db_name
    # end
    edit do
      field :name
      field :position
      field :price
      field :description
      field :addon_type_id
      field :published
      field :prod_time
      field :visible_for
      field :payable_to
      #:line_item_addons, :addon_type_value_group_id, :db_name
    end
    show do
      exclude_fields :line_item_addons, :db_name
    end
  end

  config.model AddonOptionType do
    edit do
      field :name do
        read_only true
      end
      field :position
      field :price
      field :published
    end
  end

  config.model RedirectRule do
    list do
      field :category_kind
      field :match
      field :route
    end
  end

  config.model VendorPayout do
    list do
      field :awb
      field :charge
      field :status
      field :vendor_payout_batch
      field :created_at
      field :updated_at
    end
  end

  config.model MenuColumn do
    object_label_method do
      :menu_column_menu_name
    end
  end

  config.model MenuItem do
    list do
      include_all_fields
    end
  end

  config.model VendorPayoutBatch do
    object_label_method do
      :total_records
      :failed_records
      :success_records
    end
    list do
      field :id 
      field :file_name
      field :created_at      
      field :total_records
      field :failed_records
      field :created_at
      field :success_records
    end
  end

  config.model ShipmentCharge do
    list do
      field :shipper_id
      field :country_code
      field :weight
      field :price
      field :with_surcharge
      field :created_at
      field :updated_at
    end
  end

  config.model ShipperFuelPrice do
    list do
      field :shipper
      field :country
      field :weight
      field :base_charge
      field :surcharge
    end
  end

  config.model SupportText do
    show do
      include_all_fields
    end
    edit do
      include_all_fields
      field :answer, :ck_editor
      field :category_of_faq do
        help 'Required'
      end
    
      field :support_text_parent_id do
        visible do
          false
        end
      end

      field :child_support_texts do
        visible do
          false
        end
      end
    end
  end
  
  config.model HelpCenterHeader do
    edit do
      include_all_fields
      field :international_support_texts do
        visible do
          false
        end
      end
      field :domestic_support_texts do
        visible do
          false
        end
      end
    end
  end

  config.model FailedDesign do
    list do
      include_all_fields
      field :designer_batch
    end
  end

  config.model VersionFailedDesign do
    list do
      field :id
      field :designer_batch
    end
    show do
      field :error_show
      field :row_show
      field :designer_batch
    end
  end

  config.model AwbNumber do
    list do
      include_all_fields
    end
  end

  config.model DesignerBatch do
    list do
      field :designer
      include_all_fields
      exclude_fields :failed_designs, :version_failed_designs, :delay_images, :delay_designables, :designs
    end
  end

  config.model UnbxdWidget do
    list do
      exclude_fields :unbxd_containers
    end
  end

  config.model User do
    exclude_fields :full_size_photo, :wallet, :email
    list do
      exclude_fields :carts, :friends, :addresses, :orders, :returns, :stitching_measurements, :reviews, :wishlists, :full_size_photo
    end
  end

  config.model Variant do
    list do
      exclude_fields :option_type_values, :customized_option_type_values, :option_types, :line_items, :option_type_values_variants
    end
  end

  config.model Widget do
    list do
      exclude_fields :nav_blocks, :graded_nav_blocks
    end
  end

  config.model Notification do
    list do
      include_all_fields
    end
  end

  config.model CategoryBanner
  config.model CategoryNewArrival do
    edit do
      include_all_fields
      field :country
    end
  end

  config.model Landing do
    list do
      exclude_fields :boards, :blocks, :nav_tabs, :widgets
    end

    edit do
      include_fields :label, :banner, :link, :slider, :banner1, :dual_link1, :banner_1_first_link, :banner_1_second_link, :banner2, :dual_link2, :banner_2_first_link, :banner_2_second_link, :banner3, :dual_link3, :banner_3_first_link, :banner_3_second_link, :banner_processing, :category_landing, :side_menu, :side_menu_sort
    end
  end

  config.model CategoryConnection do
    edit do
      exclude_fields :designs
    end
  end

  config.model Bag do
    list do
      exclude_fields :design
    end
  end

  config.model Consumable do
    list do
      exclude_fields :design
    end
  end

  config.model HomeDecor do
    list do
      exclude_fields :design
    end
  end

  config.model Jacket do
    list do
      exclude_fields :design
    end
  end

  config.model Jewellery do
    list do
      exclude_fields :design
    end
  end

  config.model Kurta do
    list do
      exclude_fields :design
    end
  end

  config.model Kurti do
    list do
      exclude_fields :design
    end
  end

  config.model Lehenga do
    list do
      exclude_fields :design
    end
  end

  config.model Other do
    list do
      exclude_fields :design
    end
  end

  config.model Property do
    list do
      exclude_fields :property_values, :categories, :category_ids
    end
  end

  config.model PropertyValue do
    exclude_fields :designs, :design_scores, :design_node
    list do
      exclude_fields :designs, :design_scores, :design_node
    end
  end

  config.model RackList do
    list do
      exclude_fields :designer_orders, :rack_lists_warehouse_line_items, :warehouse_line_items, through: :rack_lists_warehouse_line_items
    end
  end

  config.model Referrer do
    exclude_fields :referrals, :referred_people, :email
    # list do
    #   exclude_fields :referrals, :referred_people #need to think about user
    # end
  end

  config.model ReferredPerson do
    exclude_fields :email
  end

  config.model Shipment do
    list do
      exclude_fields :line_items, :inbound_line_items, :optional_pickups, :invoicer, :packer, :order
    end
    show do
      field :invoicer_id
      field :packer_id
      include_all_fields
      exclude_fields :invoicer, :packer
    end
    edit do
      field :invoicer_id do
        read_only true
      end
      field :packer_id do
        read_only true
      end
      field :shipper do
        read_only true
      end
      field :designer_order do
        read_only true
      end
      include_all_fields
      exclude_fields :line_items, :inbound_line_items, :optional_pickups, :order, :invoicer, :packer
    end
  end

  config.model SalwarKameez do
    list do
      exclude_fields :design
    end
  end

  config.model Saree do
    list do
      exclude_fields :design
    end
  end

  config.model Turban do
    list do
      exclude_fields :design
    end
  end

  config.model DesignerIssue do
    list do
      exclude_fields :deign, :designer_order, :line_item, :order
    end
  end

  config.model OptionType do
    list do
      exclude_fields :option_type_values, :category, :category_name
    end
  end

  config.model Shipper do
    list do
      exclude_fields :designer_shippers, :awb_numbers, :couriers, :priority_designer_orders, :shipment_charges
    end
    show do
      exclude_fields :designer_shippers, :awb_numbers, :couriers, :priority_designer_orders, :shipment_charges
    end
    edit do
      exclude_fields :designer_shippers, :awb_numbers, :couriers, :priority_designer_orders, :shipment_charges
    end
  end

  config.model TailoringInfo do
    visible do
      bindings[:controller].current_account.is_authorized_for_rails_admin
    end

    list do
      exclude_fields :stitching_measurements
    end
  end

  config.model OptionTypeValue do
    list do
      exclude_fields :variants, :option_type
    end
  end

  config.model SitemapItem do
    exclude_fields :lft, :rgt, :depth, :children_count
    object_label_method do
      :sitemap_item_with_parent_name
    end
    field :image do
      help 'Required image size - 150 x 150'
    end
  end

  config.model Account do
    exclude_fields :email, :uid
    list do
      exclude_fields :api_data, :stylist, :coupon_creators, :messages, :accountable
    end
  end

  config.model Stylist do
    exclude_fields :orders
    edit do 
      field :orders_count
      configure :available do
        read_only true
        help false
      end
    end

    configure :account do
      filterable false
      searchable false
    end
    configure :orders do
      filterable false
      searchable false
    end
  end

  config.model AutomatedNotificationAudience do
    edit do
      configure :audience_type do
          help false
          read_only true
        end
    end
  end

  config.model Board do
    exclude_fields :banner, :blocks, :landing, :designs
    field :design_ids do
      help 'Please, Enter comma seperated design ids only'
    end
    field :country do
      help "Enter country code IN, US, UK with comma separated. To get list of all countries please click <a href='https://admin.mirraw.com/admin/country'>countries list</a>.".html_safe
    end
  end

  config.model DesignableValue do
    edit do
      exclude_fields :designable
    end
  end

  config.model OperationProcess do
    exclude_fields :process_dates
  end

  config.model SubscriberDefinition do
    show do
      include_all_fields
      field :subscriber_type
      field :subscriber_id
    end
    list do
      include_all_fields
      field :subscriber_type
      field :subscriber_id
    end
  end

  config.model TableauReport do 
    visible do
      bindings[:controller].current_account.is_authorized_for_rails_admin
    end
  end

  config.model VideoListing do
    edit do
      exclude_fields :designs_video_listing
    end
    configure :designs do
      searchable [{Design => :id }]
      queryable true
    end
  end

  config.model DesignsVideoListing do
    edit do 
      field :design_id
      field :priority
    end
  end
  config.audit_with :paper_trail, 'Account', 'PaperTrail::Version'

end if defined? RailsAdmin
