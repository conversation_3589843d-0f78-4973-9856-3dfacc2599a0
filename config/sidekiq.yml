---
:verbose: false
:logfile: ./log/sidekiq.log
:max_retries: 5
development:
  :concurrency: 3
  :queues:
    - critical
    - label
    - high_priority
    - high
    - medium
    - default
    - low
    - paperclip
    - mailer
    - rake
    - long_running_recurring_jobs
    - sms_service

staging:
  :concurrency: 2
  :queues:
    - critical
    - label
    - high_priority
    - high
    - medium
    - default
    - low
    - paperclip
    - mailer
    - rake
    - long_running_recurring_jobs
    - sms_service



production:
  :concurrency: 3
  :queues:
    - critical
    - label
    - high_priority
    - high
    - medium
    - default
    - low
    - paperclip
    - mailer
    - rake
    - long_running_recurring_jobs
    - sms_service

    
:schedule:
  update_performance_metric_value:
    cron: '10 0 * * *'
    class: UpdatePerformanceMetricValueJob
  cache_clear_og:
    cron: '0 0 * * *'
    class: CacheClearOgJob
  cache_clear_og_afternoon:
    cron: '0 13 * * *'
    class: CacheClearOgJob
  sunspot_solr_batch_index:
    cron: "0 * * * *"
    queue: high
    class: BatchIndexJob
    description: "Sunspot solt batch index after every 5 mins"
  set_market_rate:
    cron: "0 0 * * *"
    class: SetMarketRateJob
    description: "set market rate rake task at 12AM everyday"
  set_e_invoice_generation:
    cron: "0 6 * * *"
    class: EInvoiceGenerationJob
    description: "E-Invoice Generation taks at 06AM everyday"
  update_sitemap:
    cron: '0 0 * * *'
    class: UpdateSitemapJob
    description: 'This task updates sitemaps'
  update_shipments:
    cron: "0 * * * *"
    class: UpdateShipmentsJob
    args: ["ups_delhivery_fedex_xindus_aramex_xpress bees_ship delight_dhl_dhl ecom_rapid delivery"]
    description: "update shipment rake task"
  # generate_monthly_commission_report:
  #   cron: "30 0 1 * *"
  #   class: GenerateMonthlyCommissionReport
  #   args: [true, nil, Date.today.strftime('%b%Y'), 1.month.ago.end_of_month.strftime('%d/%m/%Y'), 1.month.ago.beginning_of_month.beginning_of_day,1.month.ago.end_of_month.end_of_day]
  #   description: "it generated monthly commision reports at 12:30 AM on 1st of each month"
  # generate_monthly_purchase_report:
  #   cron: "0 3 1 * *"
  #   class: GenerateMonthlyPurchaseReport
  #   args: [true]
  #   description: "generate monthly purchase report at 3Am on 1st od each month"
  # monthly_return_report_to_accounts:  
  #   cron: "0 6 1 * *"
  #   class: MonthlyReturnReportToAccounts
  #   description: "monthly return reports at 6AM 1st of each month"


  metric_generate:
    cron: "25 0 * * *"
    class: MetricGenerateJob
    args: [{regenerate_offset: 1}]
    description: 'Generate metric (moving average and rating) on daily basis for orders'
  update_pending_return_payment:
    cron: "30 23 * * *"
    class: UpdatePendingReturnPaymentJob
    description: 'Daily update pending return state'
  update_shipments_with_track_removed:
    cron: "0 * * * *"
    class: UpdateShipmentsWithTrackRemovedJob
    args: ["delhivery_ups_xindus_atlantic_fedex_aramex_xpress bees_ship delight_dhl_dhl ecom_rapid delivery"]
    description: "update shipment with track removed rake task"
  # generate_monthly_commission_report:
  #   cron: "30 0 1 * *"
  #   class: GenerateMonthlyCommissionReportJob
  #   args: [true, nil, Date.today.strftime('%b%Y'), 1.month.ago.end_of_month.strftime('%d/%m/%Y'), 1.month.ago.beginning_of_month.beginning_of_day,1.month.ago.end_of_month.end_of_day]
  #   description: "it generated monthly commision reports at 12:30 AM on 1st of each month"
  # generate_monthly_purchase_report:
  #   cron: "0 6 1 * *"
  #   class: GenerateMonthlyPurchaseReportJob
  #   args: [true]
  #   description: "generate monthly purchase report at 3Am on 1st od each month"
  generate_international_adjustment_report:
    cron: "30 23 * * *"
    class: GenerateInternationalAdjustmentReportJob
    description: 'Sends International adjustments invoice and report'
  monthly_return_report_to_accounts:  
    cron: "0 6 1 * *"
    class: MonthlyReturnReportToAccountsJob
    description: "monthly return reports at 6AM 1st of each month"
  send_warning_and_banned_email_to_designer:
    cron: "30 5 * * MON"
    class: SendWarningAndBannedEmailToDesignerJob
    description: "sends warning and banned email to designers"
  sendout_cart_abandonment_notifications:
    cron: "0 */1 * * *"
    class: SendoutCartAbandonmentNotificationJob
    description: "This job sends notification email to cart abandoned users"
  sendout_cancelled_order_notification:
    cron: '0 13 * * *'
    class: SendoutCancelledOrderNotificationJob
    description: "This job sends notification email to cancled order users"
  sendout_luxe_cart_abondonment_notification:
    cron: "0 */1 * * *"
    class: SendoutLuxeCartAbandonmentNotificationJob
    description: "This job sends email to luxe users for cart abandonment"
  # designer_sold_out_notification_job:
  #   cron: "0 15 * * *"
  #   class: DesignerSoldOutNotificationJob
  #   description: "This job sends notification email to designers for sold out products "
  # not_uploaded_fifty_designs_job:
  #   cron: '0 16 * * *'
  #   class: NotUploadedFiftyDesignsJob
  #   description: "This job sends notification email to designers having design less than 50 "
  notify_dynamic_template_job:
    cron: '0 4 * * *'
    class: NotifyDynamicTemplateJob
    description: "This job sends notification mail to designers with dynamic templates "
  send_mails_for_stitching_measurement_reminder_job.rb:
    cron: '0 0 * * *'
    class: SendMailsForStitchingMeasurementReminderJob
    description: "send mails for measurements"
  # add_reward_for_late_deliveries_job:
  #   cron: '0 7 * * *'
  #   class: AddRewardForDelayedDeliveriesJob
  #   description: "add referral amount for delayed deliveries"
  # inactive_designer_alert_job:
  #   cron: '0 17 * * *'
  #   class: InactiveDesignerAlertJob
  #   description: "sends notification to designers who are inactive more than 45 days"

  unbxd_feed_upload:
    cron: '0 4 * * *'
    class: UnbxdFeedUpload
    description: "This is Used for Unbxd Product Feed Upload"

  initiate_grading_designs:
    cron: '0 10 * * FRI'
    class: InitiateGradingDesigns
    description: "This is Used for grading of designs"

  update_clickpost_shipment_state:
    cron: '0 0 * * *'
    class: UpdateClickpostShipmentStateJob
    description: "This is Used for updating clickpost shipment state"
  trigger_escalation_email:
    cron: '0 4 * * *'
    class: TriggerEscalationEmail
    description: "This use to give notification to stakeholder if issue not resolved between TAT"
  boost_in_queue_products:
    cron: "45 12 * * *"
    class: "BoostProductJob"
    queue: critical
    description: "Update Ranks and Index for all the products that are in boost queue"


