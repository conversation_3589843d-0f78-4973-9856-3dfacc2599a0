class CreateSuperMenus < ActiveRecord::Migration
  def change
    create_table :super_menus do |t|
      t.string :title, null: false
      t.text :link
      t.integer :position
      t.string :country
      t.string :app_source
      t.string :app_name
      t.boolean :is_hidden, default: false
      t.timestamps null: false
    end

    add_index :super_menus, :position
    add_index :super_menus, :is_hidden
    add_index :super_menus, [:country, :app_source]
  end
end
