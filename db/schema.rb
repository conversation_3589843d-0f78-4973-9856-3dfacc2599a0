# encoding: UTF-8
# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# Note that this schema.rb definition is the authoritative source for your
# database schema. If you need to create the application database on another
# system, you should be using db:schema:load, not running all the migrations
# from scratch. The latter is a flawed and unsustainable approach (the more migrations
# you'll amass, the slower it'll run and the greater likelihood for issues).
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: **************) do

  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"
  enable_extension "hstore"

  create_table "accounts", force: :cascade do |t|
    t.string   "email",                  limit: 255, default: "", null: false
    t.string   "encrypted_password",     limit: 128, default: "", null: false
    t.string   "reset_password_token",   limit: 255
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.integer  "sign_in_count",                      default: 0
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string   "current_sign_in_ip",     limit: 255
    t.string   "last_sign_in_ip",        limit: 255
    t.integer  "accountable_id"
    t.string   "accountable_type",       limit: 255
    t.string   "uid",                    limit: 255
    t.string   "provider",               limit: 255
    t.datetime "created_at"
    t.datetime "updated_at"
    t.integer  "notified"
    t.datetime "notified_date"
    t.string   "token",                  limit: 255
    t.string   "confirmation_token",     limit: 255
    t.datetime "confirmed_at"
    t.datetime "confirmation_sent_at"
    t.string   "unconfirmed_email",      limit: 255
    t.text     "tokens"
    t.string   "unbxd_user_id",          limit: 255
    t.boolean  "terms_of_service"
    t.integer  "role_id"
    t.string   "oauth_id",               limit: 255
    t.boolean  "guest_account"
    t.string   "account_created_from"
    t.string   "fcm_registration_token"
    t.string   "phone"
    t.string   "dial_code"
    t.string   "country"
  end

  add_index "accounts", ["accountable_id"], name: "index_accounts_on_accountable_id", using: :btree
  add_index "accounts", ["accountable_type"], name: "index_accounts_on_accountable_type", using: :btree
  add_index "accounts", ["confirmation_token"], name: "index_accounts_on_confirmation_token", unique: true, using: :btree
  add_index "accounts", ["email"], name: "index_accounts_on_email", unique: true, using: :btree
  add_index "accounts", ["phone"], name: "index_accounts_on_phone", using: :btree
  add_index "accounts", ["reset_password_token"], name: "index_accounts_on_reset_password_token", unique: true, using: :btree
  add_index "accounts", ["uid", "provider"], name: "index_accounts_on_uid_and_provider", using: :btree

  create_table "additional_payments", force: :cascade do |t|
    t.float    "total"
    t.string   "payment_gateway",            limit: 255
    t.integer  "order_id"
    t.float    "currency_rate_market_value"
    t.string   "payment_state",              limit: 255
    t.string   "paypal_txn_id",              limit: 255
    t.string   "paypal_payment_type",        limit: 255
    t.string   "paypal_ipn_track_id",        limit: 255
    t.string   "paypal_mc_gross",            limit: 255
    t.string   "paypal_mc_currency",         limit: 255
    t.string   "paypal_num_cart_items",      limit: 255
    t.string   "paypal_payer_id",            limit: 255
    t.string   "payu_mihpayid",              limit: 255
    t.string   "payu_status",                limit: 255
    t.string   "payu_unmapped_status",       limit: 255
    t.string   "payu_payment_category_mode", limit: 255
    t.string   "payu_bank_ref",              limit: 255
    t.string   "payu_bankcode",              limit: 255
    t.string   "payu_error",                 limit: 255
    t.string   "payu_error_message",         limit: 255
    t.string   "payu_name_on_card",          limit: 255
    t.string   "payu_card_num",              limit: 255
    t.string   "payu_payment_issuing_bank",  limit: 255
    t.string   "payu_card_type",             limit: 255
    t.string   "payu_money_id",              limit: 255
    t.string   "bank_deposit_txn_id",        limit: 255
    t.string   "amazon_order_id",            limit: 255
    t.string   "paytm_txn_id",               limit: 255
    t.string   "razorpay_id",                limit: 255
    t.integer  "done_by"
    t.datetime "created_at",                             null: false
    t.datetime "updated_at",                             null: false
    t.text     "notes"
    t.string   "charge_type"
    t.string   "currency_code"
    t.string   "country_code"
    t.float    "currency_rate"
    t.string   "paid_currency_code"
    t.float    "paid_currency_rate"
  end

  create_table "addon_option_types", force: :cascade do |t|
    t.string   "name",        limit: 255
    t.string   "p_name",      limit: 255
    t.integer  "position"
    t.datetime "created_at",                                 null: false
    t.datetime "updated_at",                                 null: false
    t.boolean  "published",               default: true
    t.string   "option_type",             default: "select"
    t.integer  "price",                   default: 0
  end

  add_index "addon_option_types", ["name"], name: "index_addon_option_types_on_name", using: :btree
  add_index "addon_option_types", ["p_name"], name: "index_addon_option_types_on_p_name", using: :btree

  create_table "addon_option_values", force: :cascade do |t|
    t.string   "name",                 limit: 255
    t.string   "p_name",               limit: 255
    t.integer  "position"
    t.integer  "addon_option_type_id"
    t.integer  "addon_type_value_id"
    t.datetime "created_at",                       null: false
    t.datetime "updated_at",                       null: false
    t.integer  "master_id"
    t.string   "hexcode"
  end

  add_index "addon_option_values", ["addon_option_type_id"], name: "index_addon_option_values_on_addon_option_type_id", using: :btree
  add_index "addon_option_values", ["addon_type_value_id", "name", "addon_option_type_id", "addon_type_value_id"], name: "uniq_addon_option_values", unique: true, using: :btree
  add_index "addon_option_values", ["addon_type_value_id"], name: "index_addon_option_values_on_addon_type_value_id", using: :btree

  create_table "addon_type_value_groups", force: :cascade do |t|
    t.string   "name",       limit: 255, null: false
    t.string   "p_name",     limit: 255, null: false
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "addon_type_values", force: :cascade do |t|
    t.string   "name",                      limit: 255
    t.integer  "position"
    t.integer  "price"
    t.text     "description"
    t.integer  "design_id"
    t.integer  "addon_type_id"
    t.datetime "created_at",                                               null: false
    t.datetime "updated_at",                                               null: false
    t.integer  "master_id"
    t.boolean  "published"
    t.integer  "prod_time"
    t.string   "visible_for",               limit: 255, default: "all",    null: false
    t.string   "payable_to",                limit: 255, default: "mirraw", null: false
    t.integer  "addon_type_value_group_id"
    t.string   "db_name",                   limit: 255
    t.float    "cost"
    t.integer  "quantity",                              default: 0
    t.integer  "weight",                                default: 0
    t.integer  "designer_id"
  end

  add_index "addon_type_values", ["design_id"], name: "index_addon_type_values_on_design_id", using: :btree
  add_index "addon_type_values", ["designer_id"], name: "index_addon_type_values_on_designer_id", using: :btree

  create_table "addon_types", force: :cascade do |t|
    t.string   "name",          limit: 255
    t.integer  "position"
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
    t.integer  "category_id"
    t.string   "db_name",       limit: 255
    t.string   "type_of_addon"
  end

  add_index "addon_types", ["db_name", "category_id"], name: "uniq_addon_types", unique: true, using: :btree
  add_index "addon_types", ["db_name"], name: "index_addon_types_on_db_name", using: :btree
  add_index "addon_types", ["name"], name: "index_addon_types_on_name", using: :btree

  create_table "addresses", force: :cascade do |t|
    t.string   "name",           limit: 255
    t.string   "street_address", limit: 255
    t.string   "landmark",       limit: 255
    t.string   "city",           limit: 255
    t.string   "state",          limit: 255
    t.string   "country",        limit: 255
    t.string   "pincode",        limit: 255
    t.string   "phone",          limit: 255
    t.integer  "user_id"
    t.datetime "created_at",                             null: false
    t.datetime "updated_at",                             null: false
    t.integer  "default",                    default: 0
  end

  add_index "addresses", ["user_id"], name: "index_addresses_on_user_id", using: :btree

  create_table "adjustment_reports", force: :cascade do |t|
    t.string   "invoice_number"
    t.integer  "designer_id"
    t.string   "adjustment_report_url"
    t.string   "adjustment_invoice_url"
    t.float    "total"
    t.float    "taxable_value"
    t.float    "igst"
    t.float    "sgst"
    t.float    "cgst"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string   "report_type"
    t.string   "irn_number"
    t.string   "irn_barcode"
    t.boolean  "is_valid_report",        default: true
  end

  add_index "adjustment_reports", ["designer_id"], name: "index_adjustment_reports_on_designer_id", using: :btree

  create_table "adjustments", force: :cascade do |t|
    t.integer  "amount"
    t.string   "status",                   limit: 255
    t.integer  "designer_id"
    t.text     "notes"
    t.integer  "order_id"
    t.datetime "created_at",                           null: false
    t.datetime "updated_at",                           null: false
    t.datetime "payout_date"
    t.text     "payout_notes"
    t.integer  "return_designer_order_id"
    t.string   "payout_consideration",     limit: 255
    t.integer  "commission_invoice_id"
    t.datetime "payout_uploaded_on"
    t.integer  "designer_order_id"
  end

  add_index "adjustments", ["designer_id"], name: "index_adjustments_on_designer_id", using: :btree
  add_index "adjustments", ["designer_order_id"], name: "index_adjustments_on_designer_order_id", using: :btree
  add_index "adjustments", ["order_id"], name: "index_adjustments_on_order_id", using: :btree
  add_index "adjustments", ["return_designer_order_id"], name: "index_adjustments_on_return_designer_order_id", using: :btree

  create_table "admins", force: :cascade do |t|
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string   "type"
    t.string   "freshdesk_user_id"
    t.integer  "emp_code"
    t.string   "auditor_level"
    t.string   "auditor_type"
  end

  create_table "affiliate_designs", force: :cascade do |t|
    t.integer  "design_id"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "affiliate_designs", ["design_id"], name: "index_affiliate_designs_on_design_id", using: :btree

  create_table "affiliate_orders", force: :cascade do |t|
    t.integer  "affiliate_id"
    t.integer  "order_id"
    t.string   "state"
    t.integer  "commission"
    t.float    "currency_rate"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "affiliate_orders", ["affiliate_id"], name: "index_affiliate_orders_on_affiliate_id", using: :btree
  add_index "affiliate_orders", ["order_id"], name: "index_affiliate_orders_on_order_id", using: :btree

  create_table "affiliates", force: :cascade do |t|
    t.integer  "currency_convert_id"
    t.string   "name"
    t.string   "phone_number"
    t.hstore   "profile"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "affiliates", ["currency_convert_id"], name: "index_affiliates_on_currency_convert_id", using: :btree

  create_table "ahoy_messages", force: :cascade do |t|
    t.integer  "user_id"
    t.string   "user_type"
    t.text     "to"
    t.string   "mailer"
    t.text     "subject"
    t.datetime "sent_at"
    t.string   "token"
    t.datetime "opened_at"
    t.datetime "clicked_at"
  end

  add_index "ahoy_messages", ["token"], name: "index_ahoy_messages_on_token", using: :btree

  create_table "announcements", force: :cascade do |t|
    t.text     "note"
    t.text     "link"
    t.integer  "grade"
    t.string   "live",       limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
    t.datetime "start_date"
    t.datetime "end_date"
  end

  create_table "api_data", force: :cascade do |t|
    t.string   "device_id",              limit: 255
    t.integer  "account_id"
    t.integer  "currency_convert_id"
    t.datetime "created_at",                                        null: false
    t.datetime "updated_at",                                        null: false
    t.string   "app_version",            limit: 255
    t.string   "app_source",             limit: 255
    t.boolean  "wishlist_public",                    default: true
    t.datetime "last_cart_coupon_shown"
  end

  add_index "api_data", ["account_id"], name: "index_api_data_on_account_id", using: :btree
  add_index "api_data", ["currency_convert_id"], name: "index_api_data_on_currency_convert_id", using: :btree
  add_index "api_data", ["device_id"], name: "index_api_data_on_device_id", using: :btree

  create_table "api_notification_settings", force: :cascade do |t|
    t.integer  "api_data_id"
    t.boolean  "price_drop_cart"
    t.boolean  "price_drop_wishlist"
    t.boolean  "quantity_drop_cart"
    t.boolean  "quantity_drop_wishlist"
    t.boolean  "designer_new_uploads"
    t.boolean  "designer_weekly_hot_selling"
    t.boolean  "designer_coupon"
    t.boolean  "user_wishlist"
    t.boolean  "user_rated_designs"
    t.datetime "created_at",                  null: false
    t.datetime "updated_at",                  null: false
  end

  add_index "api_notification_settings", ["api_data_id"], name: "index_api_notification_settings_on_api_data_id", using: :btree

  create_table "api_users", force: :cascade do |t|
    t.string   "device_id",                   null: false
    t.string   "salt",                        null: false
    t.string   "token",                       null: false
    t.string   "device_type",                 null: false
    t.boolean  "blocked",     default: false, null: false
    t.datetime "created_at",                  null: false
    t.datetime "updated_at",                  null: false
  end

  add_index "api_users", ["device_id"], name: "index_api_users_on_device_id", unique: true, using: :btree
  add_index "api_users", ["token"], name: "index_api_users_on_token", unique: true, using: :btree

  create_table "app_ranking_keywords", force: :cascade do |t|
    t.string   "keyword",               limit: 255
    t.integer  "autosuggest_parent_id"
    t.text     "playstore_app_links"
    t.string   "ranking_batch",         limit: 255
    t.integer  "lft"
    t.integer  "rgt"
    t.datetime "created_at",                        null: false
    t.datetime "updated_at",                        null: false
  end

  create_table "app_rankings", force: :cascade do |t|
    t.integer  "app_ranking_keyword_id"
    t.integer  "mirrawapp_id"
    t.text     "ranks"
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "audit_product_scans", force: :cascade do |t|
    t.integer  "rack_audit_id"
    t.string   "barcode_scanned"
    t.integer  "auditable_id"
    t.string   "auditable_type"
    t.string   "status"
    t.integer  "quantity_scanned",  default: 0
    t.integer  "required_quantity", default: 0
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "audit_product_scans", ["auditable_id"], name: "index_audit_product_scans_on_auditable_id", using: :btree
  add_index "audit_product_scans", ["rack_audit_id"], name: "index_audit_product_scans_on_rack_audit_id", using: :btree

  create_table "auth_group", force: :cascade do |t|
    t.string "name", limit: 150, null: false
  end

  add_index "auth_group", ["name"], name: "auth_group_name_a6ea08ec_like", using: :btree
  add_index "auth_group", ["name"], name: "auth_group_name_key", unique: true, using: :btree

  create_table "auth_group_permissions", force: :cascade do |t|
    t.integer "group_id",      null: false
    t.integer "permission_id", null: false
  end

  add_index "auth_group_permissions", ["group_id", "permission_id"], name: "auth_group_permissions_group_id_permission_id_0cd325b0_uniq", unique: true, using: :btree
  add_index "auth_group_permissions", ["group_id"], name: "auth_group_permissions_group_id_b120cbf9", using: :btree
  add_index "auth_group_permissions", ["permission_id"], name: "auth_group_permissions_permission_id_84c5c92e", using: :btree

  create_table "auth_permission", force: :cascade do |t|
    t.string  "name",            limit: 255, null: false
    t.integer "content_type_id",             null: false
    t.string  "codename",        limit: 100, null: false
  end

  add_index "auth_permission", ["content_type_id", "codename"], name: "auth_permission_content_type_id_codename_01ab375a_uniq", unique: true, using: :btree
  add_index "auth_permission", ["content_type_id"], name: "auth_permission_content_type_id_2f476e4b", using: :btree

  create_table "auth_user", force: :cascade do |t|
    t.string   "password",     limit: 128, null: false
    t.datetime "last_login"
    t.boolean  "is_superuser",             null: false
    t.string   "username",     limit: 150, null: false
    t.string   "first_name",   limit: 30,  null: false
    t.string   "last_name",    limit: 150, null: false
    t.string   "email",        limit: 254, null: false
    t.boolean  "is_staff",                 null: false
    t.boolean  "is_active",                null: false
    t.datetime "date_joined",              null: false
  end

  add_index "auth_user", ["username"], name: "auth_user_username_6821ab7c_like", using: :btree
  add_index "auth_user", ["username"], name: "auth_user_username_key", unique: true, using: :btree

  create_table "auth_user_groups", force: :cascade do |t|
    t.integer "user_id",  null: false
    t.integer "group_id", null: false
  end

  add_index "auth_user_groups", ["group_id"], name: "auth_user_groups_group_id_97559544", using: :btree
  add_index "auth_user_groups", ["user_id", "group_id"], name: "auth_user_groups_user_id_group_id_94350c0c_uniq", unique: true, using: :btree
  add_index "auth_user_groups", ["user_id"], name: "auth_user_groups_user_id_6a12ed8b", using: :btree

  create_table "auth_user_user_permissions", force: :cascade do |t|
    t.integer "user_id",       null: false
    t.integer "permission_id", null: false
  end

  add_index "auth_user_user_permissions", ["permission_id"], name: "auth_user_user_permissions_permission_id_1fbb5f2c", using: :btree
  add_index "auth_user_user_permissions", ["user_id", "permission_id"], name: "auth_user_user_permissions_user_id_permission_id_14a6b632_uniq", unique: true, using: :btree
  add_index "auth_user_user_permissions", ["user_id"], name: "auth_user_user_permissions_user_id_a95ead1b", using: :btree

  create_table "authtoken_token", primary_key: "key", force: :cascade do |t|
    t.datetime "created", null: false
    t.integer  "user_id", null: false
  end

  add_index "authtoken_token", ["key"], name: "authtoken_token_key_10f0b77e_like", using: :btree
  add_index "authtoken_token", ["user_id"], name: "authtoken_token_user_id_key", unique: true, using: :btree

  create_table "automated_notification_audiences", force: :cascade do |t|
    t.string   "audience_type",      limit: 255
    t.string   "notification_title", limit: 255
    t.string   "notification_body",  limit: 255
    t.string   "notification_type",  limit: 255
    t.string   "listing_title",      limit: 255
    t.string   "key",                limit: 255
    t.string   "value",              limit: 255
    t.string   "photo_file_name",    limit: 255
    t.string   "photo_content_type", limit: 255
    t.integer  "photo_file_size"
    t.datetime "photo_updated_at"
    t.datetime "created_at",                     null: false
    t.datetime "updated_at",                     null: false
    t.string   "utm_campaign",       limit: 255
  end

  create_table "awb_numbers", force: :cascade do |t|
    t.string   "number",       limit: 255
    t.boolean  "available"
    t.integer  "shipper_id"
    t.datetime "created_at",               null: false
    t.datetime "updated_at",               null: false
    t.string   "service_type"
  end

  create_table "backups", force: :cascade do |t|
    t.string   "item_type",  limit: 255
    t.string   "item_id",    limit: 255
    t.text     "value"
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "bags", force: :cascade do |t|
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
    t.string   "length",     limit: 255
    t.string   "width",      limit: 255
  end

  create_table "bank_details", force: :cascade do |t|
    t.integer  "user_id"
    t.string   "account_holder_name",   limit: 255
    t.string   "bank_name",             limit: 255
    t.string   "branch",                limit: 255
    t.string   "ifsc_code",             limit: 255
    t.string   "account_number",        limit: 255
    t.datetime "created_at",                        null: false
    t.datetime "updated_at",                        null: false
    t.integer  "bank_accountable_id"
    t.string   "bank_accountable_type"
  end

  add_index "bank_details", ["bank_accountable_type", "bank_accountable_id"], name: "bacc", using: :btree

  create_table "banks", force: :cascade do |t|
    t.string   "bank_name",         limit: 255
    t.integer  "account_no_length"
    t.datetime "created_at",                    null: false
    t.datetime "updated_at",                    null: false
  end

  create_table "banner_sliders", force: :cascade do |t|
    t.string   "name",                 limit: 255
    t.text     "link"
    t.integer  "grade"
    t.text     "description"
    t.datetime "start_date"
    t.datetime "end_date"
    t.datetime "created_at",                       null: false
    t.datetime "updated_at",                       null: false
    t.string   "photo_file_name",      limit: 255
    t.string   "photo_content_type",   limit: 255
    t.integer  "photo_file_size"
    t.datetime "photo_updated_at"
    t.boolean  "photo_processing"
    t.text     "country"
    t.string   "link_type",            limit: 255
    t.string   "link_value",           limit: 255
    t.string   "app_source",           limit: 255
    t.float    "aspect_ratio"
    t.string   "alternate_link"
    t.string   "alternate_link_type"
    t.string   "alternate_link_value"
    t.string   "app_name"
  end

  create_table "bestsellers", force: :cascade do |t|
    t.integer  "design_id"
    t.datetime "created_at",               null: false
    t.datetime "updated_at",               null: false
    t.string   "geo",          limit: 255
    t.integer  "design_price"
  end

  add_index "bestsellers", ["design_id"], name: "index_bestsellers_on_design_id", using: :btree

  create_table "blacklists", force: :cascade do |t|
    t.string   "email",       limit: 255
    t.string   "ip_address",  limit: 255
    t.datetime "created_at",              null: false
    t.datetime "updated_at",              null: false
    t.string   "pincode",     limit: 255
    t.text     "description"
  end

  create_table "blocks", force: :cascade do |t|
    t.string   "title",               limit: 255
    t.text     "link"
    t.datetime "created_at",                      null: false
    t.datetime "updated_at",                      null: false
    t.integer  "grade"
    t.integer  "landing_id"
    t.string   "banner_file_name",    limit: 255
    t.string   "banner_content_type", limit: 255
    t.integer  "banner_file_size"
    t.datetime "banner_updated_at"
    t.boolean  "banner_processing"
    t.integer  "board_id"
    t.string   "link_type",           limit: 255
    t.string   "link_value",          limit: 255
    t.string   "country",             limit: 255
    t.string   "app_source",          limit: 255
    t.string   "app_name"
  end

  create_table "boards", force: :cascade do |t|
    t.string   "name",                limit: 255
    t.text     "link"
    t.integer  "grade"
    t.integer  "landing_id"
    t.datetime "created_at",                                      null: false
    t.datetime "updated_at",                                      null: false
    t.string   "banner_file_name",    limit: 255
    t.string   "banner_content_type", limit: 255
    t.integer  "banner_file_size"
    t.datetime "banner_updated_at"
    t.string   "country"
    t.string   "app_source",          limit: 255
    t.string   "board_type"
    t.datetime "start_date"
    t.datetime "end_date"
    t.text     "description"
    t.boolean  "display_items_name"
    t.string   "link_type"
    t.string   "link_value"
    t.boolean  "timer",                           default: false
    t.string   "app_name"
  end

  create_table "boards_tabs", id: false, force: :cascade do |t|
    t.integer "board_id"
    t.integer "tab_id"
  end

  add_index "boards_tabs", ["board_id", "tab_id"], name: "index_boards_tabs_on_board_id_and_tab_id", using: :btree
  add_index "boards_tabs", ["tab_id", "board_id"], name: "index_boards_tabs_on_tab_id_and_board_id", using: :btree

  create_table "bucket_relations", force: :cascade do |t|
    t.integer  "entity_id"
    t.string   "entity_type"
    t.integer  "warehouse_bucket_id"
    t.string   "entity_status",        default: "assigned"
    t.string   "bucket_kind"
    t.datetime "created_at",                                null: false
    t.datetime "updated_at",                                null: false
    t.integer  "warehouse_station_id"
    t.string   "station_code"
    t.integer  "days_passed"
    t.integer  "quantity"
  end

  add_index "bucket_relations", ["entity_type", "entity_id"], name: "index_bucket_relations_on_entity_type_and_entity_id", using: :btree
  add_index "bucket_relations", ["station_code"], name: "index_bucket_relations_on_station_code", using: :btree
  add_index "bucket_relations", ["warehouse_bucket_id"], name: "index_bucket_relations_on_warehouse_bucket_id", using: :btree
  add_index "bucket_relations", ["warehouse_station_id"], name: "index_bucket_relations_on_warehouse_station_id", using: :btree

  create_table "buyers", force: :cascade do |t|
    t.string   "name",       limit: 255
    t.string   "email",      limit: 255
    t.string   "phone",      limit: 255
    t.text     "street"
    t.string   "city",       limit: 255
    t.string   "state",      limit: 255
    t.string   "country",    limit: 255
    t.string   "pincode",    limit: 255
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "buyers", ["email"], name: "index_buyers_on_email", using: :btree

  create_table "cart_archives", force: :cascade do |t|
    t.integer  "cart_id"
    t.datetime "cart_created_at"
    t.datetime "cart_updated_at"
    t.string   "email",              limit: 255
    t.string   "notified",           limit: 255
    t.integer  "coupon_id"
    t.string   "hash1",              limit: 255
    t.datetime "notified_at"
    t.integer  "notification_count",             default: 0
    t.integer  "user_id"
    t.boolean  "used",                           default: false
    t.string   "device_id",          limit: 255
    t.boolean  "cod_available"
    t.datetime "created_at",                                     null: false
    t.datetime "updated_at",                                     null: false
  end

  create_table "carts", force: :cascade do |t|
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string   "email",                  limit: 255
    t.string   "notified",               limit: 255
    t.integer  "coupon_id"
    t.string   "hash1",                  limit: 255
    t.datetime "notified_at"
    t.integer  "notification_count",                 default: 0
    t.integer  "user_id"
    t.boolean  "used",                               default: false
    t.string   "device_id",              limit: 255
    t.boolean  "cod_available"
    t.integer  "wallet_id"
    t.string   "otp",                    limit: 255
    t.string   "user_type",              limit: 255
    t.integer  "api_notification_count"
  end

  add_index "carts", ["created_at"], name: "index_carts_on_created_at", using: :btree
  add_index "carts", ["device_id", "user_id", "used"], name: "index_carts_on_device_id_and_user_id_and_used", using: :btree
  add_index "carts", ["email"], name: "index_trgm_carts_email", using: :btree
  add_index "carts", ["hash1"], name: "index_carts_on_hash1", using: :btree
  add_index "carts", ["user_id"], name: "index_carts_on_user_id", using: :btree
  add_index "carts", ["user_type", "user_id"], name: "index_carts_on_user_type_and_user_id", using: :btree

  create_table "categories", force: :cascade do |t|
    t.string   "name",                     limit: 255
    t.integer  "parent_id"
    t.integer  "lft"
    t.integer  "rgt"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.text     "title"
    t.text     "description"
    t.string   "photo_file_name",          limit: 255
    t.string   "photo_content_type",       limit: 255
    t.integer  "photo_file_size"
    t.datetime "photo_updated_at"
    t.text     "url"
    t.integer  "order"
    t.string   "size_required",            limit: 255
    t.integer  "weight"
    t.string   "international",            limit: 255
    t.boolean  "is_master"
    t.boolean  "hide"
    t.string   "invoice_name",             limit: 255
    t.integer  "invoice_price"
    t.string   "p_name",                   limit: 255
    t.integer  "dynamic_size_chart_state",             default: 0
    t.boolean  "searchable",                           default: false
    t.boolean  "super_child",                          default: false
    t.string   "super_child_label",        limit: 255
    t.string   "hsn_code"
    t.text     "breadcrumb_path"
    t.integer  "category_type",                        default: 0
    t.text     "google_product_category"
    t.text     "mirraw_product_category"
    t.boolean  "show_to_vendor",                       default: true
    t.integer  "eta",                                  default: 0
    t.string   "app_name"
  end

  add_index "categories", ["lft"], name: "index_categories_on_lft", using: :btree
  add_index "categories", ["name"], name: "index_categories_on_name", unique: true, using: :btree

  create_table "categories_designers", force: :cascade do |t|
    t.integer "category_id"
    t.integer "designer_id"
  end

  add_index "categories_designers", ["category_id", "designer_id"], name: "index_categories_designers_on_category_id_and_designer_id", using: :btree

  create_table "categories_designs", id: false, force: :cascade do |t|
    t.integer "design_id"
    t.integer "category_id"
  end

  add_index "categories_designs", ["category_id", "design_id"], name: "index_categories_designs_on_category_id_and_design_id", using: :btree
  add_index "categories_designs", ["design_id", "category_id"], name: "index_categories_designs_on_design_id_and_category_id", using: :btree
  add_index "categories_designs", ["design_id"], name: "index_categories_designs_on_design_id", using: :btree

  create_table "categories_faqs", id: false, force: :cascade do |t|
    t.integer "category_id"
    t.integer "faq_id"
  end

  add_index "categories_faqs", ["category_id"], name: "index_categories_faqs_on_category_id", using: :btree
  add_index "categories_faqs", ["faq_id"], name: "index_categories_faqs_on_faq_id", using: :btree

  create_table "categories_option_types", primary_key: "category_id", force: :cascade do |t|
    t.integer "option_type_id", null: false
  end

  add_index "categories_option_types", ["category_id", "option_type_id"], name: "index_categories_option_types_on_category_id_and_option_type_id", unique: true, using: :btree
  add_index "categories_option_types", ["option_type_id", "category_id"], name: "index_categories_option_types_on_option_type_id_and_category_id", unique: true, using: :btree

  create_table "categories_popular_links", id: false, force: :cascade do |t|
    t.integer "category_id"
    t.integer "popular_link_id"
  end

  add_index "categories_popular_links", ["category_id"], name: "index_categories_popular_links_on_category_id", using: :btree
  add_index "categories_popular_links", ["popular_link_id"], name: "index_categories_popular_links_on_popular_link_id", using: :btree

  create_table "categories_properties", force: :cascade do |t|
    t.integer "category_id"
    t.integer "property_id"
  end

  add_index "categories_properties", ["category_id", "property_id"], name: "index_categories_properties_on_category_id_and_property_id", using: :btree

  create_table "categories_property_values", force: :cascade do |t|
    t.integer "category_id"
    t.integer "property_value_id"
  end

  add_index "categories_property_values", ["category_id", "property_value_id"], name: "unique_index_on_category_property_value", unique: true, using: :btree

  create_table "category_albums", force: :cascade do |t|
    t.integer  "category_id"
    t.string   "album_name",        limit: 255
    t.string   "album_description", limit: 255
    t.boolean  "autopost"
    t.datetime "created_at",                    null: false
    t.datetime "updated_at",                    null: false
  end

  add_index "category_albums", ["autopost", "category_id"], name: "index_category_albums_on_autopost_and_category_id", using: :btree

  create_table "category_albums_fb_pages", id: false, force: :cascade do |t|
    t.integer "category_album_id"
    t.integer "fb_page_id"
  end

  add_index "category_albums_fb_pages", ["category_album_id"], name: "index_category_albums_fb_pages_on_category_album_id", using: :btree
  add_index "category_albums_fb_pages", ["fb_page_id", "category_album_id"], name: "index_fb_page_id_and_category_album_id", using: :btree
  add_index "category_albums_fb_pages", ["fb_page_id"], name: "index_category_albums_fb_pages_on_fb_page_id", using: :btree

  create_table "category_banners", force: :cascade do |t|
    t.string   "name",               limit: 255
    t.integer  "category_id"
    t.string   "photo_file_name",    limit: 255
    t.string   "photo_content_type", limit: 255
    t.integer  "photo_file_size"
    t.datetime "photo_updated_at"
    t.datetime "start_date"
    t.datetime "end_date"
    t.text     "country"
    t.datetime "created_at",                     null: false
    t.datetime "updated_at",                     null: false
  end

  create_table "category_connections", force: :cascade do |t|
    t.integer  "source_category_id"
    t.integer  "referral_category_id"
    t.text     "designs"
    t.datetime "created_at",           null: false
    t.datetime "updated_at",           null: false
  end

  add_index "category_connections", ["source_category_id"], name: "index_category_connections_on_source_category_id", using: :btree

  create_table "category_new_arrivals", force: :cascade do |t|
    t.string   "name",           limit: 255
    t.integer  "category_id"
    t.text     "new_arrival"
    t.text     "best_seller"
    t.text     "best_discount"
    t.boolean  "hide"
    t.datetime "created_at",                 null: false
    t.datetime "updated_at",                 null: false
    t.string   "country",        limit: 255
    t.text     "others"
    t.boolean  "is_top_nav_bar"
  end

  create_table "ckeditor_assets", force: :cascade do |t|
    t.string   "data_file_name",    limit: 255, null: false
    t.string   "data_content_type", limit: 255
    t.integer  "data_file_size"
    t.integer  "assetable_id"
    t.string   "assetable_type",    limit: 30
    t.string   "type",              limit: 30
    t.integer  "width"
    t.integer  "height"
    t.datetime "created_at",                    null: false
    t.datetime "updated_at",                    null: false
  end

  add_index "ckeditor_assets", ["assetable_type", "assetable_id"], name: "idx_ckeditor_assetable", using: :btree
  add_index "ckeditor_assets", ["assetable_type", "type", "assetable_id"], name: "idx_ckeditor_assetable_type", using: :btree

  create_table "claim_requests", force: :cascade do |t|
    t.integer  "designer_issue_id"
    t.integer  "design_id"
    t.datetime "created_at",        null: false
    t.datetime "updated_at",        null: false
    t.integer  "designer_id"
  end

  create_table "cms_blocks", force: :cascade do |t|
    t.integer  "page_id",                null: false
    t.string   "identifier", limit: 255, null: false
    t.text     "content"
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  add_index "cms_blocks", ["page_id", "identifier"], name: "index_cms_blocks_on_page_id_and_identifier", using: :btree

  create_table "cms_categories", force: :cascade do |t|
    t.integer "site_id",                      null: false
    t.string  "label",            limit: 255, null: false
    t.string  "categorized_type", limit: 255, null: false
  end

  add_index "cms_categories", ["site_id", "categorized_type", "label"], name: "index_cms_categories_on_site_id_and_categorized_type_and_label", unique: true, using: :btree

  create_table "cms_categorizations", force: :cascade do |t|
    t.integer "category_id",                  null: false
    t.string  "categorized_type", limit: 255, null: false
    t.integer "categorized_id",               null: false
  end

  add_index "cms_categorizations", ["category_id", "categorized_type", "categorized_id"], name: "index_cms_categorizations_on_cat_id_and_catd_type_and_catd_id", unique: true, using: :btree

  create_table "cms_files", force: :cascade do |t|
    t.integer  "site_id",                                    null: false
    t.integer  "block_id"
    t.string   "label",             limit: 255,              null: false
    t.string   "file_file_name",    limit: 255,              null: false
    t.string   "file_content_type", limit: 255,              null: false
    t.integer  "file_file_size",                             null: false
    t.string   "description",       limit: 2048
    t.integer  "position",                       default: 0, null: false
    t.datetime "created_at",                                 null: false
    t.datetime "updated_at",                                 null: false
  end

  add_index "cms_files", ["site_id", "block_id"], name: "index_cms_files_on_site_id_and_block_id", using: :btree
  add_index "cms_files", ["site_id", "file_file_name"], name: "index_cms_files_on_site_id_and_file_file_name", using: :btree
  add_index "cms_files", ["site_id", "label"], name: "index_cms_files_on_site_id_and_label", using: :btree
  add_index "cms_files", ["site_id", "position"], name: "index_cms_files_on_site_id_and_position", using: :btree

  create_table "cms_layouts", force: :cascade do |t|
    t.integer  "site_id",                                null: false
    t.integer  "parent_id"
    t.string   "app_layout", limit: 255
    t.string   "label",      limit: 255,                 null: false
    t.string   "identifier", limit: 255,                 null: false
    t.text     "content"
    t.text     "css"
    t.text     "js"
    t.integer  "position",               default: 0,     null: false
    t.boolean  "is_shared",              default: false, null: false
    t.datetime "created_at",                             null: false
    t.datetime "updated_at",                             null: false
  end

  add_index "cms_layouts", ["parent_id", "position"], name: "index_cms_layouts_on_parent_id_and_position", using: :btree
  add_index "cms_layouts", ["site_id", "identifier"], name: "index_cms_layouts_on_site_id_and_identifier", unique: true, using: :btree

  create_table "cms_pages", force: :cascade do |t|
    t.integer  "site_id",                                    null: false
    t.integer  "layout_id"
    t.integer  "parent_id"
    t.integer  "target_page_id"
    t.string   "label",          limit: 255,                 null: false
    t.string   "slug",           limit: 255
    t.string   "full_path",      limit: 255,                 null: false
    t.text     "content"
    t.integer  "position",                   default: 0,     null: false
    t.integer  "children_count",             default: 0,     null: false
    t.boolean  "is_published",               default: true,  null: false
    t.boolean  "is_shared",                  default: false, null: false
    t.datetime "created_at",                                 null: false
    t.datetime "updated_at",                                 null: false
  end

  add_index "cms_pages", ["parent_id", "position"], name: "index_cms_pages_on_parent_id_and_position", using: :btree
  add_index "cms_pages", ["site_id", "full_path"], name: "index_cms_pages_on_site_id_and_full_path", using: :btree

  create_table "cms_revisions", force: :cascade do |t|
    t.string   "record_type", limit: 255, null: false
    t.integer  "record_id",               null: false
    t.text     "data"
    t.datetime "created_at"
  end

  add_index "cms_revisions", ["record_type", "record_id", "created_at"], name: "index_cms_revisions_on_record_type_and_record_id_and_created_at", using: :btree

  create_table "cms_sites", force: :cascade do |t|
    t.string  "label",       limit: 255,                 null: false
    t.string  "identifier",  limit: 255,                 null: false
    t.string  "hostname",    limit: 255,                 null: false
    t.string  "path",        limit: 255
    t.string  "locale",      limit: 255, default: "en",  null: false
    t.boolean "is_mirrored",             default: false, null: false
  end

  add_index "cms_sites", ["hostname"], name: "index_cms_sites_on_hostname", using: :btree
  add_index "cms_sites", ["is_mirrored"], name: "index_cms_sites_on_is_mirrored", using: :btree

  create_table "cms_snippets", force: :cascade do |t|
    t.integer  "site_id",                                null: false
    t.string   "label",      limit: 255,                 null: false
    t.string   "identifier", limit: 255,                 null: false
    t.text     "content"
    t.integer  "position",               default: 0,     null: false
    t.boolean  "is_shared",              default: false, null: false
    t.datetime "created_at",                             null: false
    t.datetime "updated_at",                             null: false
  end

  add_index "cms_snippets", ["site_id", "identifier"], name: "index_cms_snippets_on_site_id_and_identifier", unique: true, using: :btree
  add_index "cms_snippets", ["site_id", "position"], name: "index_cms_snippets_on_site_id_and_position", using: :btree

  create_table "cod_requests", force: :cascade do |t|
    t.string   "ip",                   limit: 255
    t.string   "app_source",           limit: 255
    t.string   "pincode",              limit: 255
    t.integer  "design_id"
    t.boolean  "is_cod_available"
    t.string   "city",                 limit: 255
    t.string   "state",                limit: 255
    t.datetime "created_at",                       null: false
    t.datetime "updated_at",                       null: false
    t.integer  "cart_id"
    t.integer  "order_total"
    t.integer  "total_cod"
    t.integer  "line_items_count"
    t.integer  "designer_order_count"
  end

  add_index "cod_requests", ["cart_id", "pincode"], name: "index_cod_requests_on_cart_id_and_pincode", using: :btree

  create_table "collection_logs", force: :cascade do |t|
    t.string   "collection_name", limit: 255
    t.integer  "moved_by"
    t.datetime "created_at",                  null: false
    t.datetime "updated_at",                  null: false
  end

  create_table "collections", force: :cascade do |t|
    t.string   "title",               limit: 255
    t.datetime "created_at",                      null: false
    t.datetime "updated_at",                      null: false
    t.string   "banner_file_name",    limit: 255
    t.string   "banner_content_type", limit: 255
    t.integer  "banner_file_size"
    t.datetime "banner_updated_at"
  end

  create_table "commission_invoices", force: :cascade do |t|
    t.string   "invoice_url",             limit: 255
    t.integer  "designer_id"
    t.float    "total"
    t.datetime "created_at",                                         null: false
    t.datetime "updated_at",                                         null: false
    t.string   "commission_report_url",   limit: 255
    t.float    "commission",                          default: 0.0
    t.float    "cgst",                                default: 0.0
    t.float    "sgst",                                default: 0.0
    t.float    "igst",                                default: 0.0
    t.float    "tcs_cgst",                            default: 0.0
    t.float    "tcs_sgst",                            default: 0.0
    t.float    "tcs_igst",                            default: 0.0
    t.string   "tcs_report_url"
    t.string   "sales_report_url"
    t.string   "sales_return_report_url"
    t.string   "irn_number"
    t.string   "irn_barcode"
    t.boolean  "is_valid_report",                     default: true
  end

  create_table "communication_messages", force: :cascade do |t|
    t.integer  "communication_topic_id"
    t.string   "message"
    t.string   "sent_by_name"
    t.string   "sent_by_department"
    t.integer  "sent_by_id"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "communication_messages", ["communication_topic_id"], name: "index_communication_messages_on_communication_topic_id", using: :btree

  create_table "communication_topics", force: :cascade do |t|
    t.string   "topic"
    t.string   "status",              default: "open"
    t.string   "owned_by_name"
    t.integer  "owned_by_id"
    t.string   "owned_by_department"
    t.integer  "communicable_id"
    t.string   "communicable_type"
    t.datetime "closed_on"
    t.datetime "reopened_on"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "communication_topics", ["topic"], name: "index_communication_topics_on_topic", using: :btree

  create_table "consumables", force: :cascade do |t|
    t.string   "age_range_description", limit: 255
    t.string   "brand",                 limit: 255
    t.string   "certification",         limit: 255
    t.datetime "created_at",                        null: false
    t.datetime "updated_at",                        null: false
  end

  create_table "countries", force: :cascade do |t|
    t.string   "name",                       limit: 255
    t.string   "iso3166_alpha2",             limit: 255
    t.datetime "created_at",                                                                    null: false
    t.datetime "updated_at",                                                                    null: false
    t.integer  "priority"
    t.string   "time_zone",                  limit: 255
    t.decimal  "shipping_multiplier",                    precision: 8, scale: 2, default: 1.0
    t.string   "dial_code",                  limit: 255
    t.string   "crc_state_code",             limit: 255
    t.string   "crc_city",                   limit: 255
    t.string   "crc_pincode",                limit: 255
    t.text     "crc_street"
    t.text     "crc_state"
    t.boolean  "express_delivery_available"
    t.integer  "express_delivery_charge",                                        default: 0
    t.float    "shipping_time",                                                  default: 12.0
  end

  add_index "countries", ["name"], name: "index_countries_on_name", using: :btree

  create_table "coupon_partners", force: :cascade do |t|
    t.string   "email",      limit: 255
    t.string   "name",       limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "coupons", force: :cascade do |t|
    t.string   "name",                limit: 255
    t.integer  "limit"
    t.datetime "start_date"
    t.datetime "end_date"
    t.boolean  "advertise"
    t.string   "code",                limit: 255
    t.integer  "percent_off"
    t.integer  "flat_off"
    t.integer  "min_amount"
    t.integer  "designer_id"
    t.datetime "created_at",                      null: false
    t.datetime "updated_at",                      null: false
    t.string   "coupon_type",         limit: 255
    t.integer  "use_count"
    t.integer  "notified"
    t.integer  "source_order_id"
    t.integer  "created_by"
    t.string   "source_cod",          limit: 255
    t.string   "waive_shipping",      limit: 255
    t.integer  "coupon_used_on_id"
    t.text     "coupon_reason"
    t.integer  "return_id"
    t.boolean  "converted_to_refund"
    t.string   "promotion_type",      limit: 255
    t.integer  "currency_convert_id"
    t.string   "coupon_by"
    t.string   "geo"
  end

  add_index "coupons", ["code"], name: "index_coupons_on_code", using: :btree
  add_index "coupons", ["currency_convert_id"], name: "index_coupons_on_currency_convert_id", using: :btree
  add_index "coupons", ["designer_id"], name: "index_coupons_on_designer_id", using: :btree

  create_table "courier_archives", force: :cascade do |t|
    t.integer  "courier_id"
    t.string   "name",                limit: 255
    t.string   "cod",                 limit: 255
    t.string   "cbd",                 limit: 255
    t.string   "pincode",             limit: 255
    t.string   "pickup",              limit: 255
    t.string   "delivery",            limit: 255
    t.string   "oda",                 limit: 255
    t.string   "city",                limit: 255
    t.string   "district",            limit: 255
    t.string   "state_code",          limit: 255
    t.string   "zone",                limit: 255
    t.string   "wb_tax",              limit: 255
    t.string   "jk_tax",              limit: 255
    t.string   "octroi",              limit: 255
    t.string   "shipper_id",          limit: 255
    t.string   "aramex_express",      limit: 255
    t.string   "aramex_cargo",        limit: 255
    t.string   "aramex_surface",      limit: 255
    t.string   "aramex_customer_pay", limit: 255
    t.string   "delivery_stn_code",   limit: 255
    t.datetime "created_at",                      null: false
    t.datetime "updated_at",                      null: false
  end

  create_table "courier_inscans", force: :cascade do |t|
    t.string   "awb_number"
    t.string   "status"
    t.integer  "inward_bag_id"
    t.string   "remark"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string   "system_awb_number"
  end

  add_index "courier_inscans", ["inward_bag_id"], name: "index_courier_inscans_on_inward_bag_id", using: :btree

  create_table "couriers", force: :cascade do |t|
    t.string   "name",                limit: 255
    t.string   "cod",                 limit: 255
    t.string   "cbd",                 limit: 255
    t.datetime "created_at",                      null: false
    t.datetime "updated_at",                      null: false
    t.string   "pincode",             limit: 255
    t.boolean  "pickup"
    t.boolean  "delivery"
    t.boolean  "oda"
    t.string   "city",                limit: 255
    t.string   "district",            limit: 255
    t.string   "state_code",          limit: 255
    t.string   "zone",                limit: 255
    t.string   "wb_tax",              limit: 255
    t.string   "jk_tax",              limit: 255
    t.string   "octroi",              limit: 255
    t.integer  "shipper_id"
    t.boolean  "aramex_express"
    t.boolean  "aramex_cargo"
    t.boolean  "aramex_surface"
    t.boolean  "aramex_customer_pay"
    t.string   "delivery_stn_code",   limit: 255
  end

  add_index "couriers", ["cbd"], name: "index_couriers_on_cbd", using: :btree
  add_index "couriers", ["cod"], name: "index_couriers_on_cod", using: :btree
  add_index "couriers", ["pincode"], name: "index_couriers_on_pincode", using: :btree
  add_index "couriers", ["shipper_id"], name: "index_couriers_on_shipper_id", using: :btree

  create_table "crc_versions", force: :cascade do |t|
    t.text     "url"
    t.integer  "approver_1_id"
    t.integer  "approver_2_id"
    t.integer  "uploaded_by_id"
    t.boolean  "status"
    t.datetime "approved_on"
    t.datetime "created_at",     null: false
    t.datetime "updated_at",     null: false
  end

  create_table "currency_converts", force: :cascade do |t|
    t.string   "country",                        limit: 255
    t.string   "symbol",                         limit: 255
    t.float    "rate"
    t.datetime "created_at",                                                                         null: false
    t.datetime "updated_at",                                                                         null: false
    t.string   "country_code",                   limit: 255
    t.integer  "round_to",                                                           default: 2,     null: false
    t.string   "iso_code",                       limit: 255,                         default: "",    null: false
    t.string   "hex_symbol",                     limit: 255,                         default: "",    null: false
    t.float    "market_rate"
    t.decimal  "paypal_rate",                                precision: 8, scale: 4
    t.float    "delayed_hours_for_notification"
    t.string   "currency_symbol",                limit: 255
    t.float    "referral_amount",                                                    default: 0.0
    t.boolean  "commercial_invoice",                                                 default: false
    t.integer  "min_cart_value",                                                     default: 0
    t.float    "exchange_rate"
  end

  create_table "delay_design_groups", force: :cascade do |t|
    t.integer "designer_batch_id"
    t.string  "parent_sku"
    t.integer "design_id"
  end

  add_index "delay_design_groups", ["designer_batch_id"], name: "index_delay_design_groups_on_designer_batch_id", using: :btree

  create_table "delay_designables", force: :cascade do |t|
    t.text     "params"
    t.string   "kind",       limit: 255
    t.integer  "design_id"
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "delay_images", force: :cascade do |t|
    t.integer  "design_id"
    t.integer  "image_id"
    t.text     "url"
    t.text     "message"
    t.string   "kind",       limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  add_index "delay_images", ["design_id"], name: "index_delay_images_on_design_id", using: :btree

  create_table "delayed_files", force: :cascade do |t|
    t.string   "name"
    t.integer  "account_id"
    t.string   "file_file_name"
    t.string   "file_content_type"
    t.integer  "file_file_size"
    t.datetime "file_updated_at"
    t.string   "state"
    t.string   "error"
    t.datetime "created_at",        null: false
    t.datetime "updated_at",        null: false
  end

  add_index "delayed_files", ["account_id", "name"], name: "index_delayed_files_on_account_id_and_name", unique: true, using: :btree

  create_table "delayed_jobs", force: :cascade do |t|
    t.integer  "priority",               default: 0
    t.integer  "attempts",               default: 0
    t.text     "handler"
    t.text     "last_error"
    t.datetime "run_at"
    t.datetime "locked_at"
    t.datetime "failed_at"
    t.string   "locked_by",  limit: 255
    t.string   "queue",      limit: 255
    t.datetime "created_at",                         null: false
    t.datetime "updated_at",                         null: false
  end

  add_index "delayed_jobs", ["priority", "run_at"], name: "delayed_jobs_priority", using: :btree
  add_index "delayed_jobs", ["run_at"], name: "index_delayed_jobs_on_run_at", using: :btree

  create_table "delivery_nps_infos", force: :cascade do |t|
    t.string   "order_number",           limit: 255
    t.string   "order_from",             limit: 255
    t.string   "order_to",               limit: 255
    t.datetime "promised_delivery_date"
    t.datetime "revised_delivery_date"
    t.datetime "actual_delivery_date"
    t.integer  "element_id"
    t.string   "element_type",           limit: 255
    t.datetime "created_at",                         null: false
    t.datetime "updated_at",                         null: false
  end

  add_index "delivery_nps_infos", ["element_type", "element_id"], name: "index_delivery_nps_infos_on_element", unique: true, using: :btree

  create_table "design_addons", force: :cascade do |t|
    t.integer  "design_id"
    t.integer  "addon_product_id"
    t.datetime "created_at",                   null: false
    t.datetime "updated_at",                   null: false
    t.integer  "discount_percent", default: 0
  end

  add_index "design_addons", ["design_id", "addon_product_id"], name: "index_design_addons_on_design_id_and_addon_product_id", unique: true, using: :btree

  create_table "design_archives", force: :cascade do |t|
    t.integer  "design_id"
    t.datetime "design_created_at"
    t.datetime "design_updated_at"
    t.text     "description"
    t.string   "title",                      limit: 255
    t.integer  "price"
    t.integer  "designer_id"
    t.integer  "eta"
    t.integer  "tmp_category"
    t.integer  "quantity",                                                       default: 1
    t.string   "color",                      limit: 255
    t.text     "specification"
    t.boolean  "published",                                                      default: false
    t.integer  "grade",                                                          default: 40
    t.integer  "discount_percent"
    t.integer  "discount_price"
    t.string   "cached_slug",                limit: 255
    t.string   "design_code",                limit: 255
    t.integer  "min_wholesale_quantity"
    t.integer  "min_wholesale_price"
    t.integer  "weight"
    t.boolean  "retail"
    t.integer  "sell_count"
    t.boolean  "follow_master_addon"
    t.string   "state",                      limit: 255
    t.datetime "last_in_stock"
    t.datetime "last_sold_out"
    t.datetime "last_seller_out_of_stock"
    t.datetime "last_blocked"
    t.datetime "last_banned"
    t.datetime "deleted_on"
    t.datetime "reject_on"
    t.datetime "review_on"
    t.text     "notes"
    t.integer  "return_count",                                                   default: 0
    t.string   "designable_type",            limit: 255
    t.integer  "designable_id"
    t.integer  "clicks"
    t.decimal  "conversion_rate",                        precision: 8, scale: 2
    t.integer  "coupon_id"
    t.string   "in_catalog",                 limit: 255,                         default: "Y",      null: false
    t.string   "package_details",            limit: 255
    t.string   "region",                     limit: 255
    t.string   "pattern",                    limit: 255
    t.string   "embellish",                  limit: 255
    t.integer  "in_catalog_one"
    t.text     "accessories"
    t.integer  "international_grade"
    t.string   "unbxd_status",               limit: 255,                         default: "failed"
    t.float    "average_rating"
    t.integer  "total_review"
    t.integer  "designer_batch_id"
    t.boolean  "ignore_in_google_feed",                                          default: false
    t.integer  "previous_qty",                                                   default: 0
    t.datetime "created_at",                                                                        null: false
    t.datetime "updated_at",                                                                        null: false
    t.boolean  "ready_to_ship"
    t.boolean  "dynamic_pricing"
    t.boolean  "mirraw_certified"
    t.integer  "in_international_catalog"
    t.boolean  "skip_qc"
    t.integer  "return_initiated_count"
    t.integer  "designer_collection_id"
    t.float    "quality_level"
    t.datetime "last_posted_at"
    t.integer  "buy_get_free"
    t.float    "graded_rating"
    t.integer  "wishlists_count"
    t.integer  "likes_count"
    t.hstore   "dom_grade",                                                      default: {},       null: false
    t.hstore   "int_grade",                                                      default: {},       null: false
    t.boolean  "premium"
    t.boolean  "modified_design_data"
    t.integer  "international_grade_mobile"
    t.integer  "grade_mobile"
    t.integer  "gst_rate"
    t.integer  "hsn_code"
    t.integer  "actual_weight"
    t.boolean  "featured_product"
    t.integer  "in_stock_warehouse"
    t.integer  "reordering_percent"
    t.integer  "ordering_quantity"
  end

  create_table "design_clusters", force: :cascade do |t|
    t.integer  "cluster_id"
    t.integer  "design_id"
    t.integer  "score",            default: 0
    t.string   "state",            default: "new"
    t.datetime "created_at",                       null: false
    t.datetime "updated_at",                       null: false
    t.integer  "state_multiplier", default: 0
    t.integer  "winner_design_id"
  end

  add_index "design_clusters", ["cluster_id", "design_id"], name: "index_design_clusters_on_cluster_id_and_design_id", unique: true, using: :btree
  add_index "design_clusters", ["design_id"], name: "index_design_clusters_on_design_id", unique: true, using: :btree

  create_table "design_groups", force: :cascade do |t|
    t.integer "parent_design_id"
  end

  create_table "design_nodes", force: :cascade do |t|
    t.integer  "dimension_id"
    t.string   "dimension_type",    limit: 20
    t.float    "score",                        default: 0.0
    t.float    "incremental_score",            default: 0.0
    t.integer  "click",                        default: 0
    t.integer  "order",                        default: 0
    t.integer  "engagement",                   default: 0
    t.integer  "designs_count",                default: 0
    t.boolean  "reload_count",                 default: true
    t.datetime "created_at",                                  null: false
    t.datetime "updated_at",                                  null: false
  end

  add_index "design_nodes", ["dimension_id", "dimension_type"], name: "index_design_nodes_on_dimension_id_and_dimension_type", unique: true, using: :btree
  add_index "design_nodes", ["reload_count"], name: "index_design_nodes_on_reload_count", using: :btree

  create_table "design_performances", force: :cascade do |t|
    t.integer  "design_id"
    t.integer  "sell_count",  default: 0
    t.integer  "clicks",      default: 0
    t.integer  "impressions", default: 0
    t.integer  "add_to_cart", default: 0
    t.integer  "flush_key",   default: 0
    t.datetime "created_at",              null: false
    t.datetime "updated_at",              null: false
    t.integer  "views"
  end

  add_index "design_performances", ["design_id", "flush_key"], name: "index_design_performances_on_design_id_and_flush_key", unique: true, using: :btree
  add_index "design_performances", ["design_id"], name: "index_design_performances_on_design_id", using: :btree

  create_table "design_promotion_pipe_lines", force: :cascade do |t|
    t.integer  "design_id"
    t.integer  "promotion_pipe_line_id"
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
    t.datetime "start_date"
    t.datetime "end_date"
  end

  add_index "design_promotion_pipe_lines", ["design_id", "promotion_pipe_line_id"], name: "unique_design_and_promotion", unique: true, using: :btree

  create_table "design_scores", force: :cascade do |t|
    t.integer  "design_id",                            null: false
    t.integer  "click",                  default: 0
    t.integer  "incremental_click",      default: 0
    t.integer  "order",                  default: 0
    t.integer  "incremental_order",      default: 0
    t.integer  "engagement",             default: 0
    t.integer  "incremental_engagement", default: 0
    t.float    "credit",                 default: 0.0
    t.datetime "last_posted_at"
    t.datetime "created_at",                           null: false
    t.datetime "updated_at",                           null: false
  end

  add_index "design_scores", ["design_id"], name: "index_design_scores_on_design_id", unique: true, using: :btree

  create_table "design_shipper_weights", force: :cascade do |t|
    t.integer  "shipper_id"
    t.integer  "design_id"
    t.float    "weight",     default: 0.0
    t.datetime "created_at",               null: false
    t.datetime "updated_at",               null: false
  end

  create_table "designable_values", force: :cascade do |t|
    t.string   "name"
    t.string   "p_name"
    t.string   "value_type"
    t.string   "designable_type"
    t.datetime "created_at",      null: false
    t.datetime "updated_at",      null: false
  end

  create_table "designer_batches", force: :cascade do |t|
    t.string   "filename",      limit: 255
    t.integer  "no_of_records"
    t.integer  "passed"
    t.integer  "failed"
    t.integer  "designer_id"
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
    t.string   "version",       limit: 255
  end

  add_index "designer_batches", ["designer_id"], name: "index_designer_batches_on_designer_id", using: :btree

  create_table "designer_collections", force: :cascade do |t|
    t.integer  "designer_id"
    t.string   "name",               limit: 255
    t.integer  "position"
    t.string   "cached_slug",        limit: 255
    t.string   "photo_file_name",    limit: 255
    t.string   "photo_content_type", limit: 255
    t.integer  "photo_file_size"
    t.datetime "photo_updated_at"
    t.datetime "created_at",                     null: false
    t.datetime "updated_at",                     null: false
  end

  add_index "designer_collections", ["cached_slug"], name: "index_designer_collections_on_cached_slug", using: :btree
  add_index "designer_collections", ["designer_id"], name: "index_designer_collections_on_designer_id", using: :btree

  create_table "designer_invoices", force: :cascade do |t|
    t.integer  "designer_id"
    t.string   "invoice_file_file_name",    limit: 255
    t.string   "invoice_file_content_type", limit: 255
    t.integer  "invoice_file_file_size"
    t.datetime "invoice_file_updated_at"
    t.datetime "from_date"
    t.datetime "to_date"
    t.string   "status",                    limit: 255
    t.string   "comments",                  limit: 255
    t.datetime "created_at",                            null: false
    t.datetime "updated_at",                            null: false
    t.string   "payout_version",            limit: 255
    t.integer  "designer_order_id"
  end

  create_table "designer_issues", force: :cascade do |t|
    t.integer  "designer_id"
    t.integer  "order_id"
    t.string   "issue_type",                    limit: 255
    t.integer  "design_id"
    t.integer  "design_snapshot_price"
    t.datetime "created_at",                                null: false
    t.datetime "updated_at",                                null: false
    t.string   "state",                         limit: 255
    t.datetime "resolved_at"
    t.integer  "line_item_id"
    t.integer  "designer_order_id"
    t.text     "notes"
    t.datetime "email_to_vendor_at"
    t.integer  "account_id"
    t.integer  "replacement_designer_order_id"
    t.integer  "replacement_line_item_id"
    t.boolean  "claim_mark"
    t.string   "qc_issue_image1_file_name",     limit: 255
    t.string   "qc_issue_image1_content_type",  limit: 255
    t.integer  "qc_issue_image1_file_size"
    t.datetime "qc_issue_image1_updated_at"
    t.string   "qc_issue_image2_file_name",     limit: 255
    t.string   "qc_issue_image2_content_type",  limit: 255
    t.integer  "qc_issue_image2_file_size"
    t.datetime "qc_issue_image2_updated_at"
    t.string   "qc_issue_image3_file_name",     limit: 255
    t.string   "qc_issue_image3_content_type",  limit: 255
    t.integer  "qc_issue_image3_file_size"
    t.datetime "qc_issue_image3_updated_at"
  end

  add_index "designer_issues", ["account_id"], name: "index_designer_issues_on_account_id", using: :btree
  add_index "designer_issues", ["designer_order_id"], name: "index_designer_issues_on_designer_order_id", using: :btree
  add_index "designer_issues", ["email_to_vendor_at"], name: "index_designer_issues_on_email_to_vendor_at", using: :btree
  add_index "designer_issues", ["line_item_id"], name: "index_designer_issues_on_line_item_id", using: :btree
  add_index "designer_issues", ["order_id"], name: "index_designer_issues_on_order_id", using: :btree
  add_index "designer_issues", ["replacement_designer_order_id"], name: "index_designer_issues_on_replacement_designer_order_id", using: :btree
  add_index "designer_issues", ["replacement_line_item_id"], name: "index_designer_issues_on_replacement_line_item_id", using: :btree
  add_index "designer_issues", ["state", "claim_mark"], name: "index_designer_issues_on_state_and_claim_mark", using: :btree

  create_table "designer_order_archives", force: :cascade do |t|
    t.integer  "designer_order_id"
    t.datetime "designer_order_created_at"
    t.datetime "designer_order_updated_at"
    t.boolean  "email_sent"
    t.boolean  "sms_sent"
    t.datetime "pickup"
    t.integer  "designer_id"
    t.integer  "order_id"
    t.integer  "shipping"
    t.string   "state",                        limit: 255
    t.integer  "total"
    t.integer  "payout"
    t.text     "tracking"
    t.string   "tracking_num",                 limit: 255
    t.datetime "delivered_at"
    t.integer  "discount"
    t.string   "designer_payout_status",       limit: 255
    t.datetime "designer_payout_date"
    t.string   "designer_payout_notes",        limit: 255
    t.string   "tracking_partner",             limit: 255
    t.integer  "item_total"
    t.integer  "coupon_id"
    t.text     "notes"
    t.integer  "transaction_rate"
    t.datetime "in_critical_at"
    t.integer  "return_designer_order_id"
    t.datetime "package_received_on"
    t.string   "package_received_by",          limit: 255
    t.string   "package_status",               limit: 255, default: "packed"
    t.datetime "confirmed_at"
    t.integer  "shipper_id"
    t.boolean  "fedex_serviceable"
    t.string   "shipment_status",              limit: 255, default: "pending"
    t.integer  "discount_provided",                        default: 0
    t.integer  "mirraw_shipping_cost"
    t.boolean  "delhivery_serviceable"
    t.boolean  "ecomexpress_serviceable"
    t.integer  "priority_shipper_id"
    t.string   "ship_to",                      limit: 255
    t.boolean  "city_courier_serviceable"
    t.boolean  "bluedart_serviceable"
    t.boolean  "dtdc_serviceable"
    t.boolean  "speedpost_serviceable"
    t.boolean  "fedex_mirraw_serviceable"
    t.text     "shipment_error"
    t.integer  "priority_shipper_cod_id"
    t.boolean  "aramex_serviceable"
    t.string   "rack_code",                    limit: 255
    t.string   "shipment_update",              limit: 255, default: "none",    null: false
    t.integer  "rack_list_id"
    t.boolean  "replacement",                              default: false
    t.boolean  "tirupati_courier_serviceable"
    t.boolean  "gati_serviceable"
    t.boolean  "ship_delight_serviceable"
    t.datetime "created_at",                                                   null: false
    t.datetime "updated_at",                                                   null: false
  end

  create_table "designer_orders", force: :cascade do |t|
    t.boolean  "email_sent"
    t.boolean  "sms_sent"
    t.datetime "pickup"
    t.integer  "designer_id"
    t.integer  "order_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.integer  "shipping"
    t.string   "state",                        limit: 255
    t.integer  "total"
    t.float  "payout"
    t.text     "tracking"
    t.string   "tracking_num",                 limit: 255
    t.datetime "delivered_at"
    t.integer  "discount"
    t.string   "designer_payout_status",       limit: 255
    t.datetime "designer_payout_date"
    t.string   "designer_payout_notes",        limit: 255
    t.string   "tracking_partner",             limit: 255
    t.integer  "item_total"
    t.integer  "coupon_id"
    t.text     "notes"
    t.float    "transaction_rate"
    t.datetime "in_critical_at"
    t.integer  "return_designer_order_id"
    t.datetime "package_received_on"
    t.string   "package_received_by",          limit: 255
    t.string   "package_status",               limit: 255, default: "packed"
    t.datetime "confirmed_at"
    t.integer  "shipper_id"
    t.boolean  "fedex_serviceable"
    t.string   "shipment_status",              limit: 255, default: "pending"
    t.integer  "mirraw_shipping_cost"
    t.integer  "discount_provided",                        default: 0
    t.boolean  "delhivery_serviceable"
    t.boolean  "ecomexpress_serviceable"
    t.integer  "priority_shipper_id"
    t.string   "ship_to",                      limit: 255
    t.boolean  "city_courier_serviceable"
    t.boolean  "bluedart_serviceable"
    t.boolean  "dtdc_serviceable"
    t.boolean  "speedpost_serviceable"
    t.boolean  "fedex_mirraw_serviceable"
    t.text     "shipment_error"
    t.integer  "priority_shipper_cod_id"
    t.boolean  "aramex_serviceable"
    t.string   "rack_code",                    limit: 255
    t.string   "shipment_update",              limit: 255, default: "none",    null: false
    t.integer  "rack_list_id"
    t.boolean  "replace_order",                            default: false
    t.boolean  "tirupati_courier_serviceable"
    t.boolean  "gati_serviceable"
    t.boolean  "ship_delight_serviceable"
    t.integer  "promotion_discount",                       default: 0
    t.integer  "scaled_total",                             default: 0
    t.integer  "scaled_discount"
    t.datetime "completed_at"
    t.string   "recent_tracking_number",       limit: 255
    t.integer  "invoice_id"
    t.string   "rack_check_done_by",           limit: 255
    t.datetime "rack_check_done_on"
    t.boolean  "shadowfax_serviceable"
    t.string   "cancel_reason",                limit: 255
    t.string   "inscan_tracking_num",          limit: 255
    t.string   "invoice_state",                limit: 255
    t.string   "invoice_number",               limit: 255
    t.boolean  "rapid_delivery_serviceable"
    t.string   "credit_note_url"
    t.string   "credit_note_number"
    t.string   "gst_status",                               default: "release"
    t.float    "gst_tax",                                  default: 0.0
    t.boolean  "xpress_bees_serviceable"
    t.integer  "bulk_shipment_id"
    t.integer  "inward_bag_id"
    t.float    "tcs_tax",                                  default: 0.0
    t.datetime "credit_note_date"
    t.integer  "purchase_order_id"
    t.integer  "release_payout_management_id"
    t.integer  "payout_management_id"
    t.boolean  "clickpost_serviceable"
    t.string   "invoice_type"
    t.float    "tds_tax",                                  default: 0.0
    t.integer  "warehouse_address_id",                     default: 1
    t.hstore   "other_details",                            default: {}
    t.boolean  "wowexpress_serviceable"
    t.boolean  "ekart_serviceable"
    t.string   "zone",                                     default: "NA"
    t.float    "weight",                                   default: 0.0
    t.boolean  "kerryindev_serviceable"
    t.boolean  "smartr_serviceable"
    t.boolean  "first_call_serviceable"
    t.string   "app_name"
    t.boolean  "abhilaya_serviceable"
  end

  add_index "designer_orders", ["bulk_shipment_id"], name: "index_designer_orders_on_bulk_shipment_id", where: "(bulk_shipment_id IS NOT NULL)", using: :btree
  add_index "designer_orders", ["confirmed_at"], name: "index_designer_orders_on_confirmed_at", using: :btree
  add_index "designer_orders", ["created_at"], name: "index_designer_orders_on_created_at", using: :btree
  add_index "designer_orders", ["designer_id"], name: "index_designer_orders_on_designer_id", using: :btree
  add_index "designer_orders", ["inward_bag_id"], name: "index_designer_orders_on_inward_bag_id", where: "(inward_bag_id IS NOT NULL)", using: :btree
  add_index "designer_orders", ["order_id"], name: "index_designer_orders_on_order_id", using: :btree
  add_index "designer_orders", ["pickup"], name: "designer_orders_on_pickup_tracking", using: :btree
  add_index "designer_orders", ["pickup"], name: "index_designer_orders_on_pickup", using: :btree
  add_index "designer_orders", ["purchase_order_id"], name: "index_designer_orders_on_purchase_order_id", using: :btree
  add_index "designer_orders", ["rack_list_id"], name: "index_designer_orders_on_rack_list_id", using: :btree
  add_index "designer_orders", ["state"], name: "index_designer_orders_on_state", using: :btree

  create_table "designer_shippers", force: :cascade do |t|
    t.integer  "designer_id"
    t.integer  "shipper_id"
    t.integer  "priority",                               default: -1,    null: false
    t.boolean  "enabled",                                default: false
    t.datetime "created_at",                                             null: false
    t.datetime "updated_at",                                             null: false
    t.boolean  "international",                          default: true,  null: false
    t.boolean  "domestic",                               default: true,  null: false
    t.string   "delhivery_client_warehouse", limit: 255
    t.boolean  "cod",                                    default: false, null: false
  end

  create_table "designers", force: :cascade do |t|
    t.string   "name",                           limit: 255
    t.string   "email",                          limit: 255
    t.string   "phone",                          limit: 255
    t.string   "alt_phone",                      limit: 255
    t.string   "city",                           limit: 255
    t.string   "state",                          limit: 255
    t.string   "street",                         limit: 255
    t.string   "country",                        limit: 255
    t.string   "pincode",                        limit: 255
    t.text     "description"
    t.text     "url"
    t.string   "photo_file_name",                limit: 255
    t.string   "photo_content_type",             limit: 255
    t.integer  "photo_file_size"
    t.datetime "photo_updated_at"
    t.boolean  "published"
    t.text     "policy"
    t.integer  "transaction_rate"
    t.string   "account_holder_name",            limit: 255
    t.string   "bank_name",                      limit: 255
    t.string   "branch",                         limit: 255
    t.string   "ifsc_code",                      limit: 255
    t.string   "account_number",                 limit: 255
    t.integer  "grade"
    t.string   "cached_slug",                    limit: 255
    t.datetime "vacation_start_date"
    t.datetime "vacation_end_date"
    t.text     "vacation_message"
    t.string   "fb_page_name",                   limit: 255
    t.string   "fb_page_id",                     limit: 255
    t.string   "fb_page_token",                  limit: 255
    t.string   "fb_page_link",                   limit: 255
    t.datetime "last_fb_post_at"
    t.boolean  "wholesaler"
    t.boolean  "banned",                                     default: false
    t.string   "state_machine",                  limit: 255
    t.datetime "last_approved_on"
    t.datetime "last_review_on"
    t.datetime "last_banned_on"
    t.string   "seo_title",                      limit: 255
    t.string   "business_name",                  limit: 255
    t.string   "business_street",                limit: 255
    t.string   "business_city",                  limit: 255
    t.string   "business_state",                 limit: 255
    t.string   "business_country",               limit: 255
    t.string   "business_pincode",               limit: 255
    t.string   "pan_no",                         limit: 255
    t.string   "tan_no",                         limit: 255
    t.boolean  "allow_addon",                                default: false
    t.string   "state_code",                     limit: 255
    t.boolean  "mirraw_shipping"
    t.boolean  "cod"
    t.string   "pickup_name",                    limit: 255
    t.string   "pickup_company_name",            limit: 255
    t.string   "pickup_phone",                   limit: 255
    t.string   "pickup_street",                  limit: 255
    t.string   "pickup_city",                    limit: 255
    t.string   "pickup_state_code",              limit: 255
    t.string   "pickup_pincode",                 limit: 255
    t.boolean  "upload_panel",                               default: true
    t.boolean  "photo_processing"
    t.string   "p_name",                         limit: 255
    t.float    "average_rating"
    t.string   "gati_vendor_code",               limit: 255
    t.string   "vat_no",                         limit: 255
    t.string   "cst_no",                         limit: 255
    t.string   "compliant",                      limit: 255
    t.string   "cancelled_cheque_file_name",     limit: 255
    t.string   "cancelled_cheque_content_type",  limit: 255
    t.integer  "cancelled_cheque_file_size"
    t.datetime "cancelled_cheque_updated_at"
    t.string   "pan_card_file_name",             limit: 255
    t.string   "pan_card_content_type",          limit: 255
    t.integer  "pan_card_file_size"
    t.datetime "pan_card_updated_at"
    t.string   "vat_certificate_file_name",      limit: 255
    t.string   "vat_certificate_content_type",   limit: 255
    t.integer  "vat_certificate_file_size"
    t.datetime "vat_certificate_updated_at"
    t.string   "tin_certificate_file_name",      limit: 255
    t.string   "tin_certificate_content_type",   limit: 255
    t.integer  "tin_certificate_file_size"
    t.datetime "tin_certificate_updated_at"
    t.string   "cst_certificate_file_name",      limit: 255
    t.string   "cst_certificate_content_type",   limit: 255
    t.integer  "cst_certificate_file_size"
    t.datetime "cst_certificate_updated_at"
    t.integer  "additional_discount_percent"
    t.date     "additional_discount_start_date"
    t.date     "additional_discount_end_date"
    t.integer  "warning_notification"
    t.boolean  "automated_facebook_posting"
    t.boolean  "score_flag"
    t.integer  "score"
    t.text     "score_hash"
    t.string   "gst_no",                         limit: 255
    t.string   "gst_certificate_file_name",      limit: 255
    t.string   "gst_certificate_content_type",   limit: 255
    t.integer  "gst_certificate_file_size"
    t.datetime "gst_certificate_updated_at"
    t.text     "gst_address"
    t.string   "taxpayer_trade_name",            limit: 255
    t.integer  "invoice_number",                             default: 0
    t.datetime "inventory_processed_at"
    t.datetime "approved_on"
    t.string   "zone",                           limit: 255
    t.string   "designer_type",                  limit: 255
    t.boolean  "hsn_approved",                               default: false
    t.integer  "claimed_review_count",                       default: 0
    t.string   "in_catalog",                                 default: "all"
    t.datetime "last_on_hold_at"
    t.datetime "last_inactive_at"
    t.boolean  "bulk_dispatch"
    t.integer  "inactive_alert_count",                       default: 0
    t.integer  "owner_id"
    t.string   "banned_reason"
    t.integer  "transfer_model_rate",                        default: 0
    t.boolean  "exclude_gst"
    t.integer  "eta",                                        default: 0
    t.string   "production_type"
    t.float    "ship_time"
    t.float    "domestic_transaction_rate"
    t.boolean  "unicommerce_enabled"
    t.integer  "warehouse_address_id",                       default: 1
    t.string   "app_name"
  end

  add_index "designers", ["cached_slug"], name: "index_designers_on_cached_slug", unique: true, using: :btree
  add_index "designers", ["state_machine"], name: "index_designers_on_state_machine", using: :btree

  create_table "designs", force: :cascade do |t|
    t.text     "description"
    t.string   "title",                      limit: 255
    t.integer  "price"
    t.integer  "designer_id"
    t.integer  "eta"
    t.integer  "tmp_category"
    t.integer  "quantity",                                                       default: 1
    t.string   "color",                      limit: 255
    t.text     "specification"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.boolean  "published",                                                      default: false
    t.integer  "grade",                                                          default: 40
    t.integer  "discount_percent"
    t.integer  "discount_price"
    t.string   "cached_slug",                limit: 255
    t.string   "design_code",                limit: 255
    t.integer  "min_wholesale_quantity"
    t.integer  "min_wholesale_price"
    t.integer  "weight"
    t.boolean  "retail"
    t.integer  "sell_count"
    t.boolean  "follow_master_addon"
    t.string   "state",                      limit: 255
    t.datetime "last_in_stock"
    t.datetime "last_sold_out"
    t.datetime "last_seller_out_of_stock"
    t.datetime "last_blocked"
    t.datetime "last_banned"
    t.datetime "deleted_on"
    t.datetime "reject_on"
    t.datetime "review_on"
    t.text     "notes"
    t.integer  "return_count",                                                   default: 0
    t.string   "designable_type",            limit: 255
    t.integer  "designable_id"
    t.integer  "clicks"
    t.decimal  "conversion_rate",                        precision: 8, scale: 2
    t.integer  "coupon_id"
    t.boolean  "in_catalog",                                                     default: true,     null: false
    t.string   "package_details",            limit: 255
    t.string   "region",                     limit: 255
    t.string   "pattern",                    limit: 255
    t.string   "embellish",                  limit: 255
    t.text     "accessories"
    t.integer  "in_catalog_one"
    t.string   "unbxd_status",               limit: 255,                         default: "failed"
    t.integer  "international_grade"
    t.integer  "designer_batch_id"
    t.float    "average_rating",                                                 default: 0.0
    t.integer  "total_review"
    t.integer  "ignore_in_google_feed",                                          default: 0
    t.integer  "previous_qty",                                                   default: 0
    t.boolean  "ready_to_ship"
    t.boolean  "dynamic_pricing",                                                default: false,    null: false
    t.boolean  "mirraw_certified",                                               default: false
    t.boolean  "skip_qc",                                                        default: false
    t.integer  "return_initiated_count",                                         default: 0
    t.integer  "designer_collection_id"
    t.float    "quality_level",                                                  default: 1.0
    t.datetime "last_posted_at"
    t.float    "graded_rating"
    t.integer  "buy_get_free"
    t.integer  "wishlists_count"
    t.integer  "likes_count"
    t.hstore   "dom_grade",                                                      default: {},       null: false
    t.hstore   "int_grade",                                                      default: {},       null: false
    t.boolean  "premium"
    t.boolean  "modified_design_data"
    t.integer  "international_grade_mobile"
    t.integer  "grade_mobile"
    t.integer  "in_stock_warehouse"
    t.integer  "reordering_percent"
    t.integer  "ordering_quantity"
    t.integer  "gst_rate"
    t.integer  "hsn_code"
    t.integer  "actual_weight"
    t.boolean  "featured_product"
    t.integer  "similar_count"
    t.integer  "design_group_id"
    t.integer  "transfer_price"
    t.string   "last_in_stock_by"
    t.boolean  "has_video",                                                      default: false
    t.string   "app_name"
  end

  add_index "designs", ["cached_slug"], name: "index_designs_on_cached_slug", unique: true, using: :btree
  add_index "designs", ["created_at", "sell_count"], name: "index_designs_on_created_at_and_sell_count", order: {"created_at"=>:desc, "sell_count"=>:desc}, using: :btree
  add_index "designs", ["design_group_id"], name: "index_designs_on_design_group_id", using: :btree
  add_index "designs", ["designable_type"], name: "index_designs_on_designable_type", using: :btree
  add_index "designs", ["designer_batch_id"], name: "index_designs_on_designer_batch_id", using: :btree
  add_index "designs", ["designer_collection_id", "state"], name: "index_designs_on_designer_collection_id_and_state", using: :btree
  add_index "designs", ["designer_id"], name: "index_designs_on_designer_id", using: :btree
  add_index "designs", ["dom_grade"], name: "index_designs_on_dom_grade", using: :gist
  add_index "designs", ["grade"], name: "index_designs_on_grade", using: :btree
  add_index "designs", ["int_grade"], name: "index_designs_on_int_grade", using: :gist
  add_index "designs", ["state"], name: "index_designs_on_state", using: :btree

  create_table "designs_property_values", id: false, force: :cascade do |t|
    t.integer "design_id"
    t.integer "property_value_id"
  end

  add_index "designs_property_values", ["design_id"], name: "index_designs_property_values_on_design_id", using: :btree

  create_table "designs_story_collections", force: :cascade do |t|
    t.integer  "design_id"
    t.integer  "story_collection_id"
    t.datetime "created_at",          null: false
    t.datetime "updated_at",          null: false
  end

  add_index "designs_story_collections", ["design_id"], name: "index_designs_story_collections_on_design_id", using: :btree
  add_index "designs_story_collections", ["story_collection_id"], name: "index_designs_story_collections_on_story_collection_id", using: :btree

  create_table "designs_video_listings", force: :cascade do |t|
    t.integer  "video_listing_id"
    t.integer  "design_id"
    t.integer  "priority"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  create_table "dhl_ecom_pickups", force: :cascade do |t|
    t.string   "handover_id",                limit: 255
    t.string   "pickup_label_file_name",     limit: 255
    t.string   "pickup_label_content_type",  limit: 255
    t.integer  "pickup_label_file_size"
    t.datetime "pickup_label_updated_at"
    t.string   "handover_note_file_name",    limit: 255
    t.string   "handover_note_content_type", limit: 255
    t.integer  "handover_note_file_size"
    t.datetime "handover_note_updated_at"
    t.string   "pickup_status",              limit: 255
    t.hstore   "pickup_details",                         default: {}, null: false
    t.datetime "created_at",                                          null: false
    t.datetime "updated_at",                                          null: false
  end

  create_table "dirty_entities", force: :cascade do |t|
    t.integer  "entity_id"
    t.string   "entity_type"
    t.text     "reason"
    t.integer  "warehouse_station_id"
    t.datetime "created_at",           null: false
    t.datetime "updated_at",           null: false
  end

  add_index "dirty_entities", ["entity_type", "entity_id"], name: "index_dirty_entities_on_entity_type_and_entity_id", using: :btree
  add_index "dirty_entities", ["warehouse_station_id"], name: "index_dirty_entities_on_warehouse_station_id", using: :btree

  create_table "discount_line_items", force: :cascade do |t|
    t.integer  "line_item_id"
    t.integer  "return_id"
    t.integer  "order_id"
    t.integer  "discount_percent"
    t.integer  "price"
    t.integer  "quantity"
    t.string   "discount_on",      limit: 255
    t.string   "return_reason",    limit: 255
    t.datetime "created_at",                   null: false
    t.datetime "updated_at",                   null: false
  end

  create_table "django_admin_log", force: :cascade do |t|
    t.datetime "action_time",                 null: false
    t.text     "object_id"
    t.string   "object_repr",     limit: 200, null: false
    t.integer  "action_flag",     limit: 2,   null: false
    t.text     "change_message",              null: false
    t.integer  "content_type_id"
    t.integer  "user_id",                     null: false
  end

  add_index "django_admin_log", ["content_type_id"], name: "django_admin_log_content_type_id_c4bce8eb", using: :btree
  add_index "django_admin_log", ["user_id"], name: "django_admin_log_user_id_c564eba6", using: :btree

  create_table "django_content_type", force: :cascade do |t|
    t.string "app_label", limit: 100, null: false
    t.string "model",     limit: 100, null: false
  end

  add_index "django_content_type", ["app_label", "model"], name: "django_content_type_app_label_model_76bd3d3b_uniq", unique: true, using: :btree

  create_table "django_migrations", force: :cascade do |t|
    t.string   "app",     limit: 255, null: false
    t.string   "name",    limit: 255, null: false
    t.datetime "applied",             null: false
  end

  create_table "django_session", primary_key: "session_key", force: :cascade do |t|
    t.text     "session_data", null: false
    t.datetime "expire_date",  null: false
  end

  add_index "django_session", ["expire_date"], name: "django_session_expire_date_a5c62663", using: :btree
  add_index "django_session", ["session_key"], name: "django_session_session_key_c0390e0f_like", using: :btree

  create_table "dynamic_landing_pages", force: :cascade do |t|
    t.string   "name",           limit: 255
    t.string   "template",       limit: 255
    t.text     "imageurls"
    t.text     "links"
    t.string   "cached_slug",    limit: 255
    t.text     "country"
    t.string   "app_source",     limit: 255
    t.text     "design_ids"
    t.text     "view_more_url"
    t.datetime "created_at",                 null: false
    t.datetime "updated_at",                 null: false
    t.string   "seo_list_label", limit: 255
  end

  add_index "dynamic_landing_pages", ["name"], name: "index_dynamic_landing_pages_on_name", unique: true, using: :btree

  create_table "dynamic_prices", force: :cascade do |t|
    t.integer  "design_id"
    t.integer  "country_id"
    t.float    "scale",                    default: 1.0
    t.datetime "created_at",                             null: false
    t.datetime "updated_at",                             null: false
    t.string   "country_code", limit: 255
  end

  add_index "dynamic_prices", ["country_code", "design_id"], name: "index_dynamic_prices_on_country_code_and_design_id", unique: true, using: :btree
  add_index "dynamic_prices", ["design_id"], name: "index_dynamic_prices_on_design_id", using: :btree

  create_table "dynamic_size_charts", force: :cascade do |t|
    t.integer  "category_id"
    t.integer  "designer_id"
    t.boolean  "default",                          default: false
    t.text     "size_chart"
    t.string   "outline_file_name",    limit: 255
    t.string   "outline_content_type", limit: 255
    t.integer  "outline_file_size"
    t.datetime "outline_updated_at"
    t.datetime "created_at",                                         null: false
    t.datetime "updated_at",                                         null: false
    t.string   "upload_by",                        default: "Admin"
    t.integer  "design_id"
  end

  add_index "dynamic_size_charts", ["category_id", "designer_id"], name: "index_dynamic_size_charts_on_category_id_and_designer_id", using: :btree
  add_index "dynamic_size_charts", ["design_id"], name: "index_dynamic_size_charts_on_design_id", using: :btree

  create_table "dynamic_templates", force: :cascade do |t|
    t.string   "template_type", limit: 255
    t.string   "title",         limit: 255
    t.string   "name",          limit: 255
    t.text     "body"
    t.string   "sample_id",     limit: 255
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
  end

  create_table "email_logs", force: :cascade do |t|
    t.string   "loggable_type", limit: 255, null: false
    t.integer  "loggable_id",               null: false
    t.string   "email_type",    limit: 255, null: false
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
  end

  add_index "email_logs", ["loggable_id"], name: "index_email_logs_on_loggable_id", using: :btree

  create_table "errors", force: :cascade do |t|
    t.string   "name",                     limit: 255
    t.text     "link"
    t.integer  "grade"
    t.text     "description"
    t.datetime "start_date"
    t.datetime "end_date"
    t.datetime "created_at",                           null: false
    t.datetime "updated_at",                           null: false
    t.string   "brand_image_file_name",    limit: 255
    t.string   "brand_image_content_type", limit: 255
    t.integer  "brand_image_file_size"
    t.datetime "brand_image_updated_at"
  end

  create_table "events", force: :cascade do |t|
    t.string   "note_type"
    t.text     "notes"
    t.datetime "event_timestamp"
    t.string   "done_by"
    t.integer  "account_id"
    t.integer  "eventable_id"
    t.string   "eventable_type"
  end

  add_index "events", ["account_id"], name: "index_events_on_account_id", using: :btree
  add_index "events", ["eventable_type", "eventable_id"], name: "index_events_on_eventable_type_and_eventable_id", using: :btree

  create_table "facebook_posts", force: :cascade do |t|
    t.text     "design_ids"
    t.text     "operation"
    t.string   "album_name",  limit: 255
    t.string   "description", limit: 255
    t.datetime "created_at",              null: false
    t.datetime "updated_at",              null: false
  end

  create_table "facebook_reactions", force: :cascade do |t|
    t.string   "fb_post_id",    limit: 255
    t.string   "design_ids",    limit: 255
    t.string   "poster_type",   limit: 255
    t.integer  "poster_id"
    t.text     "reactions"
    t.date     "posted_at"
    t.date     "next_fetch_at"
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
  end

  create_table "failed_designs", force: :cascade do |t|
    t.text     "design_code"
    t.text     "title"
    t.text     "category"
    t.text     "package_details"
    t.text     "weight_in_gms"
    t.text     "fabric_of_saree"
    t.text     "work"
    t.text     "type_name"
    t.float    "width_of_saree_in_inches"
    t.float    "length_of_saree_in_metres"
    t.text     "saree_color"
    t.text     "blouse_availability"
    t.text     "blouse_as_shown_in_the_image"
    t.integer  "size_of_blouse_in_cms"
    t.text     "fabric_of_blouse"
    t.text     "blouse_color"
    t.text     "blouse_work"
    t.text     "petticoat_availability"
    t.integer  "size_of_petticoat_metres"
    t.text     "color_of_petticoat"
    t.text     "fabric_of_petticoat"
    t.text     "image"
    t.text     "image1"
    t.text     "image2"
    t.text     "image3"
    t.text     "image4"
    t.text     "tag_list"
    t.integer  "quantity"
    t.integer  "price"
    t.integer  "discount_percent"
    t.text     "occasion"
    t.text     "look"
    t.text     "saree_border"
    t.text     "pallu_style"
    t.text     "region"
    t.text     "pattern"
    t.text     "embellish"
    t.text     "celebrity"
    t.text     "description"
    t.text     "product_type"
    t.text     "errors_found"
    t.integer  "designer_batch_id"
    t.datetime "created_at",                   null: false
    t.datetime "updated_at",                   null: false
  end

  add_index "failed_designs", ["designer_batch_id"], name: "index_failed_designs_on_designer_batch_id", using: :btree

  create_table "faqs", force: :cascade do |t|
    t.text     "question"
    t.text     "answer"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  create_table "faqs_property_values", id: false, force: :cascade do |t|
    t.integer "faq_id"
    t.integer "property_value_id"
  end

  add_index "faqs_property_values", ["faq_id"], name: "index_faqs_property_values_on_faq_id", using: :btree
  add_index "faqs_property_values", ["property_value_id"], name: "index_faqs_property_values_on_property_value_id", using: :btree

  create_table "fashion_update_categories", force: :cascade do |t|
    t.integer  "parent_id"
    t.integer  "lft"
    t.integer  "rgt"
    t.string   "name",                      limit: 255
    t.text     "blog_category_title"
    t.text     "blog_category_description"
    t.datetime "created_at",                            null: false
    t.datetime "updated_at",                            null: false
    t.string   "cached_slug",               limit: 255
  end

  add_index "fashion_update_categories", ["name"], name: "index_fashion_update_categories_on_name", using: :btree

  create_table "fashion_updates", force: :cascade do |t|
    t.string   "blog_title",                     limit: 255
    t.text     "body"
    t.string   "blog_master_photo_file_name",    limit: 255
    t.string   "blog_master_photo_content_type", limit: 255
    t.integer  "blog_master_photo_file_size"
    t.datetime "blog_master_photo_updated_at"
    t.string   "cached_slug",                    limit: 255
    t.text     "meta_title"
    t.text     "meta_description"
    t.string   "meta_keywords",                  limit: 255
    t.datetime "created_at",                                 null: false
    t.datetime "updated_at",                                 null: false
    t.string   "display_title",                  limit: 255
  end

  add_index "fashion_updates", ["blog_title"], name: "index_fashion_updates_on_blog_title", unique: true, using: :btree

  create_table "fashion_updates_fashion_update_categories", id: false, force: :cascade do |t|
    t.integer "fashion_update_id"
    t.integer "fashion_update_category_id"
  end

  add_index "fashion_updates_fashion_update_categories", ["fashion_update_id", "fashion_update_category_id"], name: "index_blog_id_and_blog_category_id", using: :btree
  add_index "fashion_updates_fashion_update_categories", ["fashion_update_id"], name: "index_blog_id", using: :btree

  create_table "fb_pages", force: :cascade do |t|
    t.string   "name",            limit: 255
    t.string   "fb_id",           limit: 255
    t.string   "access_token",    limit: 255
    t.string   "page_link",       limit: 255
    t.integer  "admin_id"
    t.datetime "created_at",                  null: false
    t.datetime "updated_at",                  null: false
    t.datetime "last_fb_post_at"
  end

  add_index "fb_pages", ["admin_id"], name: "index_fb_pages_on_admin_id", using: :btree

  create_table "features", force: :cascade do |t|
    t.integer  "design_id"
    t.datetime "start_date"
    t.datetime "end_date"
    t.integer  "grade"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  add_index "features", ["design_id"], name: "index_features_on_design_id", using: :btree

  create_table "feeds", force: :cascade do |t|
    t.boolean  "active",                        default: true
    t.boolean  "processing",                    default: false
    t.datetime "last_attempted_at"
    t.datetime "last_completed_at"
    t.datetime "last_enqueued_on"
    t.text     "configuration"
    t.string   "name",              limit: 255
    t.string   "feed_type",         limit: 255
    t.datetime "next_run_at"
    t.datetime "created_at",                                    null: false
    t.datetime "updated_at",                                    null: false
    t.string   "state"
    t.datetime "last_crashed_at"
    t.integer  "parent_feed_id"
  end

  add_index "feeds", ["parent_feed_id"], name: "index_feeds_on_parent_feed_id", using: :btree

  create_table "filter_definitions", force: :cascade do |t|
    t.string   "name",          limit: 255
    t.text     "query"
    t.text     "parameters"
    t.text     "typecast_hash"
    t.string   "klass_type",    limit: 255, null: false
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
  end

  create_table "filter_groups", force: :cascade do |t|
    t.string   "name",          limit: 255
    t.text     "filters"
    t.text     "output_ids"
    t.integer  "output_count"
    t.datetime "last_fetch_at"
    t.string   "klass_type",    limit: 255
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
  end

  create_table "filter_notifier_schedulers", force: :cascade do |t|
    t.integer  "filter_group_id"
    t.integer  "dynamic_template_id"
    t.boolean  "active"
    t.datetime "next_run_at"
    t.integer  "interval_days"
    t.string   "notifier_type",       limit: 255
    t.string   "from_email_id",       limit: 255
    t.string   "bcc_email_id",        limit: 255
    t.datetime "created_at",                      null: false
    t.datetime "updated_at",                      null: false
  end

  add_index "filter_notifier_schedulers", ["dynamic_template_id"], name: "index_filter_notifier_schedulers_on_dynamic_template_id", using: :btree
  add_index "filter_notifier_schedulers", ["filter_group_id"], name: "index_filter_notifier_schedulers_on_filter_group_id", using: :btree

  create_table "follows", force: :cascade do |t|
    t.integer  "followable_id",                               null: false
    t.string   "followable_type", limit: 255,                 null: false
    t.integer  "follower_id",                                 null: false
    t.string   "follower_type",   limit: 255,                 null: false
    t.boolean  "blocked",                     default: false, null: false
    t.datetime "created_at",                                  null: false
    t.datetime "updated_at",                                  null: false
  end

  add_index "follows", ["followable_id", "followable_type"], name: "fk_followables", using: :btree
  add_index "follows", ["follower_id", "follower_type"], name: "fk_follows", using: :btree

  create_table "footwears", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "freshdesk_tickets", force: :cascade do |t|
    t.string   "status"
    t.string   "source"
    t.integer  "ticket_number"
    t.integer  "order_id"
    t.datetime "created_at",      null: false
    t.datetime "updated_at",      null: false
    t.string   "agent_email"
    t.string   "customer_email"
    t.text     "description"
    t.integer  "support_text_id"
  end

  add_index "freshdesk_tickets", ["order_id"], name: "index_freshdesk_tickets_on_order_id", using: :btree
  add_index "freshdesk_tickets", ["support_text_id"], name: "index_freshdesk_tickets_on_support_text_id", using: :btree
  add_index "freshdesk_tickets", ["ticket_number", "order_id"], name: "index_freshdesk_tickets_on_ticket_number_and_order_id", unique: true, using: :btree

  create_table "friendly_id_slugs", force: :cascade do |t|
    t.string   "slug",                      null: false
    t.integer  "sluggable_id",              null: false
    t.string   "sluggable_type", limit: 50
    t.string   "scope"
    t.datetime "created_at"
  end

  add_index "friendly_id_slugs", ["slug", "sluggable_type", "scope"], name: "index_friendly_id_slugs_on_slug_and_sluggable_type_and_scope", unique: true, using: :btree
  add_index "friendly_id_slugs", ["slug", "sluggable_type"], name: "index_friendly_id_slugs_on_slug_and_sluggable_type", using: :btree
  add_index "friendly_id_slugs", ["sluggable_id"], name: "index_friendly_id_slugs_on_sluggable_id", using: :btree
  add_index "friendly_id_slugs", ["sluggable_type"], name: "index_friendly_id_slugs_on_sluggable_type", using: :btree

  create_table "friends", force: :cascade do |t|
    t.string   "uid",        limit: 255
    t.string   "first_name", limit: 255
    t.string   "last_name",  limit: 255
    t.string   "gender",     limit: 255
    t.integer  "user_id"
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  add_index "friends", ["user_id"], name: "index_friends_on_user_id", using: :btree

  create_table "frontpages", force: :cascade do |t|
    t.string   "name",                 limit: 255
    t.text     "link"
    t.integer  "grade"
    t.text     "description"
    t.datetime "created_at",                                       null: false
    t.datetime "updated_at",                                       null: false
    t.string   "photo_file_name",      limit: 255
    t.string   "photo_content_type",   limit: 255
    t.integer  "photo_file_size"
    t.datetime "photo_updated_at"
    t.datetime "start_date"
    t.datetime "end_date"
    t.boolean  "photo_processing"
    t.boolean  "mobile_menu",                      default: false
    t.string   "link_type",            limit: 255
    t.string   "link_value",           limit: 255
    t.text     "country"
    t.string   "app_source",           limit: 255
    t.string   "category",             limit: 255
    t.string   "alternate_link"
    t.string   "alternate_link_type"
    t.string   "alternate_link_value"
  end

  add_index "frontpages", ["mobile_menu"], name: "index_frontpages_on_mobile_menu", using: :btree

  create_table "ga_identifiers", force: :cascade do |t|
    t.string   "view"
    t.text     "segment"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  create_table "ga_sessions", force: :cascade do |t|
    t.date     "date"
    t.integer  "count"
    t.integer  "ga_identifier_id"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "ga_sessions", ["ga_identifier_id"], name: "index_ga_sessions_on_ga_identifier_id", using: :btree

  create_table "gift_card_orders", force: :cascade do |t|
    t.integer  "gift_card_id"
    t.string   "number"
    t.string   "pay_type"
    t.string   "state"
    t.string   "name"
    t.string   "email"
    t.string   "recipient_name"
    t.string   "recipient_email"
    t.integer  "paid_amount"
    t.text     "message"
    t.integer  "mail_sent_count",    default: 0
    t.float    "currency_rate"
    t.float    "paid_currency_rate"
    t.string   "currency_code"
    t.integer  "sender_user_id"
    t.integer  "recipient_user_id"
    t.hstore   "other_details",      default: {}
    t.text     "transaction_id"
    t.datetime "created_at",                      null: false
    t.datetime "updated_at",                      null: false
  end

  add_index "gift_card_orders", ["gift_card_id"], name: "index_gift_card_orders_on_gift_card_id", using: :btree
  add_index "gift_card_orders", ["recipient_user_id"], name: "index_gift_card_orders_on_recipient_user_id", using: :btree
  add_index "gift_card_orders", ["sender_user_id"], name: "index_gift_card_orders_on_sender_user_id", using: :btree
  add_index "gift_card_orders", ["state"], name: "index_gift_card_orders_on_state", where: "((state)::text = 'sane'::text)", using: :btree

  create_table "gift_cards", force: :cascade do |t|
    t.string   "name"
    t.string   "image_file_name"
    t.string   "image_content_type"
    t.integer  "image_file_size"
    t.datetime "image_updated_at"
    t.datetime "created_at",                        null: false
    t.datetime "updated_at",                        null: false
    t.boolean  "visible",            default: true
  end

  create_table "google_analytics_data", force: :cascade do |t|
    t.integer  "metric_id"
    t.string   "metric_type", limit: 255
    t.text     "data_hash"
    t.datetime "fetched_at"
    t.datetime "created_at",              null: false
    t.datetime "updated_at",              null: false
    t.date     "date"
  end

  add_index "google_analytics_data", ["metric_id", "metric_type", "date"], name: "index_google_analytics_data_on_metric_id_type_date", unique: true, using: :btree

  create_table "grading_promotions", force: :cascade do |t|
    t.integer  "promotable_id"
    t.string   "promotable_type"
    t.string   "state",           default: "promoted"
    t.datetime "created_at",                           null: false
    t.datetime "updated_at",                           null: false
  end

  add_index "grading_promotions", ["promotable_type", "promotable_id"], name: "index_grading_promotions_on_promotable_type_and_promotable_id", unique: true, using: :btree

  create_table "gradings", force: :cascade do |t|
    t.string   "name",                     limit: 255
    t.string   "grading_csv_file_name",    limit: 255
    t.string   "grading_csv_content_type", limit: 255
    t.integer  "grading_csv_file_size"
    t.datetime "grading_csv_updated_at"
    t.text     "notes"
    t.text     "url"
    t.text     "constant_value"
    t.text     "app_source"
    t.string   "file_state",               limit: 255
    t.string   "status",                   limit: 255
    t.integer  "account_id"
    t.datetime "run_at"
    t.string   "category",                 limit: 255
    t.integer  "roll_out",                             default: 0
    t.string   "geo",                      limit: 255
    t.string   "error",                    limit: 255
    t.datetime "created_at",                                       null: false
    t.datetime "updated_at",                                       null: false
    t.text     "category_ids"
  end

  create_table "header_infos", force: :cascade do |t|
    t.string   "meta_tag",           limit: 255
    t.text     "description_header"
    t.string   "keyword_header",     limit: 255
    t.integer  "horoscope_id"
    t.datetime "created_at",                     null: false
    t.datetime "updated_at",                     null: false
    t.text     "content"
    t.string   "page_title",         limit: 255
  end

  create_table "help_center_headers", force: :cascade do |t|
    t.string "name"
  end

  create_table "home_decors", force: :cascade do |t|
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
    t.string   "length",     limit: 255
    t.string   "width",      limit: 255
    t.string   "height",     limit: 255
  end

  create_table "homepages", force: :cascade do |t|
    t.integer  "homepageble_id"
    t.string   "homepageble_type"
    t.datetime "created_at",       null: false
    t.datetime "updated_at",       null: false
    t.integer  "board_id"
  end

  create_table "horoscope_links", force: :cascade do |t|
    t.string   "url",          limit: 255
    t.integer  "horoscope_id"
    t.datetime "created_at",               null: false
    t.datetime "updated_at",               null: false
    t.string   "display_text", limit: 255
  end

  create_table "horoscopes", force: :cascade do |t|
    t.string   "name",                      limit: 255
    t.datetime "start_date"
    t.datetime "end_date"
    t.text     "content"
    t.string   "photo_file_name",           limit: 255
    t.string   "photo_content_type",        limit: 255
    t.integer  "photo_file_size"
    t.datetime "photo_updated_at"
    t.boolean  "photo_processing"
    t.boolean  "original_photo_processing"
    t.datetime "created_at",                            null: false
    t.datetime "updated_at",                            null: false
    t.string   "meta_tag",                  limit: 255
    t.string   "keyword_header",            limit: 255
    t.text     "description_header"
    t.string   "horoscope_title",           limit: 255
  end

  create_table "images", force: :cascade do |t|
    t.string   "kind",                      limit: 255
    t.integer  "design_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string   "photo_file_name",           limit: 255
    t.string   "photo_content_type",        limit: 255
    t.integer  "photo_file_size"
    t.datetime "photo_updated_at"
    t.boolean  "photo_processing"
    t.boolean  "original_photo_processing",             default: false
    t.integer  "height"
    t.integer  "width"
  end

  add_index "images", ["design_id"], name: "index_images_on_design_id", using: :btree

  create_table "inward_bags", force: :cascade do |t|
    t.string   "number"
    t.string   "status",            default: "open"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.integer  "inscanned_count"
    t.integer  "successful_count"
    t.string   "bag_report_url"
    t.integer  "purchase_order_id"
  end

  add_index "inward_bags", ["number"], name: "index_inward_bags_on_number", using: :btree
  add_index "inward_bags", ["purchase_order_id"], name: "index_inward_bags_on_purchase_order_id", using: :btree

  create_table "inward_details", force: :cascade do |t|
    t.integer  "line_item_id"
    t.boolean  "mark_inscanned",      default: false
    t.boolean  "mark_received",       default: false
    t.boolean  "rack_assigned",       default: false
    t.integer  "mark_inscanned_by"
    t.integer  "mark_received_by"
    t.integer  "rack_assigned_by"
    t.datetime "created_at",                          null: false
    t.datetime "updated_at",                          null: false
    t.string   "rack_assigned_notes"
    t.boolean  "qc_done"
    t.string   "qc_fail_reason"
    t.integer  "bad_rack_list_id"
  end

  add_index "inward_details", ["id", "line_item_id"], name: "index_inward_details_on_id_and_line_item_id", unique: true, using: :btree
  add_index "inward_details", ["line_item_id"], name: "index_inward_details_on_line_item_id", unique: true, using: :btree

  create_table "islamics", force: :cascade do |t|
    t.string   "length"
    t.string   "width"
    t.string   "height"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "item_promotion_trackings", force: :cascade do |t|
    t.integer  "line_item_id"
    t.integer  "promotion_tracking_id"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "item_promotion_trackings", ["line_item_id", "promotion_tracking_id"], name: "index_item_tracking_on_item_id_and_promotion_id", unique: true, using: :btree
  add_index "item_promotion_trackings", ["line_item_id"], name: "index_item_promotion_trackings_on_line_item_id", using: :btree
  add_index "item_promotion_trackings", ["promotion_tracking_id"], name: "index_item_promotion_trackings_on_promotion_tracking_id", using: :btree

  create_table "jackets", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "jewellery", force: :cascade do |t|
    t.datetime "created_at",                 null: false
    t.datetime "updated_at",                 null: false
    t.string   "height",         limit: 255
    t.string   "width",          limit: 255
    t.string   "speciality",     limit: 255
    t.string   "care",           limit: 255
    t.string   "setting",        limit: 255
    t.string   "finish",         limit: 255
    t.string   "earings_height", limit: 255
    t.string   "earings_width",  limit: 255
    t.string   "carat",          limit: 255
  end

  create_table "juspay_customers", force: :cascade do |t|
    t.string  "customer_id"
    t.integer "user_id"
  end

  create_table "kids", force: :cascade do |t|
    t.datetime "created_at",     null: false
    t.datetime "updated_at",     null: false
    t.string   "chunari_length"
  end

  create_table "kurta", force: :cascade do |t|
    t.string   "chunari_length", limit: 255
    t.datetime "created_at",                 null: false
    t.datetime "updated_at",                 null: false
  end

  create_table "kurtis", force: :cascade do |t|
    t.string   "length",     limit: 255
    t.boolean  "side_slit"
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "landing_images", force: :cascade do |t|
    t.string   "photo_file_name",    limit: 255
    t.string   "photo_content_type", limit: 255
    t.integer  "photo_file_size"
    t.datetime "photo_updated_at"
    t.datetime "created_at",                     null: false
    t.datetime "updated_at",                     null: false
  end

  create_table "landings", force: :cascade do |t|
    t.string   "label",                limit: 255
    t.text     "link"
    t.datetime "created_at",                                       null: false
    t.datetime "updated_at",                                       null: false
    t.string   "banner_file_name",     limit: 255
    t.string   "banner_content_type",  limit: 255
    t.integer  "banner_file_size"
    t.datetime "banner_updated_at"
    t.boolean  "banner_processing"
    t.string   "banner1_file_name",    limit: 255
    t.string   "banner1_content_type", limit: 255
    t.integer  "banner1_file_size"
    t.datetime "banner1_updated_at"
    t.string   "banner2_file_name",    limit: 255
    t.string   "banner2_content_type", limit: 255
    t.integer  "banner2_file_size"
    t.datetime "banner2_updated_at"
    t.string   "banner3_file_name",    limit: 255
    t.string   "banner3_content_type", limit: 255
    t.integer  "banner3_file_size"
    t.datetime "banner3_updated_at"
    t.boolean  "slider"
    t.text     "banner_1_first_link"
    t.text     "banner_2_first_link"
    t.text     "banner_3_first_link"
    t.boolean  "category_landing"
    t.integer  "side_menu_sort"
    t.boolean  "side_menu"
    t.boolean  "dual_link1",                       default: false
    t.boolean  "dual_link2",                       default: false
    t.boolean  "dual_link3",                       default: false
    t.text     "banner_1_second_link"
    t.text     "banner_2_second_link"
    t.text     "banner_3_second_link"
    t.string   "app_name"
  end

  create_table "lanes", force: :cascade do |t|
    t.string   "from_location"
    t.string   "to_location"
    t.integer  "eta",           default: 0
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
  end

  create_table "ledgers", force: :cascade do |t|
    t.string   "ledger_url"
    t.datetime "created_at",                     null: false
    t.datetime "updated_at",                     null: false
    t.integer  "opening_balance"
    t.integer  "closing_balance"
    t.boolean  "is_valid_ledger", default: true
    t.boolean  "is_freezed",      default: true
    t.text     "logs"
  end

  create_table "lehengas", force: :cascade do |t|
    t.datetime "created_at",                   null: false
    t.datetime "updated_at",                   null: false
    t.string   "width",            limit: 255
    t.string   "length",           limit: 255
    t.boolean  "blouse_available"
    t.string   "blouse_size",      limit: 255
    t.string   "blouse_fabric",    limit: 255
    t.string   "max_waist_size",   limit: 255
    t.string   "min_hip_size",     limit: 255
    t.string   "max_hip_size",     limit: 255
    t.boolean  "lining_available"
    t.string   "lining_size",      limit: 255
    t.boolean  "blouse_image"
    t.string   "dupatta_length",   limit: 255
    t.string   "model_size",       limit: 255
    t.string   "min_waist_size",   limit: 255
  end

  create_table "line_item_addons", force: :cascade do |t|
    t.integer  "line_item_id"
    t.integer  "addon_type_value_id"
    t.integer  "snapshot_price"
    t.string   "received",                limit: 255
    t.string   "status",                  limit: 255
    t.datetime "created_at",                                             null: false
    t.datetime "updated_at",                                             null: false
    t.string   "snapshot_payable_to",     limit: 255, default: "mirraw", null: false
    t.string   "notes",                   limit: 255, default: ""
    t.integer  "snapshot_discount_price"
    t.integer  "additional_payment_id"
    t.float    "scaling_factor",                      default: 1.0
    t.integer  "size_chart_id"
  end

  add_index "line_item_addons", ["addon_type_value_id"], name: "index_line_item_addons_on_addon_type_value_id", using: :btree
  add_index "line_item_addons", ["line_item_id"], name: "index_line_item_addons_on_line_item_id", using: :btree

  create_table "line_item_archives", force: :cascade do |t|
    t.integer  "line_item_id"
    t.datetime "line_item_created_at"
    t.datetime "line_item_updated_at"
    t.integer  "design_id"
    t.integer  "cart_id"
    t.integer  "quantity",                             default: 1
    t.text     "note"
    t.integer  "designer_order_id"
    t.integer  "snapshot_price"
    t.string   "received",                 limit: 255
    t.string   "status",                   limit: 255
    t.integer  "return_id"
    t.integer  "return_designer_order_id"
    t.integer  "variant_id"
    t.string   "received_by",              limit: 255
    t.datetime "received_on"
    t.integer  "shipment_id"
    t.integer  "inbound_shipment_id"
    t.integer  "snapshot_discount_price"
    t.string   "state",                    limit: 255
    t.boolean  "design_quantity_reduce",               default: false
    t.string   "qc_pending",               limit: 255
    t.string   "qc_done",                  limit: 255
    t.string   "stitching_pending",        limit: 255
    t.string   "stitching_done",           limit: 255
    t.string   "stitching_required",       limit: 255
    t.integer  "qc_done_by"
    t.integer  "stitching_done_by"
    t.datetime "qc_done_on"
    t.datetime "stitching_done_on"
    t.string   "stitching_sent",           limit: 255
    t.datetime "stitching_sent_on"
    t.integer  "stitching_sent_by"
    t.integer  "stitching_received_by"
    t.datetime "measuremnet_received_on"
    t.integer  "measuremnet_received_by"
    t.string   "measuremnet_confirmed",    limit: 255
    t.datetime "fabric_measured_on"
    t.integer  "fabric_measured_by"
    t.boolean  "replacement",                          default: false
    t.string   "app_source",               limit: 255
    t.string   "source",                   limit: 255
    t.string   "snapshot_country_code",    limit: 255
    t.string   "snapshot_currency_rate",   limit: 255
    t.boolean  "qc_status"
    t.datetime "created_at",                                           null: false
    t.datetime "updated_at",                                           null: false
  end

  create_table "line_items", force: :cascade do |t|
    t.integer  "design_id"
    t.integer  "cart_id"
    t.integer  "quantity",                             default: 1
    t.datetime "created_at"
    t.datetime "updated_at"
    t.text     "note"
    t.integer  "designer_order_id"
    t.integer  "snapshot_price"
    t.string   "received",                 limit: 255
    t.string   "status",                   limit: 255
    t.integer  "return_id"
    t.integer  "return_designer_order_id"
    t.integer  "variant_id"
    t.string   "received_by",              limit: 255
    t.datetime "received_on"
    t.integer  "shipment_id"
    t.integer  "inbound_shipment_id"
    t.integer  "snapshot_discount_price"
    t.boolean  "design_quantity_reduce",               default: false
    t.string   "qc_pending",               limit: 255
    t.string   "qc_done",                  limit: 255
    t.string   "stitching_pending",        limit: 255
    t.string   "stitching_done",           limit: 255
    t.string   "stitching_required",       limit: 255
    t.integer  "qc_done_by"
    t.integer  "stitching_done_by"
    t.datetime "qc_done_on"
    t.datetime "stitching_done_on"
    t.string   "stitching_sent",           limit: 255
    t.datetime "stitching_sent_on"
    t.integer  "stitching_sent_by"
    t.integer  "stitching_received_by"
    t.datetime "measuremnet_received_on"
    t.integer  "measuremnet_received_by"
    t.string   "measuremnet_confirmed",    limit: 255
    t.datetime "fabric_measured_on"
    t.integer  "fabric_measured_by"
    t.string   "app_source",               limit: 255
    t.string   "source",                   limit: 255
    t.string   "snapshot_country_code",    limit: 255
    t.string   "snapshot_currency_rate",   limit: 255
    t.boolean  "qc_status"
    t.float    "scaling_factor",                       default: 1.0,   null: false
    t.integer  "vendor_selling_price"
    t.string   "issue_resolved_by",        limit: 255
    t.string   "issue_resolve_message",    limit: 255
    t.datetime "issue_created_at"
    t.string   "issue_created_by",         limit: 255
    t.boolean  "claim_flag"
    t.float    "rtv_weight"
    t.integer  "return_quantity"
    t.string   "issue_status",             limit: 255
    t.string   "issue_message",            limit: 255
    t.string   "sent_to_invoice",          limit: 255
    t.integer  "check_items_done_by"
    t.datetime "check_items_done_at"
    t.string   "amazon_order_item_code",   limit: 255
    t.integer  "rack_list_id"
    t.text     "rtv_reason"
    t.boolean  "unpacking_status"
    t.string   "rack_list_code",           limit: 255
    t.string   "return_reason",            limit: 255
    t.string   "return_image_file_name",   limit: 255
    t.integer  "buy_get_free"
    t.boolean  "design_quantity_reset",                default: false
    t.datetime "issue_resolved_at"
    t.string   "cancel_reason",            limit: 255
    t.integer  "pretty_price"
    t.boolean  "available_in_warehouse"
    t.string   "rtv_label_url",            limit: 255
    t.string   "rtv_invoice_url",          limit: 255
    t.string   "rtv_shipment_error",       limit: 255
    t.integer  "weight"
    t.integer  "gst_rate"
    t.datetime "canceled_on"
    t.integer  "purchase_gst_rate"
    t.integer  "purchase_hsn_code"
    t.integer  "rtv_quantity"
    t.integer  "stylist_receive_id"
    t.integer  "mirraw_addon_charges",                 default: 0
    t.integer  "net_value",                            default: 0
    t.integer  "replacement_item_id"
    t.integer  "shipment_bucket_id"
    t.hstore   "item_details",                         default: {}
    t.string   "designable_type"
    t.integer  "category_id"
    t.integer  "packer_id"
    t.string   "rack_status"
    t.string   "app_name"
    t.text "reverse_product_received_by"
    t.datetime "reverse_product_received_on"
    t.string   "return_image2_file_name",   limit: 255
  end

  add_index "line_items", ["cart_id"], name: "index_line_items_on_cart_id", using: :btree
  add_index "line_items", ["check_items_done_at"], name: "index_line_items_on_check_items_done_at", using: :btree
  add_index "line_items", ["created_at"], name: "index_line_items_on_created_at", using: :btree
  add_index "line_items", ["design_id", "variant_id", "cart_id"], name: "index_line_items_on_design_id_and_variant_id_and_cart_id", using: :btree
  add_index "line_items", ["design_id"], name: "index_line_items_on_design_id", using: :btree
  add_index "line_items", ["designer_order_id", "status", "stitching_required"], name: "index_on_status_stitching_required", using: :btree
  add_index "line_items", ["designer_order_id"], name: "index_line_items_on_designer_order_id", using: :btree
  add_index "line_items", ["packer_id"], name: "index_line_items_on_packer_id", where: "(packer_id IS NOT NULL)", using: :btree
  add_index "line_items", ["received_on"], name: "index_line_items_on_received_on", using: :btree
  add_index "line_items", ["return_designer_order_id"], name: "index_line_items_on_return_designer_order_id", using: :btree
  add_index "line_items", ["shipment_bucket_id"], name: "index_line_items_on_shipment_bucket_id", using: :btree
  add_index "line_items", ["shipment_id"], name: "index_line_items_on_shipment_id", using: :btree

  create_table "listings", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "locations", force: :cascade do |t|
    t.integer  "pincode"
    t.string   "old",             limit: 255
    t.string   "location",        limit: 255
    t.string   "state",           limit: 255
    t.string   "zone",            limit: 255
    t.string   "region",          limit: 255
    t.string   "control_station", limit: 255
    t.boolean  "exp"
    t.boolean  "cgo"
    t.boolean  "sfc"
    t.boolean  "topay"
    t.boolean  "cod"
    t.string   "station_type",    limit: 255
    t.datetime "created_at",                  null: false
    t.datetime "updated_at",                  null: false
  end

  create_table "logs", force: :cascade do |t|
    t.text     "description"
    t.string   "source"
    t.integer  "entity_id"
    t.string   "entity_type"
    t.hstore   "other_details", default: {}
    t.datetime "created_at",                 null: false
    t.datetime "updated_at",                 null: false
  end

  create_table "luxe_seo_lists", force: :cascade do |t|
    t.string   "label"
    t.text     "title"
    t.text     "keyword"
    t.text     "description"
    t.text     "post"
    t.integer  "category_id"
    t.text     "top_content"
    t.string   "photo_file_name"
    t.string   "photo_content_type"
    t.integer  "photo_file_size"
    t.datetime "photo_updated_at"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "luxe_seo_lists", ["label"], name: "index_luxe_seo_lists_on_label", using: :btree

  create_table "master_addon_option_types", force: :cascade do |t|
    t.integer  "addon_type_value_id",   null: false
    t.integer  "addon_option_type_id",  null: false
    t.integer  "addon_option_value_id", null: false
    t.datetime "created_at",            null: false
    t.datetime "updated_at",            null: false
  end

  add_index "master_addon_option_types", ["addon_option_type_id"], name: "index_master_addon_option_types_on_addon_option_type_id", using: :btree
  add_index "master_addon_option_types", ["addon_option_value_id"], name: "index_master_addon_option_types_on_addon_option_value_id", using: :btree
  add_index "master_addon_option_types", ["addon_type_value_id", "addon_option_type_id", "addon_option_value_id"], name: "uniq_master_addon_option_types", unique: true, using: :btree
  add_index "master_addon_option_types", ["addon_type_value_id"], name: "index_master_addon_option_types_on_addon_type_value_id", using: :btree

  create_table "master_addons", force: :cascade do |t|
    t.integer  "category_id"
    t.integer  "designer_id"
    t.integer  "addon_type_id"
    t.integer  "addon_type_value_id"
    t.datetime "created_at",          null: false
    t.datetime "updated_at",          null: false
    t.integer  "price"
    t.integer  "prod_time"
  end

  add_index "master_addons", ["addon_type_id"], name: "index_master_addons_on_addon_type_id", using: :btree
  add_index "master_addons", ["addon_type_value_id"], name: "index_master_addons_on_addon_type_value_id", using: :btree
  add_index "master_addons", ["category_id", "designer_id", "addon_type_id", "addon_type_value_id"], name: "uniq_master_addons", unique: true, using: :btree
  add_index "master_addons", ["category_id"], name: "index_master_addons_on_category_id", using: :btree
  add_index "master_addons", ["designer_id"], name: "index_master_addons_on_designer_id", using: :btree

  create_table "measurement_infos", force: :cascade do |t|
    t.string   "product_designable_type", limit: 255
    t.string   "chest_size",              limit: 255
    t.string   "waist_size",              limit: 255
    t.string   "hip_size",                limit: 255
    t.string   "shoulder_size",           limit: 255
    t.integer  "under_bust"
    t.string   "size_around_arm_hole",    limit: 255
    t.string   "bottom_length",           limit: 255
    t.string   "size_around_ankle",       limit: 255
    t.string   "size_around_thigh",       limit: 255
    t.string   "size_around_knee",        limit: 255
    t.integer  "weight"
    t.integer  "height"
    t.datetime "created_at",                          null: false
    t.datetime "updated_at",                          null: false
    t.string   "front_styles"
    t.string   "back_styles"
    t.string   "hook"
    t.string   "sleeves"
    t.integer  "design_id"
    t.string   "length"
  end

  add_index "measurement_infos", ["weight"], name: "index_measurement_infos_on_weight", using: :btree

  create_table "men", force: :cascade do |t|
    t.string   "length"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "menu_columns", force: :cascade do |t|
    t.string   "title",              limit: 255
    t.text     "link"
    t.integer  "position"
    t.boolean  "hide"
    t.integer  "menu_id"
    t.datetime "created_at",                     null: false
    t.datetime "updated_at",                     null: false
    t.string   "link_type",          limit: 255
    t.string   "link_value",         limit: 255
    t.string   "country",            limit: 255
    t.string   "app_source",         limit: 255
    t.string   "image_file_name"
    t.string   "image_content_type"
    t.integer  "image_file_size"
    t.datetime "image_updated_at"
    t.string   "app_name"
  end

  create_table "menu_items", force: :cascade do |t|
    t.string   "title",              limit: 255
    t.integer  "position"
    t.text     "link"
    t.datetime "created_at",                                     null: false
    t.datetime "updated_at",                                     null: false
    t.boolean  "hide"
    t.integer  "menu_column_id"
    t.boolean  "mobile_menu",                    default: false
    t.string   "link_type",          limit: 255
    t.string   "link_value",         limit: 255
    t.string   "country",            limit: 255
    t.string   "app_source",         limit: 255
    t.string   "image_file_name"
    t.string   "image_content_type"
    t.integer  "image_file_size"
    t.datetime "image_updated_at"
    t.string   "app_name"
  end

  add_index "menu_items", ["mobile_menu"], name: "index_menu_items_on_mobile_menu", using: :btree

  create_table "menu_tabs", id: false, force: :cascade do |t|
    t.integer "menu_id"
    t.integer "tab_id"
  end

  add_index "menu_tabs", ["menu_id", "tab_id"], name: "index_menu_tabs_on_menu_id_and_tab_id", using: :btree
  add_index "menu_tabs", ["tab_id", "menu_id"], name: "index_menu_tabs_on_tab_id_and_menu_id", using: :btree

  create_table "menus", force: :cascade do |t|
    t.string   "title",                    limit: 255
    t.integer  "position"
    t.text     "link"
    t.datetime "created_at",                           null: false
    t.datetime "updated_at",                           null: false
    t.boolean  "hide"
    t.string   "link_type",                limit: 255
    t.string   "link_value",               limit: 255
    t.string   "country",                  limit: 255
    t.string   "app_source",               limit: 255
    t.string   "menu_photo1_file_name",    limit: 255
    t.string   "menu_photo1_content_type", limit: 255
    t.integer  "menu_photo1_file_size"
    t.datetime "menu_photo1_updated_at"
    t.string   "menu_photo2_file_name",    limit: 255
    t.string   "menu_photo2_content_type", limit: 255
    t.integer  "menu_photo2_file_size"
    t.datetime "menu_photo2_updated_at"
    t.text     "menu_link1"
    t.text     "menu_link2"
    t.string   "app_name"
  end

  create_table "messages", force: :cascade do |t|
    t.string   "title",       limit: 255
    t.text     "description"
    t.integer  "account_id"
    t.boolean  "read",                    default: false
    t.integer  "priority",                default: 0
    t.datetime "read_at"
    t.datetime "created_at",                              null: false
    t.datetime "updated_at",                              null: false
  end

  add_index "messages", ["account_id"], name: "index_messages_on_account_id", using: :btree

  create_table "metric_definitions", force: :cascade do |t|
    t.string   "name",                limit: 255
    t.integer  "duration",                                    null: false
    t.integer  "offset_days",                     default: 3
    t.string   "threshold_type",      limit: 255
    t.float    "threshold_value"
    t.string   "calculation_type",    limit: 255
    t.string   "scoring_klass",       limit: 255
    t.string   "score_scope",         limit: 255
    t.string   "group_by",            limit: 255
    t.string   "timerange_attribute", limit: 255
    t.datetime "created_at",                                  null: false
    t.datetime "updated_at",                                  null: false
    t.boolean  "show_on_dashboard"
    t.boolean  "visible_to_designer"
    t.integer  "support_text_id"
    t.boolean  "visible_to_admin"
    t.text     "custom_clause"
  end

  add_index "metric_definitions", ["name"], name: "index_metric_definitions_on_name", using: :btree

  create_table "metric_definitions_scope_events", force: :cascade do |t|
    t.integer "metric_definition_id", null: false
    t.integer "scope_event_id",       null: false
    t.boolean "numerator"
  end

  add_index "metric_definitions_scope_events", ["metric_definition_id", "numerator"], name: "metric_definitions_numerator_index", using: :btree
  add_index "metric_definitions_scope_events", ["scope_event_id", "numerator", "metric_definition_id"], name: "metric_definitions_scope_events_index", unique: true, using: :btree

  create_table "metric_values", force: :cascade do |t|
    t.integer  "actor_id"
    t.string   "actor_type",           limit: 255
    t.float    "value"
    t.integer  "metric_definition_id"
    t.date     "from_date"
    t.date     "to_date"
    t.date     "generated_on"
    t.datetime "created_at",                       null: false
    t.datetime "updated_at",                       null: false
    t.float    "denominator_count"
  end

  add_index "metric_values", ["actor_type", "metric_definition_id", "actor_id", "generated_on"], name: "index_metric_value_definition_actor", unique: true, order: {"generated_on"=>:desc}, using: :btree
  add_index "metric_values", ["actor_type", "metric_definition_id", "generated_on"], name: "index_metric_value_definition", order: {"generated_on"=>:desc}, using: :btree

  create_table "mirrawapps", force: :cascade do |t|
    t.string   "platform",   limit: 255
    t.string   "app_name",   limit: 255
    t.text     "pkg_name"
    t.text     "versions"
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "musical_instruments", force: :cascade do |t|
    t.string   "length"
    t.string   "width"
    t.string   "height"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "nav_blocks", force: :cascade do |t|
    t.string   "label",              limit: 255
    t.string   "link",               limit: 255
    t.integer  "nav_tab_id"
    t.integer  "grade"
    t.string   "photo_file_name",    limit: 255
    t.string   "photo_content_type", limit: 255
    t.integer  "photo_file_size"
    t.datetime "photo_updated_at"
    t.integer  "widget_id"
    t.datetime "created_at",                     null: false
    t.datetime "updated_at",                     null: false
  end

  create_table "nav_tabs", force: :cascade do |t|
    t.string   "display_label", limit: 255
    t.string   "name",          limit: 255
    t.integer  "landing_id"
    t.boolean  "collapse"
    t.integer  "grade"
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
  end

  create_table "newsletter_banners", force: :cascade do |t|
    t.string   "name",                             limit: 255
    t.datetime "start_date"
    t.datetime "end_date"
    t.text     "country"
    t.string   "subscription_banner_file_name",    limit: 255
    t.string   "subscription_banner_content_type", limit: 255
    t.integer  "subscription_banner_file_size"
    t.datetime "subscription_banner_updated_at"
    t.datetime "created_at",                                                                                                                              null: false
    t.datetime "updated_at",                                                                                                                              null: false
    t.string   "app_source",                       limit: 255, default: "universal"
    t.string   "details",                          limit: 255, default: "{\"text\":\"SUBSCRIBE NOW!!!\",\"color\":\"#000000\",\"bg_color\":\"#D3D3D3\"}"
  end

  create_table "notification_events", force: :cascade do |t|
    t.string   "event_name"
    t.text     "event_data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "notifications", force: :cascade do |t|
    t.string   "image_url",          limit: 255
    t.datetime "created_at",                                  null: false
    t.datetime "updated_at",                                  null: false
    t.string   "photo_file_name",    limit: 255
    t.string   "photo_content_type", limit: 255
    t.integer  "photo_file_size"
    t.datetime "photo_updated_at"
    t.integer  "user_id"
    t.jsonb    "body",                           default: {}, null: false
  end

  add_index "notifications", ["user_id"], name: "index_notifications_on_user_id", where: "(user_id IS NOT NULL)", using: :btree

  create_table "offer_panels", force: :cascade do |t|
    t.text     "text"
    t.text     "tnc"
    t.integer  "grade"
    t.datetime "start_date"
    t.datetime "end_date"
    t.string   "country_code"
    t.string   "app_source"
    t.string   "photo_file_name"
    t.string   "photo_content_type"
    t.integer  "photo_file_size"
    t.datetime "photo_updated_at"
    t.datetime "created_at",         null: false
    t.datetime "updated_at",         null: false
  end

  create_table "old_market_rates", force: :cascade do |t|
    t.date     "date"
    t.string   "currency_code", limit: 255
    t.float    "euro_rate"
    t.float    "inr_rate"
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
  end

  create_table "one_time_passwords", force: :cascade do |t|
    t.string   "code"
    t.string   "state"
    t.string   "phone"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "one_time_passwords", ["phone"], name: "index_one_time_passwords_on_phone", using: :btree

  create_table "oos_alerts", force: :cascade do |t|
    t.integer  "design_id",                                 null: false
    t.string   "email",         limit: 255
    t.string   "campaign_name", limit: 255
    t.text     "ad_url"
    t.boolean  "active",                    default: false
    t.datetime "created_at",                                null: false
    t.datetime "updated_at",                                null: false
  end

  add_index "oos_alerts", ["active"], name: "index_oos_alerts_on_active", using: :btree
  add_index "oos_alerts", ["design_id"], name: "index_oos_alerts_on_design_id", using: :btree
  add_index "oos_alerts", ["email"], name: "index_oos_alerts_on_email", using: :btree

  create_table "operation_processes", force: :cascade do |t|
    t.string   "name"
    t.integer  "epst_days"
    t.integer  "lpst_days"
    t.integer  "priority"
    t.string   "process_type"
    t.hstore   "specification", default: {}
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "operation_processes", ["name"], name: "index_operation_processes_on_name", using: :btree

  create_table "option_type_values", force: :cascade do |t|
    t.string   "name",           limit: 255
    t.integer  "position"
    t.string   "p_name",         limit: 255
    t.integer  "option_type_id"
    t.datetime "created_at",                 null: false
    t.datetime "updated_at",                 null: false
  end

  add_index "option_type_values", ["option_type_id"], name: "index_option_type_values_on_option_type_id", using: :btree

  create_table "option_type_values_variants", force: :cascade do |t|
    t.integer "option_type_value_id"
    t.integer "variant_id"
  end

  add_index "option_type_values_variants", ["option_type_value_id"], name: "index_option_type_values_variants_on_option_type_value_id", using: :btree
  add_index "option_type_values_variants", ["variant_id"], name: "index_option_type_values_variants_on_variant_id", using: :btree

  create_table "option_types", force: :cascade do |t|
    t.string   "name",           limit: 255
    t.integer  "position"
    t.string   "p_name",         limit: 255
    t.datetime "created_at",                 null: false
    t.datetime "updated_at",                 null: false
    t.integer  "category_id"
    t.string   "category_names", limit: 255
  end

  add_index "option_types", ["category_id"], name: "index_option_types_on_category_id", using: :btree

  create_table "optional_pickups", force: :cascade do |t|
    t.integer  "shipment_id"
    t.datetime "pickup_date"
    t.string   "office_close_time", limit: 255
    t.integer  "designer_id"
    t.datetime "created_at",                    null: false
    t.datetime "updated_at",                    null: false
  end

  create_table "order_addons", force: :cascade do |t|
    t.integer  "order_id"
    t.float    "gift_wrap_price"
    t.datetime "created_at",      null: false
    t.datetime "updated_at",      null: false
  end

  add_index "order_addons", ["order_id"], name: "index_order_addons_on_order_id", using: :btree

  create_table "order_archives", force: :cascade do |t|
    t.integer  "order_id"
    t.datetime "order_created_at"
    t.datetime "order_updated_at"
    t.string   "pay_type",                        limit: 255
    t.integer  "buyer_id"
    t.integer  "total"
    t.integer  "shipping"
    t.string   "state",                           limit: 255
    t.string   "name",                            limit: 255
    t.string   "email",                           limit: 255
    t.string   "phone",                           limit: 255
    t.string   "street",                          limit: 255
    t.string   "city",                            limit: 255
    t.string   "buyer_state",                     limit: 255
    t.string   "country",                         limit: 255
    t.string   "pincode",                         limit: 255
    t.string   "number",                          limit: 255
    t.text     "notes"
    t.string   "billing_name",                    limit: 255
    t.string   "billing_street",                  limit: 255
    t.string   "billing_email",                   limit: 255
    t.string   "billing_phone",                   limit: 255
    t.string   "billing_city",                    limit: 255
    t.string   "billing_state",                   limit: 255
    t.string   "billing_country",                 limit: 255
    t.string   "billing_pincode",                 limit: 255
    t.string   "ccavenue_authdesc",               limit: 255
    t.string   "ccavenue_nb_order_no",            limit: 255
    t.string   "ccavenue_nb_bid",                 limit: 255
    t.string   "ccavenue_card_category",          limit: 255
    t.string   "ccavenue_bank_name",              limit: 255
    t.integer  "track"
    t.date     "visit_mail_sent"
    t.string   "gharpay_order_id",                limit: 255
    t.string   "gharpay_status",                  limit: 255
    t.integer  "discount"
    t.integer  "coupon_id"
    t.string   "courier_company",                 limit: 255
    t.string   "tracking_number",                 limit: 255
    t.datetime "confirmed_at"
    t.datetime "pickup"
    t.integer  "paid_amount"
    t.integer  "credit_amount"
    t.boolean  "items_received_status"
    t.datetime "items_received_on"
    t.integer  "user_id"
    t.string   "payment_state",                   limit: 255
    t.string   "state_code",                      limit: 255
    t.string   "country_code",                    limit: 255
    t.string   "utm_source",                      limit: 255
    t.string   "utm_medium",                      limit: 255
    t.string   "utm_campaign",                    limit: 255
    t.string   "referral_url",                    limit: 255
    t.string   "utm_term",                        limit: 255
    t.string   "utm_content",                     limit: 255
    t.string   "paypal_payer_id",                 limit: 255
    t.string   "paypal_txn_id",                   limit: 255
    t.string   "paypal_payment_type",             limit: 255
    t.string   "paypal_ipn_track_id",             limit: 255
    t.string   "paypal_mc_gross",                 limit: 255
    t.string   "paypal_mc_currency",              limit: 255
    t.string   "paypal_num_cart_items",           limit: 255
    t.string   "fedex_shipment_error",            limit: 255
    t.integer  "cod_charge"
    t.string   "zipdial_transaction_token",       limit: 255
    t.string   "zipdial_verify_image",            limit: 255
    t.string   "zipdial_number",                  limit: 255
    t.integer  "cod_verify_msg_count",                        default: 0
    t.integer  "mirraw_addon_charges",                        default: 0
    t.datetime "pending_at"
    t.datetime "ready_for_dispatch_at"
    t.datetime "cancel_mail_sent_at"
    t.integer  "cancel_mail_sent_count",                      default: 0
    t.string   "ccavenue_order_status",           limit: 255
    t.string   "ccavenue_failure_message",        limit: 255
    t.string   "ccavenue_payment_mode",           limit: 255
    t.string   "ccavenue_bank_status_code",       limit: 255
    t.string   "ccavenue_bank_status_message",    limit: 255
    t.string   "ccavenue_customer_identifier",    limit: 255
    t.string   "ccavenue_currency",               limit: 255
    t.text     "ccavenue_merchant_data"
    t.string   "ccavenue_payment_link",           limit: 255
    t.string   "ccavenue_api_error",              limit: 255
    t.string   "ccavenue_invoice_id",             limit: 255
    t.text     "ccavenue_payment_link_qr_code"
    t.datetime "ccavenue_payment_link_valid_for"
    t.string   "ccavenue_payment_link_status",    limit: 255
    t.integer  "cart_id"
    t.string   "payu_mihpayid",                   limit: 255
    t.string   "payu_payment_category_mode",      limit: 255
    t.string   "payu_status",                     limit: 255
    t.string   "payu_error",                      limit: 255
    t.string   "payu_bankcode",                   limit: 255
    t.string   "payu_bank_ref",                   limit: 255
    t.string   "payu_unmapped_status",            limit: 255
    t.string   "payu_error_message",              limit: 255
    t.string   "payu_name_on_card",               limit: 255
    t.string   "payu_card_num",                   limit: 255
    t.string   "payu_payment_issuing_bank",       limit: 255
    t.string   "payu_card_type",                  limit: 255
    t.string   "payment_gateway",                 limit: 255
    t.string   "attempted_payment_gateway",       limit: 255
    t.string   "ip_address",                      limit: 255
    t.float    "currency_rate"
    t.string   "currency_code",                   limit: 255
    t.float    "paid_currency_rate"
    t.string   "paid_currency_code",              limit: 255
    t.float    "transaction_fee"
    t.string   "transaction_fee_currency_code",   limit: 255
    t.float    "amount_sent_payment_gateway"
    t.string   "payu_money_id",                   limit: 255
    t.string   "app_source",                      limit: 255
    t.integer  "additional_discount",                         default: 0
    t.integer  "shipping_discount",                           default: 0
    t.string   "token",                           limit: 255
    t.boolean  "cod_available"
    t.string   "geo",                             limit: 255
    t.boolean  "guest_mode"
    t.float    "currency_rate_market_value"
    t.datetime "deleted_at"
    t.string   "icn_term",                        limit: 255
    t.boolean  "feedback_flag",                               default: false
    t.string   "actual_country_code",             limit: 255
    t.datetime "created_at",                                                  null: false
    t.datetime "updated_at",                                                  null: false
  end

  create_table "order_discounts", force: :cascade do |t|
    t.integer  "order_id"
    t.string   "discount_type"
    t.float    "amount"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  create_table "order_disputes", force: :cascade do |t|
    t.string   "webhook_id"
    t.datetime "create_time"
    t.text     "summary"
    t.string   "seller_transaction_id"
    t.string   "reason"
    t.float    "dispute_amount"
    t.string   "currency_code"
    t.string   "dispute_id"
    t.string   "status"
    t.integer  "order_id"
    t.datetime "created_at",            null: false
    t.datetime "updated_at",            null: false
  end

  add_index "order_disputes", ["dispute_id"], name: "index_order_disputes_on_dispute_id", using: :btree

  create_table "order_estimations", force: :cascade do |t|
    t.float    "shipping_cost"
    t.float    "stitching_cost"
    t.integer  "order_id"
    t.datetime "created_at",                        null: false
    t.datetime "updated_at",                        null: false
    t.float    "paid_shipping_cost",  default: 0.0
    t.float    "paid_stitching_cost", default: 0.0
    t.float    "paid_payout",         default: 0.0
    t.float    "paid_total_amount",   default: 0.0
  end

  create_table "order_issues", force: :cascade do |t|
    t.integer  "order_id"
    t.string   "coordinates", limit: 255
    t.text     "issues"
    t.string   "assessment",  limit: 255
    t.datetime "created_at",              null: false
    t.datetime "updated_at",              null: false
  end

  create_table "orders", force: :cascade do |t|
    t.string   "pay_type",                        limit: 255
    t.integer  "buyer_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.integer  "total"
    t.integer  "shipping"
    t.string   "state",                           limit: 255
    t.string   "name",                            limit: 255
    t.string   "email",                           limit: 255
    t.string   "phone",                           limit: 255
    t.string   "street",                          limit: 255
    t.string   "city",                            limit: 255
    t.string   "buyer_state",                     limit: 255
    t.string   "country",                         limit: 255
    t.string   "pincode",                         limit: 255
    t.string   "number",                          limit: 255
    t.text     "notes"
    t.string   "billing_name",                    limit: 255
    t.string   "billing_street",                  limit: 255
    t.string   "billing_email",                   limit: 255
    t.string   "billing_phone",                   limit: 255
    t.string   "billing_city",                    limit: 255
    t.string   "billing_state",                   limit: 255
    t.string   "billing_country",                 limit: 255
    t.string   "billing_pincode",                 limit: 255
    t.string   "ccavenue_authdesc",               limit: 255
    t.string   "ccavenue_nb_order_no",            limit: 255
    t.string   "ccavenue_nb_bid",                 limit: 255
    t.string   "ccavenue_card_category",          limit: 255
    t.string   "ccavenue_bank_name",              limit: 255
    t.integer  "track"
    t.date     "visit_mail_sent"
    t.string   "gharpay_order_id",                limit: 255
    t.string   "gharpay_status",                  limit: 255
    t.integer  "discount"
    t.integer  "coupon_id"
    t.string   "courier_company",                 limit: 255
    t.string   "tracking_number",                 limit: 255
    t.datetime "confirmed_at"
    t.datetime "pickup"
    t.integer  "paid_amount"
    t.integer  "credit_amount"
    t.boolean  "items_received_status"
    t.datetime "items_received_on"
    t.integer  "user_id"
    t.string   "payment_state",                   limit: 255
    t.string   "state_code",                      limit: 255
    t.string   "country_code",                    limit: 255
    t.string   "utm_source",                      limit: 255
    t.string   "utm_medium",                      limit: 255
    t.string   "utm_campaign",                    limit: 255
    t.string   "referral_url",                    limit: 255
    t.string   "utm_term",                        limit: 255
    t.string   "utm_content",                     limit: 255
    t.string   "paypal_payer_id",                 limit: 255
    t.string   "paypal_txn_id",                   limit: 255
    t.string   "paypal_payment_type",             limit: 255
    t.string   "paypal_ipn_track_id",             limit: 255
    t.string   "paypal_mc_gross",                 limit: 255
    t.string   "paypal_mc_currency",              limit: 255
    t.string   "paypal_num_cart_items",           limit: 255
    t.string   "fedex_shipment_error",            limit: 255
    t.integer  "cod_charge"
    t.string   "zipdial_transaction_token",       limit: 255
    t.string   "zipdial_verify_image",            limit: 255
    t.string   "zipdial_number",                  limit: 255
    t.integer  "cod_verify_msg_count",                        default: 0
    t.integer  "mirraw_addon_charges",                        default: 0
    t.datetime "pending_at"
    t.datetime "ready_for_dispatch_at"
    t.datetime "cancel_mail_sent_at"
    t.integer  "cancel_mail_sent_count",                      default: 0
    t.string   "ccavenue_order_status",           limit: 255
    t.string   "ccavenue_failure_message",        limit: 255
    t.string   "ccavenue_payment_mode",           limit: 255
    t.string   "ccavenue_bank_status_code",       limit: 255
    t.string   "ccavenue_bank_status_message",    limit: 255
    t.string   "ccavenue_customer_identifier",    limit: 255
    t.string   "ccavenue_currency",               limit: 255
    t.text     "ccavenue_merchant_data"
    t.integer  "cart_id"
    t.string   "ccavenue_payment_link",           limit: 255
    t.string   "ccavenue_api_error",              limit: 255
    t.string   "ccavenue_invoice_id",             limit: 255
    t.text     "ccavenue_payment_link_qr_code"
    t.datetime "ccavenue_payment_link_valid_for"
    t.string   "ccavenue_payment_link_status",    limit: 255
    t.string   "payu_mihpayid",                   limit: 255
    t.string   "payu_payment_category_mode",      limit: 255
    t.string   "payu_status",                     limit: 255
    t.string   "payu_error",                      limit: 255
    t.string   "payu_bankcode",                   limit: 255
    t.string   "payu_bank_ref",                   limit: 255
    t.string   "payu_unmapped_status",            limit: 255
    t.string   "payu_error_message",              limit: 255
    t.string   "payu_name_on_card",               limit: 255
    t.string   "payu_card_num",                   limit: 255
    t.string   "payu_payment_issuing_bank",       limit: 255
    t.string   "payu_card_type",                  limit: 255
    t.string   "payment_gateway",                 limit: 255
    t.string   "attempted_payment_gateway",       limit: 255
    t.string   "ip_address",                      limit: 255
    t.float    "currency_rate"
    t.string   "currency_code",                   limit: 255
    t.float    "paid_currency_rate"
    t.string   "paid_currency_code",              limit: 255
    t.float    "transaction_fee"
    t.string   "transaction_fee_currency_code",   limit: 255
    t.float    "amount_sent_payment_gateway"
    t.string   "payu_money_id",                   limit: 255
    t.string   "app_source",                      limit: 255
    t.integer  "additional_discount",                         default: 0
    t.integer  "shipping_discount",                           default: 0
    t.boolean  "cod_available"
    t.string   "token",                           limit: 255
    t.string   "geo",                             limit: 255
    t.boolean  "guest_mode"
    t.float    "currency_rate_market_value"
    t.datetime "deleted_at"
    t.string   "icn_term",                        limit: 255
    t.boolean  "feedback_flag",                               default: false
    t.string   "actual_country_code",             limit: 255
    t.float    "referral_discount"
    t.float    "refund_discount"
    t.float    "actual_weight"
    t.float    "volumetric_weight"
    t.string   "best_shipper",                    limit: 255
    t.text     "multi_channel_marketing"
    t.integer  "notification_count",                          default: 0,     null: false
    t.datetime "out_of_mirraw_warehouse"
    t.text     "out_scan_notes"
    t.text     "paypal_error"
    t.text     "order_notification"
    t.text     "payment_gateway_details"
    t.string   "razorpay_id",                     limit: 255
    t.string   "razorpay_status",                 limit: 255
    t.string   "amazon_order_id",                 limit: 255
    t.string   "out_scan_batch",                  limit: 255
    t.string   "paytm_txn_id",                    limit: 255
    t.string   "paytm_txn_status",                limit: 255
    t.string   "bank_deposit_txn_id",             limit: 255
    t.string   "g2a_txn_id",                      limit: 255
    t.text     "additional_payments"
    t.string   "g2a_txn_status",                  limit: 255
    t.string   "utm_exp_id",                      limit: 255
    t.hstore   "other_details",                               default: {},    null: false
    t.datetime "assigned_to_stylist_at"
    t.integer  "stylist_id"
    t.integer  "express_delivery"
    t.integer  "duplicated_from_id"
    t.string   "cancel_reason"
    t.string   "juspay_txn_id"
    t.string   "juspay_order_status"
    t.string   "app_name"
  end

  add_index "orders", ["cart_id"], name: "index_orders_on_cart_id", using: :btree
  add_index "orders", ["created_at"], name: "index_orders_on_created_at", using: :btree
  add_index "orders", ["email"], name: "index_orders_on_email", using: :btree
  add_index "orders", ["guest_mode"], name: "index_orders_on_guest_mode", using: :btree
  add_index "orders", ["number"], name: "index_orders_on_number", using: :btree
  add_index "orders", ["paypal_txn_id"], name: "index_orders_on_paypal_txn_id", using: :btree
  add_index "orders", ["state", "ready_for_dispatch_at"], name: "index_ready_for_dispatch_on_orders", using: :btree
  add_index "orders", ["state"], name: "index_orders_on_state", using: :btree
  add_index "orders", ["stylist_id"], name: "index_orders_on_stylist_id", using: :btree
  add_index "orders", ["token"], name: "index_orders_on_token", using: :btree
  add_index "orders", ["user_id"], name: "index_orders_on_user_id", using: :btree

  create_table "orders_reviews", id: false, force: :cascade do |t|
    t.integer "review_id"
    t.integer "order_id"
  end

  add_index "orders_reviews", ["order_id", "review_id"], name: "index_orders_reviews_on_order_id_and_review_id", unique: true, using: :btree
  add_index "orders_reviews", ["review_id", "order_id"], name: "index_orders_reviews_on_review_id_and_order_id", unique: true, using: :btree

  create_table "others", force: :cascade do |t|
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
    t.string   "length",     limit: 255
    t.string   "width",      limit: 255
    t.string   "height",     limit: 255
  end

  create_table "payment_options", force: :cascade do |t|
    t.string   "pay_type"
    t.string   "payment_gateway"
    t.boolean  "international"
    t.boolean  "domestic"
    t.integer  "int_position"
    t.integer  "dom_position"
    t.string   "pay_type_info"
    t.string   "app_source"
    t.integer  "status"
    t.string   "created_by"
    t.string   "updated_by"
    t.datetime "uptime"
    t.datetime "created_at",        null: false
    t.datetime "updated_at",        null: false
    t.string   "logo_file_name"
    t.string   "logo_content_type"
    t.integer  "logo_file_size"
    t.datetime "logo_updated_at"
  end

  create_table "payout_managements", force: :cascade do |t|
    t.string   "payout_version",     limit: 255
    t.integer  "designer_id"
    t.float    "payout_amount"
    t.float    "adjustments_amount"
    t.text     "designer_order_ids"
    t.text     "adjustment_ids"
    t.string   "invoice_status",     limit: 255
    t.boolean  "paid"
    t.datetime "paid_date"
    t.string   "utr_number",         limit: 255
    t.text     "payout_notes"
    t.string   "payout_type",        limit: 255
    t.datetime "created_at",                                 null: false
    t.datetime "updated_at",                                 null: false
    t.integer  "gst_release_amount",             default: 0
    t.string   "gst_release_ids"
    t.string   "payout_summary_url"
  end

  create_table "performance_metric_values", force: :cascade do |t|
    t.integer  "performer_id",            null: false
    t.string   "performer_type",          null: false
    t.float    "value",                   null: false
    t.datetime "created_at",              null: false
    t.datetime "updated_at",              null: false
    t.string   "metric_name"
    t.float    "ddr"
    t.float    "negative_feedback_ratio"
  end

  add_index "performance_metric_values", ["performer_type", "performer_id", "metric_name"], name: "performer_lookup_index", unique: true, using: :btree

  create_table "performance_metrics", force: :cascade do |t|
    t.integer  "metric_id",   null: false
    t.string   "metric_type", null: false
    t.datetime "created_at",  null: false
    t.datetime "updated_at",  null: false
  end

  create_table "pickups", force: :cascade do |t|
    t.date     "pickup_date"
    t.integer  "designer_id"
    t.boolean  "pickup_created"
    t.datetime "cutoff_time"
    t.datetime "ready_time"
    t.integer  "package_count"
    t.datetime "created_at",                                                 null: false
    t.datetime "updated_at",                                                 null: false
    t.string   "pickup_confirmation_number", limit: 255
    t.string   "location_id",                limit: 255
    t.boolean  "packages_shipped"
    t.string   "status",                     limit: 255, default: "pending"
  end

  add_index "pickups", ["pickup_date"], name: "index_pickups_on_pickup_date", using: :btree

  create_table "pincodes", force: :cascade do |t|
    t.integer  "pin_code"
    t.string   "city",              limit: 255
    t.string   "district",          limit: 255
    t.string   "state",             limit: 255
    t.datetime "created_at",                    null: false
    t.datetime "updated_at",                    null: false
    t.decimal  "cod_charge"
    t.decimal  "rto_percent"
    t.decimal  "cost_to_ship"
    t.string   "zone_for_business"
  end

  create_table "popular_links", force: :cascade do |t|
    t.string   "keyword"
    t.text     "link"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "popular_links_property_values", force: :cascade do |t|
    t.integer "property_value_id"
    t.integer "popular_link_id"
  end

  add_index "popular_links_property_values", ["popular_link_id"], name: "index_popular_links_property_values_on_popular_link_id", using: :btree
  add_index "popular_links_property_values", ["property_value_id"], name: "index_popular_links_property_values_on_property_value_id", using: :btree

  create_table "process_dates", force: :cascade do |t|
    t.datetime "epst_date"
    t.datetime "lpst_date"
    t.integer  "processable_id"
    t.string   "processable_type"
    t.integer  "operation_process_id", null: false
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "process_dates", ["operation_process_id"], name: "index_process_dates_on_operation_process_id", using: :btree
  add_index "process_dates", ["processable_id"], name: "index_process_dates_on_processable_id", using: :btree

  create_table "promotion_histories", force: :cascade do |t|
    t.string   "name",         limit: 255,                null: false
    t.string   "event_name",   limit: 255,                null: false
    t.datetime "start_date",                              null: false
    t.datetime "end_date",                                null: false
    t.integer  "user_id"
    t.text     "offer",                    default: "{}", null: false
    t.string   "country_code", limit: 255
    t.datetime "created_at",                              null: false
    t.datetime "updated_at",                              null: false
  end

  create_table "promotion_pipe_lines", force: :cascade do |t|
    t.datetime "start_date",                                            null: false
    t.datetime "end_date",                                              null: false
    t.string   "event_name",                 limit: 255,                null: false
    t.integer  "user_id"
    t.text     "variables_hash",                         default: "{}", null: false
    t.text     "images_hash",                            default: "{}", null: false
    t.text     "methods_hash",                           default: "{}", null: false
    t.datetime "created_at",                                            null: false
    t.datetime "updated_at",                                            null: false
    t.string   "name",                       limit: 255
    t.string   "country_code",               limit: 255
    t.string   "app_source",                 limit: 255
    t.string   "attached_file_file_name"
    t.string   "attached_file_content_type"
    t.integer  "attached_file_file_size"
    t.datetime "attached_file_updated_at"
  end

  add_index "promotion_pipe_lines", ["end_date"], name: "index_promotion_pipe_lines_on_end_date", using: :btree
  add_index "promotion_pipe_lines", ["event_name"], name: "index_promotion_pipe_lines_on_event_name", using: :btree
  add_index "promotion_pipe_lines", ["name"], name: "index_promotion_pipe_lines_on_name", using: :btree
  add_index "promotion_pipe_lines", ["start_date"], name: "index_promotion_pipe_lines_on_start_date", using: :btree

  create_table "promotion_trackings", force: :cascade do |t|
    t.text "event"
  end

  create_table "properties", force: :cascade do |t|
    t.string   "name",                limit: 255,                 null: false
    t.integer  "position",                                        null: false
    t.datetime "created_at",                                      null: false
    t.datetime "updated_at",                                      null: false
    t.string   "p_name",              limit: 255
    t.boolean  "catalog_name_filter",             default: false, null: false
    t.boolean  "seo_title_change",                default: false, null: false
    t.integer  "keyword_boost_level",             default: 0
    t.integer  "facet_priority",                  default: 0
    t.boolean  "fixed",                           default: true
    t.string   "bulk_upload_name"
  end

  create_table "property_values", force: :cascade do |t|
    t.string   "name",             limit: 255
    t.integer  "position"
    t.integer  "property_id"
    t.datetime "created_at",                   null: false
    t.datetime "updated_at",                   null: false
    t.string   "p_name",           limit: 255
    t.string   "bulk_upload_name"
  end

  add_index "property_values", ["property_id"], name: "index_property_values_on_property_id", using: :btree

  create_table "purchase_orders", force: :cascade do |t|
    t.string   "number"
    t.string   "status",           default: "open"
    t.string   "awb_number"
    t.integer  "invoice_quantity"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "purchase_orders", ["number"], name: "index_purchase_orders_on_number", using: :btree

  create_table "purchase_reports", force: :cascade do |t|
    t.integer  "designer_id"
    t.string   "purchase_report_url",     limit: 255
    t.datetime "created_at",                                         null: false
    t.datetime "updated_at",                                         null: false
    t.string   "sales_report_url"
    t.string   "sales_return_report_url"
    t.boolean  "is_valid_report",                     default: true
  end

  create_table "push_engage_subscribers", force: :cascade do |t|
    t.text     "uniq_hash",                           null: false
    t.integer  "cart_id"
    t.integer  "account_id"
    t.integer  "cart_notification_count", default: 0
    t.datetime "created_at",                          null: false
    t.datetime "updated_at",                          null: false
  end

  add_index "push_engage_subscribers", ["account_id"], name: "index_push_engage_subscribers_on_account_id", using: :btree
  add_index "push_engage_subscribers", ["cart_id"], name: "index_push_engage_subscribers_on_cart_id", using: :btree
  add_index "push_engage_subscribers", ["uniq_hash"], name: "index_push_engage_subscribers_on_uniq_hash", unique: true, using: :btree

  create_table "quality_check_values", force: :cascade do |t|
    t.integer  "quality_check_id"
    t.string   "qc_reason",        limit: 255
    t.integer  "line_item_id"
    t.datetime "created_at",                   null: false
    t.datetime "updated_at",                   null: false
    t.boolean  "qc_type_flag"
    t.boolean  "repairable"
  end

  create_table "quality_checks", force: :cascade do |t|
    t.string   "qc_type_name",       limit: 255
    t.datetime "created_at",                                         null: false
    t.datetime "updated_at",                                         null: false
    t.string   "quality_check_type",             default: "quality"
  end

  create_table "rack_audits", force: :cascade do |t|
    t.integer  "rack_list_id"
    t.string   "status"
    t.integer  "assigned_to"
    t.datetime "assigned_at"
    t.datetime "audit_done_at"
    t.datetime "last_skipped_at"
    t.hstore   "audit_stats",          default: {}
    t.string   "audit_type"
    t.string   "audit_level"
    t.float    "density_index"
    t.integer  "parent_rack_audit_id"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "rack_audits", ["rack_list_id"], name: "index_rack_audits_on_rack_list_id", using: :btree

  create_table "rack_configs", force: :cascade do |t|
    t.integer  "last_count_jewellery"
    t.string   "last_series_jewellery", limit: 255
    t.integer  "last_count_apparel"
    t.string   "last_series_apparel",   limit: 255
    t.datetime "created_at",                        null: false
    t.datetime "updated_at",                        null: false
  end

  create_table "rack_lists", force: :cascade do |t|
    t.string   "code",            limit: 255
    t.datetime "created_at",                                      null: false
    t.datetime "updated_at",                                      null: false
    t.string   "description",     limit: 255
    t.integer  "space_filled",                default: 0
    t.integer  "max_capacity",                default: 50
    t.integer  "priority",                    default: 0
    t.datetime "last_audited_at"
    t.string   "audit_status",                default: "pending"
  end

  add_index "rack_lists", ["code"], name: "index_rack_lists_on_code", unique: true, using: :btree

  create_table "rack_lists_warehouse_line_items", force: :cascade do |t|
    t.integer "quantity_present"
    t.integer "quantity_stored"
    t.integer "item_id"
    t.integer "rack_list_id"
    t.string  "item_type",        default: "WarehouseLineItem"
    t.integer "quantity_lost",    default: 0
  end

  create_table "rack_logs", force: :cascade do |t|
    t.integer  "rack_list_id"
    t.integer  "loggable_id"
    t.string   "loggable_type"
    t.string   "log_type"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "rack_logs", ["loggable_id"], name: "index_rack_logs_on_loggable_id", using: :btree
  add_index "rack_logs", ["rack_list_id"], name: "index_rack_logs_on_rack_list_id", using: :btree

  create_table "rack_scans", force: :cascade do |t|
    t.string   "rack_barcode"
    t.string   "product_barcode"
    t.string   "device_id"
    t.datetime "product_kept_on"
  end

  add_index "rack_scans", ["product_kept_on"], name: "index_rack_scans_on_product_kept_on", using: :btree

  create_table "rails_admin_histories", force: :cascade do |t|
    t.string   "message",    limit: 255
    t.string   "username",   limit: 255
    t.integer  "item"
    t.string   "table",      limit: 255
    t.integer  "month",      limit: 2
    t.integer  "year",       limit: 8
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "rails_admin_histories", ["item", "table", "month", "year"], name: "index_rails_admin_histories", using: :btree

  create_table "recent_items", force: :cascade do |t|
    t.string   "device_id",   limit: 255
    t.string   "session_id",  limit: 255
    t.integer  "design_id"
    t.integer  "user_id"
    t.integer  "visit_count",             default: 0
    t.datetime "created_at",                          null: false
    t.datetime "updated_at",                          null: false
  end

  add_index "recent_items", ["design_id"], name: "index_recent_items_on_design_id", using: :btree
  add_index "recent_items", ["device_id"], name: "index_recent_items_on_device_id", using: :btree

  create_table "redirect_rules", force: :cascade do |t|
    t.string   "match",         limit: 255
    t.string   "route",         limit: 255
    t.string   "category_kind", limit: 255
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
  end

  add_index "redirect_rules", ["category_kind"], name: "index_redirect_rules_on_category_kind", using: :btree
  add_index "redirect_rules", ["match"], name: "index_redirect_rules_on_match", using: :btree

  create_table "referral_credits", force: :cascade do |t|
    t.integer  "referrer"
    t.integer  "referree"
    t.integer  "order_id"
    t.boolean  "credited",   default: false
    t.datetime "created_at",                 null: false
    t.datetime "updated_at",                 null: false
  end

  create_table "referrals", force: :cascade do |t|
    t.integer  "referred_person_id"
    t.integer  "referrer_id"
    t.datetime "created_at",                                     null: false
    t.datetime "updated_at",                                     null: false
    t.string   "referral_type",      limit: 255
    t.integer  "order_id"
    t.boolean  "credited",                       default: false
  end

  add_index "referrals", ["order_id"], name: "index_referrals_on_order_id", using: :btree
  add_index "referrals", ["referred_person_id"], name: "index_referrals_on_referred_person_id", using: :btree
  add_index "referrals", ["referrer_id"], name: "index_referrals_on_referrer_id", using: :btree

  create_table "referred_people", force: :cascade do |t|
    t.string   "name",       limit: 255
    t.string   "email",      limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "referrers", force: :cascade do |t|
    t.string   "name",       limit: 255
    t.string   "email",      limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
    t.integer  "notified"
  end

  create_table "refunds", force: :cascade do |t|
    t.string   "refund_mode"
    t.integer  "return_id"
    t.string   "refund_mode_id"
    t.string   "status"
    t.string   "created_by"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  create_table "reqlists", force: :cascade do |t|
    t.string   "email",              limit: 255
    t.integer  "design"
    t.string   "notified",           limit: 255
    t.datetime "created_at",                                 null: false
    t.datetime "updated_at",                                 null: false
    t.datetime "notified_at"
    t.integer  "notification_count",             default: 0
  end

  create_table "rest_framework_api_key_apikey", force: :cascade do |t|
    t.datetime "created",                 null: false
    t.string   "name",        limit: 50,  null: false
    t.boolean  "revoked",                 null: false
    t.datetime "expiry_date"
    t.string   "hashed_key",  limit: 100, null: false
    t.string   "prefix",      limit: 8,   null: false
  end

  add_index "rest_framework_api_key_apikey", ["created"], name: "rest_framework_api_key_apikey_created_c61872d9", using: :btree
  add_index "rest_framework_api_key_apikey", ["id"], name: "rest_framework_api_key_apikey_id_6e07e68e_like", using: :btree
  add_index "rest_framework_api_key_apikey", ["prefix"], name: "rest_framework_api_key_apikey_prefix_4e0db5f8_like", using: :btree
  add_index "rest_framework_api_key_apikey", ["prefix"], name: "rest_framework_api_key_apikey_prefix_key", unique: true, using: :btree

  create_table "return_designer_orders", force: :cascade do |t|
    t.string   "tracking_company",  limit: 255
    t.string   "tracking_number",   limit: 255
    t.string   "state",             limit: 255
    t.integer  "return_id"
    t.integer  "total",                         default: 0
    t.text     "notes",                         default: ""
    t.integer  "designer_id"
    t.datetime "created_at",                                 null: false
    t.datetime "updated_at",                                 null: false
    t.datetime "received_date"
    t.datetime "dispatched_date"
    t.datetime "canceled_date"
    t.string   "return_type",       limit: 255
    t.integer  "designer_order_id"
    t.integer  "gst_tax",                       default: 0
    t.integer  "shipper_id"
    t.integer  "shipment_id"
  end

  add_index "return_designer_orders", ["designer_id"], name: "index_return_designer_orders_on_designer_id", using: :btree
  add_index "return_designer_orders", ["return_id"], name: "index_return_designer_orders_on_return_id", using: :btree
  add_index "return_designer_orders", ["return_type"], name: "index_return_designer_orders_on_return_type", using: :btree
  add_index "return_designer_orders", ["shipment_id"], name: "index_return_designer_orders_on_shipment_id", using: :btree
  add_index "return_designer_orders", ["tracking_number"], name: "index_return_designer_orders_on_tracking_number", using: :btree

  create_table "returns", force: :cascade do |t|
    t.integer  "total",                                      default: 0
    t.integer  "adjustment"
    t.text     "notes"
    t.string   "account_holder_name",            limit: 255
    t.string   "bank_name",                      limit: 255
    t.string   "branch",                         limit: 255
    t.string   "ifsc_code",                      limit: 255
    t.string   "account_number",                 limit: 255
    t.integer  "order_id"
    t.string   "status",                         limit: 255
    t.integer  "coupon_id"
    t.string   "pay_type",                       limit: 255
    t.datetime "created_at",                                                         null: false
    t.datetime "updated_at",                                                         null: false
    t.datetime "pending_on"
    t.datetime "completed_on"
    t.string   "state",                          limit: 255
    t.integer  "shipping",                                   default: 0
    t.text     "reason"
    t.string   "customer_phone_number",          limit: 255
    t.string   "order_number",                   limit: 255
    t.text     "courier_name"
    t.text     "tracking_id"
    t.string   "origin_city",                    limit: 255
    t.text     "designer_name"
    t.text     "product_id"
    t.float    "product_charge"
    t.float    "stitching_charge"
    t.float    "discount"
    t.string   "coupon_code",                    limit: 255
    t.string   "instruction_to_accounts",        limit: 255
    t.string   "type_of_refund",                 limit: 255
    t.datetime "coupon_gen_date"
    t.string   "did_you_try_retaining_customer", limit: 255
    t.string   "agent_info",                     limit: 255
    t.string   "user_paypal_email",              limit: 255
    t.integer  "user_id"
    t.string   "app_source",                     limit: 255
    t.string   "ticket_id",                      limit: 255
    t.string   "refund_transaction_id",          limit: 255
    t.datetime "cancelled_on"
    t.integer  "cancelled_by"
    t.datetime "sent_for_image_approval_on"
    t.datetime "sent_for_return_approval_on"
    t.integer  "return_approved_by"
    t.datetime "refund_rejected_on"
    t.string   "voucher"
    t.string   "return_invoice_url"
    t.string   "service_type",                               default: "SELF RETURN"
    t.string   "refund_phone_number",
    t.text "support_reason"
  end

  add_index "returns", ["coupon_id"], name: "index_returns_on_coupon_id", using: :btree
  add_index "returns", ["order_id"], name: "index_returns_on_order_id", using: :btree
  add_index "returns", ["user_id"], name: "index_returns_on_user_id", using: :btree

  create_table "reverse_commissions", force: :cascade do |t|
    t.integer  "element_id"
    t.string   "element_type", limit: 255
    t.float    "amount",                   default: 0.0
    t.float    "commission",               default: 0.0
    t.float    "cgst_percent",             default: 0.0
    t.float    "igst_percent",             default: 0.0
    t.float    "sgst_percent",             default: 0.0
    t.integer  "invoice_id"
    t.integer  "designer_id"
    t.datetime "created_at",                             null: false
    t.datetime "updated_at",                             null: false
    t.string   "invoice_type"
    t.integer  "order_id"
  end

  create_table "reviews", force: :cascade do |t|
    t.integer  "rating",                                          null: false
    t.integer  "user_id"
    t.integer  "designer_id"
    t.integer  "design_id"
    t.text     "review"
    t.boolean  "approved",                        default: true
    t.datetime "created_at",                                      null: false
    t.datetime "updated_at",                                      null: false
    t.boolean  "system_user",                     default: false
    t.integer  "order_id"
    t.string   "geo",                 limit: 255
    t.string   "issue",               limit: 255
    t.boolean  "site_review"
    t.string   "assured",             limit: 255
    t.text     "notes"
    t.text     "modified_review"
    t.boolean  "approved_for_tailor"
  end

  add_index "reviews", ["approved"], name: "index_reviews_on_approved", using: :btree
  add_index "reviews", ["design_id"], name: "index_reviews_on_design_id", using: :btree
  add_index "reviews", ["designer_id"], name: "index_reviews_on_designer_id", using: :btree
  add_index "reviews", ["review"], name: "index_reviews_on_review", where: "(review IS NOT NULL)", using: :btree
  add_index "reviews", ["user_id"], name: "index_reviews_on_user_id", using: :btree

  create_table "roles", force: :cascade do |t|
    t.string   "name",       limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "rtv_shipment_line_items", id: false, force: :cascade do |t|
    t.integer "line_item_id"
    t.integer "rtv_shipment_id"
  end

  create_table "rtv_shipments", force: :cascade do |t|
    t.string   "number",                  limit: 255
    t.string   "shipper_name",            limit: 255
    t.string   "shipment_state",          limit: 255
    t.string   "invoice_url",             limit: 255
    t.string   "label_url",               limit: 255
    t.float    "weight"
    t.integer  "done_by"
    t.datetime "created_at",                          null: false
    t.datetime "updated_at",                          null: false
    t.datetime "out_of_mirraw_warehouse"
    t.string   "shipment_type"
    t.string   "reason"
    t.text     "last_event"
    t.datetime "last_event_timestamp"
  end

  create_table "sales_registers", force: :cascade do |t|
    t.integer  "order_id"
    t.string   "event",            limit: 255
    t.text     "snapshot_values"
    t.string   "sales_state",      limit: 255
    t.float    "amount_effective"
    t.datetime "created_at",                   null: false
    t.datetime "updated_at",                   null: false
  end

  create_table "salwar_kameezs", force: :cascade do |t|
    t.datetime "created_at",                     null: false
    t.datetime "updated_at",                     null: false
    t.string   "max_size",           limit: 255
    t.string   "max_kameez_length",  limit: 255
    t.string   "max_bottom_length",  limit: 255
    t.string   "dupatta_length",     limit: 255
    t.boolean  "lining_available"
    t.string   "lining_length",      limit: 255
    t.string   "kameez_length",      limit: 255
    t.string   "kameez_color",       limit: 255
    t.string   "bottom_length",      limit: 255
    t.string   "bottom_color",       limit: 255
    t.string   "dupatta_color",      limit: 255
    t.string   "lining_fabric",      limit: 255
    t.string   "min_bustsize_fit",   limit: 255
    t.string   "max_bustsize_fit",   limit: 255
    t.string   "max_dupatta_length", limit: 255
  end

  create_table "sarees", force: :cascade do |t|
    t.datetime "created_at",                      null: false
    t.datetime "updated_at",                      null: false
    t.string   "width",               limit: 255
    t.string   "length",              limit: 255
    t.string   "blouse_available",    limit: 255
    t.string   "blouse_size",         limit: 255
    t.string   "blouse_fabric",       limit: 255
    t.string   "petticoat_available", limit: 255
    t.string   "petticoat_size",      limit: 255
    t.string   "petticoat_color",     limit: 255
    t.string   "petticoat_fabric",    limit: 255
    t.string   "saree_color",         limit: 255
    t.string   "blouse_color",        limit: 255
    t.string   "blouse_work",         limit: 255
    t.string   "blouse_image",        limit: 255
    t.string   "blouse_length",       limit: 255
  end

  create_table "scans", force: :cascade do |t|
    t.integer  "line_item_id"
    t.string   "scan_type"
    t.datetime "scanned_at"
    t.integer  "scanned_by"
    t.text     "content"
  end

  add_index "scans", ["line_item_id"], name: "index_scans_on_line_item_id", using: :btree

  create_table "scope_events", force: :cascade do |t|
    t.string   "name",                limit: 255
    t.string   "s_name",              limit: 255
    t.string   "p_name",              limit: 255
    t.string   "description",         limit: 255
    t.integer  "points"
    t.string   "scope",               limit: 255
    t.boolean  "variable",                        default: false
    t.boolean  "trigger_parent",                  default: false
    t.datetime "created_at",                                      null: false
    t.datetime "updated_at",                                      null: false
    t.boolean  "visible_to_designer",             default: false
  end

  add_index "scope_events", ["name"], name: "index_scope_events_on_name", using: :btree
  add_index "scope_events", ["scope", "name"], name: "index_scope_events_on_scope_and_name", using: :btree

  create_table "scope_events_scope_scores", id: false, force: :cascade do |t|
    t.integer "scope_event_id", null: false
    t.integer "scope_score_id", null: false
  end

  add_index "scope_events_scope_scores", ["scope_event_id", "scope_score_id"], name: "scope_events_scope_scores_index", unique: true, using: :btree
  add_index "scope_events_scope_scores", ["scope_score_id"], name: "index_scope_events_scope_scores_on_scope_score_id", using: :btree

  create_table "scope_events_survey_questions", force: :cascade do |t|
    t.integer "scope_event_id",     null: false
    t.integer "survey_question_id", null: false
    t.boolean "feedback_type",      null: false
  end

  add_index "scope_events_survey_questions", ["scope_event_id", "feedback_type", "survey_question_id"], name: "scope_events_survey_questions_index", unique: true, using: :btree
  add_index "scope_events_survey_questions", ["survey_question_id", "feedback_type"], name: "survey_questions_feedback_type_index", using: :btree

  create_table "scope_scores", force: :cascade do |t|
    t.integer  "scoring_klass_id"
    t.string   "scoring_klass_type", limit: 255
    t.string   "scope",              limit: 255
    t.integer  "score",                          default: 0
    t.hstore   "variables",                      default: {}, null: false
    t.datetime "created_at",                                  null: false
    t.datetime "updated_at",                                  null: false
  end

  add_index "scope_scores", ["scoring_klass_type", "scope", "scoring_klass_id"], name: "index_scoring_klass_scope", using: :btree
  add_index "scope_scores", ["variables"], name: "index_scope_scores_on_variables", using: :gist

  create_table "searcher_buckets", force: :cascade do |t|
    t.integer "pdd_count",           default: 0
    t.integer "non_pdd_count",       default: 0
    t.boolean "active",              default: false
    t.string  "kind"
    t.integer "warehouse_bucket_id"
  end

  add_index "searcher_buckets", ["warehouse_bucket_id"], name: "index_searcher_buckets_on_warehouse_bucket_id", using: :btree

  create_table "searcher_entities", force: :cascade do |t|
    t.string   "state"
    t.integer  "searchable_entity_id"
    t.string   "searchable_entity_type"
    t.string   "device"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.integer  "breached_days"
    t.integer  "account_id"
    t.string   "type"
    t.integer  "searcher_bucket_id"
    t.datetime "process_date"
  end

  add_index "searcher_entities", ["account_id"], name: "index_searcher_entities_on_account_id", using: :btree
  add_index "searcher_entities", ["device"], name: "index_searcher_entities_on_device", using: :btree
  add_index "searcher_entities", ["searcher_bucket_id"], name: "index_searcher_entities_on_searcher_bucket_id", using: :btree

  create_table "searcher_items", force: :cascade do |t|
    t.integer  "searcher_entity_id"
    t.integer  "line_item_id"
    t.string   "abandonment_reason"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string   "kind"
    t.string   "state",              default: "open"
  end

  add_index "searcher_items", ["line_item_id"], name: "index_searcher_items_on_line_item_id", using: :btree
  add_index "searcher_items", ["searcher_entity_id"], name: "index_searcher_items_on_searcher_entity_id", using: :btree

  create_table "seller_followups", force: :cascade do |t|
    t.string   "email",          limit: 255, null: false
    t.string   "contact_number", limit: 255, null: false
    t.text     "note"
    t.datetime "created_at",                 null: false
    t.datetime "updated_at",                 null: false
    t.string   "company_name"
  end

  add_index "seller_followups", ["contact_number"], name: "index_seller_followups_on_contact_number", using: :btree
  add_index "seller_followups", ["email"], name: "index_seller_followups_on_email", unique: true, using: :btree

  create_table "seo_list_properties", force: :cascade do |t|
    t.integer  "seo_list_id",             null: false
    t.integer  "property_id",             null: false
    t.string   "title",       limit: 255
    t.string   "keyword",     limit: 255
    t.string   "description", limit: 255
    t.datetime "created_at",              null: false
    t.datetime "updated_at",              null: false
  end

  add_index "seo_list_properties", ["property_id"], name: "index_seo_list_properties_on_property_id", using: :btree
  add_index "seo_list_properties", ["seo_list_id", "property_id"], name: "index_seo_list_properties_on_seo_list_id_and_property_id", unique: true, using: :btree
  add_index "seo_list_properties", ["seo_list_id"], name: "index_seo_list_properties_on_seo_list_id", using: :btree

  create_table "seo_lists", force: :cascade do |t|
    t.string   "label",       limit: 255
    t.text     "title"
    t.text     "keyword"
    t.text     "description"
    t.text     "post"
    t.datetime "created_at",              null: false
    t.datetime "updated_at",              null: false
    t.integer  "category_id"
    t.text     "top_content"
  end

  add_index "seo_lists", ["label"], name: "index_seo_lists_on_label", using: :btree

  create_table "shipment_buckets", force: :cascade do |t|
    t.integer  "order_id",                         null: false
    t.integer  "shipment_id"
    t.datetime "created_at",                       null: false
    t.datetime "updated_at",                       null: false
    t.boolean  "sent_to_invoice"
    t.datetime "sent_to_invoice_on"
    t.float    "actual_weight",      default: 0.0
  end

  add_index "shipment_buckets", ["order_id"], name: "index_shipment_buckets_on_order_id", using: :btree
  add_index "shipment_buckets", ["shipment_id"], name: "index_shipment_buckets_on_shipment_id", using: :btree

  create_table "shipment_charges", force: :cascade do |t|
    t.integer  "shipper_id"
    t.integer  "country_id"
    t.float    "weight"
    t.float    "price"
    t.float    "with_surcharge"
    t.datetime "created_at",     null: false
    t.datetime "updated_at",     null: false
  end

  add_index "shipment_charges", ["country_id", "weight"], name: "index_on_country_weight_shipment_charge", unique: true, using: :btree
  add_index "shipment_charges", ["country_id"], name: "index_shipment_charges_on_country_id", using: :btree
  add_index "shipment_charges", ["shipper_id"], name: "index_shipment_charges_on_shipper_id", using: :btree

  create_table "shipment_invoice_items", force: :cascade do |t|
    t.string   "description",     limit: 255
    t.integer  "hsn_code"
    t.float    "rate"
    t.float    "discount"
    t.float    "discounted_rate"
    t.float    "taxable_value"
    t.float    "gst_tax"
    t.float    "gst_rate"
    t.float    "total_amount"
    t.string   "product_type",    limit: 255
    t.string   "currency",        limit: 255
    t.integer  "shipment_id",                 null: false
    t.datetime "created_at",                  null: false
    t.datetime "updated_at",                  null: false
    t.boolean  "gst_paid"
    t.integer  "quantity"
    t.float    "weight"
  end

  add_index "shipment_invoice_items", ["shipment_id"], name: "index_shipment_invoice_items_on_shipment_id", using: :btree

  create_table "shipments", force: :cascade do |t|
    t.integer  "shipper_id",                                              null: false
    t.integer  "packer_id",                                               null: false
    t.integer  "invoicer_id",                                             null: false
    t.string   "shipment_state",               limit: 255
    t.datetime "delivered_on"
    t.integer  "price"
    t.float    "weight",                                                  null: false
    t.string   "packaging_type",               limit: 255
    t.integer  "order_id"
    t.datetime "created_at",                                              null: false
    t.datetime "updated_at",                                              null: false
    t.string   "number",                       limit: 255
    t.string   "label_file_name",              limit: 255
    t.string   "label_content_type",           limit: 255
    t.integer  "label_file_size"
    t.datetime "label_updated_at"
    t.string   "delivered_to",                 limit: 255
    t.text     "last_event"
    t.string   "invoice_file_name",            limit: 255
    t.string   "invoice_content_type",         limit: 255
    t.integer  "invoice_file_size"
    t.datetime "invoice_updated_at"
    t.integer  "designer_order_id"
    t.string   "return_label_file_name",       limit: 255
    t.string   "return_label_content_type",    limit: 255
    t.integer  "return_label_file_size"
    t.datetime "return_label_updated_at"
    t.string   "return_tracking_no",           limit: 255
    t.string   "shipment_type",                limit: 255
    t.integer  "system_shipment_charges"
    t.integer  "system_weight"
    t.string   "return_last_event",            limit: 255
    t.string   "payment_state",                limit: 255
    t.datetime "estimated_delivery_timestamp"
    t.datetime "in_transit_datetime"
    t.integer  "cod_charge",                               default: 0
    t.datetime "last_event_timestamp"
    t.integer  "in_transit_sms_count",                     default: 0
    t.integer  "delivery_exception_sms_count",             default: 0
    t.integer  "delivered_sms_count",                      default: 0
    t.integer  "rto_sms_count",                            default: 0
    t.integer  "out_for_delivery_sms_count",               default: 0
    t.string   "mirraw_reference",             limit: 255
    t.string   "utr_no",                       limit: 255
    t.datetime "reconciliation_date"
    t.string   "tofrom_label_file_name",       limit: 255
    t.string   "tofrom_label_content_type",    limit: 255
    t.integer  "tofrom_label_file_size"
    t.datetime "tofrom_label_updated_at"
    t.string   "unique_tracking_number",       limit: 255
    t.string   "track_error",                  limit: 255
    t.string   "courier_label_url",            limit: 255
    t.boolean  "automated",                                default: true, null: false
    t.boolean  "track",                                    default: true, null: false
    t.decimal  "shipment_charges"
    t.string   "paid_to_courier_partner",      limit: 255
    t.datetime "paid_courier_charges_date"
    t.float    "shipment_weight"
    t.datetime "vendor_charged_date"
    t.integer  "amount_charged_to_vendor"
    t.string   "charged_to_vendor",            limit: 255
    t.float    "weight_charged_to_vendor"
    t.string   "claim_state",                  limit: 255
    t.datetime "claim_raised_date"
    t.datetime "claim_rejected_date"
    t.datetime "claim_approved_date"
    t.integer  "claim_amount"
    t.boolean  "csb_used"
    t.string   "shipping_bill_no",             limit: 255
    t.integer  "dhl_ecom_pickup_id"
    t.string   "invoice_number",               limit: 255
    t.hstore   "invoice_data",                             default: {},   null: false
    t.datetime "shipping_bill_date"
    t.string   "port_code",                    limit: 255
    t.float    "exchange_rate"
    t.string   "out_scan_batch"
    t.datetime "out_scan_date"
    t.string   "service_type"
    t.datetime "out_for_pickup_on"
    t.datetime "pickup_scheduled_on"
    t.datetime "out_for_delivery_on"
    t.string   "irn_number"
    t.string   "gst_barcode"
    t.string   "jewellery_invoice_file_name"
    t.boolean  "delivery_duty_paid"
  end

  add_index "shipments", ["designer_order_id"], name: "index_shipments_on_designer_order_id", using: :btree
  add_index "shipments", ["dhl_ecom_pickup_id"], name: "index_shipments_on_dhl_ecom_pickup_id", using: :btree
  add_index "shipments", ["number"], name: "index_shipments_on_number", using: :btree
  add_index "shipments", ["order_id"], name: "index_shipments_on_order_id", using: :btree
  add_index "shipments", ["out_scan_batch"], name: "index_shipments_on_out_scan_batch", using: :btree
  add_index "shipments", ["shipper_id"], name: "index_shipments_on_shipper_id", using: :btree

  create_table "shipper_country_estimations", force: :cascade do |t|
    t.integer  "shipper_id", null: false
    t.integer  "country_id", null: false
    t.integer  "days"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  create_table "shipper_fuel_prices", force: :cascade do |t|
    t.integer  "shipper_id"
    t.integer  "country_id"
    t.float    "weight"
    t.float    "base_charge"
    t.float    "surcharge"
    t.datetime "created_at",  null: false
    t.datetime "updated_at",  null: false
  end

  add_index "shipper_fuel_prices", ["country_id"], name: "index_shipper_fuel_prices_on_country_id", using: :btree
  add_index "shipper_fuel_prices", ["shipper_id", "country_id", "weight"], name: "index_on_shipper_country_weight", unique: true, using: :btree

  create_table "shippers", force: :cascade do |t|
    t.string   "name",                 limit: 255
    t.string   "website",              limit: 255
    t.datetime "created_at",                                       null: false
    t.datetime "updated_at",                                       null: false
    t.boolean  "enabled",                          default: false, null: false
    t.integer  "priority",                         default: -1,    null: false
    t.string   "contact_number",       limit: 255
    t.string   "alt_contact_number",   limit: 255
    t.boolean  "international",                    default: true,  null: false
    t.boolean  "domestic",                         default: true,  null: false
    t.boolean  "pickup_notify",                    default: false, null: false
    t.string   "email",                limit: 255
    t.integer  "clickpost_shipper_id"
  end

  add_index "shippers", ["name"], name: "index_shippers_on_name", using: :btree

  create_table "shortened_urls", force: :cascade do |t|
    t.integer  "owner_id"
    t.string   "owner_type", limit: 20
    t.text     "url",                                null: false
    t.string   "unique_key", limit: 20,              null: false
    t.string   "label",      limit: 255
    t.string   "string",     limit: 255
    t.integer  "use_count",              default: 0, null: false
    t.string   "category",   limit: 255
    t.datetime "expires_at"
    t.datetime "created_at",                         null: false
    t.datetime "updated_at",                         null: false
  end

  add_index "shortened_urls", ["category"], name: "index_shortened_urls_on_category", using: :btree
  add_index "shortened_urls", ["label"], name: "index_shortened_urls_on_label", using: :btree
  add_index "shortened_urls", ["owner_id", "owner_type"], name: "index_shortened_urls_on_owner_id_and_owner_type", using: :btree
  add_index "shortened_urls", ["unique_key"], name: "index_shortened_urls_on_unique_key", unique: true, using: :btree
  add_index "shortened_urls", ["url"], name: "index_shortened_urls_on_url", using: :btree

  create_table "sitemap_items", force: :cascade do |t|
    t.integer  "parent_id"
    t.integer  "lft",                                        null: false
    t.integer  "rgt",                                        null: false
    t.integer  "depth",                          default: 0, null: false
    t.integer  "children_count",                 default: 0, null: false
    t.string   "title",              limit: 255
    t.string   "cached_slug",        limit: 255
    t.integer  "position"
    t.text     "url"
    t.string   "image_file_name",    limit: 255
    t.string   "image_content_type", limit: 255
    t.integer  "image_file_size"
    t.datetime "image_updated_at"
    t.datetime "deleted_at"
  end

  add_index "sitemap_items", ["deleted_at"], name: "index_sitemap_items_on_deleted_at", using: :btree

  create_table "size_buckets", force: :cascade do |t|
    t.string "size_name"
  end

  create_table "size_charts", force: :cascade do |t|
    t.string   "size",           limit: 255
    t.boolean  "show_size"
    t.datetime "created_at",                 null: false
    t.datetime "updated_at",                 null: false
    t.integer  "size_bucket_id"
  end

  add_index "size_charts", ["size"], name: "index_size_charts_on_size", using: :btree

  create_table "sized_designs", force: :cascade do |t|
    t.integer "design_id"
    t.integer "size_bucket_id"
    t.integer "quantity",       default: 0
  end

  add_index "sized_designs", ["design_id", "size_bucket_id"], name: "index_sized_designs_on_design_id_and_size_bucket_id", unique: true, using: :btree

  create_table "slugs", force: :cascade do |t|
    t.string   "slug",           limit: 255
    t.integer  "sluggable_id"
    t.integer  "sequence",                   default: 1, null: false
    t.string   "sluggable_type", limit: 40
    t.string   "scope",          limit: 255
    t.datetime "created_at"
  end

  add_index "slugs", ["slug", "sluggable_type", "sequence", "scope"], name: "index_slugs_on_n_s_s_and_s", unique: true, using: :btree
  add_index "slugs", ["sluggable_id"], name: "index_slugs_on_sluggable_id", using: :btree

  create_table "states", force: :cascade do |t|
    t.string   "name",           limit: 255
    t.string   "iso3166_alpha2", limit: 255
    t.integer  "country_id"
    t.datetime "created_at",                 null: false
    t.datetime "updated_at",                 null: false
    t.string   "code",           limit: 255
  end

  add_index "states", ["country_id"], name: "index_states_on_country_id", using: :btree
  add_index "states", ["name"], name: "index_states_on_name", using: :btree

  create_table "statistic_attributes", force: :cascade do |t|
    t.string   "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  add_index "statistic_attributes", ["name"], name: "index_statistic_attributes_on_name", unique: true, using: :btree

  create_table "statistics", force: :cascade do |t|
    t.integer  "statistic_attribute_id"
    t.string   "value"
    t.integer  "statisticable_id"
    t.string   "statisticable_type"
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
    t.string   "category"
    t.date     "generated_on"
  end

  add_index "statistics", ["statisticable_type", "statisticable_id", "generated_on", "statistic_attribute_id", "category"], name: "index_designer_stat", unique: true, order: {"generated_on"=>:desc}, using: :btree

  create_table "stitching_addons", force: :cascade do |t|
    t.string   "addon_type",       limit: 255
    t.string   "color",            limit: 255
    t.integer  "quantity"
    t.string   "material",         limit: 255
    t.integer  "line_item_id"
    t.integer  "order_id"
    t.integer  "details_added_by"
    t.datetime "details_added_on"
    t.datetime "created_at",                   null: false
    t.datetime "updated_at",                   null: false
  end

  add_index "stitching_addons", ["line_item_id"], name: "index_stitching_addons_on_line_item_id", using: :btree
  add_index "stitching_addons", ["order_id"], name: "index_stitching_addons_on_order_id", using: :btree

  create_table "stitching_measurements", force: :cascade do |t|
    t.integer  "design_id"
    t.integer  "line_item_id"
    t.integer  "order_id"
    t.string   "size_around_ankle",               limit: 255
    t.string   "shoulder_size",                   limit: 255
    t.string   "product_designable_type",         limit: 255
    t.string   "length",                          limit: 255
    t.boolean  "embroidary"
    t.string   "chest_size",                      limit: 255
    t.string   "sleeves_length",                  limit: 255
    t.string   "sleeves_around",                  limit: 255
    t.string   "front_neck",                      limit: 255
    t.string   "back_neck",                       limit: 255
    t.string   "size_around_thigh",               limit: 255
    t.string   "bottom_length",                   limit: 255
    t.string   "size_around_arm_hole",            limit: 255
    t.string   "waist_size",                      limit: 255
    t.string   "hip_size",                        limit: 255
    t.string   "size_around_knee",                limit: 255
    t.boolean  "measurement_locked"
    t.datetime "created_at",                                               null: false
    t.datetime "updated_at",                                               null: false
    t.integer  "under_bust"
    t.string   "padded",                          limit: 255
    t.string   "style_no",                        limit: 255
    t.string   "hook",                            limit: 255
    t.integer  "weight"
    t.string   "height",                          limit: 255
    t.string   "stitching_label_url",             limit: 255
    t.string   "measurement_info_done_by",        limit: 255
    t.string   "code",                            limit: 255
    t.string   "shoulder_to_apex",                limit: 255
    t.text     "stitching_notes"
    t.string   "measurement_type",                limit: 255
    t.integer  "user_id"
    t.string   "measurement_name",                limit: 255
    t.text     "suggested_measurements"
    t.string   "state",                           limit: 255
    t.integer  "stylist_id"
    t.datetime "reject_mail_sent_at"
    t.hstore   "user_review",                                 default: {}, null: false
    t.integer  "parent_measurement_id"
    t.datetime "product_received_from_tailor_on"
    t.string   "product_received_from_tailor_by", limit: 255
    t.integer  "age"
    t.string   "garment_fit"
    t.string   "denim_waist_size"
    t.string   "waist_belt_type"
    t.string   "back_style_no"
    t.integer  "reject_mail_count",                           default: 0
    t.text     "accuracy_info"
    t.text     "source_info"
    t.string   "cancan"
    t.datetime "approved_at"
    t.datetime "rejected_at"
    t.datetime "phone_call_at"
    t.datetime "hold_at"
    t.text     "measurement_flow"
    t.string   "measurement_group"
  end

  add_index "stitching_measurements", ["design_id"], name: "index_stitching_measurements_on_design_id", using: :btree
  add_index "stitching_measurements", ["id"], name: "index_stitching_measurements_on_id", using: :btree
  add_index "stitching_measurements", ["line_item_id"], name: "index_stitching_measurements_on_line_item_id", using: :btree
  add_index "stitching_measurements", ["order_id"], name: "index_stitching_measurements_on_order_id", using: :btree
  add_index "stitching_measurements", ["parent_measurement_id"], name: "index_stitching_measurements_on_parent_measurement_id", using: :btree
  add_index "stitching_measurements", ["stylist_id"], name: "index_stitching_measurements_on_stylist_id", using: :btree

  create_table "stock_updates", force: :cascade do |t|
    t.integer  "designer_id"
    t.integer  "passed",              default: 0
    t.integer  "failed",              default: 0
    t.string   "inventory_file_name"
    t.integer  "account_id"
    t.datetime "created_at",                      null: false
    t.datetime "updated_at",                      null: false
  end

  add_index "stock_updates", ["designer_id"], name: "index_stock_updates_on_designer_id", using: :btree

  create_table "story_collections", force: :cascade do |t|
    t.string   "title"
    t.integer  "grade"
    t.string   "app_source"
    t.datetime "start_date"
    t.datetime "end_date"
    t.boolean  "photo_processing",   default: false
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string   "photo_file_name"
    t.string   "photo_content_type"
    t.integer  "photo_file_size"
    t.datetime "photo_updated_at"
    t.string   "collection_name"
    t.string   "app_name"
  end

  add_index "story_collections", ["app_source"], name: "index_story_collections_on_app_source", using: :btree
  add_index "story_collections", ["end_date"], name: "index_story_collections_on_end_date", using: :btree
  add_index "story_collections", ["start_date"], name: "index_story_collections_on_start_date", using: :btree

  create_table "stylist_receives", force: :cascade do |t|
    t.integer  "stylist_id"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "stylist_receives", ["stylist_id"], name: "index_stylist_receives_on_stylist_id", using: :btree

  create_table "stylists", force: :cascade do |t|
    t.string   "name",                limit: 255
    t.integer  "orders_count",                    default: 0,    null: false
    t.datetime "created_at",                                     null: false
    t.datetime "updated_at",                                     null: false
    t.integer  "account_id"
    t.boolean  "available",                       default: true
    t.datetime "vacation_start_date"
    t.datetime "vacation_end_date"
    t.text     "vacation_message"
    t.string   "stylist_group",       limit: 255
  end

  add_index "stylists", ["account_id"], name: "index_stylists_on_account_id", using: :btree

  create_table "subscriber_definitions", force: :cascade do |t|
    t.integer  "subscriber_id"
    t.string   "subscriber_type"
    t.string   "message_title"
    t.text     "message_body"
    t.integer  "notification_event_id"
    t.datetime "created_at",            null: false
    t.datetime "updated_at",            null: false
  end

  add_index "subscriber_definitions", ["notification_event_id"], name: "index_subscriber_definitions_on_notification_event_id", using: :btree
  add_index "subscriber_definitions", ["subscriber_type", "subscriber_id"], name: "index_subscriber_definitions_on_subscriber", using: :btree

  create_table "subscriptions", force: :cascade do |t|
    t.string   "email",      limit: 255
    t.text     "source_url"
    t.string   "country",    limit: 255
    t.string   "ip_address", limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
    t.string   "appsource",  limit: 255
  end

  add_index "subscriptions", ["email"], name: "index_subscriptions_on_email", unique: true, using: :btree

  create_table "suits", force: :cascade do |t|
    t.string   "shirt_color", limit: 255
    t.datetime "created_at",              null: false
    t.datetime "updated_at",              null: false
  end

  create_table "sunspot_index_queue_entries", force: :cascade do |t|
    t.string   "record_class_name", limit: 255,                  null: false
    t.integer  "record_id",                                      null: false
    t.boolean  "is_delete",                      default: false, null: false
    t.datetime "run_at",                                         null: false
    t.integer  "priority",                       default: 0,     null: false
    t.integer  "lock"
    t.string   "error",             limit: 4000
    t.integer  "attempts",                       default: 0,     null: false
  end

  add_index "sunspot_index_queue_entries", ["record_id"], name: "index_sunspot_index_queue_entries_on_record_id", using: :btree
  add_index "sunspot_index_queue_entries", ["run_at", "record_class_name", "priority"], name: "sunspot_index_queue_entries_run_at", using: :btree

  create_table "support_texts", force: :cascade do |t|
    t.string   "title",                  limit: 255
    t.string   "description",            limit: 255
    t.boolean  "view_flag"
    t.datetime "created_at",                                         null: false
    t.datetime "updated_at",                                         null: false
    t.string   "category_of_faq",        limit: 255
    t.integer  "priority"
    t.text     "question"
    t.text     "answer"
    t.integer  "order_for_category"
    t.boolean  "international"
    t.boolean  "domestic"
    t.boolean  "visible_to_designer",                default: false
    t.integer  "help_center_header_id"
    t.integer  "support_text_parent_id"
  end

  add_index "support_texts", ["help_center_header_id"], name: "index_support_texts_on_help_center_header_id", using: :btree

  create_table "survey_answer_options", force: :cascade do |t|
    t.integer  "survey_question_id"
    t.string   "name"
    t.integer  "value"
    t.datetime "created_at",         null: false
    t.datetime "updated_at",         null: false
  end

  add_index "survey_answer_options", ["survey_question_id"], name: "index_survey_answer_options_on_survey_question_id", using: :btree

  create_table "survey_answers", force: :cascade do |t|
    t.integer  "surveyable_id",               null: false
    t.string   "surveyable_type", limit: 255, null: false
    t.integer  "question_id",                 null: false
    t.boolean  "response",                    null: false
    t.datetime "created_at",                  null: false
    t.datetime "updated_at",                  null: false
    t.string   "answer"
  end

  create_table "survey_questions", force: :cascade do |t|
    t.string   "question",                     limit: 255,                    null: false
    t.string   "qualify_as",                   limit: 255,                    null: false
    t.boolean  "disabled",                                 default: false
    t.integer  "parent_question_id"
    t.string   "app_source",                   limit: 255
    t.string   "audience",                     limit: 255
    t.integer  "priority",                                 default: 0
    t.datetime "created_at",                                                  null: false
    t.datetime "updated_at",                                                  null: false
    t.string   "event_klass",                  limit: 255
    t.string   "designer_text",                limit: 255
    t.string   "geo",                          limit: 255, default: "global"
    t.string   "question_type"
    t.string   "survey_terminating_condition"
  end

  create_table "surveys", force: :cascade do |t|
    t.integer  "rating"
    t.string   "email",        limit: 255
    t.integer  "order_id"
    t.string   "order_number", limit: 255
    t.datetime "created_at",               null: false
    t.datetime "updated_at",               null: false
    t.text     "notes"
    t.string   "app_source",   limit: 255
  end

  add_index "surveys", ["order_id"], name: "index_surveys_on_order_id", using: :btree

  create_table "system_constants", force: :cascade do |t|
    t.string   "name",       limit: 255,                  null: false
    t.text     "value",                                   null: false
    t.boolean  "autoload",               default: false,  null: false
    t.datetime "created_at",                              null: false
    t.datetime "updated_at",                              null: false
    t.boolean  "public",                 default: false,  null: false
    t.string   "value_type",             default: "text"
  end

  add_index "system_constants", ["autoload"], name: "index_system_constants_on_autoload", using: :btree
  add_index "system_constants", ["name"], name: "index_system_constants_on_name", unique: true, using: :btree

  create_table "tableau_reports", force: :cascade do |t|
    t.string   "title"
    t.text     "link"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string   "report_for"
  end

  create_table "tabs", force: :cascade do |t|
    t.string   "name"
    t.integer  "grade"
    t.datetime "start_date"
    t.datetime "end_date"
    t.string   "country"
    t.datetime "created_at",                 null: false
    t.datetime "updated_at",                 null: false
    t.string   "app_source"
    t.boolean  "active",     default: false
    t.integer  "tab_type",   default: 0
    t.string   "slug"
  end

  create_table "tag_sliders", force: :cascade do |t|
    t.string   "title",                limit: 255
    t.text     "link"
    t.integer  "grade"
    t.string   "link_type",            limit: 255
    t.string   "link_value",           limit: 255
    t.string   "app_source",           limit: 255
    t.datetime "created_at",                                       null: false
    t.datetime "updated_at",                                       null: false
    t.string   "photo_file_name"
    t.string   "photo_content_type"
    t.integer  "photo_file_size"
    t.datetime "photo_updated_at"
    t.boolean  "photo_processing",                 default: false
    t.string   "alternate_link"
    t.string   "alternate_link_type"
    t.string   "alternate_link_value"
  end

  add_index "tag_sliders", ["app_source"], name: "index_tag_sliders_on_app_source", using: :btree

  create_table "taggings", force: :cascade do |t|
    t.integer  "tag_id"
    t.integer  "taggable_id"
    t.string   "taggable_type", limit: 255
    t.integer  "tagger_id"
    t.string   "tagger_type",   limit: 255
    t.string   "context",       limit: 128
    t.datetime "created_at"
    t.text     "reason"
  end

  add_index "taggings", ["tag_id", "taggable_id", "taggable_type", "context", "tagger_id", "tagger_type"], name: "taggings_idx", unique: true, using: :btree
  add_index "taggings", ["taggable_id", "taggable_type", "context"], name: "index_taggings_on_taggable_id_and_taggable_type_and_context", using: :btree

  create_table "tags", force: :cascade do |t|
    t.string  "name",           limit: 255
    t.integer "taggings_count",             default: 0
    t.integer "tag_type"
  end

  create_table "tailor_daily_metrics", force: :cascade do |t|
    t.string   "name"
    t.float    "price"
    t.integer  "tailor_id"
    t.datetime "created_at",                         null: false
    t.datetime "updated_at",                         null: false
    t.string   "designable_type"
    t.integer  "workers"
    t.integer  "capacity_per_worker"
    t.integer  "min_capacity"
    t.integer  "pending_count"
    t.string   "tailor_type"
    t.boolean  "active",              default: true
  end

  add_index "tailor_daily_metrics", ["tailor_id"], name: "index_tailor_daily_metrics_on_tailor_id", using: :btree

  create_table "tailor_visits", force: :cascade do |t|
    t.datetime "next_visit"
    t.integer  "tailor_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  add_index "tailor_visits", ["tailor_id"], name: "index_tailor_visits_on_tailor_id", using: :btree

  create_table "tailoring_bag_relations", force: :cascade do |t|
    t.integer  "tailoring_info_id"
    t.integer  "tailoring_batch_id"
    t.integer  "tailoring_inscan_bag_id"
    t.integer  "assigned_count",          default: 0
    t.string   "state",                   default: "assigned"
    t.datetime "created_at",                                   null: false
    t.datetime "updated_at",                                   null: false
    t.integer  "inscanned_count",         default: 0
    t.integer  "rtv_count",               default: 0
    t.datetime "completed_on"
  end

  add_index "tailoring_bag_relations", ["tailoring_batch_id"], name: "index_tailoring_bag_relations_on_tailoring_batch_id", using: :btree
  add_index "tailoring_bag_relations", ["tailoring_info_id"], name: "index_tailoring_bag_relations_on_tailoring_info_id", using: :btree
  add_index "tailoring_bag_relations", ["tailoring_inscan_bag_id"], name: "index_tailoring_bag_relations_on_tailoring_inscan_bag_id", using: :btree

  create_table "tailoring_batches", force: :cascade do |t|
    t.string   "batch_code",                          null: false
    t.float    "payout_amount"
    t.integer  "pending_item",  default: 0
    t.integer  "total_item",    default: 0
    t.datetime "sent_at"
    t.datetime "closed_at"
    t.datetime "received_at"
    t.datetime "started_at"
    t.string   "state"
    t.integer  "assignee_id",                         null: false
    t.integer  "tailor_id"
    t.datetime "created_at",                          null: false
    t.datetime "updated_at",                          null: false
    t.string   "type",          default: "Tailoring"
  end

  add_index "tailoring_batches", ["assignee_id"], name: "index_tailoring_batches_on_assignee_id", using: :btree
  add_index "tailoring_batches", ["batch_code"], name: "index_tailoring_batches_on_batch_code", unique: true, using: :btree
  add_index "tailoring_batches", ["tailor_id"], name: "index_tailoring_batches_on_tailor_id", using: :btree

  create_table "tailoring_infos", force: :cascade do |t|
    t.integer  "order_id"
    t.integer  "item_id"
    t.string   "tailor_info_done_by",                limit: 255
    t.string   "tailor_name",                        limit: 255
    t.string   "tailoring_material",                 limit: 255
    t.boolean  "material_received_status"
    t.datetime "created_at",                                     null: false
    t.datetime "updated_at",                                     null: false
    t.datetime "material_received_status_timestamp"
    t.integer  "line_item_quantity"
    t.string   "paid_status_tailor",                 limit: 255
    t.datetime "payout_tailor_paid_timestamp"
    t.datetime "assigned_to_tailor_at"
    t.datetime "picked_up_at"
    t.string   "batch_code",                         limit: 255
    t.text     "notes"
    t.datetime "reassign_material_timestamp"
    t.string   "rtv_material",                       limit: 255
    t.datetime "replacement_material_timestamp"
    t.integer  "tailor_id"
    t.integer  "price_deduction"
    t.text     "tailor_issue"
    t.text     "alteration_note"
    t.boolean  "active"
    t.datetime "alteration_added_at"
    t.string   "item_type"
    t.datetime "product_scanned_by_tailor_on"
    t.datetime "latest_inscanned_at"
    t.integer  "tailor_daily_metric_id"
  end

  add_index "tailoring_infos", ["item_id"], name: "index_tailoring_infos_on_item_id", using: :btree
  add_index "tailoring_infos", ["order_id"], name: "index_tailoring_infos_on_order_id", using: :btree
  add_index "tailoring_infos", ["tailor_daily_metric_id"], name: "index_tailoring_infos_on_tailor_daily_metric_id", using: :btree
  add_index "tailoring_infos", ["tailor_id"], name: "index_tailoring_infos_on_tailor_id", using: :btree

  create_table "tailoring_inscan_bags", force: :cascade do |t|
    t.string   "number"
    t.string   "status",           default: "open"
    t.integer  "inscanned_count"
    t.integer  "successful_count"
    t.string   "report_url"
    t.string   "created_by"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "tailoring_inscan_bags", ["number"], name: "index_tailoring_inscan_bags_on_number", using: :btree

  create_table "tailoring_inscans", force: :cascade do |t|
    t.string   "scanned_barcode"
    t.string   "status"
    t.string   "remark"
    t.integer  "tailoring_inscan_bag_id"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "tailoring_inscans", ["tailoring_inscan_bag_id"], name: "index_tailoring_inscans_on_tailoring_inscan_bag_id", using: :btree

  create_table "tailoring_issues", force: :cascade do |t|
    t.string   "issue"
    t.string   "response"
    t.integer  "tailoring_info_id"
    t.datetime "responded_at"
    t.string   "responded_by"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_index "tailoring_issues", ["tailoring_info_id"], name: "index_tailoring_issues_on_tailoring_info_id", using: :btree

  create_table "tailors", force: :cascade do |t|
    t.string   "name",               limit: 255
    t.string   "pan_card_no",        limit: 255
    t.string   "account_no",         limit: 255
    t.string   "ifsc_code",          limit: 255
    t.string   "benificiary_name",   limit: 255
    t.string   "contact_no",         limit: 255
    t.text     "address"
    t.integer  "simple_blouse_rate"
    t.integer  "padded_blouse_rate"
    t.integer  "anarkali_rate"
    t.integer  "lehenga_rate"
    t.integer  "saree_rate"
    t.datetime "created_at",                                        null: false
    t.datetime "updated_at",                                        null: false
    t.string   "email",              limit: 255
    t.float    "defect_rate",                    default: 0.0
    t.string   "state",                          default: "active"
    t.string   "worker_contanct_no"
    t.string   "encrypted_password"
    t.string   "salt"
  end

  create_table "task_logs", force: :cascade do |t|
    t.text     "messages",               null: false
    t.string   "name",       limit: 255, null: false
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  add_index "task_logs", ["created_at"], name: "index_task_logs_on_created_at", using: :btree
  add_index "task_logs", ["name"], name: "index_task_logs_on_name", using: :btree

  create_table "tickets", force: :cascade do |t|
    t.integer  "order_id"
    t.string   "issue",                             limit: 255
    t.integer  "created_by_id"
    t.integer  "resolved_by_id"
    t.string   "state",                             limit: 255
    t.text     "message"
    t.datetime "resolved_at"
    t.text     "resolve_message"
    t.integer  "tickets_raised"
    t.integer  "calls_received"
    t.string   "department",                        limit: 255
    t.datetime "created_at",                                    null: false
    t.datetime "updated_at",                                    null: false
    t.integer  "worked_by_id"
    t.datetime "started_work_at"
    t.integer  "return_id"
    t.string   "stitching_issue_image_1_file_name", limit: 255
    t.string   "stitching_issue_image_2_file_name", limit: 255
    t.datetime "reopened_on"
    t.text     "notes"
    t.string   "freshdesk_ticket_number"
    t.datetime "expected_resolve_date"
    t.datetime "expected_delivery_date"
    t.integer  "reopened_by_id"
    t.text  "images"
    t.text "video"
    t.integer "escalation_count"
  end

  add_index "tickets", ["order_id"], name: "index_tickets_on_order_id", using: :btree
  add_index "tickets", ["return_id"], name: "index_tickets_on_return_id", using: :btree

  create_table "time_series_datasets", force: :cascade do |t|
    t.date     "date"
    t.text     "data"
    t.integer  "metric_id"
    t.string   "metric_type", limit: 255
    t.datetime "created_at",              null: false
    t.datetime "updated_at",              null: false
  end

  add_index "time_series_datasets", ["metric_type", "metric_id", "date"], name: "index_time_series_datasets_on_date_metric_and_parent", unique: true, using: :btree

  create_table "timeline_events", force: :cascade do |t|
    t.string   "event_type",             limit: 255
    t.string   "subject_type",           limit: 255
    t.string   "actor_type",             limit: 255
    t.string   "secondary_subject_type", limit: 255
    t.integer  "subject_id"
    t.integer  "actor_id"
    t.integer  "secondary_subject_id"
    t.datetime "created_at",                         null: false
    t.datetime "updated_at",                         null: false
    t.string   "notified",               limit: 255
  end

  add_index "timeline_events", ["created_at"], name: "index_timeline_events_on_created_at", using: :btree
  add_index "timeline_events", ["secondary_subject_id", "secondary_subject_type"], name: "index_timeline_event_secondary", using: :btree
  add_index "timeline_events", ["subject_id", "subject_type"], name: "index_timeline_events_on_subject_id_and_subject_type", using: :btree

  create_table "transaction_orders", force: :cascade do |t|
    t.integer "order_id"
    t.integer "juspay_customer_id"
    t.string  "juspay_order_id"
    t.string  "status"
  end

  create_table "turbans", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "unbxd_containers", force: :cascade do |t|
    t.string   "title",           limit: 255
    t.text     "description"
    t.text     "template"
    t.integer  "unbxd_widget_id"
    t.datetime "created_at",                                 null: false
    t.datetime "updated_at",                                 null: false
    t.boolean  "active",                      default: true
    t.integer  "product_count"
  end

  create_table "unbxd_widgets", force: :cascade do |t|
    t.string   "title",      limit: 255
    t.string   "url",        limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "users", force: :cascade do |t|
    t.string   "email",                        limit: 255
    t.string   "first_name",                   limit: 255
    t.string   "last_name",                    limit: 255
    t.text     "image_url"
    t.datetime "birthday"
    t.string   "gender",                       limit: 255
    t.datetime "created_at",                               null: false
    t.datetime "updated_at",                               null: false
    t.date     "visit_mail_sent"
    t.text     "history"
    t.boolean  "terms_of_service"
    t.integer  "wallet_id"
    t.boolean  "referral"
    t.string   "full_size_photo_file_name",    limit: 255
    t.string   "full_size_photo_content_type", limit: 255
    t.integer  "full_size_photo_file_size"
    t.datetime "full_size_photo_updated_at"
    t.string   "slug"
    t.string   "user_type"
  end

  add_index "users", ["email"], name: "index_users_on_email", using: :btree
  add_index "users", ["slug"], name: "index_users_on_slug", using: :btree

  create_table "variants", force: :cascade do |t|
    t.integer  "design_id"
    t.integer  "quantity"
    t.integer  "position"
    t.datetime "created_at",                        null: false
    t.datetime "updated_at",                        null: false
    t.boolean  "show_variant",       default: true
    t.integer  "in_stock_warehouse"
    t.integer  "reordering_percent"
    t.integer  "ordering_quantity"
    t.integer  "price"
    t.string   "design_code"
    t.boolean  "stitched",           default: true
    t.integer  "transfer_price"
  end

  add_index "variants", ["design_id"], name: "index_variants_on_design_id", using: :btree

  create_table "vendor_payout_batches", force: :cascade do |t|
    t.string   "file_name",  limit: 255
    t.string   "url",        limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "vendor_payouts", force: :cascade do |t|
    t.string   "awb",                    limit: 255
    t.integer  "charge"
    t.string   "status",                 limit: 255
    t.integer  "vendor_payout_batch_id"
    t.datetime "created_at",                         null: false
    t.datetime "updated_at",                         null: false
    t.integer  "shipment_id"
    t.integer  "invoice_id"
    t.string   "invoice_type"
  end

  add_index "vendor_payouts", ["vendor_payout_batch_id"], name: "index_vendor_payouts_on_vendor_payout_batch_id", using: :btree

  create_table "vendor_promotions", force: :cascade do |t|
    t.date     "start_date"
    t.date     "end_date"
    t.string   "plan_name",      limit: 255
    t.float    "cost"
    t.string   "transaction_id", limit: 255
    t.string   "state",          limit: 255
    t.string   "invoice_no",     limit: 255
    t.date     "confirmed_at"
    t.integer  "designer_id"
    t.datetime "created_at",                 null: false
    t.datetime "updated_at",                 null: false
  end

  add_index "vendor_promotions", ["designer_id"], name: "index_vendor_promotions_on_designer_id", using: :btree

  create_table "version_failed_designs", force: :cascade do |t|
    t.integer  "designer_batch_id"
    t.text     "error"
    t.text     "row"
    t.datetime "created_at",        null: false
    t.datetime "updated_at",        null: false
  end

  add_index "version_failed_designs", ["designer_batch_id"], name: "index_version_failed_designs_on_designer_batch_id", using: :btree

  create_table "versions", force: :cascade do |t|
    t.string   "item_type",  limit: 255, null: false
    t.integer  "item_id",                null: false
    t.string   "event",      limit: 255, null: false
    t.string   "whodunnit",  limit: 255
    t.text     "object"
    t.datetime "created_at"
  end

  add_index "versions", ["item_type", "item_id"], name: "index_versions_on_item_type_and_item_id", using: :btree

  create_table "video_links", force: :cascade do |t|
    t.string   "link",       limit: 255
    t.boolean  "status"
    t.integer  "video_id"
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "video_listings", force: :cascade do |t|
    t.string   "name"
    t.string   "url"
    t.string   "tag"
    t.integer  "position"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  create_table "videos", force: :cascade do |t|
    t.integer  "viewable_id"
    t.string   "viewable_type", limit: 255
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
  end

  create_table "wallet_transactions", force: :cascade do |t|
    t.integer  "order_id"
    t.integer  "return_id"
    t.integer  "wallet_id"
    t.integer  "user_id"
    t.string   "state",                    limit: 255
    t.float    "referral_amount",                      default: 0.0
    t.float    "return_amount",                        default: 0.0
    t.string   "fund_type",                limit: 255
    t.text     "notes"
    t.datetime "created_at",                                         null: false
    t.datetime "updated_at",                                         null: false
    t.integer  "referral_id"
    t.string   "wallet_transactable_type"
    t.integer  "wallet_transactable_id"
  end

  add_index "wallet_transactions", ["order_id"], name: "index_wallet_transactions_on_order_id", where: "(order_id IS NOT NULL)", using: :btree
  add_index "wallet_transactions", ["wallet_id"], name: "index_wallet_transactions_on_wallet_id", using: :btree

  create_table "wallets", force: :cascade do |t|
    t.float    "referral_amount",     default: 0.0
    t.float    "return_amount",       default: 0.0
    t.integer  "currency_convert_id"
    t.datetime "created_at",                        null: false
    t.datetime "updated_at",                        null: false
    t.float    "promotional_balance", default: 0.0
    t.datetime "referral_expires_at"
  end

  create_table "warehouse_addresses", force: :cascade do |t|
    t.string   "company_name",            default: "Mirraw Online Services Pvt Ltd."
    t.text     "address_line_1"
    t.text     "address_line_2"
    t.string   "city"
    t.string   "state"
    t.string   "country"
    t.string   "state_code"
    t.string   "pincode"
    t.string   "phone"
    t.integer  "created_by"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.text     "shipping_address_line_1"
    t.text     "shipping_address_line_2"
    t.string   "shipping_city"
    t.string   "shipping_state"
    t.string   "shipping_country"
    t.string   "shipping_state_code"
    t.string   "shipping_pincode"
    t.string   "shipping_phone"
  end

  create_table "warehouse_buckets", force: :cascade do |t|
    t.string   "bucket_type",                 null: false
    t.string   "code"
    t.string   "color_hex"
    t.datetime "created_at",                  null: false
    t.datetime "updated_at",                  null: false
    t.boolean  "is_dirty",    default: false
    t.string   "state"
  end

  create_table "warehouse_line_item_joins", force: :cascade do |t|
    t.integer  "line_item_id"
    t.integer  "rack_lists_warehouse_line_item_id"
    t.integer  "warehouse_order_id"
    t.integer  "warehouse_line_item_id"
    t.datetime "created_at",                        null: false
    t.datetime "updated_at",                        null: false
  end

  add_index "warehouse_line_item_joins", ["line_item_id", "rack_lists_warehouse_line_item_id", "warehouse_line_item_id", "warehouse_order_id"], name: "uniq_join_index", unique: true, using: :btree
  add_index "warehouse_line_item_joins", ["line_item_id"], name: "index_warehouse_line_item_joins_on_line_item_id", using: :btree
  add_index "warehouse_line_item_joins", ["rack_lists_warehouse_line_item_id"], name: "rack_warehouse_item_index", using: :btree
  add_index "warehouse_line_item_joins", ["warehouse_line_item_id"], name: "index_warehouse_line_item_joins_on_warehouse_line_item_id", using: :btree
  add_index "warehouse_line_item_joins", ["warehouse_order_id"], name: "index_warehouse_line_item_joins_on_warehouse_order_id", using: :btree

  create_table "warehouse_line_items", force: :cascade do |t|
    t.integer  "warehouse_order_id"
    t.integer  "quantity"
    t.integer  "quantity_used",                  default: 0
    t.integer  "reordering_percent"
    t.integer  "design_id"
    t.integer  "variant_id"
    t.integer  "rtv_quantity"
    t.float    "snapshot_price"
    t.string   "status",             limit: 255
    t.datetime "created_at",                                 null: false
    t.datetime "updated_at",                                 null: false
    t.string   "rtv_shipment_error"
    t.integer  "quantity_lost",                  default: 0
  end

  add_index "warehouse_line_items", ["design_id"], name: "index_warehouse_line_items_on_design_id", using: :btree
  add_index "warehouse_line_items", ["warehouse_order_id"], name: "index_warehouse_line_items_on_warehouse_order_id", using: :btree

  create_table "warehouse_order_logs", force: :cascade do |t|
    t.integer  "warehouse_order_id"
    t.integer  "quantity",                          default: 0
    t.string   "action"
    t.integer  "account_id"
    t.integer  "rack_list_id"
    t.integer  "line_item_id"
    t.integer  "rack_lists_warehouse_line_item_id"
    t.datetime "created_at",                                    null: false
    t.datetime "updated_at",                                    null: false
  end

  add_index "warehouse_order_logs", ["rack_lists_warehouse_line_item_id"], name: "index_warehouse_order_logs_on_rack_lists_warehouse_line_item_id", using: :btree
  add_index "warehouse_order_logs", ["warehouse_order_id"], name: "index_warehouse_order_logs_on_warehouse_order_id", using: :btree

# Could not dump table "warehouse_orders" because of following StandardError
#   Unknown type 'warehouse_order_order_type_enum' for column 'order_type'

  create_table "warehouse_rtv_shipment_line_items", force: :cascade do |t|
    t.integer  "quantity"
    t.integer  "warehouse_line_item_id"
    t.integer  "rtv_shipment_id"
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  add_index "warehouse_rtv_shipment_line_items", ["rtv_shipment_id"], name: "index_warehouse_rtv_shipment_line_items_on_rtv_shipment_id", using: :btree
  add_index "warehouse_rtv_shipment_line_items", ["warehouse_line_item_id"], name: "index_warehouse_rtv_shipment_items_on_warehouse_item_id", using: :btree

  create_table "warehouse_size_items", force: :cascade do |t|
    t.integer  "warehouse_line_item_id"
    t.integer  "size_bucket_id"
    t.integer  "quantity",               default: 0
    t.boolean  "stitching_sent"
    t.integer  "stitching_sent_by"
    t.datetime "stitching_sent_on"
  end

  add_index "warehouse_size_items", ["warehouse_line_item_id"], name: "index_warehouse_size_items_on_warehouse_line_item_id", using: :btree

  create_table "warehouse_stations", force: :cascade do |t|
    t.string   "station_code"
    t.datetime "created_at",   null: false
    t.datetime "updated_at",   null: false
  end

  create_table "wholesales", force: :cascade do |t|
    t.string   "name",       limit: 255
    t.string   "email",      limit: 255
    t.string   "phone",      limit: 255
    t.text     "comments"
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "widgets", force: :cascade do |t|
    t.string   "label",      limit: 255
    t.integer  "landing_id"
    t.integer  "grade"
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "wishlists", force: :cascade do |t|
    t.integer  "user_id"
    t.integer  "design_id"
    t.string   "url",              limit: 255
    t.string   "session_id",       limit: 255
    t.string   "country_code",     limit: 255
    t.integer  "price"
    t.decimal  "conversion_rate",              precision: 8, scale: 2
    t.string   "state",            limit: 255,                         default: "added"
    t.datetime "carted_at"
    t.datetime "last_notified_at"
    t.datetime "created_at",                                                             null: false
    t.datetime "updated_at",                                                             null: false
    t.boolean  "wish",                                                 default: false
    t.boolean  "like"
    t.string   "app_source"
  end

  add_index "wishlists", ["design_id"], name: "index_wishlists_on_design_id", using: :btree
  add_index "wishlists", ["user_id"], name: "index_wishlists_on_user_id", using: :btree

  add_foreign_key "addon_type_values", "designers"
  add_foreign_key "adjustment_reports", "designers"
  add_foreign_key "adjustments", "designer_orders"
  add_foreign_key "affiliate_designs", "designs"
  add_foreign_key "affiliate_orders", "affiliates"
  add_foreign_key "affiliate_orders", "orders"
  add_foreign_key "affiliates", "currency_converts"
  add_foreign_key "auth_group_permissions", "auth_group", column: "group_id", name: "auth_group_permissions_group_id_b120cbf9_fk_auth_group_id"
  add_foreign_key "auth_group_permissions", "auth_permission", column: "permission_id", name: "auth_group_permissio_permission_id_84c5c92e_fk_auth_perm"
  add_foreign_key "auth_permission", "django_content_type", column: "content_type_id", name: "auth_permission_content_type_id_2f476e4b_fk_django_co"
  add_foreign_key "auth_user_groups", "auth_group", column: "group_id", name: "auth_user_groups_group_id_97559544_fk_auth_group_id"
  add_foreign_key "auth_user_groups", "auth_user", column: "user_id", name: "auth_user_groups_user_id_6a12ed8b_fk_auth_user_id"
  add_foreign_key "auth_user_user_permissions", "auth_permission", column: "permission_id", name: "auth_user_user_permi_permission_id_1fbb5f2c_fk_auth_perm"
  add_foreign_key "auth_user_user_permissions", "auth_user", column: "user_id", name: "auth_user_user_permissions_user_id_a95ead1b_fk_auth_user_id"
  add_foreign_key "authtoken_token", "auth_user", column: "user_id", name: "authtoken_token_user_id_35299eff_fk_auth_user_id"
  add_foreign_key "bucket_relations", "warehouse_buckets"
  add_foreign_key "bucket_relations", "warehouse_stations"
  add_foreign_key "categories_faqs", "categories"
  add_foreign_key "categories_faqs", "faqs"
  add_foreign_key "communication_messages", "communication_topics"
  add_foreign_key "coupons", "currency_converts"
  add_foreign_key "delayed_files", "accounts"
  add_foreign_key "design_addons", "designs"
  add_foreign_key "design_clusters", "designs"
  add_foreign_key "design_promotion_pipe_lines", "designs"
  add_foreign_key "design_promotion_pipe_lines", "promotion_pipe_lines"
  add_foreign_key "designs_story_collections", "designs"
  add_foreign_key "designs_story_collections", "story_collections"
  add_foreign_key "dirty_entities", "warehouse_stations"
  add_foreign_key "django_admin_log", "auth_user", column: "user_id", name: "django_admin_log_user_id_c564eba6_fk_auth_user_id"
  add_foreign_key "django_admin_log", "django_content_type", column: "content_type_id", name: "django_admin_log_content_type_id_c4bce8eb_fk_django_co"
  add_foreign_key "dynamic_size_charts", "designs"
  add_foreign_key "events", "accounts"
  add_foreign_key "faqs_property_values", "faqs"
  add_foreign_key "faqs_property_values", "property_values"
  add_foreign_key "feeds", "feeds", column: "parent_feed_id"
  add_foreign_key "freshdesk_tickets", "orders"
  add_foreign_key "freshdesk_tickets", "support_texts"
  add_foreign_key "gift_card_orders", "gift_cards"
  add_foreign_key "homepages", "boards"
  add_foreign_key "inward_details", "line_items"
  add_foreign_key "inward_details", "rack_lists", column: "bad_rack_list_id"
  add_foreign_key "item_promotion_trackings", "line_items"
  add_foreign_key "item_promotion_trackings", "promotion_trackings"
  add_foreign_key "juspay_customers", "users"
  add_foreign_key "order_discounts", "orders"
  add_foreign_key "order_disputes", "orders"
  add_foreign_key "process_dates", "operation_processes"
  add_foreign_key "push_engage_subscribers", "accounts"
  add_foreign_key "push_engage_subscribers", "carts"
  add_foreign_key "return_designer_orders", "shipments"
  add_foreign_key "return_designer_orders", "shippers"
  add_foreign_key "searcher_items", "line_items"
  add_foreign_key "searcher_items", "searcher_entities"
  add_foreign_key "shipment_buckets", "orders"
  add_foreign_key "shipment_buckets", "shipments"
  add_foreign_key "shipper_fuel_prices", "countries"
  add_foreign_key "statistics", "statistic_attributes"
  add_foreign_key "stock_updates", "designers"
  add_foreign_key "subscriber_definitions", "notification_events"
  add_foreign_key "survey_answer_options", "survey_questions"
  add_foreign_key "tailor_daily_metrics", "tailors"
  add_foreign_key "tailor_visits", "tailors"
  add_foreign_key "tailoring_bag_relations", "tailoring_batches"
  add_foreign_key "tailoring_bag_relations", "tailoring_infos"
  add_foreign_key "tailoring_batches", "tailors"
  add_foreign_key "transaction_orders", "orders"
  add_foreign_key "warehouse_line_item_joins", "line_items"
  add_foreign_key "warehouse_line_item_joins", "rack_lists_warehouse_line_items"
  add_foreign_key "warehouse_line_item_joins", "warehouse_line_items"
  add_foreign_key "warehouse_line_item_joins", "warehouse_orders"
  add_foreign_key "warehouse_order_logs", "accounts"
  add_foreign_key "warehouse_order_logs", "line_items"
  add_foreign_key "warehouse_order_logs", "rack_lists"
  add_foreign_key "warehouse_order_logs", "rack_lists_warehouse_line_items"
  add_foreign_key "warehouse_order_logs", "warehouse_orders"
  add_foreign_key "warehouse_rtv_shipment_line_items", "rtv_shipments"
  add_foreign_key "warehouse_rtv_shipment_line_items", "warehouse_line_items"
  add_foreign_key "warehouse_size_items", "accounts", column: "stitching_sent_by"
end
