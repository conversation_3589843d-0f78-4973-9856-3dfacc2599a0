module Payment
  class PaymentDetails
    attr_accessor :order
    
    def initialize(order, country_code, currency_symbol, country_symbol)
      self.order = order
      @country_symbol = country_symbol
      @country_code = country_code
      @currency_symbol = currency_symbol
    end
    
    def get_shipping_cost()
      shipping_cost = CurrencyConvert.to_currency(@currency_symbol, order.shipping, @country_code).round(2)
    end
  
    def get_discount_price
      CurrencyConvert.to_currency(@currency_symbol, order.total_discounts, @country_code).round(2)
    end

    def get_tax_amount
      order.total_tax.present?  ? CurrencyConvert.to_currency(@currency_symbol, order.total_tax, @country_code).round(2) : 0
    end

    def user_names
      @user_names ||= begin 
        complete_name = order.billing_name.split(' ')
        firstname = complete_name.first
        middlename = complete_name.second
        lastname = complete_name.last
        fullname = (middlename == lastname) ? firstname : [firstname, middlename].join(" ")
        [firstname, middlename, lastname, fullname]
      end
    end
  
    def set_regex
      @non_word_excluding_space_regex ||= Regexp.new(/[^\w\s]/)
    end
  
    def get_multi_line_address(address)
      non_word_excluding_space_regex = set_regex
      @multi_addr ||= order.multi_line_address(100,address.gsub(non_word_excluding_space_regex,''))
    end
  
    def get_email
      order.billing_email
    end

    def order_reference
      order.number
    end  
    
    def get_H_PhoneNumber
      order.billing_phone.gsub(/\D/,'')[0..49]
    end
  
    def get_first_name
      all_names = user_names
      non_word_excluding_space_regex = set_regex
      all_names[3].gsub(non_word_excluding_space_regex,'')[0..59]
    end
  
    def get_last_name
      all_names = user_names
      non_word_excluding_space_regex = set_regex
      all_names[2].gsub(non_word_excluding_space_regex,'')[0..59]
    end
  
    def get_zip
      non_word_excluding_space_regex = set_regex
      order.billing_pincode.gsub(non_word_excluding_space_regex,'')[0..49]
    end
  
    def get_city
      non_word_excluding_space_regex = set_regex
      order.billing_city.gsub(non_word_excluding_space_regex,'')[0..49]
    end
  
    def get_address1
      get_multi_line_address(order.billing_street)[0]
    end
  
    def get_address2
      get_multi_line_address(order.billing_street)[1]
    end
  
    def get_state_code(country = order.billing_country)
      country_obj = Country.find_by_namei_cached(country)
      code = country_obj.try(:[], :iso3166_alpha2)
      case code
      when 'BR','CA','IT','MX', 'US'
        if (states = country_obj.try(:[], :states)).present?
          states.find{|k,v| k.downcase == order.buyer_state.downcase}.try(:last)
        end
      when 'AR'
        order.buyer_state.to_s.mb_chars.upcase.to_s
      end || order.buyer_state
    end
  
    def get_shipping_recipient_name
      order.name
    end

    def billing_country
      order.billing_country
    end

    def reference_id
      order.id
    end

    def order_country
      order.country
    end
  
    def get_shipping_line1
      get_multi_line_address(order.street)[0]
    end
  
    def get_shipping_line2
      get_multi_line_address(order.street)[1]
    end
  
    def get_shipping_city
      non_word_excluding_space_regex = set_regex
      order.city.gsub(non_word_excluding_space_regex,'')[0..49]
    end
  
    def get_shipping_phone
      order.phone.gsub(/\D/,'')[0..49] 
    end
  
    def get_shipping_postal_code
      non_word_excluding_space_regex = set_regex
      order.pincode.gsub(non_word_excluding_space_regex,'')[0..49] 
    end

    def get_order_item_details(include_description = false)
      line_items = order.designer_orders.collect(&:line_items).flatten
      item_hash = {}
      item_hash['items_total'] = 0
      item_hash['items'] = []
      line_items.each_with_index do |item, index|
        item_price = CurrencyConvert.to_currency(@currency_symbol, item.price_with_addons, @country_code).round(2)
        item_hash['items_total'] += (item_price * item.quantity)
        item_sub_hash = {}
        item_sub_hash["quantity"] = "#{item.quantity}"
        item_sub_hash["name"] = item.design.title
        item_sub_hash["unit_amount"] = {
          "value": "#{item_price}",
          "currency_code": @country_symbol
         }
        item_sub_hash["sku"] = item.design.design_code
        item_sub_hash["category"] = "PHYSICAL_GOODS"
        if include_description && item.line_item_addons.present? 
          addon_total = 0 
          addon_description = ""
          item.line_item_addons.each do |lt_addon|
            addon_total += lt_addon.snapshot_price
            # addon_description << ", " if addon_description.present?
            # addon_description << lt_addon.notes
          end
          addon_total = CurrencyConvert.to_currency(@currency_symbol, addon_total, @country_code).round(2)
          item_sub_hash["description"] = "Including Custom Charges Of #{addon_total} #{@currency_symbol}  #{addon_description}"
        end 
        item_hash['items'] << item_sub_hash
      end
      order_addon = order.order_addon
      if order_addon.try(:gift_wrap_price)
        item_price =  CurrencyConvert.to_currency(@currency_symbol, order_addon.gift_wrap_price, @country_code).round(2)
        item_hash['items_total'] += item_price
        item_sub_hash = {}
        item_sub_hash["quantity"]= "1"
        item_sub_hash["name"]= "Gift Wrap charges"
           item_sub_hash["unit_amount"] = {
          "value": "#{item_price}",
          "currency_code": @country_symbol
         }
        item_sub_hash["sku"] = ""
        item_hash['items'] << item_sub_hash
      end
      return [item_hash['items_total'].round(2), item_hash['items']] 
    end

  end
  
end