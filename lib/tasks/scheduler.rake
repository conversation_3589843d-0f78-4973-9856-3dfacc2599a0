require 'sitemap_generator'
require 'mechanize'
require 'money'
require 'money/bank/google_currency'
require 'aramex/aramex_api'
# include ApplicationHelper


desc "This task to upload Designs data to Unbxd"
task :unbxd_feed_upload_rake => :environment do
  logger = Logger.new(STDOUT)
  logger.level = Logger::DEBUG
  Rails.logger = Dalli.logger = ActiveRecord::Base.logger = logger

  unbxd_obj = Unbxd::FeedUpload.new()  
  unbxd_obj.run_the_feed()

end


desc "This is used for grading 2023"
task :run_grading_rake => :environment do
  logger = Logger.new(STDOUT)
  logger.level = Logger::DEBUG
  Rails.logger = Dalli.logger = ActiveRecord::Base.logger = logger
  grad_obj = GradingService.new()
  grad_obj.delete_all_grading_files()
  grad_obj.send_mail("Grading has started")
  grad_obj.run_grading()
  grad_obj.send_mail("Grading has Finished. Attching csv's for verification")
  grad_obj.delete_all_grading_files()
end




desc "This task is called by the Heroku scheduler add-on"
task :update_sitemap => :environment do
  puts "Updating sitemap..."
  Rake::Task["sitemap:refresh"].invoke
  puts "done."
end

desc "This task is called by the Heroku scheduler add-on"
task :update_sitemap_sidekiq => :environment do
  UpdateSitemapJob.perform_async()
end

# To be runned on daily basis at 00:00 UTC
desc "This task clear the cache"
task :cache_clear => :environment do
  Rails.cache.clear if Designer.changed_additional_discount.present? || PromotionPipeLine.changed_promotion.present?
end

desc "This task will clear the cache everytime"
task :cache_clear_og => :environment do
  Rails.cache.clear

  puts "starting redis clear \n"

  cmd = "redis-cli -h #{ ENV["REDIS_HOST"] } -p #{ ENV["REDIS_PORT"] } -a #{ ENV["REDIS_PASSWORD"] } --scan --pattern cache* | xargs -d '\n' redis-cli -h #{ ENV["REDIS_HOST"] } -p #{ ENV["REDIS_PORT"] } -a #{ ENV["REDIS_PASSWORD"] } UNLINK"

  puts cmd
  puts "\n"
  didItWork = system(cmd)
  puts didItWork
end




desc "This task will clear the cache everytime"
task :cache_clear_og_sidekiq => :environment do
  CacheClearOgJob.perform_async()
end

# To be runned for each hour
desc "This task clear the cache and request store"
task :clear_cache_and_request_store => :environment do
  PromotionPipeLine.clear_cache_request_store if PromotionPipeLine.changing_promotion.present?
end


desc "This task is for setting category_wise sku count and inventory"
task cache_categorywise_stats: :environment do
  unless Rails.env.production?
    logger = Logger.new(STDOUT)
    logger.level = Logger::DEBUG
    Rails.logger = Dalli.logger = ActiveRecord::Base.logger = logger
  end
  Admin.set_categorywise_sku_count(true)
  Category.pluck(:id).each do |category_id|
    Admin.get_total_inventory(category_id)
  end
  PARENT_CATEGORIES.each do |category_name|
    Admin.get_total_inventory(Category.names_to_ids(category_name).values)
  end
end

desc "This task is for calculating daily category_wise stats for international and domestic orders"
task :calculate_categorywise_prices,[:range] => :environment do |t,args|
  unless Rails.env.production?
    #for logger
    logger = Logger.new(STDOUT)
    logger.level = Logger::DEBUG
    Rails.logger = Dalli.logger = ActiveRecord::Base.logger = logger
  end

  range = args.nil? ? args[:range].to_i : 3

  ids = Category.pluck(:id)
  (0..range).each do |i|
    to_be_imported = []
    end_date = Date.today - i.days
    start_date = end_date - 1.day
    category_revenue_count_map = Admin.set_categorywise_price(start_date,end_date)
    ids.each do |category_id|
      t1 = TimeSeriesDataset.where(metric_id: category_id,metric_type: 'Category',date: start_date).first_or_initialize
      data = {}
      data1,error = Admin.get_data_for_category(category_revenue_count_map,category_id,end_date,true)
      data['parent'] = data1 unless error
      data1,error = Admin.get_data_for_category(category_revenue_count_map,category_id,end_date,false)
      data['child']  = data1 unless error
      t1.data = data
      if t1.new_record?
        to_be_imported << t1
      else
        t1.save
      end
    end
    TimeSeriesDataset.import to_be_imported,validate: false,batch_size: 500 ,on_duplicate_key_update: {conflict_target: [:id], index_name: :index_time_series_datasets_on_date_metric_and_parent,columns: [:data]}
  end
  # persistence_date = Date.today - 90.days
  # TimeSeriesDataset.where('date <= ?',persistence_date).destroy_all
end

desc "This task gets data from ga and stores it in GA Data table"
task :get_ga_data,[:range,:ids] => :environment do |t,args|
  unless Rails.env.production?#for logger
    logger = Logger.new(STDOUT)
    logger.level = Logger::DEBUG
    Rails.logger = Dalli.logger = ActiveRecord::Base.logger = logger
  end

  range = args.present? ? args[:range].to_i : 3

  routes={}
  errors_hash = {}

  if args[:ids].present?
    Category.where(id: args[:ids].split(",")).pluck(:name).each do |name|
      routes[name] = Mirraw::Application.routes.url_for(action: 'catalog2',controller: 'store', kind: name.downcase,only_path: true)
    end
  else
    Category.pluck(:name).each do |name|
      routes[name] = Mirraw::Application.routes.url_for(action: 'catalog2',controller: 'store', kind: name.downcase,only_path: true)
    end
  end
  (0..range).each do |i|
    end_date = Date.today - i.days
    start_date = end_date - 1.day
    for name,filter in routes
      ga = GoogleAnalyticsData.where(metric_id: Category.getid(name),metric_type: "Category",date: start_date).first_or_initialize
      begin
        if ga.fetched_at.nil? || ga.fetched_at < Date.today
          desktop = GoogleApi::Analytics.show_visits(48638383,start_date.strftime('%Y-%m-%d'),end_date.strftime('%Y-%m-%d'),filter)
          mobile = GoogleApi::Analytics.show_visits(108849673,start_date.strftime('%Y-%m-%d'),end_date.strftime('%Y-%m-%d'),filter)
          data = {
            "mobile" => {
              "sessions" => mobile["ga:uniquePageviews"].to_i,
              "users" => mobile["ga:users"].to_i,
              "pageviews" => mobile["ga:pageviews"].to_i
              },
            "desktop" => {
              "sessions" => desktop["ga:uniquePageviews"].to_i,
              "users" => desktop["ga:users"].to_i,
              "pageviews" => desktop["ga:pageviews"].to_i
            }
          }
          ga.update_attributes(data_hash: data,fetched_at: DateTime.now)
        end
      rescue Google::Apis::ServerError => e
        errors_hash[name] = "#{e.body}#{e.backtrace}#{e.status_code}"
      end
    end
  end
  if errors_hash.present?
    ExceptionNotify.sidekiq_delay.notify_exceptions("Server Errors","Google Analytics Data Fetch Error",{params: errors_hash})
  end
end

desc "Intialize S3 for Product Scoring"
task intialize_product_scoring_bucket: :environment do
  s3 = AWS::S3.new(access_key_id: ENV['PRODUCT_S3_SCORING_ACCESS_KEY'] ,secret_access_key: ENV['PRODUCT_S3_SCORING_SECRET_ACCESS_KEY'])
  @bucket = s3.buckets['mirraw-jash-exp']
  @PATH = '/tmp/'
  @prepend = Rails.env.production? ? '' : '/test'
  @append = '.processed'
  @property_hash ={
    "Saree" => "look,occasion,blouse_color,fabric_of_blouse,saree_color,type,work,fabric_of_saree",
    "Lehenga" => "lehenga_fabric,lehenga_color,lehenga_work,lining_fabric,blouse_color,blouse_work,blouse_fabric,dupatta_fabric,dupatta_color,dupatta_work,occasion,work,type,look,pallu_style,lehenga_style,choli_sleeve,choli_neck_style,celebrity,stitching",
    "Kurti" => "bottom_fabric,kameez_fabric,material,kameez_color,bottom_color,occasion,fabric,work,type,look,bottom_style,kameez_sleeve,kameez_neck_style,stitching,color",
    "SalwarKameez" => "kameez_fabric,occasion,work,type,look,bottom_style,Kameez_sleeve,Kameez_neck_style,stitching,dupatta_fabric,dupatta_fabric,lining_fabric,dupatta_color,kameez_color,bottom_fabric,round_neck_kameez,bottom_color",
    "Jewellery" => "jewellery_work,color,gemstones,plating,base_material,occasion,look,certification,pearl_type,pearl_shape,piercing",
  }
end

desc "This task is for uploading performance data for automated scoring process"
task :upload_performance_data,[:category_name,:number_of_days,:locale,:upload] => [:intialize_product_scoring_bucket] do |t,args|
  unless args[:category_name].nil? || args[:category_name] == "all"
    categories = args[:category_name].split(' ').map(&:downcase)
  else
    categories = SystemConstant.get('CATEGORIES_FOR_AUTOMATED_SCORING').split(',')
  end
  unless args[:number_of_days].nil?
    time_period = args[:number_of_days].to_i.days.ago
  else
    time_period = 30.days.ago
  end
  unless args[:locale].nil? || args[:locale] == "both"
    locale = %w(international domestic).include?(args[:locale]) ? [args[:locale].to_s.downcase] : []
  else
    locale = %w(international domestic)
  end
  locale.each do |location|
    categories.each do |category_name|
      currency_condition = location == 'domestic' ? 'orders.geo <> ?' : 'orders.geo = ?'
      data_buffer = []
      p_category = Category.find_by_namei(category_name)
      Design.joins(:categories,:line_items).
      where('line_items.created_at > ? and (categories.id in (?))', time_period,p_category.cached_self_and_descendants_id).
      select('distinct designs.id as id').
      find_in_batches(batch_size: 5000) do |grp|
        ids = grp.map{|l| l['id'].to_i}
        data_buffer.push(*LineItem.joins(:design,designer_order: :order).where('line_items.design_id in (?) and line_items.created_at > ? and line_items.status is null',ids,time_period).
        where('designer_orders.state  in (?)',['pickedup', 'pending', 'dispatched', 'completed', 'critical']).
        where(currency_condition,'international').
        select('sum(line_items.quantity) as ninetydaysellcount,sum(line_items.snapshot_price*line_items.quantity) as ninetydayrevenue,
          designs.id as design_id').
        group('designs.id').order('ninetydaysellcount desc').
        map{|o| [o['design_id'].to_i,o['ninetydaysellcount'].to_i,o['ninetydayrevenue'].to_i]})
      end
      if data_buffer.present?
        unless args[:upload]
          folder_name = category_name == 'kurtas-and-kurtis' ? 'kurtis' : category_name
          upload_path = "#{@prepend}/product-scoring/performance-data/#{folder_name}-#{location}/"
          FILE_NAME = "#{folder_name}_#{location}_#{Date.today}.csv"
          filepath = @PATH + FILE_NAME
          CSV.open(filepath, "a", {col_sep: ","}) do |csv|
            data_buffer.each do |data|
              csv << data
            end
          end
          CSV.open(filepath, "wb", {col_sep: ","}) do |csv|
            csv << %w(designs_id ninetydaysellcount ninetydayrevenue)
          end
          obj = @bucket.objects[upload_path+FILE_NAME]
          obj.write(file: filepath)
        else
          category = category_name
          if category == 'salwar-kameez'
            category = 'SalwarKameez'
          elsif category == 'kurtas-and-kurtis'
            category ='Kurti'
          else
            category = category.singularize.titleize
          end
          file_name = "tmp/#{category}_#{Date.today}.csv"
          data_buffer = data_buffer.sort{|a,b| b[1] <=> a[1]}
          CSV.open(file_name, "w+") do |csv|
            data_buffer.each do |row|
              csv << [row[0]]
            end
          end
          grading = Grading.new(url: 'default', account_id: 830017, name: "bestseller_#{category}_#{location}_#{Date.today}", roll_out: 100, category: category, grading_csv: File.open(file_name), run_at: Date.today, constant_value: 'default', app_source: 'desktop', geo: location)
          grading.grading_csv_content_type = 'text/csv'
          grading.waiting!
        end
      end
    end
  end
end
desc "create grading from bestseller grading"
task :bestseller_automated_grading_international,[:day_to_run] => :environment do |t,args|
  unless Rails.env.production?#for logger
    logger = Logger.new(STDOUT)
    logger.level = Logger::DEBUG
    Rails.logger = Dalli.logger = ActiveRecord::Base.logger = logger
  end


  if Date.today.wday == args[:day_to_run].to_i
    time_period = 90.days.ago
    bestseller_designs = LineItem.joins(:design).where('line_items.created_at > ? and line_items.status is null',time_period).
      select('design_id as d_id, designs.designable_type as designable_type, sum(line_items.snapshot_price*line_items.quantity*line_items.scaling_factor) as ninetydayrevenue').
      group('design_id, designs.designable_type').order('ninetydayrevenue desc')
    designer_ids = SystemConstant.get('MOBILE_EXPERIMENT_DESIGNER_IDS', :to_a)
    bestseller_designs_designer = LineItem.joins(:design).where('line_items.created_at > ? and line_items.status is null and designs.designer_id in (?)',time_period, designer_ids).
      select('design_id as d_id, designs.designable_type as designable_type, sum(line_items.snapshot_price*line_items.quantity*line_items.scaling_factor) as ninetydayrevenue').
      group('design_id, designs.designable_type').order('ninetydayrevenue desc')

    designs_to_grade = {default: [], mobile: {'Saree' => [], 'SalwarKameez' => [], 'Kurti' => [], 'Lehenga' => [], 'Jewellery' => []}}
    designs_to_grade[:default] = bestseller_designs.collect{|d| d.d_id}

    bestseller_designs_designer.each do |designs|
      designs_to_grade[:mobile][designs.designable_type] << designs.d_id unless designs_to_grade[:mobile][designs.designable_type].nil?
    end

    daaginey = LineItem.joins(:design).where('designs.designer_id =? and line_items.created_at > ?', 10322, time_period).select('line_items.design_id as design_id, sum(line_items.snapshot_price*line_items.quantity*line_items.scaling_factor) as ninetydayrevenue').group('line_items.design_id').order('ninetydayrevenue desc').collect(&:design_id)
    designs_to_grade[:default] -= daaginey
    designs_to_grade[:default] = (daaginey + designs_to_grade[:default])
    max_grade = Design.maximum(:international_grade) + designs_to_grade[:default].size
    designs_to_grade[:default].each do |design_id|
      if (design = Design.where(id: design_id).first).present?
        design.update_column(:international_grade, max_grade)
        max_grade -= 1
      end
      puts max_grade
    end
    designs_to_grade[:default].in_groups_of(1000) do |ids|
      Sunspot.index Design.where(id: ids)
    end ; nil

    designs_to_grade[:mobile].each do |designable, design_id|
      if design_id.present?
        CSV.open("tmp/#{designable}_mobile.csv", 'w+') do |csv|
          design_id.each{|d| csv << [d]}
        end
        grading = Grading.new({name: "designer_#{designable}_bestseller_#{Date.today.day}_#{Date.today.month}_#{Date.today.year}", roll_out: 10, category: designable, url: 'recommended',
        constant_value: Kernel.const_get(GRADING_HASH.key 'recommended_mobile'), app_source: 'mobile', geo:'International',account_id: 3, run_at: Date.today, grading_csv: File.open("tmp/#{designable}_mobile.csv"), grading_csv_content_type: 'text/csv'})
        grading.waiting!
      end
    end
  end
  # case Date.today().wday
  # when 1
  #   category = 'sarees'
  # when 2
  #   category = 'salwar-kameez'
  # when 3
  #   category = 'kurtas-and-kurtis'
  # when 4
  #   category = 'jewellery'
  # when 5
  #   category = 'lehengas'
  # else
  #   category = nil
  # end
  # Rake::Task["upload_performance_data"].invoke(category,30,'both',true)
end

desc "This task is for scoring products according to locale and grade"
task :score_designs_according_to_score_file,[:category_name,:locale] => [:intialize_product_scoring_bucket] do |t,args|
  #for logger
  logger = Logger.new(STDOUT)
  logger.level = Logger::DEBUG
  Rails.logger = Dalli.logger = ActiveRecord::Base.logger = logger

  unless args[:category_name].nil? || args[:category_name] == "all"
    categories = args[:category_name].split(' ').map(&:downcase)
  else
    categories = SystemConstant.get('CATEGORIES_FOR_AUTOMATED_SCORING').split(',')
  end
  unless args[:locale].nil? || args[:locale] == "both"
    locale = %w(international domestic).include?(args[:locale]) ? [args[:locale].to_s.downcase] : []
  else
    locale = %w(international domestic)
  end
  locale.each do |location|
    categories.each do |category_name|
      folder_name = category_name == 'kurtas-and-kurtis' ? 'kurtis' : category_name
      search_path = "#{@prepend}/product-scoring/product-score-data/#{folder_name}_#{location}/"
      sorted_files = @bucket.objects.with_prefix(search_path).map{|o| o.key.split('/')[-1].split('.')[-1] == 'processed' ? nil : [o.key,o.last_modified] }.compact.sort_by{|k| k[1]}
      latest_score_file_path = sorted_files.last.first unless sorted_files.empty?
      obj = @bucket.objects[latest_score_file_path] if latest_score_file_path.present?
      FILE_NAME = latest_score_file_path.split('/')[-1] if latest_score_file_path.present?
      file_path = "#{@PATH}#{FILE_NAME}"  if latest_score_file_path.present?
      if file_path.present?
        File.open(file_path, 'w+') do |file|
          file.write(obj.read)
        end
        Design.grade_designs(file_path,location == 'international')
        obj1 = obj.move_to("#{search_path}#{FILE_NAME}#{@append}")
      end
    end
  end
end

desc "This task is for uploading product data"
task :upload_product_data,[:category_name] => [:intialize_product_scoring_bucket] do |t,args|
  unless Rails.env.production?
    #for logger
    logger = Logger.new(STDOUT)
    logger.level = Logger::DEBUG
    Rails.logger = Dalli.logger = ActiveRecord::Base.logger = logger
  end

  unless args[:category_name].nil? || args[:category_name] == "all"
    categories = args[:category_name].split(' ').map(&:downcase)
  else
    categories = SystemConstant.get('CATEGORIES_FOR_AUTOMATED_SCORING').split(',')
  end
  categories.each do |category_name|
    folder_name = category_name == 'kurtas-and-kurtis' ? 'kurtis' : category_name
    designable = Design.get_designable(category_name)
    property_list = @property_hash[designable].split(',')
    if property_list.present?
      property_map = Property.where(name: property_list).select('id,name').collect{|property| [property.id,property.name]}.to_h
      review_count_map = Review.where('designer_id is not null').group(:designer_id).count
      designer_rating_map = Designer.select('id,average_rating').where{average_rating.gt(1)}.map{|designer| [designer.id,designer.average_rating]}.to_h
      upload_path = "#{@prepend}/product-scoring/product-data/#{folder_name.downcase}/"
      file_name = "#{folder_name.downcase}.csv"
      filepath = @PATH + file_name
      CSV.open(filepath, "wb", {col_sep: ","}) do |csv|
        csv << ['product_id','category_name','price','grade', 'design_rating', 'design_total_reviews', 'designer_rating', 'designer_total_reviews', 'design_quality', 'properties']
      end
      Design.preload( :categories, :property_values).
      select('id, price,grade, total_review, average_rating, quality_level, designer_id').
      where(designable_type: designable).
      find_in_batches(batch_size: 500) do |group|
        data_buffer=[]
        group.each do |d|
          property_values = d.property_values
          array = []
          property_values.each do |pv|
            name = property_map[pv.property_id]
            if name.present?
              array << {"#{name}": "#{pv.name}"}
            end
          end
          designer_reviews = review_count_map[d.designer_id].to_i
          avg_rating = designer_rating_map[d.designer_id].to_f
          category = d.categories.first.try(:name)
          data_buffer << [d.id, category, d.price, d.grade, d.average_rating, d.total_review, avg_rating, designer_reviews , d.quality_level, array]
        end
        puts 'Saving'
        CSV.open(filepath, "a", {col_sep: ","}) do |csv|
          data_buffer.each do |data|
            csv << data
          end
        end
      end
      puts 'Done'
      obj = @bucket.objects[upload_path+file_name]
      obj.write(file: filepath,multipart_threshold: 150*1024*1024)
    else
      puts 'No properties found'
    end
  end
end

desc "To revert to last grading from file accordint to locale"
task :revert_grading,[:category_name,:locale] => [:intialize_product_scoring_bucket] do |t,args|
  unless args[:category_name].nil? || args[:category_name] == "all"
    categories = args[:category_name].split(' ').map(&:downcase)
  else
    categories = SystemConstant.get('CATEGORIES_FOR_AUTOMATED_SCORING').split(',')
  end
  unless args[:locale].nil? || args[:locale] == "both"
    locale = %w(international domestic).include?(args[:locale]) ? [args[:locale].to_s.downcase] : []
  else
    locale = %w(international domestic)
  end
  locale.each do |location|
    categories.each do |category_name|
      folder_name = category_name == 'kurtas-and-kurtis' ? 'kurtis' : category_name
      search_path = "#{@prepend}/product-scoring/product-score-data/#{folder_name.downcase}-#{location}/"
      sorted_files = @bucket.objects.with_prefix(search_path).map{|o| o.key.split('/')[-1].split('.')[-1] == 'processed' ? [o.key,o.last_modified] : nil }.compact.sort_by{|k| k[1]}
      latest_score_file_path = sorted_files.first.first unless sorted_files.empty?
      obj=@bucket.objects[latest_score_file_path] if latest_score_file_path.present?
      FILE_NAME = latest_score_file_path.split('/')[-1] if latest_score_file_path.present?
      file_path = "#{@PATH}#{FILE_NAME}"  if latest_score_file_path.present?
      if file_path.present?
        File.open(file_path, 'w+') do |file|
          file.write(obj.read)
        end
        Design.grade_designs(file_path,category_name,location == 'international')
        File.rename(file_path,"#{file_path}#{@append}")
      end
    end
  end
end

desc "This task is called by the Heroku schedule add-on to send emails out"
task :sendout_upload_notification => :environment do
  puts "Sending out notification emails..."

  design_upload_events = TimelineEvent.where(:event_type => "upload").where('notified IS NULL')

  if design_upload_events.present?
    # Group by Designer
    @events = design_upload_events.group_by {|e| e.actor }

    @events.each do |designer, timeline_events|
      if designer.present?
        # Get designs from events
        designs = timeline_events.collect! { |t| t.subject if t.subject.present? }

        # Send mail to each follower
        designer.followers.each do |follower|
          FollowMailer.delay(priority: 4).upload_notification(follower, designer, designs) if follower.present?
        end
      end
    end
    # Mark as notified
    design_upload_events.update_all :notified => 'Y'
  end
end


desc "This task runs all the leftover delayed designable created by bulk upload"
task :bulk_upload_delay_designables => :environment do
    # logger = Logger.new(STDOUT)
    # logger.level = Logger::DEBUG
    # Rails.logger = ActiveRecord::Base.logger = logger
    DelayDesignable.includes(design: [:categories,:variants,:images]).old_task.run_all
end

desc "This task runs all the leftover and errorfull delayed images created by bulk upload"
task :bulk_upload_delay_images => :environment do
    # logger = Logger.new(STDOUT)
    # logger.level = Logger::DEBUG
    # Rails.logger = ActiveRecord::Base.logger = logger
    DelayImage.includes(:design).image_without_message.run_all
    DelayImage.includes(:design).image_with_message.run_all
end


desc "This task is called by the Heroku schedule add-on to send Follow emails out to designer"
task :sendout_follow_notification => :environment do
  follow_designer_events = TimelineEvent.where(:event_type => "follow", :subject_type => "Designer").where('notified IS NULL')

  if follow_designer_events.present?
    @events = follow_designer_events.group_by {|e| e.subject }

    @events.each do |designer, followers|
      if designer.present?
        users = followers.collect! {|f| f.actor if f.actor.present? }
        FollowMailer.delay(priority: 4).follow_notification(designer, users)
      end
    end
    follow_designer_events.update_all :notified => 'Y'
  end
end

desc "This task is called by the Heroku schedule add-on to send Follow design emails out to designer"
task :sendout_like_notification => :environment do
  follow_design_events = TimelineEvent.where(:event_type => "follow", :subject_type => "Design").where('notified IS NULL')

  if follow_design_events.present?

    # Get all designers
    @events = follow_design_events.group_by {|e| e.secondary_subject }

    @events.each do |designer, followers|
      if designer.present?
        users = followers.group_by {|f| f.actor if f.actor.present? }
        FollowMailer.delay(priority: 4).design_follow_notification(designer, users)
      end
    end
    follow_design_events.update_all :notified => 'Y'
  end
end

desc "This task will send feedback mails to international users after order has been delivered"
task :notifications_after_order_completed_international => :environment do
  date_range = 30.days.ago.beginning_of_day..10.days.ago.beginning_of_day
  orders_automated = Order.joins(:shipments).where('shipments.designer_order_id is null and orders.feedback_flag=false and COALESCE(notification_count,0)<=? and lower(orders.country) <> ? and ((shipments.automated = ? and shipments.shipment_state = ? and orders.pickup >= ?) or (shipments.automated = ? and orders.pickup between ? and ?))',REMINDER_COUNT.to_i,'india', true, 'delivered', 30.days.ago.beginning_of_day, false, 30.days.ago.beginning_of_day, 10.days.ago.beginning_of_day).uniq
  orders_automated.each do |order|
    if (Time.now().to_date.mjd - order.pickup.to_date.mjd) % (SEND_OUT_ORDER_FEEDBACK_MAIL.to_i) == 0 || order.notification_count.to_i == 0
      notification_count = order.notification_count.to_i + 1
      order.update_column(:notification_count, notification_count)
      OrderMailer.delay.send_feedback_form(order)
      order.delay({run_at: Time.now+1.hour}).send_feedback_notification
    end
  end
end

desc "Hourly sends scheduled dynamic message and email"
task notify_dynamic_template: :environment do
  FilterNotifierScheduler.ready_to_run.notify!
end

desc "This task will send feedback mails to domestic users after order has been delivered"
task :notifications_after_order_completed_domestic => :environment do
  date_range = 30.days.ago.beginning_of_day.. 3.days.ago.beginning_of_day
  order_ids = DesignerOrder.unscoped.where(state: ['completed','dispatched']).where(:pickup => date_range).group(:order_id).pluck(:order_id)
  orders = Order.where(id: order_ids).where('feedback_flag=false and COALESCE(notification_count,0)<=? and lower(country) = ?',REMINDER_COUNT.to_i,'india')
  orders.each do |order|
    if (Time.now().to_date.mjd - order.designer_orders.pluck(:pickup).compact.max.to_date.mjd) % (SEND_OUT_ORDER_FEEDBACK_MAIL.to_i) == 0 || order.notification_count.to_i == 0
      notification_count = order.notification_count.to_i + 1
      order.update_column(:notification_count, notification_count)
      OrderMailer.delay.send_feedback_form(order)
      order.delay({run_at: Time.now+1.hour}).send_feedback_notification
    end
  end
end

desc "This task is called by the Heroku schedule add-on to send Follow emails out to user"
task :sendout_follow_notification_user => :environment do
  follow_user_events = TimelineEvent.where(:event_type => "follow", :subject_type => "User").where('notified IS NULL')

  if follow_user_events.present?
    @events = follow_user_events.group_by {|e| e.subject }

    @events.each do |user, followers|
      if user.present?
        user_followers = followers.collect! {|f| f.actor if f.actor.present? }
        FollowMailer.delay(priority: 4).follow_notification_user(user, user_followers)
      end
    end
    follow_user_events.update_all :notified => 'Y'
  end
end

desc "Sending out if anything that was previously unavailable is now available"
task :sendout_back_in_stock_notification => :environment do
  w_request = '(notified_at IS NOT NULL AND notified_at < ? AND notification_count < 2) OR (created_at > ? AND notified IS NULL)', 1.day.ago, 1.month.ago
  w_request_email = "email IS NOT NULL AND email LIKE '%@%'"
  pending_requests = Reqlist.where(w_request).where(w_request_email)

  pending_requests.each do |request|
    design = Design.find_by_id(request.design, :include => :variants)
    if design.present? && design.in_stock?
      if (design.variants.blank? && design.quantity.to_i > 0) || design.variants_quantity > 0
        FollowMailer.delay(priority: 4).sendout_back_in_stock_notification(request.email, design.id)
        request.notified = 'Y'
        request.notification_count += 1
        request.notified_at = DateTime.now
        request.save
      end
    end
  end
end

desc "This task is called to fix TimelineEvents whose secondary subject is missing"
task :fix_secondary_subject_type => :environment do
  follow_design_events = TimelineEvent.where(:event_type => "follow", :subject_type => "Design", :secondary_subject_id => nil)

  follow_design_events.each do |e|
    e.secondary_subject = e.subject.designer
    e.save!
  end
end


desc "Sendout Cart abandonment notifications"
task :sendout_cart_abandonment_notifications => :environment do
  batch = 1000
  offset = 0
  not_notified_ids = []
  notified_ids = []
  loop do
    carts =  Cart.current_month.select('distinct on (email) email, id, user_id, user_type').where{email.not_eq(nil)&used.eq(false)&((notified_at.not_eq(nil)&notified_at.lt(1.day.ago.to_s)&notification_count.lt(2))|(created_at.lt(2.hours.ago.to_s)&notified.eq(nil)))}.order('email, id desc').limit(batch).offset(offset)
    carts.each do |cart|
      unless (cart.email.to_s =~ VALID_EMAIL_REGEX).nil?
        OrderMailer.delay(priority: 3).cart_abandonment_notification(cart.id)
        notified_ids << cart.id
      else
        not_notified_ids << cart.id
      end
    end
    break if carts.size < batch
    offset += carts.size
  end
  Cart.where(id: notified_ids).update_all(['notification_count = coalesce(notification_count, 0) + 1, notified = ?, notified_at = ?', 'Y', DateTime.now])
  Cart.where{id.in(not_notified_ids) & notification_count.not_eq(2)}.update_all(notification_count: 2, notified: 'N', notified_at: DateTime.now)
end


desc "Sendout Wallet Rewards for delayed deliveries"
task add_reward_for_delayed_deliveries: :environment do
  Order.where(state: ['sane','dispatched','ready_for_dispatch','complete','partial_dispatch']).where.not(country: 'India').where{paid_amount > 0}.joins(:delivery_nps_info).where('delivery_nps_infos.promised_delivery_date between ? and ?',2.days.ago.beginning_of_day,2.days.ago.end_of_day).preload(:wallet_transactions,user: [wallet: :currency_convert],export_shipments: :shipper).find_each(batch_size: 100) do |order|
    order.credit_reward_for_late_delivery if order.export_shipments.find{|s| s.shipper.try(:name) == 'Atlantic'}.blank? && (order.export_shipments.select{|s| s.track == true}.collect(&:shipment_state) & ['delivered', 'rto', 'out_for_delivery']).blank?
  end
end


desc 'Sendout Fake argency notification'
task sendout_fake_urgency_notification: :environment do
  batch_size = 500
  offset = 0
  loop do
    carts = Cart.select('Distinct on (email) email, user_id, id, user_type').where('email IS not null AND used=false AND date(created_at)=?', SystemConstant.get('FAKE_URGENCY_DAYS').to_i.days.ago.to_date).order('email, id desc').limit(batch_size).offset(offset)
    carts.each do |current_cart|
      unless (current_cart.email.to_s =~ VALID_EMAIL_REGEX).nil?
        OrderMailer.delay(priority: 3).cart_sold_out(current_cart.id)
       end
    end
    break if carts.size < batch_size
    offset += batch_size
  end
end

desc 'Delete Old carts'
task :delete_old_carts => :environment do
  Cart.where('created_at < ?', 30.days.ago).destroy_all
end

desc 'Update dynamic delivery dates in system'
task update_dynamic_delivery_dates: :environment do

  if Time.current.monday?
    ################## updating vendor ship time ################
    sql = "SELECT count(designer_orders.id) FROM designer_orders WHERE designer_orders.confirmed_at > now() - interval '60'day AND designer_orders.confirmed_at < now() - interval '45'day and designer_orders.state NOT IN ('canceled','vendor_canceled') and designer_orders.ship_to = 'mirraw' GROUP BY designer_orders.designer_id"
    dos_count = ActiveRecord::Base.connection.execute(sql).to_a
    dos_count = dos_count.collect{|i| i['count']}.map(&:to_i).sort.reverse
    cut_of_sum = (90 * dos_count.sum)/100.0
    sum = 0
    dos_count.each_with_index do |i,index|
      sum += i
      if sum >= cut_of_sum
        sum=index
        break
      end
    end
    dos_cut_off = dos_count[sum]

    sql = "SELECT designer_orders.designer_id, percentile_cont(0.70) within group (order by x.dispatch_days asc) as percentile_dispatch FROM designer_orders INNER JOIN (SELECT designer_orders.id as dos_id, max(coalesce(EXTRACT(EPOCH FROM ((s.in_scan_on - designer_orders.confirmed_at)/(3600*24))),8) - coalesce(coalesce(designs.eta,designers.eta),0)) as dispatch_days FROM designer_orders INNER JOIN line_items on line_items.designer_order_id = designer_orders.id INNER JOIN (SELECT min(scans.scanned_at) as in_scan_on, scans.line_item_id from scans where scan_type = 'Package Inscan' and scans.scanned_at > now() - interval '60'day and scans.scanned_at < now() - interval '45'day group by line_item_id )s on line_items.id = s.line_item_id INNER JOIN designs on designs.id = line_items.design_id INNER JOIN designers on designers.id = designs.designer_id WHERE designer_orders.confirmed_at > now() - interval '60'day AND designer_orders.confirmed_at < now() - interval '45'day and designer_orders.state NOT IN ('canceled','vendor_canceled') and designer_orders.ship_to = 'mirraw' GROUP BY designer_orders.id)x on designer_orders.id = x.dos_id GROUP BY designer_orders.designer_id having count(designer_orders.id) > #{dos_cut_off}"
    designers = ActiveRecord::Base.connection.execute(sql).to_a
    designers = designers.map{|d| [d["designer_id"],d["percentile_dispatch"].to_f.round(2)]}.to_h
    Designer.where(id: designers.keys).find_each do |d|
      # p "#{d.id} =========> #{designers[d.id.to_s]}"
      d.update_column(:ship_time,designers[d.id.to_s])
    end
    Designer.where('ship_time is null or ship_time = ?',0).update_all(ship_time: Designer::SLA_CONFIG[:vendor_default_dispatch_days])

    ################### updating country wise delivery time ################
    order_count = Order.unscoped.joins(:shipments).where(shipments: {designer_order_id: nil}).group(:country).where('orders.confirmed_at > ?', 60.day.ago).where('orders.confirmed_at < ?', 45.days.ago).count
    order_count = order_count.values.sort.reverse
    cut_of_sum = (90 * order_count.sum)/100.0
    sum=0
    order_count.each_with_index do |i,index|
      sum += i
      if sum >= cut_of_sum
        sum=index
        break
      end
    end
    cut_of_orders = order_count[sum]

    countries = Order.unscoped.joins(:shipments).where(shipments: {designer_order_id: nil}).group(:country).where.not(country: 'India').where('orders.confirmed_at > ?', 60.day.ago).where('orders.confirmed_at < ?', 45.days.ago).having('count(distinct orders.id) > ?',cut_of_orders).pluck("country, percentile_cont(0.70) within group (order by coalesce(EXTRACT(EPOCH FROM shipments.delivered_on - orders.out_of_mirraw_warehouse)/(3600*24),10) asc) as dispatch_days").to_h
    Country.where(name: countries.keys).where.not(name: 'India').find_each do |c|
      c.update_column(:shipping_time, countries[c.name].round(2))
    end

    ################### updating shipper and country wise delivery time ################

    sql = "SELECT shippers.id as shipper_id , countries.id as country_id, percentile_cont(0.70) within group (order by coalesce(EXTRACT(EPOCH FROM (shipments.delivered_on - orders.out_of_mirraw_warehouse)/(3600*24)),5) asc) FROM orders INNER JOIN shipments ON shipments.order_id = orders.id INNER JOIN shippers ON shippers.id = shipments.shipper_id INNER JOIN countries ON countries.name = orders.country WHERE orders.confirmed_at > now() - interval '90' day AND orders.country <> 'India' group by shippers.id ,countries.id"
    shipper_country_wise_hash = ActiveRecord::Base.connection.execute(sql).to_a
    shipper_country_wise_hash.each do |sc_estimation|
      sc_eastimate = ShipperCountryEstimation.where(shipper_id: sc_estimation['shipper_id'], country_id: sc_estimation['country_id']).first_or_initialize
      sc_eastimate.days = sc_estimation['percentile_cont']
      sc_eastimate.save
    end
    Rails.cache.delete('get_shippers_and_days')

    ################### updating non stitching orders processing time ###########################
    sql = "SELECT percentile_cont(0.70) within group (order by coalesce(EXTRACT(EPOCH FROM (orders.out_of_mirraw_warehouse - s.in_scan_at)/(3600*24)),5) asc) FROM orders INNER JOIN designer_orders ON designer_orders.order_id = orders.id INNER JOIN line_items ON line_items.designer_order_id = designer_orders.id INNER JOIN (SELECT min(scans.scanned_at) as in_scan_at, scans.line_item_id from scans where scan_type = 'Package Inscan' and scans.scanned_at > now() - interval '60'day and scans.scanned_at < now() - interval '15'day group by line_item_id)s on s.line_item_id = line_items.id WHERE orders.items_received_on > now() - interval '30' day AND orders.items_received_on < now() - interval '15' day AND (orders.other_details @> hstore('stitching_order','false'))"
    non_stitching_days = ActiveRecord::Base.connection.execute(sql).to_a.first["percentile_cont"].to_i

    s=SystemConstant.where(name: 'SLA_CONFIG').first
    new_value = JSON.parse(s.value)
    new_value[:non_stitching_preparation_days] = non_stitching_days
    s.value = new_value.to_json
    s.save!

    india = Country.where(name: 'India').first
    india.shipping_time = (non_stitching_days * -1) + SHIPPING_TIME
    india.save!

    ################### updating stitching addons prod time ###########################
    sql = "SELECT x.order_type, percentile_cont(0.70) within group (order by coalesce(EXTRACT(EPOCH FROM x.dispatch_days),10) asc) as percentile_dispatch FROM orders INNER JOIN (SELECT orders.id as order_id, case when (position('custom' in string_agg(addon_type_value_groups.name,',')) > 0) then 'custom_stitching' when (position('standard' in string_agg(addon_type_value_groups.name,',')) > 0) then 'standard_stitching' else 'custom_stitching' end  as order_type, (orders.out_of_mirraw_warehouse - min(scans.scanned_at))/(3600*24) as dispatch_days FROM orders INNER JOIN designer_orders ON designer_orders.order_id = orders.id INNER JOIN line_items ON line_items.designer_order_id = designer_orders.id INNER JOIN scans on scans.line_item_id = line_items.id and scans.scan_type = 'Package Inscan' INNER JOIN line_item_addons ON line_item_addons.line_item_id = line_items.id INNER JOIN addon_type_values ON addon_type_values.id = line_item_addons.addon_type_value_id INNER JOIN addon_type_value_groups ON addon_type_value_groups.id = addon_type_values.addon_type_value_group_id WHERE orders.items_received_on > now() - interval '30'day AND orders.items_received_on < now() - interval '15'day AND line_items.stitching_required = 'Y' GROUP BY orders.id)x on orders.id = x.order_id GROUP BY x.order_type"
    addons = ActiveRecord::Base.connection.execute(sql).to_a

    addons.each do |a|
      prod_time = (a['percentile_dispatch'].to_f - non_stitching_days).round
      # p prod_time
      AddonTypeValue.where.not(description: [nil,'Non Pre-Stitched Saree']).where(design_id: nil).joins(:addon_type_value_group).where(addon_type_value_groups: {name: a['order_type']}).update_all(prod_time: prod_time)
    end
  end
end

desc 'Send out referral mailers'
task :send_out_referral_mailers => :environment do
  @referrers = Referrer.where('notified IS NULL').includes([:referred_people])
  @referrers.each do |referrer|
    referrer.referred_people.each do |person|
      ReferralMailer.delay(priority: 4).send_invite(referrer, person)
    end
    referrer.notified = 1
    referrer.save
  end
end

desc 'Shuffle Designs'
task :shuffle_designs => :environment do
  @designs = Design.published.where(:grade => 0..20)
  @designs.each do |design|
    design.grade = rand(0..20)
    design.save
  end
end

desc 'Process Images to find duplicates'
task process_images_for_duplicates: :environment do
  time_period = Date.yesterday.beginning_of_day..Date.yesterday.end_of_day
  DesignCluster.create_clusters({updated_at: time_period})
end

task :shuffle_grades => :environment do

  @purchased_products_last_7_days = LineItem.joins(:designer_order).where('line_items.created_at > ?', 7.days.ago).group_by(&:design_id)
  @purchased_products_last_month = LineItem.joins(:designer_order).where('line_items.created_at > ? and line_items.created_at < ?', 30.days.ago, 7.days.ago).group_by(&:design_id)
  @purchased_products_last_3_months = LineItem.joins(:designer_order).where('line_items.created_at > ? and line_items.created_at < ?', 90.days.ago, 30.days.ago).group_by(&:design_id)

  @purchased_products_last_7_days = @purchased_products_last_7_days.sort_by {|p| - p[1].count}
  @purchased_products_last_month = @purchased_products_last_month.sort_by {|p| -p[1].count}
  @purchased_products_last_3_months = @purchased_products_last_3_months.sort_by {|p| -p[1].count}

  @purchased_products_last_7_days.to_h.keys.count

  @purchased_products_last_7_days.each do |design_id, line_items|
    line_items_count = line_items.count
    if line_items_count > 1
      design = Design.find(design_id)
      if design.available? && design.grade > 0
        design.grade = rand(60..80) if design.grade < 10 && line_items_count > 2
        design.save
      end
    end
  end

  @purchased_products_last_month.each do |design_id, line_items|
    line_items_count = line_items.count
    if line_items_count > 1
      design = Design.find(design_id)
      if design.available? && design.price > 500 && design.grade > 0
        if design.available? && design.price > 500
          design.grade = 60 - line_items_count
          design.save
        end
      end
    end
  end

  @purchased_products_last_3_months.each do |design_id, line_items|
    line_items_count = line_items.count
    if line_items_count > 1
      design = Design.find(design_id)
      if design.available? && design.price > 500 && design.grade > 0
        if design.available? && design.price > 500
          design.grade = 80 - line_items_count
          design.save
        end
      end
    end
  end

end

desc 'Downgrade older designs with no sell count'
task :downgrade_designs_no_sell_count => :environment do
    # All designs that were uploaded 3 months back and have no sales.. Downgrade them.
  @designs = Design.published.where('created_at <= ?', 2.months.ago).where('sell_count < ?', 1).where('grade >= 0');nil;
  @designs.count
  @designs.each do |design|
    design.grade = -10;
    design.save
  end
end

desc 'Downgrade older designs with no sell count'
task :reindex_not_in_stock_designs => :environment do
    # All designs that were uploaded 3 months back and have no sales.. Downgrade them.
  designs = Design.where('state = ?', 'seller_out_of_stock').limit(5000);nil;
  Design.where('state = ?', 'seller_out_of_stock').find_in_batches do |batch|
     Sunspot.index(batch);nil;nil
  end

  designs = Design.joins(:designers).where('designers.state_machine IN (?) AND designs.state = ?', ['banned', 'on_hold', 'inactive'], 'in_stock').limit(5000);nil;

  @designs = Design.published.retail.includes([:designer]).where(state_machine: ['banned', 'on_hold', 'inactive'])

  @designs.find_in_batches do |batch|
     Sunspot.index(batch);nil;nil
  end



  Design.where('state = ?', 'banned').find_in_batches do |batch|
     Sunspot.index(batch);nil;nil
  end

  designs = Designer.banned.designs.where('designs.state = ?', 'in_stock');nil;
  designs.count
  designs.each do |design|
    design.bad_design
  end

end


desc 'Downgrade Old designs'
task :downgrade_designs => :environment do

  @designs = Design.published.where('created_at <= ? AND created_at > ?', 8.months.ago, 9.months.ago)
  @designs.update_all(:grade => 800)

  @designs = Design.published.where('created_at <= ? AND created_at > ?', 7.months.ago, 8.months.ago)
  @designs.update_all(:grade => 700)

  @designs = Design.published.where('created_at <= ? AND created_at > ?', 6.months.ago, 7.months.ago)
  @designs.update_all(:grade => 600)

  @designs = Design.published.where('created_at > ?', 15.days.ago)
  @designs.update_all(:grade => 10)

  @designs = Design.published.where('created_at <= ? AND created_at > ?', 15.days.ago, 1.month.ago)
  @designs.update_all(:grade => 20)

  @designs = Design.published.where('created_at < ?', 6.months.ago)
  @designs.update_all(:grade => 1200)

  @designs = Design.published.less_than(500)
  @designs.update_all(:grade => 800)

  @designs = Design.published.where('created_at <= ? AND created_at > ?', 4.months.ago, 5.months.ago)
  @designs.update_all(:grade => 400)

  @designs = Design.published.where('created_at <= ? AND created_at > ?', 3.months.ago, 4.months.ago)
  @designs.update_all(:grade => 300)

  Design.less_than(200).update_all(:published => false)
  Designer.find('soul-of-silver').designs.update_all(:published => false)

  Design.published.joins(:categories).in_category('chocolates').update_all(:published => false)
end


task :shuffle_designer_grades => :environment do

  # Push lower priced designs
  @designs = Design.less_than(500)
  @designs.update_all :grade => 300

  @products_summary = LineItem.joins(:designer_order).group_by(&:design_id)

  @products_summary.each do |design_id, line_items|
    line_items_count = line_items.count
    if line_items_count > 0
      design = Design.find(design_id)
      if design.available? && design.price > 500
        grade = rand(50)
        if design.grade > grade
          design.grade = grade
          design.save
        end
      end
    end
  end
end

desc 'Tag designs'
task :tag_designs => :environment do
  #@designs = Designer.find(241).designs

  @designs = Design.where('description LIKE ?', '%Kashmiri%')
  @designs.each do |design|
    design.tag_list << 'Kashmiri Earrings'
    design.save
  end

  h = Category.find_by_namei('salwar-combo')
  h.weight = 1000;
  h.save

  Category.where(:weight => nil)

  Category.find_by_namei('sarees')

  cat = "jhumkas"
  txt = "bali"
  designs = Design.where('designs.title LIKE ?', 'jhumki').includes([:categories]).in_category('danglers-drops');nil
  designs.count


  c = Category.find_by_namei('danglers-drops')

  search = Design.search(:include => [:designer, :images, :categories]) do
    with(:state, 'in_stock')
    with(:category_parents, c.id)
    fulltext "jhumki"
    with(:discount_price, 0..100000)
    paginate :page => 1, :per_page => 900
  end

  search.rows.count

  designs = search.results
  designs.count

  cat = "jhumkas"

  c = Category.find_by_namei(cat)
  designs.each do |design|
    design.category_ids = [c.id]
    design.save
  end


  cat = "half-sarees"
  designs = Design.published.where('designs.title ILIKE ?', '%half n half%').includes([:categories]).in_category('sarees');nil
  designs.count
  c = Category.find_by_namei(cat)
  designs.each do |design|
    design.category_ids = [c.id]
    design.save
  end


  designs = Design.published.tagged_with('Jhumki').includes([:categories]).in_category('danglers-drops')
  designs.count


  designs = Design.includes([:categories]).in_category('jhumkas')

  @designs = Design.where('description LIKE ?', '%Kashmiri%')
  @designs.each do |design|
    design.tag_list << 'Kashmiri Earrings'
    design.save
  end



  @designs = Design.published.joins(:categories).in_category('necklaces').where('designs.description LIKE ?', '%temple%')
  @designs.each do |design|
    design.tag_list << 'Temple Jewellery'
    design.save
  end
end

desc 'H1 tag'
task :add_h1_tag => :environment do
  @categories = Category.all
  @categories.each do |c|
    title = c.name.gsub('-', ' ').camelize + ' online'
    c.update_attribute(:title, title)
  end
end

desc 'Enter delivered date'
task :fetch_delivered_date => :environment do
  designer_orders = DesignerOrder.where('tracking IS NOT NULL AND delivered_at IS NULL')
  designer_orders.each do |d|
    if d.tracking.include?('aramex')
      DesignerOrder.delay.get_delivered_date(d)
    end
  end
end

desc "Send Weeky Mirraw Social mailers to users"
task :sendout_weekly_social_notification => :environment do
  # Send mail every tuesday -- 0:Sunday 1:Monday...6:Saturday
  if Time.now.wday == 2
    # If tuesday send weekly email
    @users = User.all
    @users.each do |user|
      FollowMailer.delay(priority: 4).user_weekly_social_notification(user)
    end
  end
end


desc "Send Buy Some Stuff email to users"
task :sendout_buysomestuff_notification => :environment do
  #whom to send - buyer whose last order was before 30 days
  @all_orders = Order.all(:conditions=>["state='sane'"], :order=>"id DESC").group_by(&:email)

  @all_orders.each do |email, orders|
    if orders.first.present?
      latest_order = orders.first
      if latest_order.created_at <= 1.month.ago && (latest_order.visit_mail_sent.nil?)
        FollowMailer.delay(priority: 4).buyer_nextorder_notification(latest_order)
        latest_order.visit_mail_sent = Date.today
        latest_order.update_attribute(:visit_mail_sent, Date.today)
      end
    end
  end
end

desc "Send Visit Us email to users"
task :sendout_visitus_notification => :environment do
  accounts = Account.all(:conditions=>["accountable_type='User' and last_sign_in_at <= ?", 15.days.ago])
  designs = Design.where(:state => 'in_stock').order("id desc, grade asc").limit(12)
  accounts.each do |account|
    user = User.find(:first, :conditions=>["email=?", account.email])
    if user.present?
      @order = Order.find(:first, :conditions=>["email=?", user.email])

      if @order.nil?
        # User is not a buyer
        if user.visit_mail_sent.nil? or user.visit_mail_sent <= Date.today - 15
          FollowMailer.delay(priority: 4).user_visitus_notification(user, *designs)
        end
      end
    end
  end
end


desc 'Sendout pending order notifications to designers'
task :sendout_pending_order_notifications => :environment do
  designer_orders = DesignerOrder.where('created_at > ? AND state = ?', 2.months.ago, 'pending')
  order_hash = designer_orders.group_by {|d| d.designer }
  order_hash.each do |designer, designer_orders|
    if designer.present?
      OrderMailer.delay.sendout_pending_order_notifications(designer, designer_orders)
    end
  end
end

desc 'Sendout pending order notification to admin'
task :pending_notifications_to_admin => :environment do
  OrderMailer.delay.pending_notifications_to_admin()
end

desc 'Sendout dispatch order notification to admin'
task :dispatch_notifications_to_admin => :environment do
  OrderMailer.delay.dispatch_notifications_to_admin()
end


# Designer Emails
#
# 1. Designer hasn't signed in since 2 weeks - send reminder to unpublish out of stock designs
# 2. Designer has signed up in last month but hasn't uploaded any new designs
# 3. Create new coupon code

desc "Not logged in mailer for designers"
task :not_logged_in_designers_mailer => :environment do
  accounts = Account.all(:conditions => ["accountable_type='Designer' and last_sign_in_at <= ? and (notified_date <= ? or notified_date IS NULL)", 2.week.ago, 1.week.ago])
  accounts.each do |account|
    designer = account.accountable

    # Notify designer to unpublish designs
    if designer && designer.name? && designer.published
      account.notified = 1
      account.notified_date = Date.today
      account.save
      DesignerMailer.delay(priority: 6).not_logged_in_since_week(account.accountable)
    end
  end
end

desc "OOS mail reminder to Customer"
task :oos_mail_reminder_to_customer => :environment do
  designer_issue = DesignerIssue.where(:created_at => 4.days.ago..3.days.ago).where(issue_type: 'OOS').joins(:line_item).joins("LEFT OUTER JOIN returns ON returns.id = line_items.return_id").where('returns.id is NULL').joins(:order).joins(:designer_order).where("(orders.pay_type !='Cash On Delivery' AND designer_issues.state = 'pending' AND orders.payment_state = 'completed') OR (orders.pay_type = 'Cash On Delivery' AND designer_orders.state = 'pending') AND line_items.status!='cancel' ")
  designer_issue.find_each(batch_size: 100) do |di|
      OrderMailer.delay(priority: 6).send_issue_qcfail_mail_to_user(di.order_id,'OOS',line_item_id: di.line_item_id)
  end
end

# Designer Educational Series Mailers
desc "Uploaded Few Designs"
task :only_few_designs_uploaded => :environment  do

  designers = Designer.includes(:account, :categories).where("accounts.created_at <= ? and (accounts.notified_date <= ? or accounts.notified_date IS NULL)", 2.week.ago, 1.week.ago).where(:state_machine => ['review', 'approved'])
  designers.each do |designer|
    notified = false
    if designer && designer.name?
      if designer.categories.present?
        designer.categories.each do |category|
          if (design_count = category.designs.count) < 10
            DesignerMailer.delay(priority: 6).only_few_designs_uploaded(designer, design_count, category.name)
            notified = true
          end
        end
      elsif (design_count = designer.designs.count) < 10
        DesignerMailer.delay(priority: 6).only_few_designs_uploaded(designer, design_count)
        notified = true
      end
      if notified == true
        account = designer.account
        account.notified = 2
        account.notified_date = Date.today
        account.save
      end
    end
  end
end
# Designer upload and update profile Series Mailers
desc "Not Uploaded  50 Designs"
task :not_uploaded_designs => :environment  do

 Designer.alert_new_designers
 Designer.alert_existing_designers

end

 #designer having return count above 10
desc "Alert mail to designmer about return count"
task :return_count_above_10 => :environment  do
  if Date.today == Date.today.at_beginning_of_month
      designers = Designer.where(:state_machine => ['review', 'approved'])
      designers.each do |designer|
        array = []
        designer.designs.each do |design|
          if design.sell_count > 20 && design.return_count != 0
            return_per = ((design.return_count*1.0)/design.sell_count)*100
            if return_per > 10
              array << design.id
            end
          end
        end
        DesignerMailer.delay(priority: 6).return_count_above_10(designer, array) if array.present?
      end
  end
end

#Revenue above 20000 but products uploaded are less than 100
desc "revenue above 20000 but products uploaded are less than 100 "
task :revenue_above_20000 => :environment  do
    if Date.today == Date.today.at_beginning_of_month + 12
      designers = Designer.joins(:designs).where(:state_machine => ['review', 'approved']).having('count(designs.*) < ?', 100).group('designers.id')
      designers.each do |designer|
        if Designer.get_revenue(designer) > 20000
          DesignerMailer.delay(priority: 6).revenue_products_less(designer,designer.designs.count)
        end
      end
    end
end

#Revenue above 50000 but not uploaded since last 2 months
desc "revenue above 50000 and upload products since last 2 months"
task :revenue_above_50000_not_uploaded_products  => :environment  do
  if Date.today == Date.today.at_beginning_of_month + 12
    designers = Designer.where(:state_machine => ['review', 'approved'])
    designers.each do |designer|
      if (designer.designs.order('created_at ASC').last) && (designer.designs.order('created_at ASC').last).created_at < 2.months.ago && Designer.get_revenue(designer) > 50000
        DesignerMailer.delay.revenue_not_uploaded_months(designer)
      end
    end
  end
end

#best selling products which are oos low on stock
desc "best selling products which are oos low on stock"
task :best_selling_products_oos_low_on_stock  => :environment  do
  if Date.today == Date.today.at_beginning_of_month
    designers = Designer.where(:state_machine => ['review', 'approved'])
    designers.each do |designer|
      designs = designer.designs.where('sell_count > 4 AND designs.quantity < 2').first(4)
      if designs.present?
        DesignerMailer.delay(priority: 6).best_sellrs_oos_los(designer,designs)
      end
    end
  end
end

desc 'daily designer notification for sold_out product'
task designer_sold_out_notification: :environment do
  sold_out_designs = {}
  sold_out_from = Date.yesterday
  Design.where{state.eq('sold_out')&last_sold_out.gte(sold_out_from)}.select('id,designer_id').each do |design|
    (sold_out_designs[design.designer_id.to_i]||=[]) << design.id
  end
  sold_out_variants = {}
  Variant.where{quantity.lte(0)&updated_at.gte(sold_out_from)}.joins(:design).select('variants.id as id,designs.designer_id as designer_id').each do |variant|
    (sold_out_variants[variant.designer_id.to_i]||=[]) << variant.id
  end

  (sold_out_designs.keys + sold_out_variants.keys).uniq.each do |designer_id|
    DesignerMailer.delay(priority: 4).sold_out_notification(designer_id,sold_out_designs[designer_id],sold_out_variants[designer_id])
  end
end


#Monthly report to vendors which contains dispatch time, quality reject, buyer return, no of uploads done, out of stock issues best
desc "Monthly report to vendors"
task :monthly_report_to_vendors => :environment  do
  if Date.today == Date.today.at_beginning_of_month
    designers = Designer.where(:state_machine => ['review', 'approved'])
    designers.each do |designer|
      if Designer.get_revenue(designer) > 20000
        DesignerMailer.delay(priority: 6).monthly_orders_report(designer)
      end
    end
  end
end
# Designer Educational Series Mailers
desc "Explain Order States & Gharpay"
task :explain_gharpay => :environment  do

  accounts = Account.all(:conditions => ["created_at >= ? and created_at <= ? and accountable_type='Designer' ", 2.week.ago, 1.week.ago])

  accounts.each do |account|
    designer = account.accountable
    if designer && designer.name? && designer.published
      DesignerMailer.delay.explain_gharpay(designer)
    end
  end
end


# Designer Educational Series Mailers
desc "Create New Coupon Code"
task :create_new_coupon_code => :environment  do

   accounts = Account.all(:conditions => ["created_at >= ? and created_at <= ? and accountable_type='Designer' ", 3.week.ago, 2.week.ago])

  accounts.each do |account|
    designer = account.accountable
    if designer && designer.name? && designer.published
      DesignerMailer.delay(priority: 4).create_new_coupon_code(designer)
    end
  end
end


#  Coupon code notification
#  1: Buyers are notified of creation
#  2: Followers are notified of creation
#  3: Buyers are notified of expiry of coupon
#  4: Followers are notified of expiry of coupon
#  5: Sendout coupon codes to partners


desc 'Sendout coupon code notifications to buyers'
task :sendout_coupon_code_notifications_to_buyers => :environment do
  coupons = Coupon.preload(designer: [designer_orders: :payment_order]).valid_to_show
  coupons.find_each(batch_size: 250) do |coupon|
    email_list = []
    coupon.designer.designer_orders.each do |d|
      if d.payment_order.present? && !email_list.include?(d.payment_order.email)
        FollowMailer.delay(priority: 4).sendout_coupon_code_notifications_to_buyers(d.payment_order, coupon)
        email_list << d.payment_order.email
      end
    end
  end
  coupons.update_all(notified: 1)
end

desc 'Sendout coupon code notifications to followers'
task :sendout_coupon_code_notifications_to_followers => :environment do
  coupons = Coupon.where('notified = ?', 1)
  coupons.each do |coupon|
    if coupon.advertise && coupon.live?
      designer = coupon.designer
      designer.followers.each do |follower|
        if follower.present?
          FollowMailer.delay(priority: 4).sendout_coupon_code_notifications_to_followers(follower, coupon)
        end
      end
    end
  end
  coupons.update_all :notified => 2
end


desc 'Sendout coupon code notifications to partners'
task :sendout_coupon_code_notification_to_partners => :environment do
  @promote_list = Array.new

  coupons = Coupon.where('notified = ?', 2)
  coupons.each do |coupon|
    if coupon.advertise && coupon.live?
      @promote_list << coupon
    end
  end
  coupons.update_all :notified => 3

  unless @promote_list.blank?
    @partners = CouponPartner.all
    @partners.each do |partner|
      FollowMailer.delay(priority: 4).sendout_coupon_code_notification_to_partners(@promote_list, partner)
    end
  end
end


desc 'Sendout expiry coupon code notifications to buyers'
task :sendout_expiry_coupon_code_notifications_to_buyers => :environment do
  # Buyers
  coupons = Coupon.where('notified is NULL AND designer_id is NOT NULL AND advertise=? AND coupons.use_count >= ? AND coupons.use_count < coupons.limit', true, 0).where(end_date: (1.day.from_now.beginning_of_day..1.day.from_now.end_of_day))
  coupons.each do |coupon|
    email_list = Array.new
    coupon.designer.designer_orders.where("designer_orders.created_at > ?", 6.month.ago).includes([:order]).find_each(batch_size: 500) do |d|
      if d.order.present?
        FollowMailer.delay(priority: 4).sendout_expiry_coupon_code_notifications_to_buyers(d.order, coupon) unless email_list.include?(d.order.email)
        email_list << d.order.email
      end
    end
  end
  coupons.update_all :notified => 1
end

desc 'Sendout expiry coupon code notifications to followers'
task :sendout_expiry_coupon_code_notifications_to_followers => :environment do
  coupons = Coupon.where('notified = ?', 4)
  coupons.each do |coupon|
    if coupon.advertise && coupon.live? && coupon.end_date.to_date <= (Date.today + 1.day)
      designer = coupon.designer
      designer.followers.each do |follower|
        if follower.present?
          FollowMailer.delay(priority: 4).sendout_expiry_coupon_code_notifications_to_followers(follower, coupon)
        end
      end
    end
  end
  coupons.update_all :notified => 5
end

desc 'Update USD to INR rates'
task :update_currency_rates => :environment do
  agent = Mechanize.new
  agent.user_agent = "Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_2; ru-ru) AppleWebKit/533.2+ (KHTML, like Gecko) Version/4.0.4 Safari/531.21.10"

  page = agent.get('http://rate-exchange.appspot.com/currency?from=USD&to=INR')

  data = ActiveSupport::JSON.decode(page.body)

  c = CurrencyConvert.where(:symbol => "USD").first
  c.rate = data["rate"].round(2) - 2 # For currency conversion difference
  c.save
end

# This job set paypal conversion rate for those countries whose currency is not supported by paypal
task set_paypal_conversion_rate: :environment do
  errors = {}
  paypal_allowed_arr = Order::PAYPAL_ALLOWED_CURRENCIES
  all_country_arr = CurrencyConvert.pluck(:iso_code).uniq
  paypal_conversion_countries = all_country_arr - paypal_allowed_arr
  paypal_conversion_countries.each do |currency_iso_code|
    begin
      convert = "#{currency_iso_code}_USD"
      current_paypal_rate = JSON.parse(HTTParty.get("https://free.currconv.com/api/v7/convert?q=#{convert}&compact=ultra&apiKey=07ff7062a2ff71958f36").response.body)
      unless current_paypal_rate[convert].to_f == 0
        CurrencyConvert.where(iso_code: currency_iso_code).update_all(paypal_rate: current_paypal_rate[convert].to_f)
        puts current_paypal_rate[convert].to_f
      end
    rescue => e
      errors[currency_iso_code] = e.message
    end
  end
  raise errors.values.join(',') if errors.present?
end

# This job set market rate for all country in INR
task set_market_rate_sidekiq: :environment do
  SetMarketRateJob.perform_async()
end

task set_market_rate: :environment do
  errors = {}
  priority_one = []
  priority_two = []
  CurrencyConvert.all.each do |record|
    if SET_MARKET_RATE_PRIORITY.include? record.iso_code
      priority_one << record.iso_code
    else
      priority_two << record.iso_code
    end
  end
  priority_one.each do |iso_code|
    begin
      CurrencyConvert.market_rate_api_call(iso_code)
    rescue => e
      errors[iso_code] = e.message
    end
  end
  priority_two.each do |iso_code|
    begin
      CurrencyConvert.delay(run_at: 60.minutes.from_now).market_rate_api_call(iso_code)
    rescue => e
      errors[iso_code] = e.message
    end
  end
  raise errors.values.join(',') if errors.present?
end

desc 'Random try'
task :create_random_code => :environment do
    @jobs = Delayed::Job.where('created_at > ?', 2.hour.ago).where('handler LIKE ?', '%post_to_facebook%').where('handler LIKE ?', '%AdminController%').where('run_at > ?', Date.tomorrow).delete_all
    Delayed::Job.where('last_error is not NULL').count

    jobs = Delayed::Job.where('last_error is not NULL').delete_all


    Delayed::Job.first
    jobs = Delayed::Job.where('last_error is not NULL')
    jobs.each {|j|j.invoke_job}

    d = Design.first(10)
    Delayed::Job.enqueue Sunspot.index(d)

    Delayed::Job.where('created_at > ? and created_at < ?', 1.hour.ago, 2.minutes.ago).first.handler[80..200]

    Design.published.where('discount_price < 151').update_all :published => false

    Delayed::Job.where('created_at > ?', 20.minutes.ago).first.handler.class

    no_seo = Array.new
    categories = Category.all
    categories.each do |category|
      @seo = SeoList.where(:label => category.name.downcase).first
      no_seo << category.name unless @seo.present?
    end

    @seo = SeoList.where(:label => @kind).first
    if @seo && @seo.category.present?
      @category = @seo.category
      @kind = @seo.category.name
    else
      category = Category.find_by_namei(@kind)
      @category = category
    end

    designs = Design.published.joins(:categories).joins(:variants).where('categories.id <> ?', Category.find_by_namei('kurtas-and-kurtis')).select('designs.id')
    v = Design.find('75281').variants
    v.destroy_all

    designs = Designer.find('v-v-shop').designs.published
    designs.count
    designs.each do |design|
      design = Design.find(design.id)
      design.grade = design.grade * 2
      design.save
    end

    designs = Designer.find('shaun-design').designs.published
    designs.count
    designs.each do |design|
      design = Design.find(design.id)
      design.grade = design.grade / 20
      design.save
    end

    @carts = Cart.where('created_at > ?', 10.days.ago)

    @filtered_carts = Array.new

    @carts.each do |cart|
      if cart.line_items.count > 50
        @filtered_carts <<  [cart.id, cart.hash1]
      end
    end

    #
    # [[257411, "6c1d72846fc00b7d204bfdccfdc55c2c"], [257812, "f2b70617b8fade55b6b66ab55eb698a8"],
    # [258390, "ddf3caba72adeb4cca7ff4c09c24d2fe"], [259413, "f8f3e417cc05204e6fe28e00a84c5833"]]
    #
    #

    designs = Designer.find('radhas-creations').designs

    designs.each do |design1|
      design = Design.find(design1.id)
      design.price = (design.price * 1.2).to_i
      design.save
    end

    account = Designer.find('amore').account
    designer_order = Designer.find('chaahat-fashion-jewellery').designer_orders.first
    ability = Ability.new(account)

    designer_order1 = Designer.find('amore').designer_orders.first


    ability.can?(:manage, designer_order)
    ability.can?(:read, designer_order)
    ability.can?(:read, designer_order1)
    ability.can?(:manage, designer_order1)
    ability.can?(:index, DesignerOrder)
    DesignerOrder.accessible_by(ability) # see if returns the records the user can access

    DesignerOrder.accessible_by(ability).to_sql # see what the generated SQL looks like to help determine why it's not fetching the records you want

    @total_dos = DesignerOrder.where('designer_orders.state = ?', 'completed').where('designer_orders.created_at BETWEEN ? AND  ?', @start_datetime, @end_datetime).where('designer_orders.designer_payout_status = ?', 'paid').to_a
    @total_dos = @total_dos.sum(&:payout)
    @total_dos.sum(&:total)

    @orders = Order.includes(:designer_orders).where('designer_orders.state = ?', 'completed').where('designer_orders.created_at BETWEEN ? AND  ?', @start_datetime, @end_datetime).to_a
    @orders.sum(&:shipping)

    designs = Designer.find('bunkar').designs.published.where('discount_price = ?', 1349).select('id')

    designs.each do |design|
      puts design.id
    end

    designs.each do |design|
      design.quantity = 0
      design.published = false
      design.save
    end

    designs = Designer.find('vardhaman-goodwill').designs.published
    designs.count
    designs.update_all :published => false

    designs = Designer.find('fabdeal').designs.where('created_at > ?', 1.day.ago)
    designs.count
    designs.each do |design|
      design.images.update_all :kind => ''
      image = design.images.first
      image.kind = 'master'
      image.save
    end

    @search = Design.search do
      fulltext 'starplus'
      with(:published, true)
      order_by(:created_at, :desc)
    end
    @designs = @search.results
    @designs.count

    @designs.each do |design|
      design.collection_list = 'star-plus-serial-sarees'
      design.save
    end

    @search = Design.search do
      fulltext 'sonali bendre'
      with(:state, 'in_stock')
      order_by(:created_at, :desc)
    end
    @designs = @search.results

    category = Category.find_by_namei('bollywood-salwar-kameez-online')

    @designs.each do |design|
      design.categories << category
      design.save
    end

    @designs = @designs.sort_by {|d|d.id}
    @designs.each do |design|
      puts "#{design.id} -- #{design.quantity} -- #{design.specification}"
    end

    designs = Designer.find('chaahat-fashion-jewellery').designs.where('created_at > ?', 3.hours.ago)

    designers = Designer.where('transaction_rate < 25')

    designers.update_all(:transaction_rate => 25)

    designers.count

    designs = Design.published.joins(:categories).in_category('express-rakhi')
    designs.count
    designs.update_all(:eta => '-6')




    brand = Designer.find('anvi-collections').id
    designs = Design.published.retail.where('grade > ?', 10).includes([:categories]).with_brand(brand).in_category('earrings').sample(20)


    #http://www.mirraw.com/designers/alankruthi
    #designs = Designer.find('pearls-n-more').designs.published.joins(:category).in_category('earrings').sample(30)
    designs = Designer.find('vardhaman-goodwill').designs.published.order('sell_count DESC').sample(50)

    #designs = Designer.find('sareegold').designs.published.where('grade < 100')
    #designs.count
    designs.each do |design|
      #design = Design.find(design1.id)
      design.grade = design.grade / 10
      design.save
    end

    Designer.find('zahara').designs.published.count

    designs.update_all :published => false

    # Designer.find('zahara').designs.count
    category = Category.find_by_namei('other-sarees')

    designs.each do |design|
      if design.categories.blank?
        design.categories << category
        design.save
      end
    end


    category_id = Category.getid('bollywood-sarees')

    designs.each do |design|
      design.categories.find(category_id)
      design.categories.delete(category)
    end

    designs.update_all :published => false

    designs = Designer.find('all-that-glitters').designs.published.less_than(300).update_all :published => false

    designs = Designer.find('beautona').designs.published
    designs.each do |design|
      design.grade = design.grade + 250
      design.save
    end

    designs = Designer.find('india-circus').designs
    designs.each do |design|
      if design.images.count == 1
        design.published = false
        design.save
      end
    end

    designs = Designer.find('sareegold').designs.published
    designs.count

    designs.each do |design|
      design.price = design.price + design.price / 10
      design.save
    end

    designers = Designer.all
    designers.each do |designer|
      designer.designs.update_all :published => false
    end

    designs = Design.unpublished.order('id desc')
    designs.each do |design|
      latest_version = design.versions.last
      design = latest_version.reify
      if design && design.published
        design.update_attributes(:published => true)
      end

    designs = Design.published.order('id desc').where('quantity < ?', 1)
    designs.each do |design|
      if design.quantity <= 0
        design.update_attributes(:published => false)
      end
    end

    designs = Design.where(:published => true)

    designs.each do |design|
      prev_version = design.previous_version
      if prev_version && prev_version.published == false && prev_version.updated_at > 3.hours.ago
        prev_version.save
      elsif prev_version
        prev_version = prev_version.previous_version
        if prev_version && prev_version.published == false && prev_version.updated_at > 3.hours.ago
          prev_version.save
        end
      end
    end

    designs = Design.published.joins(:categories).in_category('rakhi')
    designs.count
    designs.update_all :published => false


    designs = Design.where('discount_price = ? OR discount_price IS NULL', 0)
    designs.each do |design|
      design.discount_percent = 0 if design.discount_percent.nil?
      design.discount_price = ((100 - design.discount_percent) * design.price)/100
      design.save
    end

    designs.count



    #
    # Chaahat, Bunkar, Azhag
    #
    #
    design = Design.find(23993)
    design.versions[-1].reify

    designs = Designer.find('pal-dzigns-high-end-jewelry').designs
    designs.each do |design|
      if design
        pdesign = design.versions.where('versions.created_at < ?', 6.hours.ago).order('versions.id asc').last.reify
        pdesign.save if pdesign
      end
    end

    designs = Designer.find('pal-dzigns-high-end-jewelry').designs

    versions = design.versions.where('versions.created_at < ?', 6.hours.ago).order('created_at asc')
    versions.last.reify

    design = designs.first
    design = Design.find(18533)

    designs.each do |design|
      if design && design.versions.count > 0
        attr1 = design.version_at(6.hours.ago).published
        design.update_attributes(:published => attr1)
      end
    end

    designs = Designer.find('ramdhenu').designs
    designs.each do |design|
      if design && design.versions.count == 1
        design.update_attributes(:published => true)
      end
    end

    designs = Designer.find('sareegold').designs
    designs.each do |design|
      if design && design.versions.count == 1
        design.update_attributes(:published => true)
      end
    end

    designs = Designer.find('vividha-designs').designs
    designs.each do |design|
      if design && design.versions.count == 1
        design.update_attributes(:published => true)
      end
    end

    designs = Designer.find('saubhagya-shines').designs
    designs.each do |design|
      if design && design.versions.count == 1
        design.update_attributes(:published => true)
      end
    end

    designs = Designer.find('couture-dahlin-by-neha-ahuja').designs.unpublished

    designs = Design.published.joins(:categories).in_category('rakhi')
    designs.count
    designs.update_all(:eta => -4)

    categories = Category.where('weight IS NULL')
    categories.count
    categories.first
    categories.each do |category|
      category.weight = 500
      category.save
    end

    designs1 = Design.find(designs)
    designs1.update_attributes :published => false

    designs = Design.where('id > 0 and id < 5000').unpublished.collect {|d|d.id}

    Design.where('id > 25000').unpublished.count

    prev = Design.find(16393).previous_version
    prev = prev.previous_version

    #
    Design.find(37374).versions.count

    d = Design.find(37374).version_at(8.hours.ago)
    d.versions.count
    Design.find(37414).versions

    d = Design.find(37374).update_attributes(:published => true)

    prev_version = d.previous_version
    prev_version = prev_version.previous_version

    end

    latest_version = Design.find(26149).versions.last
    latest_version.reify
    latest_version.save
end

task :update_gharpay_orders => :environment do
  # find all pending gharpay orders

  @orders = Order.orders_between(Date.today-31 , Date.today).where({:state => 'pending', :pay_type => GHARPAY})

  @orders.each do |order|

    begin
      res = HTTParty.get("#{Mirraw::Application.config.delhivery_baseurl}/api/p/viewOrderDetails/xml/", :query => {:token => Mirraw::Application.config.delhivery_token, :orderId => order.number}, :headers => {'Content-Type' => 'application/xml'})
      if res && res['viewOrderDetailsResponse'] && res['viewOrderDetailsResponse']['orderStatus']
        order.gharpay_status = res['viewOrderDetailsResponse']['orderStatus']
        if order.gharpay_status == "Delivered"
          # Cool. Payments have been collected by gharpay executives. We can alert the designers to ship the item now.
          order.good_data!
        elsif order.gharpay_status == "Cancelled by Customer" || order.gharpay_status == "Failed" || order.gharpay_status == "Invalid"
          order.order_cancel_reason = "Gharpay Status:#{order.gharpay_status}"
          order.cancel!
        end
        order.save
      end
    rescue Exception => e
      puts "caught exception #{e}! ohnoes!"
    end
  end
end

desc 'Update gharpay pincodes'
task :update_gharpay_pincodes => :environment do
  if Time.now.wday == 0
    Courier.where(:name => 'Gharpay').update_all :cbd => 'N'

    res = HTTParty.get("http://track.delhivery.com/c/api/pin-codes/xml/?token=ee64db0a4b9c2db22e1751f4f4b22204e0649028&cash=Y", :headers => {'Content-Type' => 'application/xml'})

    if res && res['delivery_codes']['postal_code']
      pincodes = res['delivery_codes']['postal_code']
      pincodes.each do |pincode|
        courier = Courier.where(:pincode => pincode['pin']).first
        if courier.present?
          courier.cbd = 'Y'
          courier.name = 'Delhivery'
          courier.save
        else
          Courier.create!(:name => 'Delhivery', :cod => 'N', :cbd => 'Y', :pincode => pincode['pin'])
        end
      end
    end
  end
end

task :create_gharpay_order => :environment do
  @order = Order.where(:number => 'M256256307').first

  # Build xml object by hand since we want to add product details as well.
  # Hash doesn't allow duplicate keys. productDetails is something that is
  # repeated.

  xm = Builder::XmlMarkup.new

  xm.transaction {
    xm.customerDetails {
      xm.address(@order.billing_address)
      xm.contactNo(@order.billing_phone)
      xm.email(@order.billing_email)
      xm.firstName(@order.billing_name)
    }
     xm.orderDetails {
       xm.pincode(@order.billing_pincode)
       xm.clientOrderID(@order.number)
       xm.deliveryDate(Time.zone.now.strftime('%d-%m-%Y'))
       xm.orderAmount(@order.total)
       xm.paymentMode('cash')
     }
   }

   options = {:body => {:data => xm.target!}, :headers => {'Content-Type' => 'application/xml', 'Accept' => 'application/xml'}}
   res = HTTParty.post("http://track.delhivery.com/api/p/createOrder/xml/?token=ee64db0a4b9c2db22e1751f4f4b22204e0649028", options)
   puts res

   if res
     unless res['createOrderResponse']['errorMessage']
       @order.gharpay_order_id = res['createOrderResponse']['orderID']
       @order.gharpay_status = ''
       @order.save
       @order.gharpay! if @order.state == "new"
     else
       @order.gharpay_status = res['createOrderResponse']['errorMessage']
       @order.save
     end
   end
end

task :upload_google_merchant_files => :environment do
  require 'csv'

  connection = Fog::Storage.new({
    :provider                 => 'AWS',
    :aws_access_key_id        => ENV['AWS_ACCESS_KEY_ID'],
    :aws_secret_access_key    => ENV['AWS_SECRET_ACCESS_KEY'],
    :region => "ap-southeast-1"
  })

  # First, a place to contain the glorious details
  directory = connection.directories.new(
    :key    => "mirraw-test"
  )

    # most_selling_15jan
  ["earrings", "necklace-sets","dress-materials","salwar-kameez","anarkali-salwar-kameez","bollywood-sarees", "sarees", "anklets", "mangalsutra", "pendants", "bangles-and-bracelets", "rings", "maang-tikka", "bags","kurtas-and-kurtis"].each_with_index do |c, i|
  # ["mangalsutra"].each_with_index do |c, i|
    category = c
    adgroup = "all_#{c}"
    min_sell_count = 0
    filename = "#{category}_#{adgroup}" + '.csv'

    PATH = "/tmp/"
    filepath = PATH + filename

    Mapping = Hash.new

    if category == "necklaces" || category == "necklace-sets"
      designs = Design.published.where('sell_count >= ?', min_sell_count).joins([:categories, :designer]).active_designer_designs.where('categories.id = ? OR categories.id = ?', Category.getids('necklaces'), Category.getids('necklace-sets'))
    else
      designs = Design.published.where('sell_count >= ?', min_sell_count).joins([:categories, :designer]).active_designer_designs.in_category(category).uniq!
    end

    if category == "earrings"
      google_product_category = "Apparel & Accessories > Jewelry > Earrings"
      mirraw_product_category = "Jewellery > Earrings"
    elsif category == "bollywood-sarees"
      google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
      mirraw_product_category = "Apparel > Clothing > Sarees > Bollywood Sarees"
    elsif category == "sarees"
      google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
      mirraw_product_category = "Apparel > Clothing > Sarees"
    elsif category == "necklace-sets" || category == "necklace" || category == "mangalsutra"
      google_product_category = "Apparel & Accessories > Jewelry > Necklaces"
      mirraw_product_category = "Jewellery > Necklace Sets"
    elsif category == "kurtas-and-kurtis"
      google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
      mirraw_product_category = "Apparel > Clothing > Kurtas & Kurtis"
    elsif category == "salwar-kameez"
      google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
      mirraw_product_category = "Apparel > Clothing > Salwar Kameez & Suits"
    elsif category == "anarkali-salwar-kameez"
      google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
      mirraw_product_category = "Apparel > Clothing > Salwar Kameez & Suits > Anarkali"
    elsif category == "dress-materials"
      google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
      mirraw_product_category = "Apparel > Clothing > Dress Materials"
    elsif category == "anklets"
      google_product_category = "Apparel & Accessories > Jewelry > Anklets"
      mirraw_product_category = "Jewellery > Anklets"
    elsif category == "bangles-and-bracelets"
      google_product_category = "Apparel & Accessories > Jewelry > Bracelets"
      mirraw_product_category = "Jewellery > Bangles & Bracelets"
    elsif category == "pendants"
      google_product_category = "Apparel & Accessories > Jewelry > Charms & Pendants"
      mirraw_product_category = "Jewellery > Pendants"
    elsif category == "rings"
      google_product_category = "Apparel & Accessories > Jewelry > Rings"
      mirraw_product_category = "Jewellery > Rings"
    elsif category == "maang-tikka"
      google_product_category = "Apparel & Accessories > Jewelry > Body Jewelry"
      mirraw_product_category = "Jewellery > Maang Tikka"
    elsif category == "bags"
      google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
      mirraw_product_category = "Bags"
    end

    CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
      csv << ["id", "title", "description", "google_product_category", "product_type", "adwords_grouping","adwords_labels","link", "image_link", "condition", "availability", "price", "gender", "age_group"]
      designs.each do |design|
        if design && design.title && design.specification.present? && design.images.present? && design.cached_slug.present? && design.designer.present?
          url = "http://www.mirraw.com/designers/" + design.designer.cached_slug + "/designs/" + design.cached_slug
          design_category = design.categories.first.name.gsub('-', ' ').camelize
          if category == "sarees"
            mirraw_product_category1 = mirraw_product_category + ' > ' + design_category
          else
            mirraw_product_category1 = mirraw_product_category
          end
          design_description = design.specification.gsub(/\r/, " ").gsub(/\n/, " ") + " " + design.description.gsub(/\r/, " ").gsub(/\n/, " ")
          csv << [design.id, design.designer.name + '-' + design_category, design_description, google_product_category, mirraw_product_category1, adgroup, design.designer.name,url, design.master_image.photo.url(:original), "new", "in stock", design.effective_price, "Female","Adult"]
        end
      end
    end
   # upload that file
    file =  directory.files.create(
      :key    => "googlemerchant/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
  end
end
#-------------------------all countries---------------------------------------------
task :upload_google_merchant_all_countries_in_one => :environment do
  require 'csv'
  connection = Fog::Storage.new({
    :provider                 => 'AWS',
    :aws_access_key_id        => ENV['AWS_ACCESS_KEY_ID'],
    :aws_secret_access_key    => ENV['AWS_SECRET_ACCESS_KEY'],
    :region => "ap-southeast-1"
  })

  # First, a place to contain the glorious details
  directory = connection.directories.new(
    :key    => "mirraw-test"
  )
  PATH = "/tmp/"

  shopping_countries = {USD: {currency: 'USD',country: 'US'}, AUD: {currency: 'AUD',country: 'AU'},GBP: {currency: 'GBP',country: 'GB'},CAD: {currency: 'CAD',country: 'CA'},AED: {currency: 'AED',country: 'AE'},NL: {currency: 'EUR',country: 'NL'}, ZA: {currency: 'ZAR',country: 'ZA'}, IT: {currency: 'EUR',country: 'IT'}, NO: {currency: "NOK", country: "NO"}, PT: {currency: "EUR", country: "PT"}, SE: {currency: "SEK", country: "SE"}, BE: {currency: "EUR", country: "BE"}, IE: {currency: "EUR", country: "IE"}, ES: {currency: "EUR", country: "ES"}}

  earring_category = Category.find_by_namei('earrings').self_and_descendants.collect{|c| c.name}
  sarees_category = Category.find_by_namei('sarees').self_and_descendants.collect{|c| c.name}
  jewellery_category = Category.find_by_namei('jewellery').self_and_descendants.collect{|c| c.name}
  salwar_category = Category.find_by_namei('salwar-kameez').self_and_descendants.collect{|c| c.name}
  lehengas_category = Category.find_by_namei('lehengas').self_and_descendants.collect{|c| c.name}
  rakhi_category = Category.find_by_namei('rakhi-online').self_and_descendants.collect{|c| c.name}
  gemstone_category = Category.find_by_namei('gemstones').descendants.collect(&:name)
  kids_category = Category.find_by_namei('kids').self_and_descendants.collect{|c| c.name}
  blouse_category = Category.find_by_namei('blouse').self_and_descendants.collect{|c| c.name}
  kurtas_category = Category.find_by_namei('kurtas-and-kurtis').self_and_descendants.collect{|c| c.name}
  mens_category = Category.find_by_namei('men').self_and_descendants.collect{|c| c.name}
  islamic_clothing_category = Category.find_by_namei('islamic-clothing').self_and_descendants.collect{|c| c.name}

  if Rails.env.development?
    #for logger
    logger = Logger.new(STDOUT)
    logger.level = Logger::DEBUG
    Rails.logger = ActiveRecord::Base.logger = logger
  end
  puts "Initialising File names."
  #----india-----
  filename = "new_google_feed" + '.csv'
  filepath = PATH + filename
  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "brand","identifier_exists", "google_product_category", "product_type", "adwords_grouping","adwords_labels", "image_link", "condition", "availability", "gender", "age_group", "link", "mobile_link", "custom_label_0", "custom_label_1", "price", "custom_label_2", "custom_label_3", "custom_label_4"]
  end
  #----US------
  filename = "mirraw_feed_USD" + '.csv'
  filepath = PATH + filename
  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "brand","identifier_exists", "google_product_category", "product_type", "adwords_grouping","adwords_labels", "image_link", "condition", "availability", "gender", "age_group", "size", "color", "link", "mobile_link", "custom_label_0", "custom_label_1", "price", "tax", "shipping(price)", "shipping_weight", "custom_label_2", "promotion_id", "custom_label_3", "custom_label_4"]
  end
  conversion_rate_us = CurrencyConvert.where(:country_code => 'US').first.rate
  #----Australia---
  filename = "mirraw_feed_AUD" + '.csv'
  filepath = PATH + filename
  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "brand","identifier_exists", "google_product_category", "product_type", "adwords_grouping","adwords_labels", "image_link", "condition", "availability", "gender", "age_group", "size", "color", "link", "mobile_link", "custom_label_0", "custom_label_1", "price", "shipping(price)", "shipping_weight", "custom_label_2", "promotion_id", "custom_label_3", "custom_label_4"]
  end
  conversion_rate_aud = CurrencyConvert.where(:country_code => 'AU').first.rate
  #----UK--------
  filename = "mirraw_feed_GBP" + '.csv'
  filepath = PATH + filename
  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "brand","identifier_exists", "google_product_category", "product_type", "adwords_grouping","adwords_labels", "image_link", "condition", "availability", "gender", "age_group", "size", "color", "link", "mobile_link", "custom_label_0", "custom_label_1", "price", "shipping(price)", "shipping_weight", "custom_label_2", "promotion_id", "custom_label_3", "custom_label_4"]
  end
  conversion_rate_gbp = CurrencyConvert.where(:country_code => 'GB').first.rate
  #----Canada--------
  filename = "mirraw_feed_CAD" + '.csv'
  filepath = PATH + filename
  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "brand","identifier_exists", "google_product_category", "product_type", "adwords_grouping","adwords_labels", "image_link", "condition", "availability", "gender", "age_group", "size", "color", "link", "mobile_link", "custom_label_0", "custom_label_1", "price", "shipping(price)", "shipping_weight", "custom_label_2", "promotion_id", "custom_label_3", "custom_label_4"]
  end
  conversion_rate_cad = CurrencyConvert.where(:country_code => 'CA').first.rate
  shopping_countries.except(:USD,:AUD,:GBP).keys.each do |var|
    filename = "mirraw_shopping_feed_#{var.to_s}" + '.csv'
    filepath = PATH + filename
    CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
      csv << ["id", "title", "description", "brand","identifier_exists", "google_product_category", "product_type", "adwords_grouping","adwords_labels", "image_link", "condition", "availability", "gender", "age_group", "size", "color", "link", "mobile_link", "custom_label_0", "custom_label_1", "price", "shipping(price)", "shipping_weight", "custom_label_2", "promotion_id", "custom_label_3", "custom_label_4"]
    end
  end
  #----US------
  filename = "mirraw_shopping_feed_USD" + '.csv'
  filepath = PATH + filename
  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "brand","identifier_exists", "google_product_category", "product_type", "adwords_grouping","adwords_labels", "image_link", "condition", "availability", "gender", "age_group", "size", "color", "link", "mobile_link", "custom_label_0", "custom_label_1", "price", "tax", "shipping(price)", "shipping_weight", "custom_label_2", "promotion_id", "custom_label_3", "custom_label_4"]
  end
  #----Australia---
  filename = "mirraw_shopping_feed_AUD" + '.csv'
  filepath = PATH + filename
  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "brand","identifier_exists", "google_product_category", "product_type", "adwords_grouping","adwords_labels", "image_link", "condition", "availability", "gender", "age_group", "size", "color", "link", "mobile_link", "custom_label_0", "custom_label_1", "price", "shipping(price)", "shipping_weight", "custom_label_2", "promotion_id", "custom_label_3", "custom_label_4"]
  end
  #----UK--------
  filename = "mirraw_shopping_feed_GBP" + '.csv'
  filepath = PATH + filename
  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "brand","identifier_exists", "google_product_category", "product_type", "adwords_grouping","adwords_labels", "image_link", "condition", "availability", "gender", "age_group", "size", "color", "link", "mobile_link", "custom_label_0", "custom_label_1", "price", "shipping(price)", "shipping_weight", "custom_label_2", "promotion_id", "custom_label_3", "custom_label_4"]
  end
  #----UAE--------
  conversion_rate_ae = CurrencyConvert.where(:country_code => 'AE').first.rate
  #----Netherlands--------
  conversion_rate_nl = CurrencyConvert.where(:country_code => 'NL').first.rate
  #----South Africa--------
  conversion_rate_za = CurrencyConvert.where(:country_code => 'ZA').first.rate
  #----Italy--------
  conversion_rate_it = CurrencyConvert.where(:country_code => 'IT').first.rate
  #----------Norway------------
  conversion_rate_no = CurrencyConvert.where(:country_code => "NO").first.rate
  #----------Portugal------------
  conversion_rate_pt = CurrencyConvert.where(:country_code => "PT").first.rate
  #----------Sweden------------
  conversion_rate_se = CurrencyConvert.where(:country_code => "SE").first.rate
  #----------Belgium------------
  conversion_rate_be = CurrencyConvert.where(:country_code => "BE").first.rate
  #----------Ireland------------
  conversion_rate_ie = CurrencyConvert.where(:country_code => "IE").first.rate
  #----------Spain------------
  conversion_rate_es = CurrencyConvert.where(:country_code => "ES").first.rate

  #----Switzerland--------
  filename = "mirraw_feed_CH" + '.csv'
  filepath = PATH + filename
  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "brand","identifier_exists", "google_product_category", "product_type", "adwords_grouping","adwords_labels", "image_link", "condition", "availability", "gender", "age_group", "size", "color", "link", "mobile_link", "custom_label_0", "custom_label_1", "price", "shipping(price)", "shipping_weight", "custom_label_2", "promotion_id", "custom_label_3", "custom_label_4"]
  end
  conversion_rate_ch = CurrencyConvert.where(:country_code => 'CH').first.rate
  #----France--------
  filename = "mirraw_feed_FR" + '.csv'
  filepath = PATH + filename
  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "brand","identifier_exists", "google_product_category", "product_type", "adwords_grouping","adwords_labels", "image_link", "condition", "availability", "gender", "age_group", "size", "color", "link", "mobile_link", "custom_label_0", "custom_label_1", "price", "shipping(price)", "shipping_weight", "custom_label_2", "promotion_id", "custom_label_3", "custom_label_4"]
  end
  conversion_rate_fr = CurrencyConvert.where(:country_code => 'FR').first.rate
  #----Singapore--------
  filename = "mirraw_feed_SG" + '.csv'
  filepath = PATH + filename
  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "brand","identifier_exists", "google_product_category", "product_type", "adwords_grouping","adwords_labels", "image_link", "condition", "availability", "gender", "age_group", "size", "color", "link", "mobile_link", "custom_label_0", "custom_label_1", "price", "shipping(price)", "shipping_weight", "custom_label_2", "promotion_id", "custom_label_3", "custom_label_4"]
  end
  conversion_rate_sg = CurrencyConvert.where(:country_code => 'SG').first.rate
  #----NewZealand--------
  filename = "mirraw_feed_NZ" + '.csv'
  filepath = PATH + filename
  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "brand","identifier_exists", "google_product_category", "product_type", "adwords_grouping","adwords_labels", "image_link", "condition", "availability", "gender", "age_group", "size", "color", "link", "mobile_link", "custom_label_0", "custom_label_1", "price", "shipping(price)", "shipping_weight", "custom_label_2", "promotion_id", "custom_label_3", "custom_label_4"]
  end
  conversion_rate_nz = CurrencyConvert.where(:country_code => 'NZ').first.rate
  #----Germany--------
  filename = "mirraw_feed_DE" + '.csv'
  filepath = PATH + filename
  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "brand","identifier_exists", "google_product_category", "product_type", "adwords_grouping","adwords_labels", "image_link", "condition", "availability", "gender", "age_group", "size", "color", "link", "mobile_link", "custom_label_0", "custom_label_1", "price", "shipping(price)", "shipping_weight", "custom_label_2", "promotion_id", "custom_label_3", "custom_label_4"]
  end
  conversion_rate_de = CurrencyConvert.where(:country_code => 'DE').first.rate
  countries = Country.where(iso3166_alpha2: ['US', 'AU', 'GB', 'CA', 'AE', 'NL', 'ZA', 'IT', 'NO', 'PT', 'SE', 'BE', 'IE', 'ES', 'CH', 'FR', 'SG', 'NZ', 'DE', 'IN']).collect do |country|
    [country.iso3166_alpha2, country]
  end.to_h

  puts "Selecting Designs."

  black_listed_categories = SystemConstant.get('google_feed_black_listed_categories').to_s.split(',').collect(&:strip).to_set
  preload_arr = [:categories, :designer, :images, :custom_color_property_values]
  preload_arr.push(:dynamic_prices) if DYNAMIC_PRICE_ENABLED
  bmgnx_hash = PromotionPipeLine.bmgnx_promotion_country_lable_hash
  Design.published.not_banned_in_catalog(:international).
  select("DISTINCT on (designs.id) designs.id, designs.title, designs.buy_get_free ,designs.designer_id,
  designs.published, designs.state, designs.designable_id, designs.description, designs.price,
  designs.discount_percent, designs.cached_slug, designs.ignore_in_google_feed, designs.sell_count,
  designs.created_at, designs.specification, designs.dynamic_pricing, designs.average_rating,
  case when line_items.id is null then 0 else 1 end as any_line_items_30_days").preload(preload_arr).
  joins([:designer]).joins("left outer join line_items on
    line_items.design_id = designs.id and line_items.created_at > CURRENT_DATE - INTERVAL '1 months'").
  where('designs.ignore_in_google_feed in (?)', [0, 2]).
  active_designer_designs.find_in_batches(batch_size: 500) do |grp|
    next if grp.blank?
    puts "Iterating Designs."
    data_buffer=[]

    grp.each do |design|
      category_parent = ''
      available_categories = design.categories.to_a.collect(&:name)
      category = (available_categories.find{|cat| black_listed_categories.exclude? cat} || available_categories.first).to_s
      design_category = category.gsub('-', ' ').camelize
      if (gem_category = (design.categories.collect(&:name) & gemstone_category)).present?
        category_parent = "gemstones"
        category = gem_category[0]
      elsif kids_category.include?(category)
        category = "kids"
      elsif mens_category.include?(category)
        category = "men"
      elsif islamic_clothing_category.include?(category)
        category = 'islamic-clothing'
      elsif sarees_category.include?(category)
        category = "sarees"
      elsif salwar_category.include?(category)
        category = "salwar-kameez"
      elsif earring_category.include?(category)
        category = "earrings"
      elsif lehengas_category.include?(category)
        category = "lehengas"
      elsif jewellery_category.include?(category)
        category_parent = "jewellery"
      elsif rakhi_category.include?(category)
        category = "rakhi"
      elsif blouse_category.include?(category)
        category = "blouse"
      elsif kurtas_category.include?(category)
        category = "kurtas-and-kurtis"
      end

      color_property_hash={}
      design.custom_color_property_values.each do |property|
        color_property_hash[property.property_id]||= property.p_name
      end

      google_product_category = ''
      mirraw_product_category = ''

      if category == "earrings"
        google_product_category = "Apparel & Accessories > Jewelry > Earrings"
        mirraw_product_category = "Jewellery > Earrings"
      elsif category == 'men'
        google_product_category = "Apparel & Accessories"
        mirraw_product_category = "Men"
      elsif category == 'islamic-clothing'
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Islamic Clothing"
      elsif category == "bollywood-sarees"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Sarees > Bollywood Sarees"
      elsif category == "sarees"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Sarees"
        color = color_property_hash[45]
      elsif category == "necklace-sets" || category == "necklace" || category == "mangalsutra" || category == "necklaces"
        google_product_category = "Apparel & Accessories > Jewelry > Necklaces"
        mirraw_product_category = "Jewellery > Necklace Sets"
      elsif category == "kurtas-and-kurtis"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Kurtas & Kurtis"
      elsif category == "salwar-kameez"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Salwar Kameez & Suits"
        color = color_property_hash[46]
      elsif category == 'blouse'
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Women > Blouse"
      elsif category == "kids"
        google_product_category = "Apparel & Accessories > Clothing"
        mirraw_product_category = "Apparel > Clothing > Kids"
      elsif category == "anklets"
        google_product_category = "Apparel & Accessories > Jewelry > Anklets"
        mirraw_product_category = "Jewellery > Anklets"
      elsif category == "bangles-and-bracelets"
        google_product_category = "Apparel & Accessories > Jewelry > Bracelets"
        mirraw_product_category = "Jewellery > Bangles & Bracelets"
      elsif category == "pendants"
        google_product_category = "Apparel & Accessories > Jewelry > Charms & Pendants"
        mirraw_product_category = "Jewellery > Pendants"
      elsif category == "rings"
        google_product_category = "Apparel & Accessories > Jewelry > Rings"
        mirraw_product_category = "Jewellery > Rings"
      elsif category == "maang-tikka"
        google_product_category = "Apparel & Accessories > Jewelry > Body Jewelry"
        mirraw_product_category = "Jewellery > Maang Tikka"
      elsif category == "bags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
        mirraw_product_category = "Bags"
      elsif category == "rakhi"
        google_product_category = "Apparel & Accessories > Clothing Accessories > Wristbands"
        mirraw_product_category = "Rakhi"
      elsif category == "lehengas"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Lehengas"
      elsif category == "tote-bags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Tote Handbags"
        mirraw_product_category = "Apparel > Bags > Tote Bags"
      elsif category == "wallets"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
        mirraw_product_category = "Apparel > Bags > Wallets"
      elsif category == "clutches"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Clutches & Special Occasion Bags"
        mirraw_product_category = "Apparel > Bags > Clutches"
      elsif category == "sling-bags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Cross-Body Handbags"
        mirraw_product_category = "Apparel > Bags > Sling Bags"
      elsif category == "handbags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags"
        mirraw_product_category = "Apparel > Bags > Handbags"
      elsif category == "potli-bags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags"
        mirraw_product_category = "Apparel > Bags > Handbags > Potli Bags"
      elsif category == "backpacks"
        google_product_category = "Luggage & Bags > Backpacks"
        mirraw_product_category = "Apparel > Bags > Backpacks"
      elsif category == "leggings"
        google_product_category = "Apparel & Accessories > Clothing > Pants > Leggings"
        mirraw_product_category = "Apparel > Women > Leggings"
      elsif category == "stole-and-dupattas"
        google_product_category = "Apparel & Accessories > Clothing Accessories > Scarves & Shawls"
        mirraw_product_category = "Apparel > Stoles And Dupattas"
      elsif category == "tops"
        google_product_category = "Apparel & Accessories > Clothing > Shirts & Tops"
        mirraw_product_category = "Apparel > Women > Tops"
      elsif category == "skirts"
        google_product_category = "Apparel & Accessories > Clothing > Skirts"
        mirraw_product_category = "Apparel > Women > Skirts"
      elsif category == "hair-accessories"
        google_product_category = "Apparel & Accessories > Clothing Accessories > Hair Accessories"
        mirraw_product_category = "Apparel > Women > Jewellery > Hair Accessories"
      elsif category == "wall-decals"
        google_product_category = "Home & Garden > Decor > Wall & Window Decals"
        mirraw_product_category = "Home Decor > Wall Decals"
      elsif category == 'cushion-covers'
        google_product_category = "Home & Garden > Decor > Chair & Sofa Cushions"
        mirraw_product_category = "Home Decor > Cushion Covers"
      elsif category == "wall-clocks"
        google_product_category = "Home & Garden > Decor > Clocks > Wall Clocks"
        mirraw_product_category = "Home Decor > Wall Clocks"
      elsif category == "bed-sheets"
        google_product_category = "Home & Garden > Linens & Bedding > Bedding > Bed Sheets"
        mirraw_product_category = "Home Furnishing > Bed Sheets"
      elsif category == "pillow-covers"
        google_product_category = "Home & Garden > Linens & Bedding > Bedding > Pillows"
        mirraw_product_category = "Home Furnishing > Pillows"
      elsif category == "table-lamps"
        google_product_category = "Home & Garden > Lighting > Lamps > Table Lamps"
        mirraw_product_category = "Home Decor > Table Lamps"
      elsif category == "tapestries"
        google_product_category = "Home & Garden > Decor > Artwork > Decorative Tapestries"
        mirraw_product_category = "Home Furnishing > Tapestries"
      elsif category == "curtains"
        google_product_category = "Home & Garden > Decor > Window Treatments > Curtains & Drapes"
        mirraw_product_category = "Home Furnishing > Curtains"
      elsif category == "laptop-skins"
        google_product_category = "Luggage & Bags > Business Bags > Electronics Bags & Cases > Computer Skins & Sleeves"
        mirraw_product_category = "Home Decor > Laptop Skins"
      elsif category == "tunics"
        google_product_category = "Apparel & Accessories > Clothing > Shirts & Tops"
        mirraw_product_category = "Women > Tunics"
      elsif category_parent == "jewellery"
        google_product_category = "Apparel & Accessories > Jewelry"
        mirraw_product_category = "Jewellery"
      elsif category_parent == "gemstones"
        category_titleized = category.titleize
        google_product_category = "Arts & Entertainment > Hobbies & Creative Arts > Arts & Crafts > Art & Crafting Materials > Embellishments & Trims > Loose Stones"
        mirraw_product_category = "Jewellery > #{category_titleized}"
      elsif category == "bras"
        google_product_category = "Apparel & Accessories > Clothing > Underwear & Socks > Bras"
        mirraw_product_category = "Other Apparel > Lingerie > Bras"
      elsif category == "sleepwear"
        google_product_category = "Apparel & Accessories > Clothing > Sleepwear & Loungewear"
        mirraw_product_category = "Other Apparel > Lingerie > Sleepwear"
      elsif category == "panties"
        google_product_category = "Apparel & Accessories > Clothing > Underwear & Socks > Underwear"
        mirraw_product_category = "Other Apparel > Lingerie > Panties"
      elsif category == "quilts"
        google_product_category = "Home & Garden > Linens & Bedding > Bedding > Quilts & Comforters"
        mirraw_product_category = "Home Decor > Quilts"
      elsif category == "duvet-covers"
        google_product_category = "Home & Garden > Linens & Bedding > Bedding > Duvet Covers"
        mirraw_product_category = "Home Decor > Duvet Covers"
      elsif category == "harmonium"
        google_product_category = "Arts & Entertainment > Hobbies & Creative Arts > Musical Instruments"
        mirraw_product_category = "Musical Instruments & Accessories > Instruments > Harmonium"
      elsif category == "tanpura"
        google_product_category = "Arts & Entertainment > Hobbies & Creative Arts > Musical Instruments"
        mirraw_product_category = "Musical Instruments & Accessories > Instruments > Tanpura"
      elsif category == "tabla"
        google_product_category = "Arts & Entertainment > Hobbies & Creative Arts > Musical Instruments > Percussion > Hand Percussion > Hand Drums > Tablas"
        mirraw_product_category = "Musical Instruments & Accessories > Instruments > Tabla"
      elsif category == "sitar"
        google_product_category = "Arts & Entertainment > Hobbies & Creative Arts > Musical Instruments"
        mirraw_product_category = "Musical Instruments & Accessories > Instruments > Sitar"
      elsif category == "santoor"
        google_product_category = "Arts & Entertainment > Hobbies & Creative Arts > Musical Instruments"
        mirraw_product_category = "Musical Instruments & Accessories > Instruments > Santoor"
      elsif category == "shruti-box"
        google_product_category = "Arts & Entertainment > Hobbies & Creative Arts > Musical Instruments"
        mirraw_product_category = "Musical Instruments & Accessories > Instruments > Shruti Box"
      elsif category == "dhol"
        google_product_category = "Arts & Entertainment > Hobbies & Creative Arts > Musical Instruments"
        mirraw_product_category = "Musical Instruments & Accessories > Instruments > Dhol"
      elsif category == "swarmandal"
        google_product_category = "Arts & Entertainment > Hobbies & Creative Arts > Musical Instruments"
        mirraw_product_category = "Musical Instruments & Accessories > Instruments > Swarmandal"
      elsif category == "sarangi"
        google_product_category = "Arts & Entertainment > Hobbies & Creative Arts > Musical Instruments"
        mirraw_product_category = "Musical Instruments & Accessories > Instruments > Sarangi"
      end
      color||= color_property_hash[20] || 'multicolor'

      if design && design.title && design.images.present? && design.cached_slug.present? && design.designer.present? && !design.master_image.photo_processing?
        url = "https://www.mirraw.com/designers/" + design.designer.cached_slug + "/designs/" + design.cached_slug
        mobile_url = "https://m.mirraw.com/designers/" + design.designer.cached_slug + "/designs/" + design.cached_slug
        if %w(sarees salwar-kameez earrings kids blouse kurtas-and-kurtis islamic-clothing men).include?(category) && category.casecmp(design_category.gsub(' ', '-')) != 0
          mirraw_product_category1 = mirraw_product_category + ' > ' + design_category
        else
          mirraw_product_category1 = mirraw_product_category
        end

        # Using custom labels
        if design.created_at > 7.days.ago
          custom_label_0 = "listing0"
        elsif design.created_at > 30.days.ago
          custom_label_0 = "listing1"
        elsif design.created_at > 2.months.ago
          custom_label_0 = "listing2"
        elsif design.created_at > 6.months.ago
          custom_label_0 = "listing3"
        else
          custom_label_0 = "listing4"
        end

        if design.sell_count.to_i > 100
          custom_label_1 = "sell0"
        elsif design.sell_count.to_i > 10
          custom_label_1 = "sell1"
        elsif design.sell_count.to_i > 1
          custom_label_1 = "sell2"
        elsif design.sell_count.to_i == 1
          custom_label_1 = "sell3"
        else
          custom_label_1 = "sell4"
        end

        custom_label_4 = ''
        if design.average_rating.nil?
          custom_label_4 = "unrated products"
        elsif design.average_rating >= 4.0
          custom_label_4 = "4 star rated products"
        elsif design.average_rating >= 3.0
          custom_label_4 = "3 star rated products"
        elsif design.average_rating >= 2.0
          custom_label_4 = "2 star rated products"
        elsif design.average_rating >= 1.0
          custom_label_4 = "1 star rated products"
        elsif design.average_rating > 0.0
          custom_label_4 = "0 star rated products"
        else
          custom_label_4 = "unrated products"
        end

        design_description = ''
        if design.specification.present?
          design_description = design.specification.gsub(/\r/, " ").gsub(/\n/, " ")
        end

        if design.description.present?
          design_description += " " + design.description.gsub(/\r/, " ").gsub(/\n/, " ")
        end

        design_description = design_description.presence || Feed.get_default_description_for(category_parent.presence || category)
        weight = design.approx_weight
        buy_get_free = design.buy_get_free == 1
        age_group = category == 'kids' ? 'kids' : 'adult'
        # to trigger saree blouses search terms in blouses pla campaign
        title_to_append = category == 'blouse' ? "Saree #{design_category}" : design_category
        # puts design.title

        data = {
          design_id: design.id.to_s,
          common_data: [
          design.title.gsub(',', ':') + ' - ' + title_to_append,
          design_description.gsub(',',':').force_encoding("UTF-8"),
          design.designer.name,
          "no",
          google_product_category,
          mirraw_product_category1,
          category,
          design_category,
          design.master_image.photo.url(:original).to_s,
          "new",
          "in stock",
          "Female",
          age_group,
          ],
          optional_common_data:["one size",color],
          url: url,
          mobile_url: mobile_url,
          custom_label_0_1: [custom_label_0,custom_label_1],
          custom_label_3: design.any_line_items_30_days,
          custom_label_4: [custom_label_4]
        }
        #-------------India---------------------------------
        design_price = design.effective_price_for_country('IN', RETURN_NORMAL)
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end
        data[:IN]=[design_price,custom_label_2]

        #-------------US---------------------------------
        design_price = design.effective_price_for_country('US',RETURN_SCALED)
        shipping = weight > 0 ? Country.shipping_cost_for(weight, countries['US']) : 0
        buy_get_free_label = buy_get_free && bmgnx_hash.present? ? (bmgnx_hash.find{|countries, label| countries.to_s.include?('US')}.try(:second) || bmgnx_hash[''] || bmgnx_hash[nil]) : nil
        buy_get_free_label += '_US' if buy_get_free_label.present?
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end
        shipping_charge = (shipping/conversion_rate_us).round(2).to_s + " USD"
        data[:US]=[(design_price/conversion_rate_us).round(2), "US::0:", shipping_charge, weight, custom_label_2, buy_get_free_label]

        #-------------------Australia--------------------
        design_price = design.effective_price_for_country('AU',RETURN_SCALED)
        shipping = weight > 0 ? Country.shipping_cost_for(weight, countries['AU']) : 0
        buy_get_free_label = buy_get_free && bmgnx_hash.present? ? (bmgnx_hash.find{|countries, label| countries.to_s.include?('AU')}.try(:second) || bmgnx_hash[''] || bmgnx_hash[nil]) : nil
        buy_get_free_label += '_AU' if buy_get_free_label.present?
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end
        shipping_charge = (shipping/conversion_rate_aud).round(2).to_s + " AUD"
        data[:AU]=[(design_price/conversion_rate_aud).round(2), shipping_charge, weight, custom_label_2, buy_get_free_label]

        #-------------------UK-----------------------------
        design_price = design.effective_price_for_country('GB',RETURN_SCALED)
        shipping = weight > 0 ? Country.shipping_cost_for(weight, countries['GB']) : 0
        buy_get_free_label = buy_get_free && bmgnx_hash.present? ? (bmgnx_hash.find{|countries, label| countries.to_s.include?('GB')}.try(:second) || bmgnx_hash[''] || bmgnx_hash[nil]) : nil
        buy_get_free_label += '_GB' if buy_get_free_label.present?
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end
        shipping_charge = (shipping/conversion_rate_gbp).round(2).to_s + " " + "GBP"
        data[:GB]=[(design_price/conversion_rate_gbp).round(2), shipping_charge, weight, custom_label_2, buy_get_free_label]

        #-------------------Canada-----------------------------
        design_price = design.effective_price_for_country('CA',RETURN_SCALED)
        shipping = weight > 0 ? Country.shipping_cost_for(weight, countries['CA']) : 0
        buy_get_free_label = buy_get_free && bmgnx_hash.present? ? (bmgnx_hash.find{|countries, label| countries.to_s.include?('CA')}.try(:second) || bmgnx_hash[''] || bmgnx_hash[nil]) : nil
        buy_get_free_label += '_CA' if buy_get_free_label.present?
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end
        shipping_charge = (shipping/conversion_rate_cad).round(2).to_s + " " + "CAD"
        data[:CA]=[(design_price/conversion_rate_cad).round(2), shipping_charge, weight, custom_label_2, buy_get_free_label]

        #-------------------Switzerland-----------------------------
        design_price = design.effective_price_for_country('CH',RETURN_SCALED)
        shipping = weight > 0 ? Country.shipping_cost_for(weight, countries['CH']) : 0
        buy_get_free_label = buy_get_free && bmgnx_hash.present? ? (bmgnx_hash.find{|countries, label| countries.to_s.include?('CH')}.try(:second) || bmgnx_hash[''] || bmgnx_hash[nil]) : nil
        buy_get_free_label += '_CH' if buy_get_free_label.present?
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end
        shipping_charge = (shipping/conversion_rate_ch).round(2).to_s + " " + "CHF"
        data[:CH]=[(design_price/conversion_rate_ch).round(2), shipping_charge, weight, custom_label_2, buy_get_free_label]

        #-------------------France-----------------------------
        design_price = design.effective_price_for_country('FR',RETURN_SCALED)
        shipping = weight > 0 ? Country.shipping_cost_for(weight, countries['FR']) : 0
        buy_get_free_label = buy_get_free && bmgnx_hash.present? ? (bmgnx_hash.find{|countries, label| countries.to_s.include?('FR')}.try(:second) || bmgnx_hash[''] || bmgnx_hash[nil]) : nil
        buy_get_free_label += '_FR' if buy_get_free_label.present?
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end
        shipping_charge = (shipping/conversion_rate_fr).round(2).to_s + " " + "EUR"
        data[:FR]=[(design_price/conversion_rate_fr).round(2), shipping_charge, weight, custom_label_2, buy_get_free_label]

        #-------------------Singapore-----------------------------
        design_price = design.effective_price_for_country('SG',RETURN_SCALED)
        shipping = weight > 0 ? Country.shipping_cost_for(weight, countries['SG']) : 0
        buy_get_free_label = buy_get_free && bmgnx_hash.present? ? (bmgnx_hash.find{|countries, label| countries.to_s.include?('SG')}.try(:second) || bmgnx_hash[''] || bmgnx_hash[nil]) : nil
        buy_get_free_label += '_SG' if buy_get_free_label.present?
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end
        shipping_charge = (shipping/conversion_rate_sg).round(2).to_s + " " + "SGD"
        data[:SG]=[(design_price/conversion_rate_sg).round(2), shipping_charge, weight, custom_label_2, buy_get_free_label]
        #-------------------UAE-----------------------------
        design_price = design.effective_price_for_country('AE',RETURN_SCALED)
        shipping = weight > 0 ? Country.shipping_cost_for(weight, countries['AE']) : 0
        buy_get_free_label = buy_get_free && bmgnx_hash.present? ? (bmgnx_hash.find{|countries, label| countries.to_s.include?('AE')}.try(:second) || bmgnx_hash[''] || bmgnx_hash[nil]) : nil
        buy_get_free_label += '_AE' if buy_get_free_label.present?
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end
        shipping_charge = (shipping/conversion_rate_ae).round(2).to_s + " " + "AED"
        data[:AE]=[(design_price/conversion_rate_ae).round(2), shipping_charge, weight, custom_label_2, buy_get_free_label]
        #-------------------Netherlands-----------------------------
        design_price = design.effective_price_for_country('NL',RETURN_SCALED)
        shipping = weight > 0 ? Country.shipping_cost_for(weight, countries['NL']) : 0
        buy_get_free_label = buy_get_free && bmgnx_hash.present? ? (bmgnx_hash.find{|countries, label| countries.to_s.include?('NL')}.try(:second) || bmgnx_hash[''] || bmgnx_hash[nil]) : nil
        buy_get_free_label += '_NL' if buy_get_free_label.present?
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end
        shipping_charge = (shipping/conversion_rate_nl).round(2).to_s + " " + "EUR"
        data[:NL]=[(design_price/conversion_rate_nl).round(2), shipping_charge, weight, custom_label_2, buy_get_free_label]
        #-------------------South Africa-----------------------------
        design_price = design.effective_price_for_country('ZA',RETURN_SCALED)
        shipping = weight > 0 ? Country.shipping_cost_for(weight, countries['ZA']) : 0
        buy_get_free_label = buy_get_free && bmgnx_hash.present? ? (bmgnx_hash.find{|countries, label| countries.to_s.include?('ZA')}.try(:second) || bmgnx_hash[''] || bmgnx_hash[nil]) : nil
        buy_get_free_label += '_ZA' if buy_get_free_label.present?
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end
        shipping_charge = (shipping/conversion_rate_za).round(2).to_s + " " + "ZAR"
        data[:ZA]=[(design_price/conversion_rate_za).round(2), shipping_charge, weight, custom_label_2, buy_get_free_label]
        #-------------------Italy-----------------------------
        design_price = design.effective_price_for_country('IT',RETURN_SCALED)
        shipping = weight > 0 ? Country.shipping_cost_for(weight, countries['IT']) : 0
        buy_get_free_label = buy_get_free && bmgnx_hash.present? ? (bmgnx_hash.find{|countries, label| countries.to_s.include?('IT')}.try(:second) || bmgnx_hash[''] || bmgnx_hash[nil]) : nil
        buy_get_free_label += '_IT' if buy_get_free_label.present?
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end
        shipping_charge = (shipping/conversion_rate_it).round(2).to_s + " " + "EUR"
        data[:IT]=[(design_price/conversion_rate_it).round(2), shipping_charge, weight, custom_label_2, buy_get_free_label]
        #-------------------NewZealand-----------------------------
        design_price = design.effective_price_for_country('NZ',RETURN_SCALED)
        shipping = weight > 0 ? Country.shipping_cost_for(weight, countries['NZ']) : 0
        buy_get_free_label = buy_get_free && bmgnx_hash.present? ? (bmgnx_hash.find{|countries, label| countries.to_s.include?('NZ')}.try(:second) || bmgnx_hash[''] || bmgnx_hash[nil]) : nil
        buy_get_free_label += '_NZ' if buy_get_free_label.present?
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end
        shipping_charge = (shipping/conversion_rate_nz).round(2).to_s + " " + "NZD"
        data[:NZ]=[(design_price/conversion_rate_nz).round(2), shipping_charge, weight, custom_label_2, buy_get_free_label]
        #-------------------Germany-----------------------------
        design_price = design.effective_price_for_country('DE',RETURN_SCALED)
        shipping = weight > 0 ? Country.shipping_cost_for(weight, countries['DE']) : 0
        buy_get_free_label = buy_get_free && bmgnx_hash.present? ? (bmgnx_hash.find{|countries, label| countries.to_s.include?('DE')}.try(:second) || bmgnx_hash[''] || bmgnx_hash[nil]) : nil
        buy_get_free_label += '_DE' if buy_get_free_label.present?
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end
        shipping_charge = (shipping/conversion_rate_de).round(2).to_s + " " + "EUR"
        data[:DE]=[(design_price/conversion_rate_de).round(2), shipping_charge, weight, custom_label_2, buy_get_free_label]
        #----------Norway------------
        design_price = design.effective_price_for_country('NO',RETURN_SCALED)
        shipping = weight > 0 ? Country.shipping_cost_for(weight, countries['NO']) : 0
        buy_get_free_label = buy_get_free && bmgnx_hash.present? ? (bmgnx_hash.find{|countries, label| countries.to_s.include?('NO')}.try(:second) || bmgnx_hash[''] || bmgnx_hash[nil]) : nil
        buy_get_free_label += '_NO' if buy_get_free_label.present?
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end
        shipping_charge = (shipping/conversion_rate_no).round(2).to_s + " " + "NOK"
        data[:NO]=[(design_price/conversion_rate_no).round(2), shipping_charge, weight, custom_label_2, buy_get_free_label]
        #----------Portugal------------
        design_price = design.effective_price_for_country('PT',RETURN_SCALED)
        shipping = weight > 0 ? Country.shipping_cost_for(weight, countries['PT']) : 0
        buy_get_free_label = buy_get_free && bmgnx_hash.present? ? (bmgnx_hash.find{|countries, label| countries.to_s.include?('PT')}.try(:second) || bmgnx_hash[''] || bmgnx_hash[nil]) : nil
        buy_get_free_label += '_PT' if buy_get_free_label.present?
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end
        shipping_charge = (shipping/conversion_rate_pt).round(2).to_s + " " + "EUR"
        data[:PT]=[(design_price/conversion_rate_pt).round(2), shipping_charge, weight, custom_label_2, buy_get_free_label]
        #----------Sweden------------
        design_price = design.effective_price_for_country('SE',RETURN_SCALED)
        shipping = weight > 0 ? Country.shipping_cost_for(weight, countries['SE']) : 0
        buy_get_free_label = buy_get_free && bmgnx_hash.present? ? (bmgnx_hash.find{|countries, label| countries.to_s.include?('SE')}.try(:second) || bmgnx_hash[''] || bmgnx_hash[nil]) : nil
        buy_get_free_label += '_SE' if buy_get_free_label.present?
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end
        shipping_charge = (shipping/conversion_rate_se).round(2).to_s + " " + "SEK"
        data[:SE]=[(design_price/conversion_rate_se).round(2), shipping_charge, weight, custom_label_2, buy_get_free_label]
        #----------Belgium------------
        design_price = design.effective_price_for_country('BE',RETURN_SCALED)
        shipping = weight > 0 ? Country.shipping_cost_for(weight, countries['BE']) : 0
        buy_get_free_label = buy_get_free && bmgnx_hash.present? ? (bmgnx_hash.find{|countries, label| countries.to_s.include?('BE')}.try(:second) || bmgnx_hash[''] || bmgnx_hash[nil]) : nil
        buy_get_free_label += '_BE' if buy_get_free_label.present?
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end
        shipping_charge = (shipping/conversion_rate_be).round(2).to_s + " " + "EUR"
        data[:BE]=[(design_price/conversion_rate_be).round(2), shipping_charge, weight, custom_label_2, buy_get_free_label]
        #----------Ireland------------
        design_price = design.effective_price_for_country('IE',RETURN_SCALED)
        shipping = weight > 0 ? Country.shipping_cost_for(weight, countries['IE']) : 0
        buy_get_free_label = buy_get_free && bmgnx_hash.present? ? (bmgnx_hash.find{|countries, label| countries.to_s.include?('IE')}.try(:second) || bmgnx_hash[''] || bmgnx_hash[nil]) : nil
        buy_get_free_label += '_IE' if buy_get_free_label.present?
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end
        shipping_charge = (shipping/conversion_rate_ie).round(2).to_s + " " + "EUR"
        data[:IE]=[(design_price/conversion_rate_ie).round(2), shipping_charge, weight, custom_label_2, buy_get_free_label]
        #----------Spain------------
        design_price = design.effective_price_for_country('ES',RETURN_SCALED)
        shipping = weight > 0 ? Country.shipping_cost_for(weight, countries['ES']) : 0
        buy_get_free_label = buy_get_free && bmgnx_hash.present? ? (bmgnx_hash.find{|countries, label| countries.to_s.include?('ES')}.try(:second) || bmgnx_hash[''] || bmgnx_hash[nil]) : nil
        buy_get_free_label += '_ES' if buy_get_free_label.present?
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end
        shipping_charge = (shipping/conversion_rate_es).round(2).to_s + " " + "EUR"
        data[:ES]=[(design_price/conversion_rate_es).round(2), shipping_charge, weight, custom_label_2, buy_get_free_label]
        data_buffer << data
      end
    end

    puts "Saving..."
    filename = "new_google_feed" + '.csv'
    filepath = PATH + filename
    CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
      data_buffer.each do |data|
        csv << [data[:design_id]] + data[:common_data] + [data[:url] + '?country_code=IN'] + [data[:mobile_url] + '?country_code=IN'] + data[:custom_label_0_1] + data[:IN] + [data[:custom_label_3]] + data[:custom_label_4]
      end
    end

    shopping_countries.except(:GBP,:AUD,:CAD, :NO ,:PT ,:SE ,:BE ,:IE ,:ES).each do |var,v|
      filename = "mirraw_shopping_feed_#{var}" + '.csv'
      filepath = PATH + filename
      CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
        data_buffer.each do |data|
          csv << [data[:design_id]+"_#{v[:currency]}"] + data[:common_data] + data[:optional_common_data] + [data[:url] + "?country_code=#{v[:country]}"] + [data[:mobile_url] + "?country_code=#{v[:country]}"] + data[:custom_label_0_1] + data[v[:country].to_sym] + [data[:custom_label_3]] + data[:custom_label_4]
        end
      end
    end

    shopping_countries.slice(:NO ,:PT ,:SE ,:BE ,:IE ,:ES).each do |var,v|
      filename = "mirraw_shopping_feed_#{var}" + '.csv'
      filepath = PATH + filename
      CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
        data_buffer.each do |data|
          csv << [data[:design_id]+"_#{v[:country]}"] + data[:common_data] + data[:optional_common_data] + [data[:url] + "?country_code=#{v[:country]}"] + [data[:mobile_url] + "?country_code=#{v[:country]}"] + data[:custom_label_0_1] + data[v[:country].to_sym] + [data[:custom_label_3]] + data[:custom_label_4]
        end
      end
    end

    filename = "mirraw_feed_USD" + '.csv'
    filepath = PATH + filename
    CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
      data_buffer.each do |data|
        csv << [data[:design_id]+'_us'] + data[:common_data] + data[:optional_common_data] + [data[:url] + '?country_code=US'] + [data[:mobile_url] + '?country_code=US'] + data[:custom_label_0_1] + data[:US] + [data[:custom_label_3]] + data[:custom_label_4]
      end
    end

    filename = "mirraw_feed_AUD" + '.csv'
    filepath = PATH + filename
    CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
      data_buffer.each do |data|
        csv << [data[:design_id]+'_AUD'] + data[:common_data] + data[:optional_common_data] + [data[:url] + '?country_code=AU'] + [data[:mobile_url] + '?country_code=AU'] + data[:custom_label_0_1] + data[:AU] + [data[:custom_label_3]] + data[:custom_label_4]
      end
    end

    filename = "mirraw_feed_GBP" + '.csv'
    filepath = PATH + filename
    CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
      data_buffer.each do |data|
        csv << [data[:design_id]+'_GBP'] + data[:common_data] + data[:optional_common_data] + [data[:url] + '?country_code=GB'] + [data[:mobile_url] + '?country_code=GB'] + data[:custom_label_0_1] + data[:GB] + [data[:custom_label_3]] + data[:custom_label_4]
      end
    end

    filename = "mirraw_shopping_feed_CAD" + '.csv'
    filepath = PATH + filename
    CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
      data_buffer.each do |data|
        csv << [data[:design_id]+'_CAN'] + data[:common_data] + data[:optional_common_data] + [data[:url] + '?country_code=CA'] + [data[:mobile_url] + '?country_code=CA'] + data[:custom_label_0_1] + data[:CA] + [data[:custom_label_3]] + data[:custom_label_4]
      end
    end
    filename = "mirraw_shopping_feed_AUD" + '.csv'
    filepath = PATH + filename
    CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
      data_buffer.each do |data|
        csv << [data[:design_id]+'_AU'] + data[:common_data] + data[:optional_common_data] + [data[:url] + '?country_code=AU'] + [data[:mobile_url] + '?country_code=AU'] + data[:custom_label_0_1] + data[:AU] + [data[:custom_label_3]] + data[:custom_label_4]
      end
    end

    filename = "mirraw_shopping_feed_GBP" + '.csv'
    filepath = PATH + filename
    CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
      data_buffer.each do |data|
        csv << [data[:design_id]+'_GB'] + data[:common_data] + data[:optional_common_data] + [data[:url] + '?country_code=GB'] + [data[:mobile_url] + '?country_code=GB'] + data[:custom_label_0_1] + data[:GB] + [data[:custom_label_3]] + data[:custom_label_4]
      end
    end

    filename = "mirraw_feed_CAD" + '.csv'
    filepath = PATH + filename
    CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
      data_buffer.each do |data|
        csv << [data[:design_id]+'_CADN'] + data[:common_data] + data[:optional_common_data] + [data[:url] + '?country_code=CA'] + [data[:mobile_url] + '?country_code=CA'] + data[:custom_label_0_1] + data[:CA] + [data[:custom_label_3]] + data[:custom_label_4]
      end
    end

    filename = "mirraw_feed_CH" + '.csv'
    filepath = PATH + filename
    CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
      data_buffer.each do |data|
        csv << [data[:design_id]+'_CH'] + data[:common_data] + data[:optional_common_data] + [data[:url] + '?country_code=CH'] + [data[:mobile_url] + '?country_code=CH'] + data[:custom_label_0_1] + data[:CH] + [data[:custom_label_3]] + data[:custom_label_4]
      end
    end

    filename = "mirraw_feed_FR" + '.csv'
    filepath = PATH + filename
    CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
      data_buffer.each do |data|
        csv << [data[:design_id]+'_FR'] + data[:common_data] + data[:optional_common_data] + [data[:url] + '?country_code=FR'] + [data[:mobile_url] + '?country_code=FR'] + data[:custom_label_0_1] + data[:FR] + [data[:custom_label_3]] + data[:custom_label_4]
      end
    end

    filename = "mirraw_feed_SG" + '.csv'
    filepath = PATH + filename
    CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
      data_buffer.each do |data|
        csv << [data[:design_id]+'_SG'] + data[:common_data] + data[:optional_common_data] + [data[:url] + '?country_code=SG'] + [data[:mobile_url] + '?country_code=SG'] + data[:custom_label_0_1] + data[:SG] + [data[:custom_label_3]] + data[:custom_label_4]
      end
    end

    filename = "mirraw_feed_NZ" + '.csv'
    filepath = PATH + filename
    CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
      data_buffer.each do |data|
        csv << [data[:design_id]+'_NZ'] + data[:common_data] + data[:optional_common_data] + [data[:url] + '?country_code=NZ'] + [data[:mobile_url] + '?country_code=NZ'] + data[:custom_label_0_1] + data[:NZ] + [data[:custom_label_3]] + data[:custom_label_4]
      end
    end

    filename = "mirraw_feed_DE" + '.csv'
    filepath = PATH + filename
    CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
      data_buffer.each do |data|
        csv << [data[:design_id]+'_DE'] + data[:common_data] + data[:optional_common_data] + [data[:url] + '?country_code=DE'] + [data[:mobile_url] + '?country_code=DE'] + data[:custom_label_0_1] + data[:DE] + [data[:custom_label_3]] + data[:custom_label_4]
      end
    end
  end


  if Rails.env.production? || Rails.env.admin?
    # upload India file
    puts "Uploading Feeds."

    filename = "new_google_feed" + '.csv'
    filepath = PATH + filename
    file =  directory.files.create(
      :key    => "googlemerchant/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
    # upload US file
    filename = "mirraw_feed_USD" + '.csv'
    filepath = PATH + filename
    file =  directory.files.create(
      :key    => "googlemerchant/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
    # upload Australia file
    filename = "mirraw_feed_AUD" + '.csv'
    filepath = PATH + filename
    file =  directory.files.create(
      :key    => "googlemerchant/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
    # upload UK file
    filename = "mirraw_feed_GBP" + '.csv'
    filepath = PATH + filename
    file =  directory.files.create(
      :key    => "googlemerchant/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
    # upload Canada file
    filename = "mirraw_feed_CAD" + '.csv'
    filepath = PATH + filename
    file =  directory.files.create(
      :key    => "googlemerchant/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
    shopping_countries.keys.each do |var|
      filename = "mirraw_shopping_feed_#{var}" + '.csv'
      filepath = PATH + filename
      file =  directory.files.create(
        :key    => "googlemerchant/#{filename}",
        :body   => File.open(filepath),
        :public => true
      )
    end
    # upload Switzerland file
    filename = "mirraw_feed_CH" + '.csv'
    filepath = PATH + filename
    file =  directory.files.create(
      :key    => "googlemerchant/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
    # upload France file
    filename = "mirraw_feed_FR" + '.csv'
    filepath = PATH + filename
    file =  directory.files.create(
      :key    => "googlemerchant/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
    # upload Singapore file
    filename = "mirraw_feed_SG" + '.csv'
    filepath = PATH + filename
    file =  directory.files.create(
      :key    => "googlemerchant/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
    # upload NewZealand file
    filename = "mirraw_feed_NZ" + '.csv'
    filepath = PATH + filename
    file =  directory.files.create(
      :key    => "googlemerchant/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
    # upload Germany file
    filename = "mirraw_feed_DE" + '.csv'
    filepath = PATH + filename
    file =  directory.files.create(
      :key    => "googlemerchant/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
  end
end
#################################### Facebook feed for US ###################################
task :upload_facebook_feed => :environment do
  require 'csv'
  connection = Fog::Storage.new({
    :provider                 => 'AWS',
    :aws_access_key_id        => ENV['AWS_ACCESS_KEY_ID'],
    :aws_secret_access_key    => ENV['AWS_SECRET_ACCESS_KEY'],
    :region => "ap-southeast-1"
  })

  if Rails.env.development?
    #for logger
    logger = Logger.new(STDOUT)
    logger.level = Logger::DEBUG
    Rails.logger = ActiveRecord::Base.logger = logger
  end

  # First, a place to contain the glorious details
  directory = connection.directories.new(
    :key    => "mirraw-test"
  )
  PATH = "/tmp/"

  earring_category = Category.find_by_namei('earrings').self_and_descendants.collect{|c| c.name}
  sarees_category = Category.find_by_namei('sarees').self_and_descendants.collect{|c| c.name}
  jewellery_category = Category.find_by_namei('jewellery').self_and_descendants.collect{|c| c.name}
  salwar_category = Category.find_by_namei('salwar-kameez').self_and_descendants.collect{|c| c.name}
  lehengas_category = Category.find_by_namei('lehengas').self_and_descendants.collect{|c| c.name}

  puts "Initialising File names."

  #----United States---
  filename = "mirraw_facebook_feed_US" + '.csv'
  filepath = PATH + filename
  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "google_product_category", "product_type", "link", "image_link", "condition", "availability", "price", "sale_price", "brand", "color", "size"]
  end
  conversion_rate_us = CurrencyConvert.where(:country_code => 'US').first.rate
  Design.country_code = "US"

  puts "Selecting Designs."
  Design.published.select("DISTINCT(designs.id), designs.title, designs.designer_id, designs.published, designs.state, designs.designable_type, designs.designable_id, designs.description, designs.price, designs.discount_percent, designs.cached_slug, designs.ignore_in_google_feed, designs.specification, designs.dynamic_pricing").preload([:categories, :designer, :images, :customized_option_type_values, :custom_color_property_values, :dynamic_price_for_current_country]).joins([:designer]).where('designs.ignore_in_google_feed = ?', 'false').active_designer_designs.find_in_batches(batch_size: 200) do |grp|
    next if grp.blank?
    puts "Iterating Designs."
    data_buffer=[]
    grp.each do |design|
      if design && design.title && design.images.present? && design.cached_slug.present? && design.designer.present? && !design.master_image.photo_processing?
        puts design.id.to_s
        category_parent = ''
        category = design.categories.first.try(:name)
        design_category = category.to_s.camelize
        if sarees_category.include?(category)
          category = "sarees"
        elsif salwar_category.include?(category)
          category = "salwar-kameez"
        elsif earring_category.include?(category)
          category = "earrings"
        elsif lehengas_category.include?(category)
          category = "lehengas"
        elsif jewellery_category.include?(category)
          category_parent = "jewellery"
        end

        color_property_hash={}
        design.custom_color_property_values.each do |property|
          color_property_hash[property.property_id]||= property.p_name
        end

        google_product_category = ''
        mirraw_product_category = ''

        if category == "earrings"
          google_product_category = "Apparel & Accessories > Jewelry > Earrings"
          mirraw_product_category = "Jewellery > Earrings"
        elsif category == "bollywood-sarees"
          google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
          mirraw_product_category = "Apparel > Clothing > Sarees > Bollywood Sarees"
        elsif category == "sarees"
          google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
          mirraw_product_category = "Apparel > Clothing > Sarees"
          color = color_property_hash[45]
        elsif category == "necklace-sets" || category == "necklace" || category == "mangalsutra" || category == "necklaces"
          google_product_category = "Apparel & Accessories > Jewelry > Necklaces"
          mirraw_product_category = "Jewellery > Necklace Sets"
        elsif category == "kurtas-and-kurtis"
          google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
          mirraw_product_category = "Apparel > Clothing > Kurtas & Kurtis"
        elsif category == "salwar-kameez"
          google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
          mirraw_product_category = "Apparel > Clothing > Salwar Kameez & Suits"
          color = color_property_hash[46]
        elsif category == "anklets"
          google_product_category = "Apparel & Accessories > Jewelry > Anklets"
          mirraw_product_category = "Jewellery > Anklets"
        elsif category == "bangles-and-bracelets"
          google_product_category = "Apparel & Accessories > Jewelry > Bracelets"
          mirraw_product_category = "Jewellery > Bangles & Bracelets"
        elsif category == "pendants"
          google_product_category = "Apparel & Accessories > Jewelry > Charms & Pendants"
          mirraw_product_category = "Jewellery > Pendants"
        elsif category == "rings"
          google_product_category = "Apparel & Accessories > Jewelry > Rings"
          mirraw_product_category = "Jewellery > Rings"
        elsif category == "maang-tikka"
          google_product_category = "Apparel & Accessories > Jewelry > Body Jewelry"
          mirraw_product_category = "Jewellery > Maang Tikka"
        elsif category == "bags"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
          mirraw_product_category = "Bags"
        elsif category == "rakhi"
          google_product_category = "Apparel & Accessories > Clothing Accessories > Wristbands"
          mirraw_product_category = "Rakhi"
        elsif category == "lehengas"
          google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
          mirraw_product_category = "Apparel > Clothing > Lehengas"
        elsif category == "tote-bags"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Tote Handbags"
          mirraw_product_category = "Apparel > Bags > Tote Bags"
        elsif category == "wallets"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
          mirraw_product_category = "Apparel > Bags > Wallets"
        elsif category == "clutches"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Clutches & Special Occasion Bags"
          mirraw_product_category = "Apparel > Bags > Clutches"
        elsif category == "sling-bags"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Cross-Body Handbags"
          mirraw_product_category = "Apparel > Bags > Sling Bags"
        elsif category == "handbags"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags"
          mirraw_product_category = "Apparel > Bags > Handbags"
        elsif category == "backpacks"
          google_product_category = "Luggage & Bags > Backpacks"
          mirraw_product_category = "Apparel > Bags > Backpacks"
        elsif category == "leggings"
          google_product_category = "Apparel & Accessories > Clothing > Pants > Leggings"
          mirraw_product_category = "Apparel > Women > Leggings"
        elsif category == "stole-and-dupattas"
          google_product_category = "Apparel & Accessories > Clothing Accessories > Scarves & Shawls"
          mirraw_product_category = "Apparel > Stoles And Dupattas"
        elsif category == "tops"
          google_product_category = "Apparel & Accessories > Clothing > Shirts & Tops"
          mirraw_product_category = "Apparel > Women > Tops"
        elsif category == "skirts"
          google_product_category = "Apparel & Accessories > Clothing > Skirts"
          mirraw_product_category = "Apparel > Women > Skirts"
        elsif category == "wall-decals"
          google_product_category = "Home & Garden > Decor > Wall & Window Decals"
          mirraw_product_category = "Home Decor > Wall Decals"
        elsif category == "wall-clocks"
          google_product_category = "Home & Garden > Decor > Clocks > Wall Clocks"
          mirraw_product_category = "Home Decor > Wall Clocks"
        elsif category == "bed-sheets"
          google_product_category = "Home & Garden > Linens & Bedding > Bedding > Bed Sheets"
          mirraw_product_category = "Home Furnishing > Bed Sheets"
        elsif category == "pillow-covers"
          google_product_category = "Home & Garden > Linens & Bedding > Bedding > Pillows"
          mirraw_product_category = "Home Furnishing > Pillows"
        elsif category == "table-lamps"
          google_product_category = "Home & Garden > Lighting > Lamps > Table Lamps"
          mirraw_product_category = "Home Decor > Table Lamps"
        elsif category == "laptop-skins"
          google_product_category = "Luggage & Bags > Business Bags > Electronics Bags & Cases > Computer Skins & Sleeves"
          mirraw_product_category = "Home Decor > Laptop Skins"
        elsif category == "tunics"
          google_product_category = "Apparel & Accessories > Clothing > Shirts & Tops"
          mirraw_product_category = "Women > Tunics"
        elsif category_parent == "jewellery"
          google_product_category = "Apparel & Accessories > Jewelry"
          mirraw_product_category = "Jewellery"
        elsif category == "bridal-sets"
          google_product_category = "Apparel & Accessories > Clothing Accessories > Bridal Accessories"
          mirraw_product_category = "Bridal > Bridal Sets"
        elsif category == "home-furnishing"
          google_product_category = "Home & Garden > Decor"
          mirraw_product_category = "Home Decor > Home Furnishing"
        else
          mirraw_product_category = category.to_s.titleize
        end

        color||= color_property_hash[20] || 'multicolor'

        url = "http://www.mirraw.com/designers/" + design.designer.cached_slug + "/designs/" + design.cached_slug
        if category == "sarees" || category == "salwar-kameez" || category == "earrings"
          product_type = mirraw_product_category + ' > ' + design_category
        else
          product_type = mirraw_product_category
        end

        design_description = ''
        if design.specification.present?
          design_description = design.specification.gsub(/\r/, " ").gsub(/\n/, " ")
        end

        if design.description.present?
          design_description += " " + design.description.gsub(/\r/, " ").gsub(/\n/, " ")
        end
        design_description += ' ' + design.title
        design_size =  design.customized_option_type_values.present? ? design.customized_option_type_values.map { |vary| vary[:name].to_s }.join(',') : 'one size'
        design_category+=":-" if design_category.present?
        #----United States---------
        data_buffer << [design.id.to_s, design.title, design_category.to_s + design_description.downcase, google_product_category, product_type, url, design.master_image.photo.url(:original), "new", "in stock", (design.price/conversion_rate_us).round(2).to_s + ' USD', (design.effective_price/conversion_rate_us).round(2).to_s + ' USD', design.designer.name, color, design_size]
      end
    end
    CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
      data_buffer.each do |data|
        csv << data
      end
    end
  end

  if Rails.env.production?
    puts "Uploading Feeds."
    #----upload united states file---
    filename = "mirraw_facebook_feed_US" + '.csv'
    filepath = PATH + filename
    file =  directory.files.create(
      :key    => "facebook/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
  end
end

#################################### Facebook feed for IN ###################################
task :upload_facebook_feed_india_designers_transaction_rate => :environment do
  require 'csv'
  connection = Fog::Storage.new({
    :provider                 => 'AWS',
    :aws_access_key_id        => ENV['AWS_ACCESS_KEY_ID'],
    :aws_secret_access_key    => ENV['AWS_SECRET_ACCESS_KEY'],
    :region => "ap-southeast-1"
  })

  if Rails.env.development?
    #for logger
    logger = Logger.new(STDOUT)
    logger.level = Logger::DEBUG
    Rails.logger = ActiveRecord::Base.logger = logger
  end

  # First, a place to contain the glorious details
  directory = connection.directories.new(
    :key    => "mirraw-test"
  )
  PATH = "/tmp/"

  earring_category = Category.find_by_namei('earrings').self_and_descendants.collect{|c| c.name}
  sarees_category = Category.find_by_namei('sarees').self_and_descendants.collect{|c| c.name}
  jewellery_category = Category.find_by_namei('jewellery').self_and_descendants.collect{|c| c.name}
  salwar_category = Category.find_by_namei('salwar-kameez').self_and_descendants.collect{|c| c.name}
  lehengas_category = Category.find_by_namei('lehengas').self_and_descendants.collect{|c| c.name}

  puts "Initialising File names."

  #----India---
  filename = "mirraw_facebook_feed_IN" + '.csv'
  filepath = PATH + filename
  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "google_product_category", "product_type", "link", "image_link", "condition", "availability", "price", "sale_price", "brand", "color", "size"]
  end
  conversion_rate_in = CurrencyConvert.where(:country_code => 'IN').first.rate
  Design.country_code = "IN"

  puts "Selecting Designs."
  Design.published.select("DISTINCT(designs.id), designs.title, designs.designer_id, designs.published, designs.state, designs.designable_type, designs.designable_id, designs.description, designs.price, designs.discount_percent, designs.cached_slug, designs.ignore_in_google_feed, designs.specification, designs.dynamic_pricing").preload([:categories, :designer, :images, :customized_option_type_values, :custom_color_property_values, :dynamic_price_for_current_country]).joins([:designer]).where('designs.ignore_in_google_feed = ? and designers.transaction_rate >= ?', 'false',35).active_designer_designs.find_in_batches(batch_size: 200) do |grp|
    next if grp.blank?
    puts "Iterating Designs."
    data_buffer=[]
    grp.each do |design|
      if design && design.title && design.images.present? && design.cached_slug.present? && design.designer.present? && !design.master_image.photo_processing?
        puts design.id.to_s
        category_parent = ''
        category = design.categories.first.try(:name)
        design_category = category.to_s.camelize
        if sarees_category.include?(category)
          category = "sarees"
        elsif salwar_category.include?(category)
          category = "salwar-kameez"
        elsif earring_category.include?(category)
          category = "earrings"
        elsif lehengas_category.include?(category)
          category = "lehengas"
        elsif jewellery_category.include?(category)
          category_parent = "jewellery"
        end

        color_property_hash={}
        design.custom_color_property_values.each do |property|
          color_property_hash[property.property_id]||= property.p_name
        end

        google_product_category = ''
        mirraw_product_category = ''

        if category == "earrings"
          google_product_category = "Apparel & Accessories > Jewelry > Earrings"
          mirraw_product_category = "Jewellery > Earrings"
        elsif category == "bollywood-sarees"
          google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
          mirraw_product_category = "Apparel > Clothing > Sarees > Bollywood Sarees"
        elsif category == "sarees"
          google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
          mirraw_product_category = "Apparel > Clothing > Sarees"
          color = color_property_hash[45]
        elsif category == "necklace-sets" || category == "necklace" || category == "mangalsutra" || category == "necklaces"
          google_product_category = "Apparel & Accessories > Jewelry > Necklaces"
          mirraw_product_category = "Jewellery > Necklace Sets"
        elsif category == "kurtas-and-kurtis"
          google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
          mirraw_product_category = "Apparel > Clothing > Kurtas & Kurtis"
        elsif category == "salwar-kameez"
          google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
          mirraw_product_category = "Apparel > Clothing > Salwar Kameez & Suits"
          color = color_property_hash[46]
        elsif category == "anklets"
          google_product_category = "Apparel & Accessories > Jewelry > Anklets"
          mirraw_product_category = "Jewellery > Anklets"
        elsif category == "bangles-and-bracelets"
          google_product_category = "Apparel & Accessories > Jewelry > Bracelets"
          mirraw_product_category = "Jewellery > Bangles & Bracelets"
        elsif category == "pendants"
          google_product_category = "Apparel & Accessories > Jewelry > Charms & Pendants"
          mirraw_product_category = "Jewellery > Pendants"
        elsif category == "rings"
          google_product_category = "Apparel & Accessories > Jewelry > Rings"
          mirraw_product_category = "Jewellery > Rings"
        elsif category == "maang-tikka"
          google_product_category = "Apparel & Accessories > Jewelry > Body Jewelry"
          mirraw_product_category = "Jewellery > Maang Tikka"
        elsif category == "bags"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
          mirraw_product_category = "Bags"
        elsif category == "rakhi"
          google_product_category = "Apparel & Accessories > Clothing Accessories > Wristbands"
          mirraw_product_category = "Rakhi"
        elsif category == "lehengas"
          google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
          mirraw_product_category = "Apparel > Clothing > Lehengas"
        elsif category == "tote-bags"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Tote Handbags"
          mirraw_product_category = "Apparel > Bags > Tote Bags"
        elsif category == "wallets"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
          mirraw_product_category = "Apparel > Bags > Wallets"
        elsif category == "clutches"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Clutches & Special Occasion Bags"
          mirraw_product_category = "Apparel > Bags > Clutches"
        elsif category == "sling-bags"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Cross-Body Handbags"
          mirraw_product_category = "Apparel > Bags > Sling Bags"
        elsif category == "handbags"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags"
          mirraw_product_category = "Apparel > Bags > Handbags"
        elsif category == "backpacks"
          google_product_category = "Luggage & Bags > Backpacks"
          mirraw_product_category = "Apparel > Bags > Backpacks"
        elsif category == "leggings"
          google_product_category = "Apparel & Accessories > Clothing > Pants > Leggings"
          mirraw_product_category = "Apparel > Women > Leggings"
        elsif category == "stole-and-dupattas"
          google_product_category = "Apparel & Accessories > Clothing Accessories > Scarves & Shawls"
          mirraw_product_category = "Apparel > Stoles And Dupattas"
        elsif category == "tops"
          google_product_category = "Apparel & Accessories > Clothing > Shirts & Tops"
          mirraw_product_category = "Apparel > Women > Tops"
        elsif category == "skirts"
          google_product_category = "Apparel & Accessories > Clothing > Skirts"
          mirraw_product_category = "Apparel > Women > Skirts"
        elsif category == "wall-decals"
          google_product_category = "Home & Garden > Decor > Wall & Window Decals"
          mirraw_product_category = "Home Decor > Wall Decals"
        elsif category == "wall-clocks"
          google_product_category = "Home & Garden > Decor > Clocks > Wall Clocks"
          mirraw_product_category = "Home Decor > Wall Clocks"
        elsif category == "bed-sheets"
          google_product_category = "Home & Garden > Linens & Bedding > Bedding > Bed Sheets"
          mirraw_product_category = "Home Furnishing > Bed Sheets"
        elsif category == "pillow-covers"
          google_product_category = "Home & Garden > Linens & Bedding > Bedding > Pillows"
          mirraw_product_category = "Home Furnishing > Pillows"
        elsif category == "table-lamps"
          google_product_category = "Home & Garden > Lighting > Lamps > Table Lamps"
          mirraw_product_category = "Home Decor > Table Lamps"
        elsif category == "laptop-skins"
          google_product_category = "Luggage & Bags > Business Bags > Electronics Bags & Cases > Computer Skins & Sleeves"
          mirraw_product_category = "Home Decor > Laptop Skins"
        elsif category == "tunics"
          google_product_category = "Apparel & Accessories > Clothing > Shirts & Tops"
          mirraw_product_category = "Women > Tunics"
        elsif category_parent == "jewellery"
          google_product_category = "Apparel & Accessories > Jewelry"
          mirraw_product_category = "Jewellery"
        elsif category == "bridal-sets"
          google_product_category = "Apparel & Accessories > Clothing Accessories > Bridal Accessories"
          mirraw_product_category = "Bridal > Bridal Sets"
        elsif category == "home-furnishing"
          google_product_category = "Home & Garden > Decor"
          mirraw_product_category = "Home Decor > Home Furnishing"
        else
          mirraw_product_category = category.to_s.titleize
        end

        color||= color_property_hash[20] || 'multicolor'

        url = "http://www.mirraw.com/designers/" + design.designer.cached_slug + "/designs/" + design.cached_slug
        if category == "sarees" || category == "salwar-kameez" || category == "earrings"
          product_type = mirraw_product_category + ' > ' + design_category
        else
          product_type = mirraw_product_category
        end

        design_description = ''
        if design.specification.present?
          design_description = design.specification.gsub(/\r/, " ").gsub(/\n/, " ")
        end

        if design.description.present?
          design_description += " " + design.description.gsub(/\r/, " ").gsub(/\n/, " ")
        end
        design_description += ' ' + design.title
        design_size =  design.customized_option_type_values.present? ? design.customized_option_type_values.map { |vary| vary[:name].to_s }.join(',') : 'one size'
        design_category+=":-" if design_category.present?
        #----India---------
        data_buffer << [design.id.to_s, design.title, design_category.to_s + design_description.downcase, google_product_category, product_type, url, design.master_image.photo.url(:original), "new", "in stock", (design.price/conversion_rate_in).round(2).to_s + ' INR', (design.effective_price/conversion_rate_in).round(2).to_s + ' INR', design.designer.name, color, design_size]
      end
    end
    CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
      data_buffer.each do |data|
        csv << data
      end
    end
  end

  if Rails.env.production?
    puts "Uploading Feeds."
    #----upload india file---
    filename = "mirraw_facebook_feed_IN" + '.csv'
    filepath = PATH + filename
    file =  directory.files.create(
      :key    => "facebook/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
  end
end

#################################### Facebook feed for US ###################################
task :upload_facebook_bestsellers_feed => :environment do
  require 'csv'
  connection = Fog::Storage.new({
    :provider                 => 'AWS',
    :aws_access_key_id        => ENV['AWS_ACCESS_KEY_ID'],
    :aws_secret_access_key    => ENV['AWS_SECRET_ACCESS_KEY'],
    :region => "ap-southeast-1"
  })

  if Rails.env.development?
    #for logger
    logger = Logger.new(STDOUT)
    logger.level = Logger::DEBUG
    Rails.logger = ActiveRecord::Base.logger = logger
  end

  # First, a place to contain the glorious details
  directory = connection.directories.new(
    :key    => "mirraw-test"
  )
  PATH = "/tmp/"

  earring_category = Category.find_by_namei('earrings').self_and_descendants.collect{|c| c.name}
  sarees_category = Category.find_by_namei('sarees').self_and_descendants.collect{|c| c.name}
  jewellery_category = Category.find_by_namei('jewellery').self_and_descendants.collect{|c| c.name}
  salwar_category = Category.find_by_namei('salwar-kameez').self_and_descendants.collect{|c| c.name}
  lehengas_category = Category.find_by_namei('lehengas').self_and_descendants.collect{|c| c.name}

  puts "Initialising File names."

  #----United States---
  filename = 'mirraw_facebook_bestsellers_feed_US.csv'
  filepath = PATH + filename
  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "google_product_category", "product_type", "link", "image_link", "condition", "availability", "price", "sale_price", "brand", "color", "size"]
  end
  conversion_rate_us = CurrencyConvert.where(:country_code => 'US').first.rate
  Design.country_code = "US"

  puts "Selecting Designs."
  design_ids = Order.joins(:line_items).where('designer_orders.state in (?) and lower(orders.billing_country) <> ? and line_items.created_at::date > ?',['pending','pickedup','dispatched','completed'],'india',(Date.today - 30.days)).pluck('line_items.design_id').uniq

  design_ids.each_slice(200) do |ids|
    puts "Iterating Designs."
    data_buffer=[]
      Design.select("DISTINCT(designs.id), designs.title, designs.designer_id, designs.published, designs.state, designs.designable_type, designs.designable_id, designs.description, designs.price, designs.discount_percent, designs.cached_slug, designs.ignore_in_google_feed, designs.specification, designs.dynamic_pricing").joins(:designer).where('designs.ignore_in_google_feed = ? and designers.state_machine NOT IN (?) and designs.id in (?)','false',['banned', 'inactive','on_hold'],ids).preload([:categories, :designer, :images, :customized_option_type_values, :custom_color_property_values, :dynamic_price_for_current_country]).joins([:designer]).each do |design|
      if design && design.title && design.images.present? && design.cached_slug.present? && design.designer.present? && !design.master_image.photo_processing?
        puts design.id.to_s
        category_parent = ''
        category = design.categories.first.try(:name)
        design_category = category.to_s.camelize
        if sarees_category.include?(category)
          category = "sarees"
        elsif salwar_category.include?(category)
          category = "salwar-kameez"
        elsif earring_category.include?(category)
          category = "earrings"
        elsif lehengas_category.include?(category)
          category = "lehengas"
        elsif jewellery_category.include?(category)
          category_parent = "jewellery"
        end

        color_property_hash={}
        design.custom_color_property_values.each do |property|
          color_property_hash[property.property_id]||= property.p_name
        end

        google_product_category = ''
        mirraw_product_category = ''

        if category == "earrings"
          google_product_category = "Apparel & Accessories > Jewelry > Earrings"
          mirraw_product_category = "Jewellery > Earrings"
        elsif category == "bollywood-sarees"
          google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
          mirraw_product_category = "Apparel > Clothing > Sarees > Bollywood Sarees"
        elsif category == "sarees"
          google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
          mirraw_product_category = "Apparel > Clothing > Sarees"
          color = color_property_hash[45]
        elsif category == "necklace-sets" || category == "necklace" || category == "mangalsutra" || category == "necklaces"
          google_product_category = "Apparel & Accessories > Jewelry > Necklaces"
          mirraw_product_category = "Jewellery > Necklace Sets"
        elsif category == "kurtas-and-kurtis"
          google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
          mirraw_product_category = "Apparel > Clothing > Kurtas & Kurtis"
        elsif category == "salwar-kameez"
          google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
          mirraw_product_category = "Apparel > Clothing > Salwar Kameez & Suits"
          color = color_property_hash[46]
        elsif category == "anklets"
          google_product_category = "Apparel & Accessories > Jewelry > Anklets"
          mirraw_product_category = "Jewellery > Anklets"
        elsif category == "bangles-and-bracelets"
          google_product_category = "Apparel & Accessories > Jewelry > Bracelets"
          mirraw_product_category = "Jewellery > Bangles & Bracelets"
        elsif category == "pendants"
          google_product_category = "Apparel & Accessories > Jewelry > Charms & Pendants"
          mirraw_product_category = "Jewellery > Pendants"
        elsif category == "rings"
          google_product_category = "Apparel & Accessories > Jewelry > Rings"
          mirraw_product_category = "Jewellery > Rings"
        elsif category == "maang-tikka"
          google_product_category = "Apparel & Accessories > Jewelry > Body Jewelry"
          mirraw_product_category = "Jewellery > Maang Tikka"
        elsif category == "bags"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
          mirraw_product_category = "Bags"
        elsif category == "rakhi"
          google_product_category = "Apparel & Accessories > Clothing Accessories > Wristbands"
          mirraw_product_category = "Rakhi"
        elsif category == "lehengas"
          google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
          mirraw_product_category = "Apparel > Clothing > Lehengas"
        elsif category == "tote-bags"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Tote Handbags"
          mirraw_product_category = "Apparel > Bags > Tote Bags"
        elsif category == "wallets"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
          mirraw_product_category = "Apparel > Bags > Wallets"
        elsif category == "clutches"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Clutches & Special Occasion Bags"
          mirraw_product_category = "Apparel > Bags > Clutches"
        elsif category == "sling-bags"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Cross-Body Handbags"
          mirraw_product_category = "Apparel > Bags > Sling Bags"
        elsif category == "handbags"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags"
          mirraw_product_category = "Apparel > Bags > Handbags"
        elsif category == "backpacks"
          google_product_category = "Luggage & Bags > Backpacks"
          mirraw_product_category = "Apparel > Bags > Backpacks"
        elsif category == "leggings"
          google_product_category = "Apparel & Accessories > Clothing > Pants > Leggings"
          mirraw_product_category = "Apparel > Women > Leggings"
        elsif category == "stole-and-dupattas"
          google_product_category = "Apparel & Accessories > Clothing Accessories > Scarves & Shawls"
          mirraw_product_category = "Apparel > Stoles And Dupattas"
        elsif category == "tops"
          google_product_category = "Apparel & Accessories > Clothing > Shirts & Tops"
          mirraw_product_category = "Apparel > Women > Tops"
        elsif category == "skirts"
          google_product_category = "Apparel & Accessories > Clothing > Skirts"
          mirraw_product_category = "Apparel > Women > Skirts"
        elsif category == "wall-decals"
          google_product_category = "Home & Garden > Decor > Wall & Window Decals"
          mirraw_product_category = "Home Decor > Wall Decals"
        elsif category == "wall-clocks"
          google_product_category = "Home & Garden > Decor > Clocks > Wall Clocks"
          mirraw_product_category = "Home Decor > Wall Clocks"
        elsif category == "bed-sheets"
          google_product_category = "Home & Garden > Linens & Bedding > Bedding > Bed Sheets"
          mirraw_product_category = "Home Furnishing > Bed Sheets"
        elsif category == "pillow-covers"
          google_product_category = "Home & Garden > Linens & Bedding > Bedding > Pillows"
          mirraw_product_category = "Home Furnishing > Pillows"
        elsif category == "table-lamps"
          google_product_category = "Home & Garden > Lighting > Lamps > Table Lamps"
          mirraw_product_category = "Home Decor > Table Lamps"
        elsif category == "laptop-skins"
          google_product_category = "Luggage & Bags > Business Bags > Electronics Bags & Cases > Computer Skins & Sleeves"
          mirraw_product_category = "Home Decor > Laptop Skins"
        elsif category == "tunics"
          google_product_category = "Apparel & Accessories > Clothing > Shirts & Tops"
          mirraw_product_category = "Women > Tunics"
        elsif category_parent == "jewellery"
          google_product_category = "Apparel & Accessories > Jewelry"
          mirraw_product_category = "Jewellery"
        elsif category == "bridal-sets"
          google_product_category = "Apparel & Accessories > Clothing Accessories > Bridal Accessories"
          mirraw_product_category = "Bridal > Bridal Sets"
        elsif category == "home-furnishing"
          google_product_category = "Home & Garden > Decor"
          mirraw_product_category = "Home Decor > Home Furnishing"
        else
          mirraw_product_category = category.to_s.titleize
        end

        color||= color_property_hash[20] || 'multicolor'

        url = "http://www.mirraw.com/designers/" + design.designer.cached_slug + "/designs/" + design.cached_slug
        if category == "sarees" || category == "salwar-kameez" || category == "earrings"
          product_type = mirraw_product_category + ' > ' + design_category
        else
          product_type = mirraw_product_category
        end

        design_description = ''
        if design.specification.present?
          design_description = design.specification.gsub(/\r/, " ").gsub(/\n/, " ")
        end

        if design.description.present?
          design_description += " " + design.description.gsub(/\r/, " ").gsub(/\n/, " ")
        end
        design_description += ' ' + design.title
        design_size =  design.customized_option_type_values.present? ? design.customized_option_type_values.map { |vary| vary[:name].to_s }.join(',') : 'one size'
        design_category+=":-" if design_category.present?
        #----United States---------
        data_buffer << [design.id.to_s, design.title, design_category.to_s + design_description.downcase, google_product_category, product_type, url, design.master_image.photo.url(:original), "new", "in stock", (design.price/conversion_rate_us).round(2).to_s + ' USD', (design.effective_price/conversion_rate_us).round(2).to_s + ' USD', design.designer.name, color, design_size]
      end
    end
    CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
      data_buffer.each do |data|
        csv << data
      end
    end
  end

  if Rails.env.production?
    puts "Uploading Feeds."
    #----upload united states file---
    filename = 'mirraw_facebook_bestsellers_feed_US.csv'
    filepath = PATH + filename
    file =  directory.files.create(
      :key    => "facebook/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
  end
end

###############################India_google_merchant###############################
task :upload_google_merchant_files1 => :environment do
  require 'csv'

  connection = Fog::Storage.new({
    :provider                 => 'AWS',
    :aws_access_key_id        => ENV['AWS_ACCESS_KEY_ID'],
    :aws_secret_access_key    => ENV['AWS_SECRET_ACCESS_KEY'],
    :region => "ap-southeast-1"
  })

  # First, a place to contain the glorious details
  directory = connection.directories.new(
    :key    => "mirraw-test"
  )
  filename = "new_google_feed" + '.csv'

  PATH = "/tmp/"
  filepath = PATH + filename

  Mapping = Hash.new

  earring_category = Category.find_by_namei('earrings').self_and_descendants.collect{|c| c.name}
  sarees_category = Category.find_by_namei('sarees').self_and_descendants.collect{|c| c.name}
  jewellery_category = Category.find_by_namei('jewellery').self_and_descendants.collect{|c| c.name}
  salwar_category = Category.find_by_namei('salwar-kameez').self_and_descendants.collect{|c| c.name}
  lehengas_category = Category.find_by_namei('lehengas').self_and_descendants.collect{|c| c.name}

  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "brand", "google_product_category", "product_type", "adwords_grouping","adwords_labels","link", "image_link", "condition", "availability", "price", "gender", "age_group", "custom_label_0", "custom_label_1", "custom_label_2", "custom_label_3", "custom_label_4"]
    @designs = Design.published.select("DISTINCT(designs.id), designs.title, designs.designer_id, designs.published, designs.state, designs.designable_id, designs.description, designs.price, designs.discount_percent, designs.cached_slug, designs.ignore_in_google_feed, designs.sell_count, designs.created_at, designs.specification").includes([:categories, :designer, :images]).joins([:categories, :designer]).where('designs.ignore_in_google_feed = ?', 'false').active_designer_designs.where('designs.sell_count > 0 OR designs.created_at > ?', 6.months.ago);

    @designs.each do |design|
      category_parent = ''
      category = design.categories.first.name
      if sarees_category.include?(category)
        category = "sarees"
      elsif salwar_category.include?(category)
        category = "salwar-kameez"
      elsif earring_category.include?(category)
        category = "earrings"
      elsif lehengas_category.include?(category)
        category = "lehengas"
      elsif jewellery_category.include?(category)
        category_parent = "jewellery"
      end

      google_product_category = ''
      mirraw_product_category = ''
      if category == "earrings"
        google_product_category = "Apparel & Accessories > Jewelry > Earrings"
        mirraw_product_category = "Jewellery > Earrings"
      elsif category == "bollywood-sarees"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Sarees > Bollywood Sarees"
      elsif category == "sarees"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Sarees"
      elsif category == "necklace-sets" || category == "necklace" || category == "mangalsutra" || category == "necklaces"
        google_product_category = "Apparel & Accessories > Jewelry > Necklaces"
        mirraw_product_category = "Jewellery > Necklace Sets"
      elsif category == "kurtas-and-kurtis"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Kurtas & Kurtis"
      elsif category == "salwar-kameez"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Salwar Kameez & Suits"
      elsif category == "anklets"
        google_product_category = "Apparel & Accessories > Jewelry > Anklets"
        mirraw_product_category = "Jewellery > Anklets"
      elsif category == "bangles-and-bracelets"
        google_product_category = "Apparel & Accessories > Jewelry > Bracelets"
        mirraw_product_category = "Jewellery > Bangles & Bracelets"
      elsif category == "pendants"
        google_product_category = "Apparel & Accessories > Jewelry > Charms & Pendants"
        mirraw_product_category = "Jewellery > Pendants"
      elsif category == "rings"
        google_product_category = "Apparel & Accessories > Jewelry > Rings"
        mirraw_product_category = "Jewellery > Rings"
      elsif category == "maang-tikka"
        google_product_category = "Apparel & Accessories > Jewelry > Body Jewelry"
        mirraw_product_category = "Jewellery > Maang Tikka"
      elsif category == "bags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
        mirraw_product_category = "Bags"
      elsif category == "rakhi"
        google_product_category = "Apparel & Accessories > Clothing Accessories > Wristbands"
        mirraw_product_category = "Rakhi"
      elsif category == "lehengas"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Lehengas"
      elsif category == "tote-bags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Tote Handbags"
        mirraw_product_category = "Apparel > Bags > Tote Bags"
      elsif category == "wallets"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
        mirraw_product_category = "Apparel > Bags > Wallets"
      elsif category == "clutches"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Clutches & Special Occasion Bags"
        mirraw_product_category = "Apparel > Bags > Clutches"
      elsif category == "sling-bags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Cross-Body Handbags"
        mirraw_product_category = "Apparel > Bags > Sling Bags"
      elsif category == "handbags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags"
        mirraw_product_category = "Apparel > Bags > Handbags"
      elsif category == "backpacks"
        google_product_category = "Luggage & Bags > Backpacks"
        mirraw_product_category = "Apparel > Bags > Backpacks"
      elsif category == "leggings"
        google_product_category = "Apparel & Accessories > Clothing > Pants > Leggings"
        mirraw_product_category = "Apparel > Women > Leggings"
      elsif category == "stole-and-dupattas"
        google_product_category = "Apparel & Accessories > Clothing Accessories > Scarves & Shawls"
        mirraw_product_category = "Apparel > Stoles And Dupattas"
      elsif category == "tops"
        google_product_category = "Apparel & Accessories > Clothing > Shirts & Tops"
        mirraw_product_category = "Apparel > Women > Tops"
      elsif category == "skirts"
        google_product_category = "Apparel & Accessories > Clothing > Skirts"
        mirraw_product_category = "Apparel > Women > Skirts"
      elsif category == "wall-decals"
        google_product_category = "Home & Garden > Decor > Wall & Window Decals"
        mirraw_product_category = "Home Decor > Wall Decals"
      elsif category == "wall-clocks"
        google_product_category = "Home & Garden > Decor > Clocks > Wall Clocks"
        mirraw_product_category = "Home Decor > Wall Clocks"
      elsif category == "bed-sheets"
        google_product_category = "Home & Garden > Linens & Bedding > Bedding > Bed Sheets"
        mirraw_product_category = "Home Furnishing > Bed Sheets"
      elsif category == "pillow-covers"
        google_product_category = "Home & Garden > Linens & Bedding > Bedding > Pillows"
        mirraw_product_category = "Home Furnishing > Pillows"
      elsif category == "table-lamps"
        google_product_category = "Home & Garden > Lighting > Lamps > Table Lamps"
        mirraw_product_category = "Home Decor > Table Lamps"
      elsif category == "laptop-skins"
        google_product_category = "Luggage & Bags > Business Bags > Electronics Bags & Cases > Computer Skins & Sleeves"
        mirraw_product_category = "Home Decor > Laptop Skins"
      elsif category == "tunics"
        google_product_category = "Apparel & Accessories > Clothing > Shirts & Tops"
        mirraw_product_category = "Women > Tunics"
      elsif category_parent == "jewellery"
        google_product_category = "Apparel & Accessories > Jewelry"
        mirraw_product_category = "Jewellery"
      end

      if design && design.title && design.images.present? && design.cached_slug.present? && design.designer.present?
        url = "http://www.mirraw.com/designers/" + design.designer.cached_slug + "/designs/" + design.cached_slug + '?country_code=IN'
        design_category = design.categories.first.name.gsub('-', ' ').camelize
        if category == "sarees" || category == "salwar-kameez" || category == "earrings"
          mirraw_product_category1 = mirraw_product_category + ' > ' + design_category
        else
          mirraw_product_category1 = mirraw_product_category
        end

        # Using custom labels

        if design.created_at > 7.days.ago
          custom_label_0 = "listing0"
        elsif design.created_at > 30.days.ago
          custom_label_0 = "listing1"
        elsif design.created_at > 2.months.ago
          custom_label_0 = "listing2"
        elsif design.created_at > 6.months.ago
          custom_label_0 = "listing3"
        else
          custom_label_0 = "listing4"
        end

        if design.sell_count.to_i > 100
          custom_label_1 = "sell0"
        elsif design.sell_count.to_i > 10
          custom_label_1 = "sell1"
        elsif design.sell_count.to_i > 1
          custom_label_1 = "sell2"
        elsif design.sell_count.to_i == 1
          custom_label_1 = "sell3"
        else
          custom_label_1 = "sell4"
        end
        design_price = design.effective_price
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end

        custom_label_3 = ''
        custom_label_4 = ''

        design_description = ''
        if design.specification.present?
          design_description = design.specification.gsub(/\r/, " ").gsub(/\n/, " ")
        end

        if design.description.present?
          design_description += " " + design.description.gsub(/\r/, " ").gsub(/\n/, " ")
        end

        csv << [design.id, design.title.gsub(',', ':') + ' - ' + design_category, design_description.gsub(',',':').force_encoding("UTF-8"), design.designer.name, google_product_category, mirraw_product_category1, category, design_category,url, design.master_image.photo.url(:original), "new", "in stock", design_price, "Female","Adult", custom_label_0, custom_label_1, custom_label_2, custom_label_3, custom_label_4]
      end
    end
    # upload that file
    file =  directory.files.create(
      :key    => "googlemerchant/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
  end
end
################################US_google_merchant_feed######################################
task :upload_google_merchant_us => :environment do
  require 'csv'

  connection = Fog::Storage.new({
    :provider                 => 'AWS',
    :aws_access_key_id        => ENV['AWS_ACCESS_KEY_ID'],
    :aws_secret_access_key    => ENV['AWS_SECRET_ACCESS_KEY'],
    :region => "ap-southeast-1"
  })

  # First, a place to contain the glorious details
  directory = connection.directories.new(
    :key    => "mirraw-test"
  )

  filename = "mirraw_feed_usd" + '.csv'

  PATH = "/tmp/"
  filepath = PATH + filename

  Mapping = Hash.new

  earring_category = Category.find_by_namei('earrings').self_and_descendants.collect{|c| c.name}
  sarees_category = Category.find_by_namei('sarees').self_and_descendants.collect{|c| c.name}
  jewellery_category = Category.find_by_namei('jewellery').self_and_descendants.collect{|c| c.name}
  salwar_category = Category.find_by_namei('salwar-kameez').self_and_descendants.collect{|c| c.name}
  lehengas_category = Category.find_by_namei('lehengas').self_and_descendants.collect{|c| c.name}

  conversion_rate = CurrencyConvert.where(:symbol => 'USD').first.rate

  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "brand", "google_product_category", "product_type", "adwords_grouping","adwords_labels","link", "image_link", "condition", "availability", "price", "gender", "age_group", "size", "color", "tax", "shipping(price)", "shipping_weight","custom_label_0", "custom_label_1", "custom_label_2", "custom_label_3", "custom_label_4"]
    # @designs = Design.published.select("DISTINCT(designs.id), designs.*").joins([:categories, :designer]).where('designs.ignore_in_google_feed = ?', 'false').where('designers.state_machine <> ?', 'banned').order('designs.created_at DESC')
    @designs = Design.published.select("DISTINCT(designs.id), designs.title, designs.designer_id, designs.published, designs.state, designs.designable_id, designs.description, designs.price, designs.discount_percent, designs.cached_slug, designs.ignore_in_google_feed, designs.sell_count, designs.created_at, designs.specification").includes([:categories, :designer, :images]).joins([:categories, :designer]).where('designs.ignore_in_google_feed = ?', 'false').active_designer_designs.where('designs.sell_count > 0 OR designs.created_at > ?', 6.months.ago);
    @designs.each do |design|
      category_parent = ''
      category = design.categories.first.name
      if sarees_category.include?(category)
        category = "sarees"
      elsif salwar_category.include?(category)
        category = "salwar-kameez"
      elsif earring_category.include?(category)
        category = "earrings"
      elsif lehengas_category.include?(category)
        category = "lehengas"
      elsif jewellery_category.include?(category)
        category_parent = "jewellery"
      end

      color = 'multicolor'
      c = design.property_values.where(:property_id => 20).first
      color = c.p_name if c.present? && c.p_name.present?

      google_product_category = ''
      mirraw_product_category = ''

      if category == "earrings"
        google_product_category = "Apparel & Accessories > Jewelry > Earrings"
        mirraw_product_category = "Jewellery > Earrings"
      elsif category == "bollywood-sarees"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Sarees > Bollywood Sarees"
      elsif category == "sarees"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Sarees"
        c = design.property_values.where(:property_id => 45).first
        color = c.p_name if c.present? && c.p_name.present?
      elsif category == "necklace-sets" || category == "necklace" || category == "mangalsutra" || category == "necklaces"
        google_product_category = "Apparel & Accessories > Jewelry > Necklaces"
        mirraw_product_category = "Jewellery > Necklace Sets"
      elsif category == "kurtas-and-kurtis"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Kurtas & Kurtis"
      elsif category == "salwar-kameez"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Salwar Kameez & Suits"
        c = design.property_values.where(:property_id => 46).first
        color = c.p_name if c.present? && c.p_name.present?
      elsif category == "anklets"
        google_product_category = "Apparel & Accessories > Jewelry > Anklets"
        mirraw_product_category = "Jewellery > Anklets"
      elsif category == "bangles-and-bracelets"
        google_product_category = "Apparel & Accessories > Jewelry > Bracelets"
        mirraw_product_category = "Jewellery > Bangles & Bracelets"
      elsif category == "pendants"
        google_product_category = "Apparel & Accessories > Jewelry > Charms & Pendants"
        mirraw_product_category = "Jewellery > Pendants"
      elsif category == "rings"
        google_product_category = "Apparel & Accessories > Jewelry > Rings"
        mirraw_product_category = "Jewellery > Rings"
      elsif category == "maang-tikka"
        google_product_category = "Apparel & Accessories > Jewelry > Body Jewelry"
        mirraw_product_category = "Jewellery > Maang Tikka"
      elsif category == "bags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
        mirraw_product_category = "Bags"
      elsif category == "rakhi"
        google_product_category = "Apparel & Accessories > Clothing Accessories > Wristbands"
        mirraw_product_category = "Rakhi"
      elsif category == "lehengas"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Lehengas"
      elsif category == "tote-bags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Tote Handbags"
        mirraw_product_category = "Apparel > Bags > Tote Bags"
      elsif category == "wallets"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
        mirraw_product_category = "Apparel > Bags > Wallets"
      elsif category == "clutches"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Clutches & Special Occasion Bags"
        mirraw_product_category = "Apparel > Bags > Clutches"
      elsif category == "sling-bags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Cross-Body Handbags"
        mirraw_product_category = "Apparel > Bags > Sling Bags"
      elsif category == "handbags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags"
        mirraw_product_category = "Apparel > Bags > Handbags"
      elsif category == "backpacks"
        google_product_category = "Luggage & Bags > Backpacks"
        mirraw_product_category = "Apparel > Bags > Backpacks"
      elsif category == "leggings"
        google_product_category = "Apparel & Accessories > Clothing > Pants > Leggings"
        mirraw_product_category = "Apparel > Women > Leggings"
      elsif category == "stole-and-dupattas"
        google_product_category = "Apparel & Accessories > Clothing Accessories > Scarves & Shawls"
        mirraw_product_category = "Apparel > Stoles And Dupattas"
      elsif category == "tops"
        google_product_category = "Apparel & Accessories > Clothing > Shirts & Tops"
        mirraw_product_category = "Apparel > Women > Tops"
      elsif category == "skirts"
        google_product_category = "Apparel & Accessories > Clothing > Skirts"
        mirraw_product_category = "Apparel > Women > Skirts"
      elsif category == "wall-decals"
        google_product_category = "Home & Garden > Decor > Wall & Window Decals"
        mirraw_product_category = "Home Decor > Wall Decals"
      elsif category == "wall-clocks"
        google_product_category = "Home & Garden > Decor > Clocks > Wall Clocks"
        mirraw_product_category = "Home Decor > Wall Clocks"
      elsif category == "bed-sheets"
        google_product_category = "Home & Garden > Linens & Bedding > Bedding > Bed Sheets"
        mirraw_product_category = "Home Furnishing > Bed Sheets"
      elsif category == "pillow-covers"
        google_product_category = "Home & Garden > Linens & Bedding > Bedding > Pillows"
        mirraw_product_category = "Home Furnishing > Pillows"
      elsif category == "table-lamps"
        google_product_category = "Home & Garden > Lighting > Lamps > Table Lamps"
        mirraw_product_category = "Home Decor > Table Lamps"
      elsif category == "laptop-skins"
        google_product_category = "Luggage & Bags > Business Bags > Electronics Bags & Cases > Computer Skins & Sleeves"
        mirraw_product_category = "Home Decor > Laptop Skins"
      elsif category == "tunics"
        google_product_category = "Apparel & Accessories > Clothing > Shirts & Tops"
        mirraw_product_category = "Women > Tunics"
      elsif category_parent == "jewellery"
        google_product_category = "Apparel & Accessories > Jewelry"
        mirraw_product_category = "Jewellery"
      end

      if design && design.title && design.images.present? && design.cached_slug.present? && design.designer.present?
        url = "http://www.mirraw.com/designers/" + design.designer.cached_slug + "/designs/" + design.cached_slug + '?country_code=US'
        design_category = design.categories.first.name.gsub('-', ' ').camelize
        if category == "sarees" || category == "salwar-kameez" || category == "earrings"
          mirraw_product_category1 = mirraw_product_category + ' > ' + design_category
        else
          mirraw_product_category1 = mirraw_product_category
        end

        # Using custom labels

        if design.created_at > 7.days.ago
          custom_label_0 = "listing0"
        elsif design.created_at > 30.days.ago
          custom_label_0 = "listing1"
        elsif design.created_at > 2.months.ago
          custom_label_0 = "listing2"
        elsif design.created_at > 6.months.ago
          custom_label_0 = "listing3"
        else
          custom_label_0 = "listing4"
        end

        if design.sell_count > 100
          custom_label_1 = "sell0"
        elsif design.sell_count > 10
          custom_label_1 = "sell1"
        elsif design.sell_count > 1
          custom_label_1 = "sell2"
        elsif design.sell_count == 1
          custom_label_1 = "sell3"
        else
          custom_label_1 = "sell4"
        end

        design_price = design.effective_price

        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end

        custom_label_3 = ''
        custom_label_4 = ''

        design_description = ''
        if design.specification.present?
          design_description = design.specification.gsub(/\r/, " ").gsub(/\n/, " ")
        end

        if design.description.present?
          design_description += " " + design.description.gsub(/\r/, " ").gsub(/\n/, " ")
        end

        shipping = 0
        weight = 0
        if (weight = design.categories.first.weight).present?
          if weight > 0 && weight <= 500
            shipping = 500
          elsif weight > 500 && weight <= 1000
            shipping = 900
          elsif weight > 1000 && weight <= 1500
            shipping = 1100
          elsif weight > 1500 && weight <= 2000
            shipping = 1300
          elsif weight > 2000 && weight <= 2500
            shipping = 1500
          end
        end
        shipping = (shipping/conversion_rate).round.to_s + " USD"
        csv << [design.id.to_s + '_us', design.title.gsub(',', ':') + ' - ' + design_category, design_description.gsub(',',':').force_encoding("UTF-8"), design.designer.name, google_product_category, mirraw_product_category1, category, design_category,url, design.master_image.photo.url(:original), "new", "in stock", (design.effective_price/conversion_rate).round, "Female","Adult", "one size", color, "US::0:", shipping, weight, custom_label_0, custom_label_1, custom_label_2, custom_label_3, custom_label_4]
      end
    end
    # upload that file
    file =  directory.files.create(
      :key    => "googlemerchant/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
  end
end
###############################################Australia_google_merchant_feed#######################################
task :upload_google_merchant_au => :environment do
  require 'csv'

  connection = Fog::Storage.new({
    :provider                 => 'AWS',
    :aws_access_key_id        => ENV['AWS_ACCESS_KEY_ID'],
    :aws_secret_access_key    => ENV['AWS_SECRET_ACCESS_KEY'],
    :region => "ap-southeast-1"
  })

  # First, a place to contain the glorious details
  directory = connection.directories.new(
    :key    => "mirraw-test"
  )

  currency_symbol = "AUD"

  filename = "mirraw_feed" + currency_symbol + '.csv'

  PATH = "/tmp/"
  filepath = PATH + filename

  earring_category = Category.find_by_namei('earrings').self_and_descendants.collect{|c| c.name}
  sarees_category = Category.find_by_namei('sarees').self_and_descendants.collect{|c| c.name}
  jewellery_category = Category.find_by_namei('jewellery').self_and_descendants.collect{|c| c.name}
  salwar_category = Category.find_by_namei('salwar-kameez').self_and_descendants.collect{|c| c.name}
  lehengas_category = Category.find_by_namei('lehengas').self_and_descendants.collect{|c| c.name}

  conversion_rate = CurrencyConvert.where(symbol: currency_symbol).first.rate

  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "brand", "google_product_category", "product_type", "adwords_grouping","adwords_labels","link", "image_link", "condition", "availability", "price", "gender", "age_group", "size", "color", "shipping(price)", "shipping_weight", "custom_label_0", "custom_label_1", "custom_label_2", "custom_label_3", "custom_label_4", ]

    # @designs = Design.published.select("DISTINCT(designs.id), designs.*").joins([:categories, :designer]).where('designs.ignore_in_google_feed = ?', 'false').where('designers.state_machine <> ?', 'banned').order('designs.created_at DESC')
    Design.published.select("DISTINCT(designs.id), designs.title, designs.designer_id, designs.published, designs.state, designs.designable_id, designs.description, designs.price, designs.discount_percent, designs.cached_slug, designs.ignore_in_google_feed, designs.sell_count, designs.created_at, designs.specification").includes([:categories, :designer, :images, :property_values]).joins([:categories, :designer]).where('designs.ignore_in_google_feed = ?', 'false').active_designer_designs.where('designs.sell_count > 0 OR designs.created_at > ?', 6.months.ago).find_in_batches(batch_size: 200) do |grp|
      next if grp.blank?
      grp.each do |design|
        category_parent = ''
        category = design.categories.first.name
        if sarees_category.include?(category)
          category = "sarees"
        elsif salwar_category.include?(category)
          category = "salwar-kameez"
        elsif earring_category.include?(category)
          category = "earrings"
        elsif lehengas_category.include?(category)
          category = "lehengas"
        elsif jewellery_category.include?(category)
          category_parent = "jewellery"
        end

        color = 'multicolor'
        c = design.property_values.where(:property_id => 20).first
        color = c.p_name if c.present? && c.p_name.present?

        google_product_category = ''
        mirraw_product_category = ''

        if category == "earrings"
          google_product_category = "Apparel & Accessories > Jewelry > Earrings"
          mirraw_product_category = "Jewellery > Earrings"
        elsif category == "bollywood-sarees"
          google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
          mirraw_product_category = "Apparel > Clothing > Sarees > Bollywood Sarees"
        elsif category == "sarees"
          google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
          mirraw_product_category = "Apparel > Clothing > Sarees"
          c = design.property_values.where(:property_id => 45).first
          color = c.p_name if c.present? && c.p_name.present?
        elsif category == "necklace-sets" || category == "necklace" || category == "mangalsutra" || category == "necklaces"
          google_product_category = "Apparel & Accessories > Jewelry > Necklaces"
          mirraw_product_category = "Jewellery > Necklace Sets"
        elsif category == "kurtas-and-kurtis"
          google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
          mirraw_product_category = "Apparel > Clothing > Kurtas & Kurtis"
        elsif category == "salwar-kameez"
          google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
          mirraw_product_category = "Apparel > Clothing > Salwar Kameez & Suits"
          c = design.property_values.where(:property_id => 46).first
          color = c.p_name if c.present? && c.p_name.present?
        elsif category == "anklets"
          google_product_category = "Apparel & Accessories > Jewelry > Anklets"
          mirraw_product_category = "Jewellery > Anklets"
        elsif category == "bangles-and-bracelets"
          google_product_category = "Apparel & Accessories > Jewelry > Bracelets"
          mirraw_product_category = "Jewellery > Bangles & Bracelets"
        elsif category == "pendants"
          google_product_category = "Apparel & Accessories > Jewelry > Charms & Pendants"
          mirraw_product_category = "Jewellery > Pendants"
        elsif category == "rings"
          google_product_category = "Apparel & Accessories > Jewelry > Rings"
          mirraw_product_category = "Jewellery > Rings"
        elsif category == "maang-tikka"
          google_product_category = "Apparel & Accessories > Jewelry > Body Jewelry"
          mirraw_product_category = "Jewellery > Maang Tikka"
        elsif category == "bags"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
          mirraw_product_category = "Bags"
        elsif category == "rakhi"
          google_product_category = "Apparel & Accessories > Clothing Accessories > Wristbands"
          mirraw_product_category = "Rakhi"
        elsif category == "lehengas"
          google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
          mirraw_product_category = "Apparel > Clothing > Lehengas"
        elsif category == "tote-bags"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Tote Handbags"
          mirraw_product_category = "Apparel > Bags > Tote Bags"
        elsif category == "wallets"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
          mirraw_product_category = "Apparel > Bags > Wallets"
        elsif category == "clutches"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Clutches & Special Occasion Bags"
          mirraw_product_category = "Apparel > Bags > Clutches"
        elsif category == "sling-bags"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Cross-Body Handbags"
          mirraw_product_category = "Apparel > Bags > Sling Bags"
        elsif category == "handbags"
          google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags"
          mirraw_product_category = "Apparel > Bags > Handbags"
        elsif category == "backpacks"
          google_product_category = "Luggage & Bags > Backpacks"
          mirraw_product_category = "Apparel > Bags > Backpacks"
        elsif category == "leggings"
          google_product_category = "Apparel & Accessories > Clothing > Pants > Leggings"
          mirraw_product_category = "Apparel > Women > Leggings"
        elsif category == "stole-and-dupattas"
          google_product_category = "Apparel & Accessories > Clothing Accessories > Scarves & Shawls"
          mirraw_product_category = "Apparel > Stoles And Dupattas"
        elsif category == "tops"
          google_product_category = "Apparel & Accessories > Clothing > Shirts & Tops"
          mirraw_product_category = "Apparel > Women > Tops"
        elsif category == "skirts"
          google_product_category = "Apparel & Accessories > Clothing > Skirts"
          mirraw_product_category = "Apparel > Women > Skirts"
        elsif category == "wall-decals"
          google_product_category = "Home & Garden > Decor > Wall & Window Decals"
          mirraw_product_category = "Home Decor > Wall Decals"
        elsif category == "wall-clocks"
          google_product_category = "Home & Garden > Decor > Clocks > Wall Clocks"
          mirraw_product_category = "Home Decor > Wall Clocks"
        elsif category == "bed-sheets"
          google_product_category = "Home & Garden > Linens & Bedding > Bedding > Bed Sheets"
          mirraw_product_category = "Home Furnishing > Bed Sheets"
        elsif category == "pillow-covers"
          google_product_category = "Home & Garden > Linens & Bedding > Bedding > Pillows"
          mirraw_product_category = "Home Furnishing > Pillows"
        elsif category == "table-lamps"
          google_product_category = "Home & Garden > Lighting > Lamps > Table Lamps"
          mirraw_product_category = "Home Decor > Table Lamps"
        elsif category == "laptop-skins"
          google_product_category = "Luggage & Bags > Business Bags > Electronics Bags & Cases > Computer Skins & Sleeves"
          mirraw_product_category = "Home Decor > Laptop Skins"
        elsif category == "tunics"
          google_product_category = "Apparel & Accessories > Clothing > Shirts & Tops"
          mirraw_product_category = "Women > Tunics"
        elsif category_parent == "jewellery"
          google_product_category = "Apparel & Accessories > Jewelry"
          mirraw_product_category = "Jewellery"
        end

        if design && design.title && design.images.present? && design.cached_slug.present? && design.designer.present?
          url = "http://www.mirraw.com/designers/" + design.designer.cached_slug + "/designs/" + design.cached_slug + '?country_code=AU'
          design_category = design.categories.first.name.gsub('-', ' ').camelize
          if category == "sarees" || category == "salwar-kameez" || category == "earrings"
            mirraw_product_category1 = mirraw_product_category + ' > ' + design_category
          else
            mirraw_product_category1 = mirraw_product_category
          end

          # Using custom labels

          if design.created_at > 7.days.ago
            custom_label_0 = "listing0"
          elsif design.created_at > 30.days.ago
            custom_label_0 = "listing1"
          elsif design.created_at > 2.months.ago
            custom_label_0 = "listing2"
          elsif design.created_at > 6.months.ago
            custom_label_0 = "listing3"
          else
            custom_label_0 = "listing4"
          end

          if design.sell_count.to_i > 100
            custom_label_1 = "sell0"
          elsif design.sell_count.to_i > 10
            custom_label_1 = "sell1"
          elsif design.sell_count.to_i > 1
            custom_label_1 = "sell2"
          elsif design.sell_count.to_i == 1
            custom_label_1 = "sell3"
          else
            custom_label_1 = "sell4"
          end

          design_price = design.effective_price

          if design_price > 30000
            custom_label_2 = "price0"
          elsif design_price > 8000
            custom_label_2 = "price1"
          elsif design_price > 4000
            custom_label_2 = "price2"
          elsif design_price > 1000
            custom_label_2 = "price3"
          else
            custom_label_2 = "price4"
          end

          custom_label_3 = ''
          custom_label_4 = ''

          design_description = ''
          if design.specification.present?
            design_description = design.specification.gsub(/\r/, " ").gsub(/\n/, " ")
          end

          if design.description.present?
            design_description += " " + design.description.gsub(/\r/, " ").gsub(/\n/, " ")
          end

          shipping = 0
          weight = 0
          if (weight = design.categories.first.weight).present?
            if weight > 0 && weight <= 500
              shipping = 500
            elsif weight > 500 && weight <= 1000
              shipping = 700
            elsif weight > 1000 && weight <= 1500
              shipping = 900
            elsif weight > 1500 && weight <= 2000
              shipping = 1100
            elsif weight > 2000 && weight <= 2500
              shipping = 1300
            end
          end
          shipping = (shipping/conversion_rate).round(2).to_s + " AUD"
          puts design.id
          csv << [design.id.to_s + '_' + currency_symbol, design.title.gsub(',', ':') + ' - ' + design_category, design_description.gsub(',',':').force_encoding("UTF-8"), design.designer.name, google_product_category, mirraw_product_category1, category, design_category,url, design.master_image.photo.url(:original), "new", "in stock", (design_price/conversion_rate).round(2), "Female","Adult", "one size", color,shipping, weight, custom_label_0, custom_label_1, custom_label_2, custom_label_3, custom_label_4]
        end
      end
    end
    # upload that file
    file =  directory.files.create(
      :key    => "googlemerchant/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
  end
end
############################################# UK_google_merchant_feed#######################################
task :upload_google_merchant_uk => :environment do
  require 'csv'

  connection = Fog::Storage.new({
    :provider                 => 'AWS',
    :aws_access_key_id        => ENV['AWS_ACCESS_KEY_ID'],
    :aws_secret_access_key    => ENV['AWS_SECRET_ACCESS_KEY'],
    :region => "ap-southeast-1"
  })

  # First, a place to contain the glorious details
  directory = connection.directories.new(
    :key    => "mirraw-test"
  )

  currency_symbol = "GBP"

  filename = "mirraw_feed" + currency_symbol + '.csv'

  PATH = "/tmp/"
  filepath = PATH + filename

  Mapping = Hash.new

  earring_category = Category.find_by_namei('earrings').self_and_descendants.collect{|c| c.name}
  sarees_category = Category.find_by_namei('sarees').self_and_descendants.collect{|c| c.name}
  jewellery_category = Category.find_by_namei('jewellery').self_and_descendants.collect{|c| c.name}
  salwar_category = Category.find_by_namei('salwar-kameez').self_and_descendants.collect{|c| c.name}
  lehengas_category = Category.find_by_namei('lehengas').self_and_descendants.collect{|c| c.name}

  conversion_rate = CurrencyConvert.where(:symbol => currency_symbol).first.rate

  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "brand", "google_product_category", "product_type", "adwords_grouping","adwords_labels","link", "image_link", "condition", "availability", "price", "gender", "age_group", "size", "color", "shipping(price)", "shipping_weight", "custom_label_0", "custom_label_1", "custom_label_2", "custom_label_3", "custom_label_4"]
    # @designs = Design.published.select("DISTINCT(designs.id), designs.*").joins([:categories, :designer]).where('designs.ignore_in_google_feed = ?', 'false').where('designers.state_machine <> ?', 'banned').order('designs.created_at DESC')
    @designs = Design.published.select("DISTINCT(designs.id), designs.title, designs.designer_id, designs.published, designs.state, designs.designable_id, designs.description, designs.price, designs.discount_percent, designs.cached_slug, designs.ignore_in_google_feed, designs.sell_count, designs.created_at, designs.specification").includes([:categories, :designer, :images]).joins([:categories, :designer]).where('designs.ignore_in_google_feed = ?', 'false').active_designer_designs.where('designs.sell_count > 0 OR designs.created_at > ?', 6.months.ago);
    @designs.each do |design|
      category_parent = ''
      category = design.categories.first.name
      if sarees_category.include?(category)
        category = "sarees"
      elsif salwar_category.include?(category)
        category = "salwar-kameez"
      elsif earring_category.include?(category)
        category = "earrings"
      elsif lehengas_category.include?(category)
        category = "lehengas"
      elsif jewellery_category.include?(category)
        category_parent = "jewellery"
      end

      color = 'multicolor'
      c = design.property_values.where(:property_id => 20).first
      color = c.p_name if c.present? && c.p_name.present?

      google_product_category = ''
      mirraw_product_category = ''

      if category == "earrings"
        google_product_category = "Apparel & Accessories > Jewelry > Earrings"
        mirraw_product_category = "Jewellery > Earrings"
      elsif category == "bollywood-sarees"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Sarees > Bollywood Sarees"
      elsif category == "sarees"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Sarees"
        c = design.property_values.where(:property_id => 45).first
        color = c.p_name if c.present? && c.p_name.present?
      elsif category == "necklace-sets" || category == "necklace" || category == "mangalsutra" || category == "necklaces"
        google_product_category = "Apparel & Accessories > Jewelry > Necklaces"
        mirraw_product_category = "Jewellery > Necklace Sets"
      elsif category == "kurtas-and-kurtis"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Kurtas & Kurtis"
      elsif category == "salwar-kameez"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Salwar Kameez & Suits"
        c = design.property_values.where(:property_id => 46).first
        color = c.p_name if c.present? && c.p_name.present?
      elsif category == "anklets"
        google_product_category = "Apparel & Accessories > Jewelry > Anklets"
        mirraw_product_category = "Jewellery > Anklets"
      elsif category == "bangles-and-bracelets"
        google_product_category = "Apparel & Accessories > Jewelry > Bracelets"
        mirraw_product_category = "Jewellery > Bangles & Bracelets"
      elsif category == "pendants"
        google_product_category = "Apparel & Accessories > Jewelry > Charms & Pendants"
        mirraw_product_category = "Jewellery > Pendants"
      elsif category == "rings"
        google_product_category = "Apparel & Accessories > Jewelry > Rings"
        mirraw_product_category = "Jewellery > Rings"
      elsif category == "maang-tikka"
        google_product_category = "Apparel & Accessories > Jewelry > Body Jewelry"
        mirraw_product_category = "Jewellery > Maang Tikka"
      elsif category == "bags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
        mirraw_product_category = "Bags"
      elsif category == "rakhi"
        google_product_category = "Apparel & Accessories > Clothing Accessories > Wristbands"
        mirraw_product_category = "Rakhi"
      elsif category == "lehengas"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Lehengas"
      elsif category == "tote-bags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Tote Handbags"
        mirraw_product_category = "Apparel > Bags > Tote Bags"
      elsif category == "wallets"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
        mirraw_product_category = "Apparel > Bags > Wallets"
      elsif category == "clutches"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Clutches & Special Occasion Bags"
        mirraw_product_category = "Apparel > Bags > Clutches"
      elsif category == "sling-bags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Cross-Body Handbags"
        mirraw_product_category = "Apparel > Bags > Sling Bags"
      elsif category == "handbags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags"
        mirraw_product_category = "Apparel > Bags > Handbags"
      elsif category == "backpacks"
        google_product_category = "Luggage & Bags > Backpacks"
        mirraw_product_category = "Apparel > Bags > Backpacks"
      elsif category == "leggings"
        google_product_category = "Apparel & Accessories > Clothing > Pants > Leggings"
        mirraw_product_category = "Apparel > Women > Leggings"
      elsif category == "stole-and-dupattas"
        google_product_category = "Apparel & Accessories > Clothing Accessories > Scarves & Shawls"
        mirraw_product_category = "Apparel > Stoles And Dupattas"
      elsif category == "tops"
        google_product_category = "Apparel & Accessories > Clothing > Shirts & Tops"
        mirraw_product_category = "Apparel > Women > Tops"
      elsif category == "skirts"
        google_product_category = "Apparel & Accessories > Clothing > Skirts"
        mirraw_product_category = "Apparel > Women > Skirts"
      elsif category == "wall-decals"
        google_product_category = "Home & Garden > Decor > Wall & Window Decals"
        mirraw_product_category = "Home Decor > Wall Decals"
      elsif category == "wall-clocks"
        google_product_category = "Home & Garden > Decor > Clocks > Wall Clocks"
        mirraw_product_category = "Home Decor > Wall Clocks"
      elsif category == "bed-sheets"
        google_product_category = "Home & Garden > Linens & Bedding > Bedding > Bed Sheets"
        mirraw_product_category = "Home Furnishing > Bed Sheets"
      elsif category == "pillow-covers"
        google_product_category = "Home & Garden > Linens & Bedding > Bedding > Pillows"
        mirraw_product_category = "Home Furnishing > Pillows"
      elsif category == "table-lamps"
        google_product_category = "Home & Garden > Lighting > Lamps > Table Lamps"
        mirraw_product_category = "Home Decor > Table Lamps"
      elsif category == "laptop-skins"
        google_product_category = "Luggage & Bags > Business Bags > Electronics Bags & Cases > Computer Skins & Sleeves"
        mirraw_product_category = "Home Decor > Laptop Skins"
      elsif category == "tunics"
        google_product_category = "Apparel & Accessories > Clothing > Shirts & Tops"
        mirraw_product_category = "Women > Tunics"
      elsif category_parent == "jewellery"
        google_product_category = "Apparel & Accessories > Jewelry"
        mirraw_product_category = "Jewellery"
      end

      if design && design.title && design.images.present? && design.cached_slug.present? && design.designer.present?
        url = "http://www.mirraw.com/designers/" + design.designer.cached_slug + "/designs/" + design.cached_slug + '?country_code=GB'
        design_category = design.categories.first.name.gsub('-', ' ').camelize
        if category == "sarees" || category == "salwar-kameez" || category == "earrings"
          mirraw_product_category1 = mirraw_product_category + ' > ' + design_category
        else
          mirraw_product_category1 = mirraw_product_category
        end

        # Using custom labels

        if design.created_at > 7.days.ago
          custom_label_0 = "listing0"
        elsif design.created_at > 30.days.ago
          custom_label_0 = "listing1"
        elsif design.created_at > 2.months.ago
          custom_label_0 = "listing2"
        elsif design.created_at > 6.months.ago
          custom_label_0 = "listing3"
        else
          custom_label_0 = "listing4"
        end

        if design.sell_count > 100
          custom_label_1 = "sell0"
        elsif design.sell_count > 10
          custom_label_1 = "sell1"
        elsif design.sell_count > 1
          custom_label_1 = "sell2"
        elsif design.sell_count == 1
          custom_label_1 = "sell3"
        else
          custom_label_1 = "sell4"
        end

        design_price = design.effective_price

        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end

        custom_label_3 = ''
        custom_label_4 = ''

        design_description = ''
        if design.specification.present?
          design_description = design.specification.gsub(/\r/, " ").gsub(/\n/, " ")
        end

        if design.description.present?
          design_description += " " + design.description.gsub(/\r/, " ").gsub(/\n/, " ")
        end

        shipping = 0
        weight = 0
        if (weight = design.categories.first.weight).present?
          if weight > 0 && weight <= 500
            shipping = 500
          elsif weight > 500 && weight <= 1000
            shipping = 900
          elsif weight > 1000 && weight <= 1500
            shipping = 1100
          elsif weight > 1500 && weight <= 2000
            shipping = 1300
          elsif weight > 2000 && weight <= 2500
            shipping = 1500
          end
        end
        shipping = (shipping/conversion_rate).round.to_s + " " + currency_symbol

        csv << [design.id.to_s + '_' + currency_symbol, design.title.gsub(',', ':') + ' - ' + design_category, design_description.gsub(',',':').force_encoding("UTF-8"), design.designer.name, google_product_category, mirraw_product_category1, category, design_category,url, design.master_image.photo.url(:original), "new", "in stock", (design.effective_price/conversion_rate).round, "Female","Adult", "one size", color, shipping, weight, custom_label_0, custom_label_1, custom_label_2, custom_label_3, custom_label_4]
      end
    end
    # upload that file
    file =  directory.files.create(
      :key    => "googlemerchant/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
  end
end

task :upload_google_merchant_eur => :environment do
  require 'csv'

  connection = Fog::Storage.new({
    :provider                 => 'AWS',
    :aws_access_key_id        => ENV['AWS_ACCESS_KEY_ID'],
    :aws_secret_access_key    => ENV['AWS_SECRET_ACCESS_KEY'],
    :region => "ap-southeast-1"
  })

  # First, a place to contain the glorious details
  directory = connection.directories.new(
    :key    => "mirraw-test"
  )

  currency_symbol = "EUR"

  filename = "mirraw_feed" + currency_symbol + '.csv'

  PATH = "/tmp/"
  filepath = PATH + filename

  Mapping = Hash.new

  earring_category = Category.find_by_namei('earrings').self_and_descendants.collect{|c| c.name}
  sarees_category = Category.find_by_namei('sarees').self_and_descendants.collect{|c| c.name}
  jewellery_category = Category.find_by_namei('jewellery').self_and_descendants.collect{|c| c.name}
  salwar_category = Category.find_by_namei('salwar-kameez').self_and_descendants.collect{|c| c.name}
  lehengas_category = Category.find_by_namei('lehengas').self_and_descendants.collect{|c| c.name}

  conversion_rate = CurrencyConvert.where(:symbol => currency_symbol).first.rate

  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "brand", "google_product_category", "product_type", "adwords_grouping","adwords_labels","link", "image_link", "condition", "availability", "price", "gender", "age_group", "size", "color", "shipping(price)", "shipping_weight", "custom_label_0", "custom_label_1", "custom_label_2", "custom_label_3", "custom_label_4"]
    # @designs = Design.published.select("DISTINCT(designs.id), designs.*").joins([:categories, :designer]).where('designers.state_machine <> ?', 'banned').order('designs.created_at DESC')
    @designs = Design.published.select("DISTINCT(designs.id), designs.title, designs.designer_id, designs.published, designs.state, designs.designable_id, designs.description, designs.price, designs.discount_percent, designs.cached_slug, designs.ignore_in_google_feed, designs.sell_count, designs.created_at, designs.specification").includes([:categories, :designer, :images]).joins([:categories, :designer]).where('designs.ignore_in_google_feed = ?', 'false').active_designer_designs.where('designs.sell_count > 0 OR designs.created_at > ?', 6.months.ago);
    @designs.each do |design|
      category_parent = ''
      category = design.categories.first.name
      if sarees_category.include?(category)
        category = "sarees"
      elsif salwar_category.include?(category)
        category = "salwar-kameez"
      elsif earring_category.include?(category)
        category = "earrings"
      elsif lehengas_category.include?(category)
        category = "lehengas"
      elsif jewellery_category.include?(category)
        category_parent = "jewellery"
      end

      color = 'multicolor'
      c = design.property_values.where(:property_id => 20).first
      color = c.p_name if c.present? && c.p_name.present?

      google_product_category = ''
      mirraw_product_category = ''

      if category == "earrings"
        google_product_category = "Apparel & Accessories > Jewelry > Earrings"
        mirraw_product_category = "Jewellery > Earrings"
      elsif category == "bollywood-sarees"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Sarees > Bollywood Sarees"
      elsif category == "sarees"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Sarees"
        c = design.property_values.where(:property_id => 45).first
        color = c.p_name if c.present? && c.p_name.present?
      elsif category == "necklace-sets" || category == "necklace" || category == "mangalsutra" || category == "necklaces"
        google_product_category = "Apparel & Accessories > Jewelry > Necklaces"
        mirraw_product_category = "Jewellery > Necklace Sets"
      elsif category == "kurtas-and-kurtis"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Kurtas & Kurtis"
      elsif category == "salwar-kameez"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Salwar Kameez & Suits"
        c = design.property_values.where(:property_id => 46).first
        color = c.p_name if c.present? && c.p_name.present?
      elsif category == "anklets"
        google_product_category = "Apparel & Accessories > Jewelry > Anklets"
        mirraw_product_category = "Jewellery > Anklets"
      elsif category == "bangles-and-bracelets"
        google_product_category = "Apparel & Accessories > Jewelry > Bracelets"
        mirraw_product_category = "Jewellery > Bangles & Bracelets"
      elsif category == "pendants"
        google_product_category = "Apparel & Accessories > Jewelry > Charms & Pendants"
        mirraw_product_category = "Jewellery > Pendants"
      elsif category == "rings"
        google_product_category = "Apparel & Accessories > Jewelry > Rings"
        mirraw_product_category = "Jewellery > Rings"
      elsif category == "maang-tikka"
        google_product_category = "Apparel & Accessories > Jewelry > Body Jewelry"
        mirraw_product_category = "Jewellery > Maang Tikka"
      elsif category == "bags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
        mirraw_product_category = "Bags"
      elsif category == "rakhi"
        google_product_category = "Apparel & Accessories > Clothing Accessories > Wristbands"
        mirraw_product_category = "Rakhi"
      elsif category == "lehengas"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Lehengas"
      elsif category == "tote-bags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Tote Handbags"
        mirraw_product_category = "Apparel > Bags > Tote Bags"
      elsif category == "wallets"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
        mirraw_product_category = "Apparel > Bags > Wallets"
      elsif category == "clutches"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Clutches & Special Occasion Bags"
        mirraw_product_category = "Apparel > Bags > Clutches"
      elsif category == "sling-bags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Cross-Body Handbags"
        mirraw_product_category = "Apparel > Bags > Sling Bags"
      elsif category == "handbags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags"
        mirraw_product_category = "Apparel > Bags > Handbags"
      elsif category == "backpacks"
        google_product_category = "Luggage & Bags > Backpacks"
        mirraw_product_category = "Apparel > Bags > Backpacks"
      elsif category == "leggings"
        google_product_category = "Apparel & Accessories > Clothing > Pants > Leggings"
        mirraw_product_category = "Apparel > Women > Leggings"
      elsif category == "stole-and-dupattas"
        google_product_category = "Apparel & Accessories > Clothing Accessories > Scarves & Shawls"
        mirraw_product_category = "Apparel > Stoles And Dupattas"
      elsif category == "tops"
        google_product_category = "Apparel & Accessories > Clothing > Shirts & Tops"
        mirraw_product_category = "Apparel > Women > Tops"
      elsif category == "skirts"
        google_product_category = "Apparel & Accessories > Clothing > Skirts"
        mirraw_product_category = "Apparel > Women > Skirts"
      elsif category == "wall-decals"
        google_product_category = "Home & Garden > Decor > Wall & Window Decals"
        mirraw_product_category = "Home Decor > Wall Decals"
      elsif category == "wall-clocks"
        google_product_category = "Home & Garden > Decor > Clocks > Wall Clocks"
        mirraw_product_category = "Home Decor > Wall Clocks"
      elsif category == "bed-sheets"
        google_product_category = "Home & Garden > Linens & Bedding > Bedding > Bed Sheets"
        mirraw_product_category = "Home Furnishing > Bed Sheets"
      elsif category == "pillow-covers"
        google_product_category = "Home & Garden > Linens & Bedding > Bedding > Pillows"
        mirraw_product_category = "Home Furnishing > Pillows"
      elsif category == "table-lamps"
        google_product_category = "Home & Garden > Lighting > Lamps > Table Lamps"
        mirraw_product_category = "Home Decor > Table Lamps"
      elsif category == "laptop-skins"
        google_product_category = "Luggage & Bags > Business Bags > Electronics Bags & Cases > Computer Skins & Sleeves"
        mirraw_product_category = "Home Decor > Laptop Skins"
      elsif category == "tunics"
        google_product_category = "Apparel & Accessories > Clothing > Shirts & Tops"
        mirraw_product_category = "Women > Tunics"
      elsif category_parent == "jewellery"
        google_product_category = "Apparel & Accessories > Jewelry"
        mirraw_product_category = "Jewellery"
      end

      if design && design.title && design.images.present? && design.cached_slug.present? && design.designer.present?
        url = "http://www.mirraw.com/designers/" + design.designer.cached_slug + "/designs/" + design.cached_slug
        design_category = design.categories.first.name.gsub('-', ' ').camelize
        if category == "sarees" || category == "salwar-kameez" || category == "earrings"
          mirraw_product_category1 = mirraw_product_category + ' > ' + design_category
        else
          mirraw_product_category1 = mirraw_product_category
        end

        # Using custom labels

        if design.created_at > 7.days.ago
          custom_label_0 = "listing0"
        elsif design.created_at > 30.days.ago
          custom_label_0 = "listing1"
        elsif design.created_at > 2.months.ago
          custom_label_0 = "listing2"
        elsif design.created_at > 6.months.ago
          custom_label_0 = "listing3"
        else
          custom_label_0 = "listing4"
        end

        if design.sell_count > 100
          custom_label_1 = "sell0"
        elsif design.sell_count > 10
          custom_label_1 = "sell1"
        elsif design.sell_count > 1
          custom_label_1 = "sell2"
        elsif design.sell_count == 1
          custom_label_1 = "sell3"
        else
          custom_label_1 = "sell4"
        end

        design_price = design.effective_price
        if design_price > 30000
          custom_label_2 = "price0"
        elsif design_price > 8000
          custom_label_2 = "price1"
        elsif design_price > 4000
          custom_label_2 = "price2"
        elsif design_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end

        custom_label_3 = ''
        custom_label_4 = ''

        design_description = ''
        if design.specification.present?
          design_description = design.specification.gsub(/\r/, " ").gsub(/\n/, " ")
        end

        if design.description.present?
          design_description += " " + design.description.gsub(/\r/, " ").gsub(/\n/, " ")
        end

        shipping = 0
        weight = 0
        if (weight = design.categories.first.weight).present?
          if weight > 0 && weight <= 500
            shipping = 500
          elsif weight > 500 && weight <= 1000
            shipping = 900
          elsif weight > 1000 && weight <= 1500
            shipping = 1100
          elsif weight > 1500 && weight <= 2000
            shipping = 1300
          elsif weight > 2000 && weight <= 2500
            shipping = 1500
          end
        end
        shipping = (shipping/conversion_rate).round.to_s + " " + currency_symbol

        csv << [design.id.to_s + '_' + currency_symbol, design.title.gsub(',', ':') + ' - ' + design_category, design_description.gsub(',',':').force_encoding("UTF-8"), design.designer.name, google_product_category, mirraw_product_category1, category, design_category,url, design.master_image.photo.url(:original), "new", "in stock", (design.effective_price/conversion_rate).round, "Female","Adult", "one size", color, shipping, weight, custom_label_0, custom_label_1, custom_label_2, custom_label_3, custom_label_4]
      end
    end
    # upload that file
    file =  directory.files.create(
      :key    => "googlemerchant/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
  end
end

task :upload_sociomantic => :environment do
  require 'csv'

  connection = Fog::Storage.new({
    :provider                 => 'AWS',
    :aws_access_key_id        => ENV['AWS_ACCESS_KEY_ID'],
    :aws_secret_access_key    => ENV['AWS_SECRET_ACCESS_KEY'],
    :region => "ap-southeast-1"
  })

  # First, a place to contain the glorious details
  directory = connection.directories.new(
    :key    => "mirraw-test"
  )

  filename = "mirraw_feed" + '.csv'

  PATH = "/tmp/"
  filepath = PATH + filename

  Mapping = Hash.new

  earring_category = Category.find_by_namei('earrings').self_and_descendants.collect{|c| c.name}
  sarees_category = Category.find_by_namei('sarees').self_and_descendants.collect{|c| c.name}
  jewellery_category = Category.find_by_namei('jewellery').self_and_descendants.collect{|c| c.name}
  salwar_category = Category.find_by_namei('salwar-kameez').self_and_descendants.collect{|c| c.name}
  lehengas_category = Category.find_by_namei('lehengas').self_and_descendants.collect{|c| c.name}


  CSV.open(filepath, "wb") do |csv|
    csv << ["id", "title", "description", "brand", "google_product_category", "product_type", "adwords_grouping","adwords_labels","link", "image_link", "condition", "availability", "price", "gender", "age_group", "custom_label_0", "custom_label_1", "custom_label_2", "custom_label_3", "custom_label_4"]
    @designs = Design.published.select("DISTINCT(designs.id), designs.*").joins([:categories, :designer]).active_designer_designs.order('designs.created_at DESC');
    @designs.each do |design|
      category_parent = ''
      category = design.categories.first.name
      if sarees_category.include?(category)
        category = "sarees"
      elsif salwar_category.include?(category)
        category = "salwar-kameez"
      elsif earring_category.include?(category)
        category = "earrings"
      elsif lehengas_category.include?(category)
        category = "lehengas"
      elsif jewellery_category.include?(category)
        category_parent = "jewellery"
      end

      google_product_category = ''
      mirraw_product_category = ''
      if category == "earrings"
        google_product_category = "Apparel & Accessories > Jewelry > Earrings"
        mirraw_product_category = "Jewellery > Earrings"
      elsif category == "bollywood-sarees"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Sarees > Bollywood Sarees"
      elsif category == "sarees"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Sarees"
      elsif category == "necklace-sets" || category == "necklace" || category == "mangalsutra" || category == "necklaces"
        google_product_category = "Apparel & Accessories > Jewelry > Necklaces"
        mirraw_product_category = "Jewellery > Necklace Sets"
      elsif category == "kurtas-and-kurtis"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Kurtas & Kurtis"
      elsif category == "salwar-kameez"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Salwar Kameez & Suits"
      elsif category == "anklets"
        google_product_category = "Apparel & Accessories > Jewelry > Anklets"
        mirraw_product_category = "Jewellery > Anklets"
      elsif category == "bangles-and-bracelets"
        google_product_category = "Apparel & Accessories > Jewelry > Bracelets"
        mirraw_product_category = "Jewellery > Bangles & Bracelets"
      elsif category == "pendants"
        google_product_category = "Apparel & Accessories > Jewelry > Charms & Pendants"
        mirraw_product_category = "Jewellery > Pendants"
      elsif category == "rings"
        google_product_category = "Apparel & Accessories > Jewelry > Rings"
        mirraw_product_category = "Jewellery > Rings"
      elsif category == "maang-tikka"
        google_product_category = "Apparel & Accessories > Jewelry > Body Jewelry"
        mirraw_product_category = "Jewellery > Maang Tikka"
      elsif category == "bags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
        mirraw_product_category = "Bags"
      elsif category == "rakhi"
        google_product_category = "Apparel & Accessories > Clothing Accessories > Wristbands"
        mirraw_product_category = "Rakhi"
      elsif category == "lehengas"
        google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
        mirraw_product_category = "Apparel > Clothing > Lehengas"
      elsif category == "tote-bags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Tote Handbags"
        mirraw_product_category = "Apparel > Bags > Tote Bags"
      elsif category == "wallets"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases"
        mirraw_product_category = "Apparel > Bags > Wallets"
      elsif category == "clutches"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Clutches & Special Occasion Bags"
        mirraw_product_category = "Apparel > Bags > Clutches"
      elsif category == "sling-bags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags > Cross-Body Handbags"
        mirraw_product_category = "Apparel > Bags > Sling Bags"
      elsif category == "handbags"
        google_product_category = "Apparel & Accessories > Handbags, Wallets & Cases > Handbags"
        mirraw_product_category = "Apparel > Bags > Handbags"
      elsif category == "backpacks"
        google_product_category = "Luggage & Bags > Backpacks"
        mirraw_product_category = "Apparel > Bags > Backpacks"
      elsif category == "leggings"
        google_product_category = "Apparel & Accessories > Clothing > Pants > Leggings"
        mirraw_product_category = "Apparel > Women > Leggings"
      elsif category == "stole-and-dupattas"
        google_product_category = "Apparel & Accessories > Clothing Accessories > Scarves & Shawls"
        mirraw_product_category = "Apparel > Stoles And Dupattas"
      elsif category == "tops"
        google_product_category = "Apparel & Accessories > Clothing > Shirts & Tops"
        mirraw_product_category = "Apparel > Women > Tops"
      elsif category == "skirts"
        google_product_category = "Apparel & Accessories > Clothing > Skirts"
        mirraw_product_category = "Apparel > Women > Skirts"
      elsif category == "wall-decals"
        google_product_category = "Home & Garden > Decor > Wall & Window Decals"
        mirraw_product_category = "Home Decor > Wall Decals"
      elsif category == "wall-clocks"
        google_product_category = "Home & Garden > Decor > Clocks > Wall Clocks"
        mirraw_product_category = "Home Decor > Wall Clocks"
      elsif category == "bed-sheets"
        google_product_category = "Home & Garden > Linens & Bedding > Bedding > Bed Sheets"
        mirraw_product_category = "Home Furnishing > Bed Sheets"
      elsif category == "pillow-covers"
        google_product_category = "Home & Garden > Linens & Bedding > Bedding > Pillows"
        mirraw_product_category = "Home Furnishing > Pillows"
      elsif category == "table-lamps"
        google_product_category = "Home & Garden > Lighting > Lamps > Table Lamps"
        mirraw_product_category = "Home Decor > Table Lamps"
      elsif category == "laptop-skins"
        google_product_category = "Luggage & Bags > Business Bags > Electronics Bags & Cases > Computer Skins & Sleeves"
        mirraw_product_category = "Home Decor > Laptop Skins"
      elsif category == "tunics"
        google_product_category = "Apparel & Accessories > Clothing > Shirts & Tops"
        mirraw_product_category = "Women > Tunics"
      elsif category_parent == "jewellery"
        google_product_category = "Apparel & Accessories > Jewelry"
        mirraw_product_category = "Jewellery"
      end

      if design && design.title && design.images.present? && design.cached_slug.present? && design.designer.present?
        url = "http://www.mirraw.com/designers/" + design.designer.cached_slug + "/designs/" + design.cached_slug
        design_category = design.categories.first.name.gsub('-', ' ').camelize
        if category == "sarees" || category == "salwar-kameez" || category == "earrings"
          mirraw_product_category1 = mirraw_product_category + ' > ' + design_category
        else
          mirraw_product_category1 = mirraw_product_category
        end

        # Using custom labels

        if design.created_at > 7.days.ago
          custom_label_0 = "listing0"
        elsif design.created_at > 30.days.ago
          custom_label_0 = "listing1"
        elsif design.created_at > 2.months.ago
          custom_label_0 = "listing2"
        elsif design.created_at > 6.months.ago
          custom_label_0 = "listing3"
        else
          custom_label_0 = "listing4"
        end

        if design.sell_count > 100
          custom_label_1 = "sell0"
        elsif design.sell_count > 10
          custom_label_1 = "sell1"
        elsif design.sell_count > 1
          custom_label_1 = "sell2"
        elsif design.sell_count == 1
          custom_label_1 = "sell3"
        else
          custom_label_1 = "sell4"
        end

        if design.effective_price > 30000
          custom_label_2 = "price0"
        elsif design.effective_price > 8000
          custom_label_2 = "price1"
        elsif design.effective_price > 4000
          custom_label_2 = "price2"
        elsif design.effective_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end

        custom_label_3 = ''
        custom_label_4 = ''

        design_description = ''
        if design.specification.present?
          design_description = design.specification.gsub(/\r/, " ").gsub(/\n/, " ")
        end

        if design.description.present?
          design_description += " " + design.description.gsub(/\r/, " ").gsub(/\n/, " ")
        end

        csv << [design.id, design.title.gsub(',', ':') + ' - ' + design_category, design_description.gsub(',',':').force_encoding("UTF-8"), design.designer.name, google_product_category, mirraw_product_category1, category, design_category,url, design.master_image.photo.url(:original), "new", "in stock", design.effective_price, "Female","Adult", custom_label_0, custom_label_1, custom_label_2, custom_label_3, custom_label_4]
      end
    end
    # upload that file
    file =  directory.files.create(
      :key    => "googlemerchant/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
  end
end



task :upload_salwar_kameez_feed => :environment do
  require 'csv'

  connection = Fog::Storage.new({
    :provider                 => 'AWS',
    :aws_access_key_id        => ENV['AWS_ACCESS_KEY_ID'],
    :aws_secret_access_key    => ENV['AWS_SECRET_ACCESS_KEY'],
    :region => "ap-southeast-1"
  })

  # First, a place to contain the glorious details
  directory = connection.directories.new(
    :key    => "mirraw-test"
  )

  filename = "new_google_feed_salwar_kameez" + '.csv'

  PATH = "/tmp/"
  filepath = PATH + filename

  Mapping = Hash.new

  salwar_category = Category.find_by_namei('salwar-kameez').self_and_descendants.collect{|c| c.name}

  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "brand", "google_product_category", "product_type", "adwords_grouping","adwords_labels","link", "image_link", "condition", "availability", "price", "gender", "age_group", "custom_label_0", "custom_label_1", "custom_label_2", "custom_label_3", "custom_label_4"]
    @designs = Design.published.select("DISTINCT(designs.id), designs.*").joins([:categories, :designer]).active_designer_designs.order('designs.created_at DESC').in_category("salwar-kameez");nil

    @designs.each do |design|

      google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
      mirraw_product_category = "Apparel > Clothing > Salwar Kameez & Suits"

      if design && design.title && design.images.present? && design.cached_slug.present? && design.designer.present?
        url = "http://www.mirraw.com/designers/" + design.designer.cached_slug + "/designs/" + design.cached_slug
        design_category = design.categories.first.name.gsub('-', ' ').camelize
        mirraw_product_category1 = mirraw_product_category + ' > ' + design_category

        # Using custom labels
        if design.created_at > 7.days.ago
          custom_label_0 = "listing0"
        elsif design.created_at > 30.days.ago
          custom_label_0 = "listing1"
        elsif design.created_at > 2.months.ago
          custom_label_0 = "listing2"
        elsif design.created_at > 6.months.ago
          custom_label_0 = "listing3"
        else
          custom_label_0 = "listing4"
        end

        if design.sell_count > 100
          custom_label_1 = "sell0"
        elsif design.sell_count > 10
          custom_label_1 = "sell1"
        elsif design.sell_count > 1
          custom_label_1 = "sell2"
        elsif design.sell_count == 1
          custom_label_1 = "sell3"
        else
          custom_label_1 = "sell4"
        end

        if design.effective_price > 30000
          custom_label_2 = "price0"
        elsif design.effective_price > 8000
          custom_label_2 = "price1"
        elsif design.effective_price > 4000
          custom_label_2 = "price2"
        elsif design.effective_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end

        custom_label_3 = ''
        custom_label_4 = ''

        design_description = ''
        if design.specification.present?
          design_description = design.specification.gsub(/\r/, " ").gsub(/\n/, " ")
        end

        if design.description.present?
          design_description += " " + design.description.gsub(/\r/, " ").gsub(/\n/, " ")
        end

        csv << [design.id, design.title.camelize.gsub(',', ':') + ' - ' + design_category, design_description.gsub(',',':').force_encoding("UTF-8"), design.designer.name, google_product_category, mirraw_product_category1, "salwar-kameez", design_category,url, design.master_image.photo.url(:original), "new", "in stock", design.effective_price, "Female","Adult", custom_label_0, custom_label_1, custom_label_2, custom_label_3, custom_label_4]
      end
    end
    # upload that file
    file =  directory.files.create(
      :key    => "googlemerchant/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
  end
end


task :upload_sarees_feed => :environment do
  require 'csv'

  connection = Fog::Storage.new({
    :provider                 => 'AWS',
    :aws_access_key_id        => ENV['AWS_ACCESS_KEY_ID'],
    :aws_secret_access_key    => ENV['AWS_SECRET_ACCESS_KEY'],
    :region => "ap-southeast-1"
  })

  # First, a place to contain the glorious details
  directory = connection.directories.new(
    :key    => "mirraw-test"
  )

  filename = "new_google_feed_sarees" + '.csv'

  PATH = "/tmp/"
  filepath = PATH + filename

  Mapping = Hash.new

  CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
    csv << ["id", "title", "description", "brand", "google_product_category", "product_type", "adwords_grouping","adwords_labels","link", "image_link", "condition", "availability", "price", "gender", "age_group", "custom_label_0", "custom_label_1", "custom_label_2", "custom_label_3", "custom_label_4"]
    @designs = Design.published.select("DISTINCT(designs.id), designs.*").joins([:categories, :designer]).active_designer_designs.order('designs.created_at DESC').in_category("sarees");nil

    @designs.each do |design|

      google_product_category = "Apparel & Accessories > Clothing > Traditional & Ceremonial Clothing"
      mirraw_product_category = "Apparel > Clothing > Sarees"

      if design && design.title && design.images.present? && design.cached_slug.present? && design.designer.present?
        url = "http://www.mirraw.com/designers/" + design.designer.cached_slug + "/designs/" + design.cached_slug
        design_category = design.categories.first.name.gsub('-', ' ').camelize
        mirraw_product_category1 = mirraw_product_category + ' > ' + design_category

        # Using custom labels
        if design.created_at > 7.days.ago
          custom_label_0 = "listing0"
        elsif design.created_at > 30.days.ago
          custom_label_0 = "listing1"
        elsif design.created_at > 2.months.ago
          custom_label_0 = "listing2"
        elsif design.created_at > 6.months.ago
          custom_label_0 = "listing3"
        else
          custom_label_0 = "listing4"
        end

        if design.sell_count > 100
          custom_label_1 = "sell0"
        elsif design.sell_count > 10
          custom_label_1 = "sell1"
        elsif design.sell_count > 1
          custom_label_1 = "sell2"
        elsif design.sell_count == 1
          custom_label_1 = "sell3"
        else
          custom_label_1 = "sell4"
        end

        if design.effective_price > 30000
          custom_label_2 = "price0"
        elsif design.effective_price > 8000
          custom_label_2 = "price1"
        elsif design.effective_price > 4000
          custom_label_2 = "price2"
        elsif design.effective_price > 1000
          custom_label_2 = "price3"
        else
          custom_label_2 = "price4"
        end

        custom_label_3 = ''
        custom_label_4 = ''
        design_description = design.specification.gsub(/\r/, " ").gsub(/\n/, " ") + " " + design.description.gsub(/\r/, " ").gsub(/\n/, " ")
        csv << [design.id, design.title.camelize.gsub(',', ':') + ' - ' + design_category, design_description.gsub(',',':').force_encoding("UTF-8"), design.designer.name, google_product_category, mirraw_product_category1, "salwar-kameez", design_category,url, design.master_image.photo.url(:original), "new", "in stock", design.effective_price, "Female","Adult", custom_label_0, custom_label_1, custom_label_2, custom_label_3, custom_label_4]
      end
    end
    # upload that file
    file =  directory.files.create(
      :key    => "googlemerchant/#{filename}",
      :body   => File.open(filepath),
      :public => true
    )
  end
end

task :upload_adroll_feed => :environment do
  require 'csv'

  connection = Fog::Storage.new({
    :provider                 => 'AWS',
    :aws_access_key_id        => ENV['AWS_ACCESS_KEY_ID'],
    :aws_secret_access_key    => ENV['AWS_SECRET_ACCESS_KEY'],
    :region => "ap-southeast-1"
  })

  # First, a place to contain the glorious details
  directory = connection.directories.new(
    :key    => "mirraw-test"
  )

  filename = "adroll_feed" + '.csv'

  PATH = "/tmp/"
  filepath = PATH + filename

  Mapping = Hash.new

  designs = Design.published.joins([:categories, :designer, :images]).active_designer_designs.uniq!

  CSV.open(filepath, "wb") do |csv|
    csv << ["id", "title", "link", "image_link","price"]
    designs.each do |design|
      if design && design.title && design.images.present? && design.cached_slug.present? && design.designer.present?
        url = "http://www.mirraw.com/designers/" + design.designer.cached_slug + "/designs/" + design.cached_slug
        csv << [design.id, design.title.camelize, url, design.master_image.photo.url(:original), design.discount_price/60]
      end
    end
  end
 # upload that file
  file =  directory.files.create(
    :key    => "adroll/#{filename}",
    :body   => File.open(filepath),
    :public => true
  )
end


task :upload_sweetcouch_feed => :environment do
  require 'csv'

  connection = Fog::Storage.new({
    :provider                 => 'AWS',
    :aws_access_key_id        => ENV['AWS_ACCESS_KEY_ID'],
    :aws_secret_access_key    => ENV['AWS_SECRET_ACCESS_KEY'],
    :region => "ap-southeast-1"
  })

  # First, a place to contain the glorious details
  directory = connection.directories.new(
    :key    => "mirraw-test"
  )

  filename = "sweetcouch_feed" + '.csv'

  PATH = "/tmp/"
  filepath = PATH + filename

  designs = Design.joins([:categories, :designer, :images]).active_designer_designs.uniq!

  CSV.open(filepath, "wb") do |csv|
    csv << ["id", "title", "link", "image_link","price", "Category", "Description", "Availability"]
    designs.each do |design|
      if design && design.title && design.images.present? && design.cached_slug.present? && design.designer.present?
        url = "http://www.mirraw.com/designers/" + design.designer.cached_slug + "/designs/" + design.cached_slug
        description = ''
        description = design.description.force_encoding('UTF-8') if design.description.present?
        state = design.state == 'in_stock' ? 'in_stock' : 'oos'

        csv << [design.id, design.title.camelize, url, design.master_image.photo.url(:original), design.discount_price, design.categories.first.name.gsub('-', ' ').camelize, description, state]
      end
    end
  end

 # upload that file
  file =  directory.files.create(
    :key    => "sweetcouch/#{filename}",
    :body   => File.open(filepath),
    :public => true
  )
end

desc 'Upload Junglee XML feed'
task :junglee_xml_feed => :environment do


  connection = Fog::Storage.new({
    :provider                 => 'AWS',
    :aws_access_key_id        => ENV['AWS_ACCESS_KEY_ID'],
    :aws_secret_access_key    => ENV['AWS_SECRET_ACCESS_KEY'],
    :region => "ap-southeast-1"
  })

  # First, a place to contain the glorious details
  directory = connection.directories.new(
    :key    => "mirraw-test"
  )

  AllColors = ["pink", "blue", "green", "red", "violet", "yellow", "cream", "brown", "gold", "silver", "black", "gray", "grey", "white", "orange", "cyan", "teal", "turquoise", "copper", "Emerald", "Ruby", "Sapphire", "Olive", "Wine", "Military"]
  ColorMap = ["Pink", "Blue", "Green", "Red", "Blue", "Yellow", "Off-White", "Brown", "Gold", "Silver", "Black", "Grey", "Grey", "White", "Orange", "Blue", "Blue", "Turquoise", "Brown", "Green", "Red", "Blue", "Green", "Red", "Green"]
  Materials = ["Temple Jewellery", "Kundan", "Meenakari", "Minakari", "Polki", "Thewa", "Terracotta", "Victorian", "Pearl", "Victorian", "Filigree", "Ivory", "Navratna", "Brass", "Wood", "Mughal", "Bead", "Stone", "Chanderi", "Kota", "Bengal", "Kalamkari", "Jute", "Bamboo", "Paithani", "Tussar", "Bandhani", "Patola", "Maheshawari", "Gadwal", "Kota Doria", "Crepe", "Benarasi", "Banarasi", "Kanjeevaram", "Pashmina", "Baluchari", "Chikan", "Varanasi", "Kantha", "Nine Yards", "Metal", "Silver Plated", "Gold Plated", "Base Metal", "Assam Silk", "Raw Silk", "Silk", "Cotton", "100% Cotton", "Pure Cotton", "Copper", "Alloy", "American Diamonds", "Pacchi", "Marble", "Carved Wood", "Wood", "Warli", "Ceramic", "Plastic", "Resin", "Synthetic", "Leather", "Cushion", "Velvet", "Crystal", "Jade", "Agate", "Onyx", "Brass", "SemiPrecious", "Semi Precious", "Vinyl", "Zari"]
  Fabric = ["Cotton", "Georgette", "Silk", "Kalamkari", "Tussar", "Tusser", "Supernet", "Net", "Jute", "Crepe", "Chiffon", "Art Silk", "Satin", "Velvet", "Chanderi", "Banarasi", "Brocade"]

  sarees_category = Category.find_by_namei('sarees').self_and_descendants.collect{|c| c.name}
  jewellery_category = Category.find_by_namei('jewellery').self_and_descendants.collect{|c| c.name}
  salwar_category = Category.find_by_namei('salwar-kameez').self_and_descendants.collect{|c| c.name}
  lehengas_category = Category.find_by_namei('lehengas').self_and_descendants.collect{|c| c.name}

  PATH = "#{Rails.root}/lib/tasks/"
  filepath = "/tmp/junglee.txt"

  Mapping = Hash.new

  CSV.foreach(PATH + "mapping.csv", :headers => true, :header_converters => :symbol, :converters => :all) do |row|
    Mapping[row[:name].downcase] = [row[:browsenode], row[:parentj], row[:parentm]]
  end

  designs = Design.published.select("DISTINCT(designs.id), designs.*").joins([:categories, :designer]).active_designer_designs

  builder = Nokogiri::XML::Builder.new do |xml|
  xml.Mirraw {
    designs.each do |design|
      if design && design.images.present? && design.categories.present? && design.designer.present?

        description = ''
        description = design.specification if design.specification
        description = description + '\n' + design.description if design.description

        color = 'Multicolor'
        AllColors.each_with_index do |c, i|
          if description && description.downcase.include?(c)
            color = ColorMap[i]
            break
          end
        end
        colour_name = color

        first_category = design.categories.first.name

        material_index = ''
        if sarees_category.include?(first_category) || salwar_category.include?(first_category)
          material_index = 'Synthetic'
          Fabric.each do |m|
            if description && description.downcase.include?(m.downcase)
              material_index = m
              break
            end
          end
        elsif jewellery_category.include?(first_category)
          material_index = 'Copper'
          Materials.each do |m|
            if description && description.downcase.include?(m.downcase)
              material_index = m
              break
            end
          end
        end
        categories = design.categories.collect {|c|c.name}.join(' ')
        categories.downcase!
        category = ''
        browse_node = ''
        readable_category = ''
        title_column = ''
        keywords1 = ''
        keywords2 = ''
        keywords3 = ''
        keywords4 = ''
        keywords5 = ''
        design.categories.each do |c|
          if Mapping[c.name.downcase].present? || (c.parent.present? && Mapping[c.parent.name.downcase].present?)

            if Mapping[c.name.downcase].present?
              value = Mapping[c.name.downcase]
              key = c.name
            elsif
              value = Mapping[c.parent.name.downcase]
              key = c.parent.name
            end
            category = value[1]
            browse_node = value[0]
            readable_category = key.gsub('-', ' ').camelize
            title_column = readable_category + ' - ' + design.title.gsub(/delivery in 14 days/i, '')
            keywords1 = readable_category
            keywords2 = ' '
            keywords3 = ' '
            keywords4 = ' '
            keywords5 = ' '
            @seo = SeoList.find(:first, :conditions => [ "lower(label) = ?", key.downcase ])
            if @seo
              keywords_arr = Array.new
              keywords_arr = @seo.keyword.split(',')
              keywords2 = keywords_arr[0] if keywords_arr[0]
              keywords3 = keywords_arr[1] if keywords_arr[1]
              keywords4 = keywords_arr[2] if keywords_arr[2]
              keywords5 = keywords_arr[3] if keywords_arr[3]
            elsif design.tag_list.present?
              keywords_arr = Array.new
              keywords_arr = design.tag_list
              keywords2 = keywords_arr[0] if keywords_arr[0]
              keywords3 = keywords_arr[1] if keywords_arr[1]
              keywords4 = keywords_arr[2] if keywords_arr[2]
              keywords5 = keywords_arr[3] if keywords_arr[3]
            end
          end
        end

        xml.product {
          xml.Title title_column
          xml.Sku design.id
          xml.Price design.effective_price
          xml.List_Price design.price
          xml.Description description.force_encoding('UTF-8') if description.present?
          xml.Availability design.in_stock? ? "TRUE" : "FALSE"
          xml.Shipping_Cost 0
          xml.Brand "Mirraw.com"
          xml.Gender "Female"
          xml.Image_link design.images.first.photo.url(:large).gsub('d1lycdyubshuoc.cloudfront.net', 'mirraw.s3.amazonaws.com')
          xml.Link_Column "http://www.mirraw.com/designers/" + design.designer.cached_slug + "/designs/" + design.cached_slug
          xml.Colour_Name colour_name
          xml.Material_Index material_index
          xml.Category category
          xml.Browse_Node browse_node
          xml.DeliveryTime 3
          xml.keywords1 keywords1
          xml.keywords2 keywords2
          xml.keywords3 keywords3
          xml.keywords4 keywords4
          xml.keywords5 keywords5
        }
      end
    end
  }
  end

  # upload that file
  file =  directory.files.create(
    :key    => "junglee/junglee_feed.xml",
    :body   => builder.to_xml,
    :public => true,
    :content_type => "text/xml"
  )
end

desc 'Upload junglee files'
task :upload_junglee_files => :environment do

  # Upload files on twice a week.
  if Time.now.wday == 0 || Time.now.wday == 3
    require 'csv'
    require 'net/ssh'
    require 'net/sftp'


    AllColors = ["pink", "blue", "green", "red", "violet", "yellow", "cream", "brown", "gold", "silver", "black", "gray", "grey", "white", "orange", "cyan", "teal", "turquoise", "copper", "Emerald", "Ruby", "Sapphire", "Olive", "Wine", "Military"]
    ColorMap = ["Pink", "Blue", "Green", "Red", "Blue", "Yellow", "Off-White", "Brown", "Gold", "Silver", "Black", "Grey", "Grey", "White", "Orange", "Blue", "Blue", "Turquoise", "Brown", "Green", "Red", "Blue", "Green", "Red", "Green"]
    Materials = ["Temple Jewellery", "Kundan", "Meenakari", "Minakari", "Polki", "Thewa", "Terracotta", "Victorian", "Pearl", "Victorian", "Filigree", "Ivory", "Navratna", "Brass", "Wood", "Mughal", "Bead", "Stone", "Chanderi", "Kota", "Bengal", "Kalamkari", "Jute", "Bamboo", "Paithani", "Tussar", "Bandhani", "Patola", "Maheshawari", "Gadwal", "Kota Doria", "Crepe", "Benarasi", "Banarasi", "Kanjeevaram", "Pashmina", "Baluchari", "Chikan", "Varanasi", "Kantha", "Nine Yards", "Metal", "Silver Plated", "Gold Plated", "Base Metal", "Assam Silk", "Raw Silk", "Silk", "Cotton", "100% Cotton", "Pure Cotton", "Copper", "Alloy", "American Diamonds", "Pacchi", "Marble", "Carved Wood", "Wood", "Warli", "Ceramic", "Plastic", "Resin", "Synthetic", "Leather", "Cushion", "Velvet", "Crystal", "Jade", "Agate", "Onyx", "Brass", "SemiPrecious", "Semi Precious", "Vinyl", "Zari"]

    PATH = "#{Rails.root}/lib/tasks/"
    filepath = "/tmp/junglee.txt"

    Mapping = Hash.new

    CSV.foreach(PATH + "mapping.csv", :headers => true, :header_converters => :symbol, :converters => :all) do |row|
      Mapping[row[:name].downcase] = [row[:browsenode], row[:parentj], row[:parentm]]
    end

    CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|

      # write junglee header
      header = ["SKU","Title","Link","Price","Delivery Time","Recommended Browse Node","Standard Product ID","Product ID Type","Category","Description","Shipping Cost","Image","List Price","Availability","Discount Amount","Sale Start Date","Sale End Date","Junglee Exclusive Offer","Offer Promo Code","Accepted Payment Methods","Language","Publication Date","Publisher","Authors","Binding","Edition","Book Volume","Release Date","Video Format","DVD Regions","Blu-ray Regions","Censor Rating","ESRB Rating","Actors","Directors","Producers","Genre","Sub Genre","Audio Format","Music Label","Artists","No of discs","Runtime","Brand","Manufacturer","Mfr part number","Model Number","Operating System","Processor Type","Processor Brand","Media Format","Gaming Hardware Platform","Computer Hardware Platform","Computer CPU speed","Hard Disk Format","Hard disk size","System RAM Type","Included RAM size","Wireless Type","Optical zoom","Digital zoom","Megapixels","Image Stabilization","Max Focal Length","Min Focal Length","Mounting Type","Printing Speed","Printer Maximum Media Size","Printer Output","Processor Socket","Refresh Rate","Monitor Response Time","Speaker Connectivity","Supported Audio Format","Tuner","Maps","Display size","Screen Resolution","Display Technology","Memory Storage Capacity","Memory Card Type","Viewfinder type","Flash type","Cellular Technology","Talk Time","Standby Time","Charging Time","User Input","Device Type","Form Factor","Connectivity Technology","Aspect Ratio","Special Features","Colour Name","Colour Map","Item package quantity","Warranty","Maximum Age","Minimum Age","League and Team","Target Gender","Target Audience","Band Material","Watch Movement","Watch Display","Band Colour","Band Width","Watch Dial Colour","Water Resistance Depth","Size Map","Material","Flavour","Scent","Theme HPC","Occasion Lifestyle","Chest Size","Cup Size","Waist Size","Inseam Length","Neck Size","Sleeve Length","Waist Style","Collar Type","Sleeve Type","Style","Heel Height","Sole Material","Boot Height","Shoe Width","Ring Size","Total Diamond Weight","Metal Type","Stone Clarity","Stone Colour","Stone Shape","Stone Type","Gold Carat","Size per Pearl","Puzzle Pieces","Rail Gauge","Scale","Recommended Season","Assembly required","Battery Cell Composition","Batteries Included","Batteries Required","Wattage","Power Source","Power Adapter Included","Shipping Weight","Weight","Length","Height","Width","Keywords1","Keywords2","Keywords3","Keywords4","Keywords5","Bullet point1","Bullet point2","Bullet point3","Bullet point4","Bullet point5","Other image-url1","Other image-url2","Other image-url3","Other image-url4","Other image-url5","Offer note","Is Gift Wrap Available","Registered Parameter","Update Delete"]

      hh = Hash.new
      header.each_with_index do |data, i|
        hh[data] = i
      end

      csv << header

      #designs = Design.published.joins(:categories).uniq!
      #designs = Design.published.joins(:categories).in_category('rakhi')
      #designs = Design.published.joins(:categories).where('categories.id <> ?',168).uniq
      #designs = Design.published
      designs = Design.published

      sku_column = hh["SKU"]
      title_column = hh["Title"]
      link_column = hh["Link"]
      price_column = hh["Price"]
      delivery_time_column = hh["Delivery Time"]
      standart_product_id = hh["Standard Product ID"]
      product_id_type = hh["Product ID Type"]
      description = hh["Description"]
      shipping_cost = hh["Shipping Cost"]
      list_price = hh["List Price"]
      availability = hh["Availability"]
      brand = hh["Brand"]
      manufacturer = hh["Manufacturer"]
      part_number = hh["Mfr part number"]
      model_number = hh["Model Number"]
      size_map = hh["Size Map"]
      gender = hh["Target Gender"]
      category = hh["Category"]
      browse_node = hh["Recommended Browse Node"]
      image = hh["Image"]
      discount_amount = hh["Discount Amount"]
      sale_start_date = hh["Sale Start Date"]
      sale_end_date = hh["Sale End Date"]
      junglee_exclusive_offer = hh["Junglee Exclusive Offer"]
      offer_promo_code = hh["Offer Promo Code"]
      colour_name = hh["Colour Name"]
      material_index = hh["Material"]
      metal = hh["Metal Type"]
      payments = hh["Accepted Payment Methods"]
      keywords1 = hh["Keywords1"]
      keywords2 = hh["Keywords2"]
      keywords3 = hh["Keywords3"]
      keywords4 = hh["Keywords4"]
      keywords5 = hh["Keywords5"]
      image5 = hh["Other image-url5"]
      image4 = hh["Other image-url4"]
      image3 = hh["Other image-url3"]
      image2 = hh["Other image-url2"]
      image1 = hh["Other image-url1"]
      offer_note = hh["Offer note"]

      # Coupons
      designs.each do |design|
        if design && design.designer && design.master_image
          row = Array.new
          row[payments] = "credit_card|debit_card|net_banking|paypal|prepaid_card"
          row[sku_column] = design.id
          row[link_column] = "http://www.mirraw.com/designers/" + design.designer.cached_slug + "/designs/" + design.cached_slug
          row[price_column] = design.effective_price
          row[delivery_time_column] = 3
          row[standart_product_id] = ""
          row[product_id_type] = ""
          row[description] = ''
          row[description] = design.specification if design.specification
          row[description] = row[description] + '\n' + design.description if design.description
          row[shipping_cost] = 0
          row[list_price] = design.price
          row[availability] = design.published? ? "TRUE" : "FALSE"
          row[brand] = "Mirraw.com"
          row[manufacturer] = "Mirraw.com"
          row[part_number] = design.id
          row[model_number] = model_number
          row[size_map] = "M"
          row[gender] = 'female'

          images = Array.new
          # Get all the image urls
          design.images.each do |image|
            images << image.photo.url(:original).gsub('d1lycdyubshuoc.cloudfront.net', 'mirraw.s3.amazonaws.com')
          end

          row[image] = images[0]
          row[image5] = images[5] if images.count >= 6
          row[image4] = images[4] if images.count >= 5
          row[image3] = images[3] if images.count >= 4
          row[image2] = images[2] if images.count >= 3
          row[image1] = images[1] if images.count >= 2


          coupon = Coupon.eligible_for(design)
          if coupon.present?
            if coupon.coupon_type == "FOFF"
              discounts = coupon.flat_off
            elsif coupon.coupon_type == "POFF"
              discounts = (coupon.percent_off * design.effective_price) / 100
            end
            row[discount_amount] = discounts
            row[sale_start_date] = coupon.start_date.to_date.strftime('%Y-%m-%d')
            row[sale_end_date] = coupon.end_date.to_date.strftime('%Y-%m-%d')
            row[junglee_exclusive_offer] = "FALSE"
            row[offer_promo_code] = coupon.code
          else
            if design.designer.coupons.live
              coupon = design.designer.coupons.live.first
              row[offer_note] = coupon.to_s if coupon && coupon.valid? && (coupon.percent_off || coupon.flat_off) && coupon.min_amount
            end
          end

          categories = design.categories.collect {|c|c.name}.join(' ')
          categories.downcase!

          design.categories.each do |c|
            if Mapping[c.name.downcase].present? || (c.parent.present? && Mapping[c.parent.name.downcase].present?)

              if Mapping[c.name.downcase].present?
                value = Mapping[c.name.downcase]
                key = c.name
              elsif
                value = Mapping[c.parent.name.downcase]
                key = c.parent.name
              end
              row[category] = value[1]
              row[browse_node] = value[0]
              readable_category = key.gsub('-', ' ').camelize
              row[title_column] = readable_category + ' - ' + design.title.gsub(/delivery in 14 days/i, '')
              row[keywords1] = readable_category

              @seo = SeoList.find(:first, :conditions => [ "lower(label) = ?", key.downcase ])
              if @seo
                keywords_arr = Array.new
                keywords_arr = @seo.keyword.split(',')

                row[keywords2] = keywords_arr[0] if keywords_arr[0]
                row[keywords3] = keywords_arr[1] if keywords_arr[1]
                row[keywords4] = keywords_arr[2] if keywords_arr[2]
                row[keywords5] = keywords_arr[3] if keywords_arr[3]
              elsif design.tag_list.present?
                keywords_arr = Array.new
                keywords_arr = design.tag_list
                row[keywords2] = keywords_arr[0] if keywords_arr[0]
                row[keywords3] = keywords_arr[1] if keywords_arr[1]
                row[keywords4] = keywords_arr[2] if keywords_arr[2]
                row[keywords5] = keywords_arr[3] if keywords_arr[3]
              end
              csv << row
              break
            end
          end

          if row[category] == 'jewelry'
            row[metal] = 'Copper'
          end
        end
      end
    end

    # Now ftp file to junglee
    Net::SFTP.start('dar-eu.amazon-digital-ftp.com', 'M_MIRRAWCOMH_1178988', :password => 'DjFCgRE6I2') do |sftp|
      sftp.upload!(filepath, "junglee_remote.txt")
    end
  end

end


desc 'Mail order csv file to aapno rajasthan'
task :send_csv_to_aapno => :environment do
  # Get all orders between 3pm yesterday and 10am today
  now  = DateTime.now.in_time_zone
  yest = now.yesterday

  today_10am = DateTime.new(now.year, now.month, now.day, 4, 30, 0, 0).in_time_zone
  yest_3pm = DateTime.new(yest.year, yest.month, yest.day, 9, 30, 0, 0).in_time_zone

  # Get all orders between 10am today and 3pm today
  today_3pm = DateTime.new(now.year, now.month, now.day, 9, 30, 0, 0).in_time_zone
  filepath = ''
  filepath_all = ''

  # Run when time is 10am and 3pm
  if Time.now.in_time_zone.strftime('%H').to_i == 10 || Time.now.in_time_zone.strftime('%H').to_i == 15 || ENV['FORCE'].present?

    if Time.now.in_time_zone.strftime('%H').to_i == 10
      time1 = yest_3pm
      time2 = today_10am
    else
      time1 = today_10am
      time2 = today_3pm
    end

    PATH = "/tmp/"
    filepath = PATH + "mirraw_aashi_time_#{Time.now.strftime("%m_%d_%H_%M")}.csv"
    filepath_all = PATH + "mirraw_all_pending_orders_#{Time.now.strftime("%m_%d_%H_%M")}.csv"

    CSV.open(filepath, "wb", {:col_sep => ","}) do |csv|
      header = ['Created On', 'Confirmed On', 'Order Number', 'Product/Variant Code', 'Quantity', 'Billing Name', 'Billing Address', 'Billing Phone', 'Shipping Name', 'Shipping Street', 'Shipping City', 'Shipping State', 'Shipping Country', 'Shipping pincode', 'Shipping Phone', 'Message', 'Delivery Date', 'Product Link', 'Variant Details with total price']
      csv << header
      DesignerOrder.includes(:line_items => [:design]).where('designer_id = ?', 129).where('designer_orders.state = ?', 'pending').where('designer_orders.created_at > ? and designer_orders.created_at < ?', time1, time2).find_in_batches(batch_size: 100) do |grp|
        next if grp.blank?
        grp.each do |dos|
          dos.line_items.each do |item|
            addon_text = item.design.title + ' - Rs ' + (item.price * item.quantity).to_s
            design_code = item.design.design_code.to_s
            variant_code = (design_code.include?('only') ? design_code : design_code + '_only')
            line_item_note = ''
            delivery_date = (dos.created_at.in_time_zone('Mumbai').to_date + 8.days).strftime('%d/%m/%Y')
            if item.note? && item.note.match('Rakhi Schedule Delivery')
              if dos.order.international?
                line_item_note = item.note.sub('Rakhi Schedule Delivery','').to_s
              else
                line_item_note = item.note.to_s
                delivery_date = '12-17 August, 2016'
              end
            end
            if (@addons = ApplicationHelper.get_addons(item.id))
              @addons.each do |addon|
                addon_price = addon[:price] * item.quantity
                variant_code = addon[:description]
                if addon_price != 0
                  addon_text = addon[:name].camelize + ' - Rs ' + ((item.price * item.quantity) + addon_price).to_s
                else
                  addon_text = addon[:name].camelize + ' - Rs ' + (item.price * item.quantity).to_s
                end
              end
            end
            csv << [dos.order.created_at.in_time_zone('Mumbai').strftime('%d/%m/%Y %H:%M'), dos.order.confirmed_at.in_time_zone('Mumbai').strftime('%d/%m/%Y %H:%M'), dos.order.number, variant_code, item.quantity.to_s, dos.order.billing_name, dos.order.billing_address, dos.order.billing_phone, dos.order.name, dos.order.street, dos.order.city, dos.order.buyer_state, dos.order.country, dos.order.pincode, dos.order.phone, line_item_note, delivery_date,"http://www.mirraw.com/designers/" + item.design.designer.cached_slug + "/designs/" + item.design.cached_slug, addon_text]
          end
        end
      end
    end

    CSV.open(filepath_all, "wb", {:col_sep => ","}) do |csv|
      header = ['Created On', 'Confirmed On', 'Order Number', 'Product/Variant Code', 'Quantity', 'Billing Name', 'Billing Address', 'Billing Phone', 'Shipping Name', 'Shipping Street', 'Shipping City', 'Shipping State', 'Shipping Country', 'Shipping pincode', 'Shipping Phone', 'Message', 'Delivery Date', 'Product Link', 'Variant Details with total price']
      csv << header
      DesignerOrder.includes(:line_items => [:design]).where('designer_id = ?', 129).where('designer_orders.state = ?', 'pending').find_in_batches(batch_size: 100) do |grp|
        next if grp.blank?
        grp.each do |dos|
          dos.line_items.each do |item|
            addon_text = item.design.title + ' - Rs ' + (item.price * item.quantity).to_s
            design_code = item.design.design_code.to_s
            variant_code = (design_code.include?('only') ? design_code : design_code + '_only')
            line_item_note = ''
            delivery_date = (dos.created_at.in_time_zone('Mumbai').to_date + 8.days).strftime('%d/%m/%Y')
            if item.note? && item.note.match('Rakhi Schedule Delivery')
              if dos.order.international?
                line_item_note = item.note.sub('Rakhi Schedule Delivery','').to_s
              else
                line_item_note = item.note.to_s
                delivery_date = '12-17 August, 2016'
              end
            end
            if (@addons = ApplicationHelper.get_addons(item.id))
              @addons.each do |addon|
                addon_price = addon[:price] * item.quantity
                variant_code = addon[:description]
                if addon_price != 0
                  addon_text = addon[:name].camelize + ' - Rs ' + ((item.price * item.quantity) + addon_price).to_s
                else
                  addon_text = addon[:name].camelize + ' - Rs ' + (item.price * item.quantity).to_s
                end
              end
            end
            csv << [dos.order.created_at.in_time_zone('Mumbai').strftime('%d/%m/%Y %H:%M'), dos.order.confirmed_at.in_time_zone('Mumbai').strftime('%d/%m/%Y %H:%M'), dos.order.number, variant_code, item.quantity.to_s, dos.order.billing_name, dos.order.billing_address, dos.order.billing_phone, dos.order.name, dos.order.street, dos.order.city, dos.order.buyer_state, dos.order.country, dos.order.pincode, dos.order.phone, line_item_note, delivery_date, "http://www.mirraw.com/designers/" + item.design.designer.cached_slug + "/designs/" + item.design.cached_slug, addon_text]
          end
        end
      end
    end
    #File.read(filepath_all)
    OrderMailer.send_csv_to_aashi(filepath, filepath_all).deliver
  end
end

task :pick_bestsellers1 => :environment do
  @designs = Design.where('grade > 0').where('created_at < ?', 30.days.ago)
  @designs.each do |design|
    if design && design.discount_price && design.quantity && design.available? && design.grade >= 0
      count = @design.line_items.where('line_items.created_at > ?', 5.days.ago).count
      unless count > 10
        design.grade = (design.grade / 2).to_i
        design.save
      end
    end
  end

  sell_count_hash = Hash.new
  @products_summary = LineItem.joins(:designer_order).where('designer_orders.created_at > ?', 2.days.ago).where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pickedup', 'pending', 'dispatched', 'completed').includes(:design).group('design_id').select('design_id, count(*) as item_count')
  @products_summary.each do |design|
    if design.item_count.to_i > 1
      sell_count_hash[design] = design.item_count.to_i
    end
  end

  sell_count_hash.each do |item, count|
    design = Design.find(item.design_id)
    if design && design.discount_price && design.available?
      grade = (((count * design.discount_price)/1000 ** 2)).to_i
      design.grade = grade > 0 ? grade : count
      design.save
    end
  end


  sell_count_hash1 = Hash.new
  @products_summary = LineItem.joins(:designer_order).where('designer_orders.created_at > ? AND designer_orders.created_at < ?', 5.days.ago, 2.days.ago).where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pickedup', 'pending', 'dispatched', 'completed').includes(:design).group('design_id').select('design_id, count(*) as item_count')
  @products_summary.each do |design|
    if design.item_count.to_i > 2
      unless sell_count_hash1[design].present?
        sell_count_hash1[design] = design.item_count.to_i
      end
    end
  end

  sell_count_hash1.each do |item, count|
    design = Design.find(item.design_id)
    if design && design.discount_price && design.available?
      grade = ((count * design.discount_price)/1000 ** 1.8).to_i
      design.grade = grade > 0 ? grade : count
      design.save
    end
  end


  sell_count_hash2 = Hash.new
  @products_summary = LineItem.joins(:designer_order).where('designer_orders.created_at > ? AND designer_orders.created_at < ?', 15.days.ago, 6.days.ago).where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pickedup', 'pending', 'dispatched', 'completed').includes(:design).group('design_id').select('design_id, count(*) as item_count')
  @products_summary.each do |design|
    if design.item_count.to_i > 3
      unless sell_count_hash2[design].present?
        sell_count_hash2[design] = design.item_count.to_i
      end
    end
  end

  sell_count_hash2.each do |item, count|
    design = Design.find(item.design_id)
    if design && design.discount_price && design.quantity && design.available?
      grade = ((count * design.discount_price)/1000 ** 1.5).to_i
      design.grade = grade > 0 ? grade : count
      design.save
    end
  end


  sell_count_hash3 = Hash.new
  @products_summary = LineItem.joins(:designer_order).where('designer_orders.created_at > ? AND designer_orders.created_at < ?', 30.days.ago, 16.days.ago).where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pickedup', 'pending', 'dispatched', 'completed').includes(:design).group('design_id').select('design_id, count(*) as item_count')
  @products_summary.each do |design|
    if design.item_count.to_i > 3
      unless sell_count_hash3[design].present?
        sell_count_hash3[design] = design.item_count.to_i
      end
    end
  end

  sell_count_hash3.each do |item, count|
    design = Design.find(item.design_id)
    if design && design.discount_price && design.quantity && design.available?
      grade = ((count * design.discount_price)/1000 ** 1.3).to_i
      design.grade = grade > 0 ? grade : count
      design.save
    end
  end
end


task :pick_bestsellers => :environment do

  # Downgrade old designs if they are not selling well
  @designs = Design.where('grade > 0').where('created_at < ?', 30.days.ago)
  @designs.each do |design|
    if design && design.discount_price && design.quantity && design.available? && design.grade >= 0
      count = @design.line_items.where('line_items.created_at > ?', 5.days.ago).count
      unless count > 10
        design.grade = (design.grade / 2).to_i
        design.save
      end
    end
  end

  sell_count_hash = Hash.new
  @products_summary = LineItem.joins(:designer_order).where('designer_orders.created_at > ?', 30.days.ago).where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pickedup', 'pending', 'dispatched', 'completed').includes(:design).group('design_id').select('design_id, count(*) as item_count')
  @products_summary.each do |design|
    if design.item_count.to_i > 1
      sell_count_hash[design] = (design.item_count.to_i ** 2).to_i
    end
  end

  sell_count_hash.each do |item, count|
    design = Design.find(item.design_id)
    if design && design.discount_price && design.quantity && design.available? && design.grade >= 0
      if design.discount_price < 1000
        if rand(3) == 2
          design.grade = rand(20) + 1
        else
          design.grade = count  if design.grade < count
        end
      else
        design.grade = count  if design.grade < count
      end
      design.save
    end
  end

  @designs = Design.published.where('grade > 1').where('price < ?', 2000).joins(:categories).in_category('jewellery')
  @designs.each do |design1|
    design = Design.find(design1.id)
    design.grade = (0.01 * design.grade).to_i
    design.save
  end

  @designs = Design.published.joins(:categories).in_category('jewellery').where('grade > 0').update_all(:grade => 0)
  Sunspot.index(@designs);nil;nil

  @designs = Designer.find(112).designs
  Sunspot.index(@designs);nil;nil


  sell_count_hash1 = Hash.new
  @products_summary = LineItem.joins(:designer_order).where('designer_orders.created_at > ? AND designer_orders.created_at < ?', 5.days.ago, 2.days.ago).where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pickedup', 'pending', 'dispatched', 'completed').includes(:design).group('design_id').select('design_id, count(*) as item_count')
  @products_summary.each do |design|
    if design.item_count.to_i > 2
      unless sell_count_hash1[design].present?
        sell_count_hash1[design] = (design.item_count.to_i ** 1.8).to_i
      end
    end
  end

  sell_count_hash1.each do |item, count|
    design = Design.find(item.design_id)
    if design && design.discount_price && design.quantity && design.available? && design.grade >= 0
      if design.discount_price < 700
        if rand(3) == 2
          design.grade = rand(20) + 1
        else
          design.grade = count  if design.grade < count
        end
      else
        design.grade = count  if design.grade < count
      end
    end
  end


  sell_count_hash2 = Hash.new
  @products_summary = LineItem.joins(:designer_order).where('designer_orders.created_at > ? AND designer_orders.created_at < ?', 15.days.ago, 6.days.ago).where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pickedup', 'pending', 'dispatched', 'completed').includes(:design).group('design_id').select('design_id, count(*) as item_count')
  @products_summary.each do |design|
    if design.item_count.to_i > 3
      unless sell_count_hash2[design].present?
        sell_count_hash2[design] = (design.item_count.to_i ** 1.5).to_i
      end
    end
  end

  sell_count_hash2.each do |item, count|
    design = Design.find(item.design_id)
    if design && design.discount_price && design.quantity && design.available? && design.grade >= 0
      if design.discount_price < 700
        if rand(3) == 2
          design.grade = rand(20) + 1
        else
          design.grade = count.to_i  if design.grade < count.to_i
        end
      else
        design.grade = count  if design.grade < count
      end
      design.save
    end
  end

  sell_count_hash3 = Hash.new
  @products_summary = LineItem.joins(:designer_order).where('designer_orders.created_at > ? AND designer_orders.created_at < ?', 30.days.ago, 16.days.ago).where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pickedup', 'pending', 'dispatched', 'completed').includes(:design).group('design_id').select('design_id, count(*) as item_count')
  @products_summary.each do |design|
    if design.item_count.to_i > 3
      unless sell_count_hash3[design].present?
        sell_count_hash3[design] = (design.item_count.to_i ** 1.2).to_i
      end
    end
  end

  sell_count_hash3.each do |item, count|
    design = Design.find(item.design_id)
    if design && design.discount_price && design.quantity && design.available? && design.grade >= 0
      if design.discount_price < 700
        if rand(3) == 2
          design.grade = rand(20) + 1
        else
          design.grade = count.to_i  if design.grade < count.to_i
        end
      else
        design.grade = count  if design.grade < count
      end
      design.save
    end
  end

  # Products with higher grade that were set by system

  # Bump new products higher
  sell_count_hash = Hash.new

  @products_summary = LineItem.joins(:designer_order).where('designer_orders.created_at > ?', 30.days.ago).where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pickedup', 'pending', 'dispatched', 'completed').includes(:design).group('design_id').select('design_id, count(*) as item_count')
  @products_summary.each do |design|
    if design.item_count.to_i > 1
      sell_count_hash[design] = (design.item_count.to_i ** 2.5).to_i
    end
  end

  sell_count_hash.each do |item, count|
    design = Design.find(item.design_id)
    if design && design.discount_price && design.quantity && design.available? && design.grade >= 0 && design.created_at > 30.days.ago
      if design.discount_price < 700
        if rand(3) == 2
          design.grade = rand(20) + 1
        else
          design.grade = count  if design.grade < count
        end
      else
        design.grade = count  if design.grade < count
      end
      design.save
    end
  end

  # Now downgrade designs that are not performing well
end


desc 'Update sell count'
task :update_sell_count => :environment do
  @products_summary = LineItem.joins(:designer_order).where('designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ? OR designer_orders.state = ?', 'pickedup', 'pending', 'dispatched', 'completed').includes(:design).group('design_id').select('design_id, count(*) as item_count')
  @products_summary = @products_summary.collect {|design| [design, design.item_count.to_i]}

  @products_summary.each do |item, count|
    design = Design.find(item.design_id)
    design.sell_count = count
    design.save
  end
  Design.where('sell_count is NULL').update_all(:sell_count => 0)
end

desc 'send email to designers about orders items'
task :send_email_to_designers_orders_items => :environment do
  s_clause = 'orders.number as order_number, designs.designer_id as designer_id, designs.id as design_id, designs.design_code as design_code'
  o_clause = 'orders.number'
  w_clause = 'orders.confirmed_at IS NOT NULL'
  w_inter_clause = 'LOWER(orders.country) <> ?', 'india'

  designer_list = Designer.select('name, email, id').where('name is not null').where('email is not null')

  d_received_items = LineItem.
    select(s_clause).
    joins([:designer_order => :order], :design).
    where('line_items.received_on between ? and ?', Date.yesterday.beginning_of_day, Date.yesterday.end_of_day).
    where(w_clause).
    where(w_inter_clause).
    order(o_clause).
    group_by{|item| item[:designer_id]}

  d_not_received_items = Order.
    select(s_clause).
    joins(:designer_orders => [:line_items => :design]).
    where('orders.items_received_status != ?', true).
    where('orders.created_at BETWEEN ? and ?', 1.month.ago, Date.current).
    where('line_items.received != ?', 'Y').
    where(w_clause).
    where(w_inter_clause).
    order(o_clause).
    group_by{|item| item[:designer_id]}

  designer_list.each do |designer|

    id = designer.id.to_s

    received_items_list, not_received_items_list = [],[]
    send = false

    if d_received_items[id].present?
      send = true
      d_received_items[id].each {|item| received_items_list << item.attributes }
    end

    if d_not_received_items[id].present?
      send = true
      d_not_received_items[id].each {|item| not_received_items_list << item.attributes}
    end

    DesignerMailer.delay.inventory_report(designer.email, designer.name, received_items_list, not_received_items_list) if send

  end
end

desc 'Get ccavenue payment bulk status for yesterdays orders'
task :ccavenue_payment_bulk_status => :environment do
  date = Date.yesterday.strftime('%Y-%m-%d')
  #date = '2014-01-15'
  api = 'https://mars.ccavenue.com/servlet/ccav.MerchantReconReport?User_Id='+MERCHANT_ID+'&Date='+date
  begin
    res = HTTParty.get(api)
    orders = []
    order_no_regex = /^M{1}\d{9}$/
    if res.code == 200
      orders_data = res.body.split('Customer IP')[1]
      ip_add_regex = /\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/
      orders_data.split(ip_add_regex).each do |line|
        p = line.split(',')
        #Resonse file table column headers
        #Order Id,Transaction Gateway,Transaction Date,Transaction Amount,Transaction Status,
        #Customer Name,Customer Address,Customer Email,Customer Tel,Customer IP
        if p.count == 9
          if (order_no = order_no_regex.match(p[0]).to_a).present?
            orders << {:no => order_no[0],:amount => p[3],:status => p[4],:name => p[5],:email => p[7],:tel => p[8]}
          end
        end
      end
      orders = orders.group_by{|o| o[:order_no]}
      list = []
      # Removing declined, error if order has success
      orders.each do |order, values|
        if values.count > 1
          success, error, declined, other = [],[],[],[]
          values.each_with_index do |value, index|
            # Collecting multiple statuses for same order
            if value[:status].present?
              case value[:status].downcase
              when 'success'
                success << index
              when 'declined'
                declined << index
              when 'error'
                error << index
              else
                other << index
              end
            end
          end
          # Deleting error and declined status index if success status present
          if success.present?
            values.delete(error) if error.present?
            values.delete(declined) if declined.present?
          end
        end
        list += values.select{|v| v.present?}
      end
      if list.present?
        list = list.group_by {|l| l[:status]}
        order_number_list = list['SUCCESS'].collect{|l| l[:no]}
        orders_db = Order.where(:number => order_number_list).where(:state => ['new','pending'])
        success = list['SUCCESS'].group_by{|l| l[:no]}
        list['NOT MARKED SANE'] = []
        list.delete('SUCCESS')
        orders_db.each do |o|
          list['NOT MARKED SANE'] << {:no => o.number, :amount => success[o.number].first[:amount], :status => 'NOT MARKED SANE', :name => o.name, :email => o.email, :tel => o.phone}
        end
        list = {'NOT MARKED SANE' => list['NOT MARKED SANE'],
          'ERROR' => list['ERROR'],
          'DECLINED' => list['DECLINED'],
          'REFUNDED' => list['REFUNDED']
          }
        OrderMailer.send_mail_ccavenue_payments_status(list).deliver
      end
    end
  rescue Timeout::Error
    # Do Nothing
  rescue
    # Do Nothing
  end
end

desc 'refund details pending'
task :refund_details_pending => :environment do
  if Time.now.wday == 0 || Time.now.wday == 3
    s_clause = 'orders.billing_name as name, orders.billing_email as email, orders.number as order_number'
    returns = Return.select(s_clause).joins(:order).where(:state => 'refund_details_pending')
    returns.each {|return1| ReturnMailer.inform_refund_details_pending(return1[:name], return1[:email], return1[:order_number]).deliver }
  end
end

desc 'return items pending'
task :return_items_pending => :environment do
  if Time.now.wday == 0 || Time.now.wday == 3
    returns = Return.includes([[:return_designer_orders => [:line_items => :design]], :order]).where(:state => 'new')
    returns.each do |return1|
      pending_items_rdo, received_items_rdo = return1.check_items
      if pending_items_rdo.present? or received_items_rdo.present?
        ReturnMailer.inform_items_not_returned(return1.order.name, return1.order.email, return1.order.number, pending_items_rdo, received_items_rdo).deliver
      end
    end
  end
end

desc 'Items received with delay'
task :items_not_received_yet => :environment do
  ### Not received within 5 days
  all_orders = Order.where(geo: 'international').where('state = ? and items_received_status <> ? and confirmed_at::date between ? and ? ','sane',true,Date.today - 2.month ,Date.today - 6.day)
  orders_number = []
  all_orders.each do |order|
    OrderMailer.delay(priority: 5).items_not_received_yet(order.id) if order.confirmed_at.to_date == (Date.today - 6.days)
    orders_number << order.number
  end
  OrderMailer.delay.list_items_not_received_yet(orders_number)
end

desc 'buyer_dispatch_items'
task :buyer_disptach_items => :environment do
  if Time.now.wday == 0 || Time.now.wday == 3
    rdos_des = ReturnDesignerOrder.includes([:designer, [:line_items => :design]]).where(:state => 'buyer_dispatched').group_by{|rdo| rdo[:designer_id]}
    rdos_des.each {|designer_id, rdos| ReturnMailer.enquiry_items_received(rdos).deliver}
  end
end

desc 'Update Tracking State'
task :update_tracking_state_fedex => :environment do
  fedex_ids = Shipper.where(:name => ['Fedex', 'Fedex-Mirraw']).pluck(:id)
  shipments = Shipment.where('shipment_state <> ?', 'delivered').where(:shipper_id => fedex_ids)
  shipments.each {|s| s.delay.update_state('FEDEX') }
end

desc 'Update Tracking State Delhivery'
task :update_tracking_state_delhivery_processing => :environment do
  shipper_id = Shipper.where(:name => ['Delhivery']).pluck(:id)
  Shipment.find_in_batches(:conditions => {:shipment_state => 'processing', :shipper_id => shipper_id}, :batch_size => 300) do |shipments|
    awb_numbers = shipments.collect(&:number)
    Shipment.delhivery_mass_tracking(awb_numbers, shipper_id)
  end
end

desc 'Update Tracking State Delhivery'
task :update_tracking_state_delhivery_in_transit => :environment do
  shipper_id = Shipper.where(:name => ['Delhivery']).pluck(:id)
  Shipment.find_in_batches(:conditions => {:shipment_state => 'in_transit', :shipper_id => shipper_id}, :batch_size => 300) do |shipments|
    awb_numbers = shipments.collect(&:number)
    Shipment.delhivery_mass_tracking(awb_numbers, shipper_id)
  end
end

desc 'Update Tracking State Delhivery'
task :update_tracking_state_delhivery_out_for_delivery => :environment do
  shipper_id = Shipper.where(:name => ['Delhivery']).pluck(:id)
  Shipment.find_in_batches(:conditions => {:shipment_state => 'out_for_delivery', :shipper_id => shipper_id}, :batch_size => 300) do |shipments|
    awb_numbers = shipments.collect(&:number)
    Shipment.delhivery_mass_tracking(awb_numbers, shipper_id)
  end
end

desc 'Update Tracking State Delhivery'
task :update_tracking_state_delhivery_delivery_exception => :environment do
  shipper_id = Shipper.where(:name => ['Delhivery']).pluck(:id)
  Shipment.find_in_batches(:conditions => {:shipment_state => 'delivery_exception', :shipper_id => shipper_id}, :batch_size => 300) do |shipments|
    awb_numbers = shipments.collect(&:number)
    Shipment.delhivery_mass_tracking(awb_numbers, shipper_id)
  end
end

task :import_list_to_mailup => :environment do

  @orders = Order.where('created_at > ?', 1.day.ago)
  @orders.each do |order|
    email = order.billing_email
    name = order.billing_name.split(' ').first #campo1
    city = order.billing_city #campo4
    zip = order.billing_pincode #campo6
    state = order.billing_state #campo7
    region = order.billing_country #campo8
    number = order.number  #campo13
    date = order.created_at.strftime('%m/%d/%Y')
    amount = order.total #campo15
    res = HTTParty.get(URI.encode("http://d4b8h.s70.it/frontend/xmlSubscribe.aspx?list=2&group=21&email=#{email}&confirm=false&csvFldNames=campo1;campo4;campo6;campo7;campo8;campo13;campo14;campo15;&csvFldValues=#{name};#{city};#{zip};#{state};#{region};#{number};#{date};#{amount}"))
  end
end

# Fedex pickup availability
desc 'pick availability for designers'
task :pick_availability_for_designers => :environment do
  today_datetime = DateTime.now.in_time_zone
  options = {:country_code => 'IN', :request_type => 'SAME_DAY', :dispatch_date => today_datetime.strftime('%Y-%m-%d'), :carrier_code => 'FDXE'}

  # Get list of designer_id for whom pickup availability is already checked
  designer_ids_pickup = Pickup.select(:designer_id).where(:pickup_date => today_datetime.to_date)
  designer_ids = []
  designer_ids = designer_ids_pickup.collect{|p| p.designer_id}

  # Get list of [designer_id, pincode] for whom pickup availability is not checked
  fedex_shipper_id = Shipper.where(name: 'Fedex').first.id
  designers = Designer.select([:id, :pincode]).joins(:designer_shippers).where('pincode IS NOT NULL').
                where(designer_shippers: {cod: true, shipper_id: fedex_shipper_id})
  designers = designers.where('designers.id NOT IN (?)', designer_ids) if designer_ids.present?

  pickup = []
  fedex  = Shipment.fedex_api('domestic')
  designers.each do |d|
    if d.pincode.length == 6
      options[:postal_code] = d.pincode
      begin
        fedex.pickup_availability(options)[:options].each do |option|
          # Date Time object requires integer parameters
          pickup_split = option[:pickup_date].split('-').map(&:to_i)
          cutoff_time  = option[:cut_off_time].split(':').map(&:to_i)
          cutoff_date_time = DateTime.new(pickup_split[0], pickup_split[1], pickup_split[2], cutoff_time[0], cutoff_time[1], 00, '+0530').utc

          if option[:country_relationship] == 'DOMESTIC' && Pickup.where(:pickup_date => option[:pickup_date], :designer_id => d.id).blank?
            pickup << {:pickup_date => option[:pickup_date], :designer_id => d.id, :pickup_created => false, :cutoff_time => cutoff_date_time}
          end
        end
      rescue => error
        puts error
        puts d.pincode
      end
    end
  end
  Pickup.create!(pickup)
end

desc 'create pickups for designers with early cutoffs'
task :create_pickups_designers_early_cutoffs => :environment do
  designer_ids = DesignerOrder.joins(:shipment).where(:state => 'pickedup').where('designer_orders.designer_id IS NOT NULL').pluck(:designer_id)
  today_zero_hour = DateTime.now.beginning_of_day
  pickups = Pickup.where(:designer_id => designer_ids).where('cutoff_time < ?', today_zero_hour.advance(:hours => 13)).where(:pickup_date => today_zero_hour.to_date)
  pickups.each do |pickup|
    readytime = pickup.cutoff_time.advance(:minutes => -10)
    closetime = today_zero_hour.advance(:hours => 19)
    pickup.delay.create_fedex_pickup(readytime, closetime)
  end
end

desc 'update_pickups_details'
task :update_pickups_details => :environment do
  pickups = Pickup.where('packages_shipped IS NULL').where('pickup_date < ? ', Date.today).each(&:update_pickup_details_shipment)
end

task :send_empty_category_email => :environment do
  arr = Array.new

  Category.leaves.each do |c|
    if c.designs.published.count < 60
      arr << c.name
    end
  end

  if arr.present?
    FollowMailer.delay(priority: 4).sendout_category_blank_alert(arr)
  end
end

desc 'send mails for optional pickup'
task :send_mails_for_optional_pickup => :environment do
  designer_orders = DesignerOrder.joins(:order).joins(:shipment).
                                where(order: {state: 'sane',pay_type: COD},designer_orders: {state: 'pickedup'}).
                                where('designer_orders.tracking_num is not null and designer_orders.tracking_partner is not null').
                                where('shipments.shipment_state = ?','processing')
  normal_pickup_by_shipper = designer_orders.includes(:designer).where('designer_orders.pickup between ? and ?',3.days.ago,Time.now).group_by(&:tracking_partner)
  premium_pickup_by_shipper = designer_orders.includes(:designer).where('designer_orders.pickup between ? and ?',2.months.ago,4.days.ago).group_by(&:tracking_partner)
  normal_pickup_by_shipper = Hash[normal_pickup_by_shipper.map{|k,v| [k.strip.downcase,v]}]
  premium_pickup_by_shipper =Hash[premium_pickup_by_shipper.map{|k,v| [k.strip.downcase,v]}]
  SHIPPER_EMAILS.keys.each do |shipper_name|
    if normal_pickup = normal_pickup_by_shipper[shipper_name.strip.downcase]
      normal_csv_by_shipper = OptionalPickup.to_csv(normal_pickup)
      ShipmentMailer.delay.optional_pickup_list(normal_csv_by_shipper, shipper_name, SHIPPER_EMAILS[shipper_name],false) if normal_csv_by_shipper.length > 1
    end
    if premium_pickup = premium_pickup_by_shipper[shipper_name.strip.downcase]
      premium_pickup_csv_by_shipper = OptionalPickup.to_csv(premium_pickup)
      ShipmentMailer.delay.optional_pickup_list(premium_pickup_csv_by_shipper, shipper_name, SHIPPER_EMAILS[shipper_name],true) if premium_pickup_csv_by_shipper.length > 1
    end
  end
end

task :send_mails_for_stitching_measurement_reminder => :environment do
  Order.unscoped.
    joins(:line_items).joins{stitching_measurements.outer}.
    select("orders.id, orders.billing_phone, orders.country_code, orders.number, orders.confirmed_at,count(stitching_measurements.id) as sm_count,(sum(case when line_items.stitching_required = 'Y' then line_items.quantity else 0 end)/count(line_items.id)) as li_count").
    where("other_details @> hstore(:key, :value)",key: 'stitching_order',value: 'true').
    where('line_items.status is null and designer_orders.state NOT IN (?) and orders.state = ? and orders.confirmed_at between ? and ?',%w(canceled rto vendor_canceled buyer_returned),'sane',1.month.ago,2.days.ago).
    group('orders.id').find_each(batch_size: 200) do |order|
    if (Date.today - order.confirmed_at.to_date) % 2 == 0 && order['li_count'].to_i > order['sm_count'].to_i && order.stitching_addon?(true, true)
      OrderMailer.delay.stitching_measurement(order.id)
      order.send_confirmation_sms_international('stitching_form')
    end
  end
end

task send_oos_alert_for_bestsellers: :environment do
  value = (Time.now.beginning_of_day - SystemConstant.get('BEST_SELL_FOR').to_i.days)..(Time.now.end_of_day)
  products_summary = Design.where("designs.state <> 'in_stock'").joins(line_items: :designer_order).
      where(line_items: {designer_order: {state: ['pickedup', 'pending', 'dispatched', 'completed']},
      created_at: value}).group('designs.id').having('SUM(line_items.quantity) > 4').sum('line_items.quantity')

  if products_summary.present?
    FollowMailer.delay(priority: 4).bestseller_oos_alerts(products_summary)
  end
end

task populate_bestsellers: :environment do
   bestseller_designs = Bestseller.get_bestsellers()
   fields = []
   Bestseller.delete_all
   bestseller_designs[0].each do |design|
    fields.push({design_id: design.id, design_price: design.effective_price, geo: 'international'});
   end
   bestseller_designs[1].each do |design|
    fields.push({design_id: design.id, design_price: design.effective_price, geo: 'domestic'});
   end
   Bestseller.create(fields)
end

task send_oos_alert_for_campaign: :environment do
  products_summary = OosAlert.where(active: true).includes(:design).
                      where("designs.state <> 'in_stock'").group_by(&:email)

  if products_summary.present?
    products_summary.each do |email, data|
      FollowMailer.delay(priority: 4).campaign_oos_alerts(email, data)
    end
  end
end

task :sendout_cancelled_order_notification  => :environment do
  frequency = 2
  cancelled_orders = Order.where('cancel_mail_sent_at IS NOT NULL AND cancel_mail_sent_at < ? AND cancel_mail_sent_count < ?', 1.day.ago, frequency)

  if cancelled_orders.present?
    cancelled_orders.reject! do |order|
      Order.where(:email => order.email).where("created_at > ?", order.created_at).present?
    end

    cancelled_orders.each do |order|
      OrderMailer.delay.convey_cancel_update_to_buyer(order) unless order.tag_list.include?('oos')
      Order.where(:id => order.id).update_all(:cancel_mail_sent_count => order.cancel_mail_sent_count + 1,  :cancel_mail_sent_at => DateTime.now)
    end
  end
end

task :update_sold_out_products_in_solr_index => :environment do
  sold_out_designs = Design.where(:state => 'sold_out').where('updated_at > ?', 6.months.ago).count
end

#Run task by joining shipper names with underscore :
#rake update_shipments["ship delight_delhivery_fedex_fedex-mirraw_aramex_bluedart_dhl_speedpost_gati_atlantic"]
task :update_shipments, [:all_shipper_seq] => :environment do |task,args|
  unless Rails.env.production?
    logger = Logger.new(STDOUT)
    logger.level = Logger::DEBUG
    Rails.logger = Dalli.logger = ActiveRecord::Base.logger = logger
  end
  shippers = Hash[Shipper.select('id,name').where('lower(name) in (?)',args[:all_shipper_seq].split('_')).map do  |u|
    [u.id, u.name] if Shipment.respond_to?(( u.name.present? && u.name.downcase.gsub(/[^a-z]/,'') + '_mass_tracking').to_sym)
  end.compact]
  shippers.each do |shipper_id, shipper_name|
    method_to_invoke = (shippers[shipper_id].downcase.gsub(/[^a-z]/,'') + '_mass_tracking').to_sym
    Shipment.forward.select('shipments.id, number').
    where(shipper_id: shipper_id, track: true).
    where('shipment_state NOT IN (?) and shipments.created_at >= ?', ['delivered', 'rto', 'processing_abandoned', 'intransit_abandoned'], 3.months.ago).find_in_batches(batch_size: 500) do |shipments|
      Shipment.delay({:priority => -6}).send(method_to_invoke, shipments.collect(&:number), shipper_id)
    end
  end
end

task :update_shipments_sidekiq, [:all_shipper_seq] => :environment do |task,args|
  UpdateShipmentsJob.perform_async(args[:all_shipper_seq])
end

task :update_shipments_with_track_removed, [:all_shipper_seq] => :environment do |task,args|
  unless Rails.env.production?
    logger = Logger.new(STDOUT)
    logger.level = Logger::DEBUG
    Rails.logger = Dalli.logger = ActiveRecord::Base.logger = logger
  end
  shippers = Hash[Shipper.select('id,name').where('lower(name) in (?)',args[:all_shipper_seq].split('_')).map do  |u|
    [u.id, u.name] if Shipment.respond_to?(( u.name.present? && u.name.downcase.gsub(/[^a-z]/,'') + '_mass_tracking').to_sym)
  end.compact]
  shippers.each do |shipper_id, shipper_name|
    method_to_invoke = (shippers[shipper_id].downcase.gsub(/[^a-z]/,'') + '_mass_tracking').to_sym
    Shipment.forward.select('shipments.id, number').
    where(shipper_id: shipper_id).
    where('shipment_state NOT IN (?) and shipments.created_at >= ?', ['delivered', 'rto', 'processing_abandoned', 'intransit_abandoned'], 3.months.ago).find_in_batches(batch_size: 500) do |shipments|
      Shipment.delay({:priority => -6}).send(method_to_invoke, shipments.collect(&:number), shipper_id)
    end
  end
end

task :update_shipments_with_track_removed_sidekiq, [:all_shipper_seq] => :environment do |task,args|
  UpdateShipmentsWithTrackRemovedJob.perform_async(args[:all_shipper_seq])
end 

task :order_delayed_notifications_customer_domestic => :environment do
  Order.processing_notifications(DesignerOrder.unscoped.designer_orders_in_three_months.
    joins(:order).
    where(state: 'pending').
    where("DATE_PART('day', CURRENT_TIMESTAMP - designer_orders.confirmed_at) > #{DELAY_DOM_PENDING_FIRST_MAIL}").
    where.not(designer_orders: {confirmed_at: nil}).
    where.not(orders: {confirmed_at: nil}).
    where(orders: {country: 'India'}).
    pluck('distinct(designer_orders.order_id)'))
end

# After 24 hours, User receives Update only if order is in pending state
task :order_delayed_notifications_after_24_hrs_customer_domestic => :environment do
  flag_24_hrs = true
  w_confirmed_at_present = 'designer_orders.confirmed_at IS NOT NULL'
  w_time = "DATE_PART('day', CURRENT_TIMESTAMP - designer_orders.confirmed_at) = 1"
  w_states = {:state => 'pending'}
  w_india = {:orders => {:country => 'India'}}

  order_ids = DesignerOrder.unscoped.
    joins(:order).
    where(w_states).
    where(w_time).
    where(w_confirmed_at_present).
    where(w_india).
    pluck('distinct(designer_orders.order_id)')
  unless order_ids.nil?
    orders = Order.where(:id => order_ids)
    orders.each do |order|
      if order.email_logs.where(:email_type => 'order_processing_notification_24_hrs').count < 1
        OrderMailer.delay.order_processing_notification(order.id, flag_24_hrs)
      end
    end
  end
end

task :order_delayed_notifications_customer_international => :environment do
  Order.processing_notifications(Order.orders_in_three_months.where(state: 'sane').
    where("DATE_PART('day', CURRENT_TIMESTAMP - orders.confirmed_at) > #{DELAY_INT_PENDING_FIRST_MAIL}").
    where.not(orders: {confirmed_at: nil}).
    where.not(orders: {country: 'India'}).
    pluck(:id))
end

task :pickup_mailer => :environment do
  Shipment.mail_pickup_list
end

task :stitching_summary, [:day] => [:environment] do |t, args|
  summary = Hash.new
  today = args[:day].try(:to_time) || Time.zone.now.to_time
  value = today.beginning_of_day()..today.end_of_day()

  international_orders = Order.international_orders
  orders_by_type = international_orders.stitching.pluck('orders.id')
  orders_by_tag = international_orders.tagged_with('stitching').pluck('orders.id')
  order_ids = (orders_by_type + orders_by_tag).uniq

  item_ids_by_addon = LineItem.stitching.joins(:designer_order).
                where(designer_orders: {order_id: order_ids}).
                pluck('line_items.id')


  item_ids_by_tag = LineItem.where(stitching_required: 'Y').joins(:designer_order).
                where(designer_orders: {order_id: orders_by_tag}).
                pluck('line_items.id')

  item_ids = (item_ids_by_addon + item_ids_by_tag).uniq

  stitching_orders = Order.where(id:  order_ids)
  stitching_items = LineItem.where(id: item_ids)

  mark_sane = stitching_orders.where(state: 'sane', confirmed_at: value).count
  received_in_warehouse = stitching_items.includes(:designer_order).
                          where(designer_order: {package_received_on: value}).count
  item_received = stitching_items.where(received_on: value).count
  qc_done = stitching_items.where("qc_done IS NOT NULL").where(qc_done_on: value).count
  fabric_measured = stitching_items.where("fabric_measured_by IS NOT NULL").
                    where(fabric_measured_on: value).count
  measurement_received = stitching_items.where(measuremnet_received_on: value).count
  stitching_sent = stitching_items.where("stitching_sent IS NOT NULL").
                  where(stitching_sent_on: value).count
  received_from_stitching = stitching_items.where(stitching_done_on: value).count
  dispatched = stitching_orders.where(state: 'dispatched', pickup: value).count

  summary[:mark_sane] = mark_sane
  summary[:received_in_warehouse] = received_in_warehouse
  summary[:item_received] = item_received
  summary[:qc_done] = qc_done
  summary[:fabric_measured] = fabric_measured
  summary[:measurement_received] = measurement_received
  summary[:stitching_sent] = stitching_sent
  summary[:received_from_stitching] = received_from_stitching
  summary[:dispatched] = dispatched

  OrderMailer.stitching_summary(summary, today).deliver
end

task :oos_report_last_30days => :environment do
  # OOS Data
  connection = Fog::Storage.new({
    :provider                 => 'AWS',
    :aws_access_key_id        => ENV['AWS_ACCESS_KEY_ID'] ,
    :aws_secret_access_key    => ENV['AWS_SECRET_ACCESS_KEY'],
    :region => ENV['AWS_REGION']
  })

  # First, a place to contain the glorious details
  directory = connection.directories.new(
    :key    => "mirraw-test"
  )

  filepath = "/tmp/oos.csv"

  value = (Time.now.beginning_of_day - 30.days)..(Time.now.end_of_day)
  products_summary = Design.where("designs.state <> 'in_stock'").joins(line_items: :designer_order).
      where(line_items: {designer_order: {state: ['pickedup', 'pending', 'dispatched', 'completed']},
      created_at: value}).group('designs.id').having('SUM(line_items.quantity) > 3').sum('line_items.quantity')
  designs = Design.where(id: products_summary.keys)


  CSV.open(filepath, "wb") do |csv|
    csv << ["Design ID", "Vendor Name", "Vendor Email ID", "Vendor Phone", "Created At", "Sell Count", "Return Count", "Category", "State", "Price", "URL"]
    designs.each do |design|
      csv << [design.id, design.designer.name, design.designer.email, design.designer.phone, design.created_at, design.sell_count, design.return_count, design.categories.first.name, design.state, design.price, "http://www.mirraw.com/designers/" + design.designer.cached_slug + "/designs/" + design.cached_slug]
    end
  end

  # upload that file
  file =  directory.files.create(
    :key    => "junglee/oos.csv",
    :body   => File.open(filepath),
    :public => true
  )

end

task :disable_addons_for_domestic => :environment do
  categories = ['sarees', 'salwar-kameez']
  categories.each do |category|
    atv_ids = Design.select('DISTINCT(addon_type_values.id)').joins(:categories).in_category(category).joins(:addon_type_values).where(addon_type_values: {visible_for: 'all'}).pluck('addon_type_values.id')
      AddonTypeValue.where(id: atv_ids).update_all(visible_for: 'international')
  end
end

desc "Cancels orders pending in new state and attempted with payu"
task :cancel_pending_payu_orders_in_new_state => :environment do
  Order.where(
    state: 'new',
    app_source: 'Mobile',
    billing_country: ['India', 'india'],
    pay_type: Order::PAYMENT_GATEWAY
  ).find_in_batches(batch_size: 100) do |grp|
    (grp||[]).each do |order|
      order.order_cancel_reason = "pending payu orders in new state"
      order.cancel!
    end
  end
end


desc "Blacklist users who give fake reviews"
task blacklist_fake_reviewers: :environment do
  reviews = Review.select('array_agg(id) as id_array, user_id,designer_id').where('order_id is null and user_id is not null and created_at > ?', 1.week.ago).group('user_id, designer_id').having('count(id) > 15').order('count(id) desc')
  user_ids = reviews.collect(&:user_id)
  designer_ids = reviews.collect(&:designer_id)
  emails = User.where(id: user_ids).pluck(:email)
  id_array  = reviews.collect {|review| review.id_array.tr('{','').tr('[','').tr('}','').tr(']','').split(',')}.flatten
  emails.each do |email|
    Blacklist.where(email: email, description: 'fake_reviews').first_or_create
  end
  Designer.where(id: designer_ids).update_all("warning_notification = warning_notification + 1")
  Review.where(id: id_array).delete_all
end

desc "Mark Designer Order critical more than x days"
task mark_designer_order_critical: :environment do
  critical_days = SystemConstant.get('DES_ORD_CRIT_DAYS').to_i
  DesignerOrder.includes(:line_items).select('designer_orders.*').joins(:order).where(state: ['dispatched','completed', 'pickedup'], ship_to: 'mirraw', orders: {state: 'sane', geo: 'international'}).where('designer_orders.pickup::date between ? and ?', (critical_days+6).days.ago.to_date, critical_days.days.ago.to_date).find_in_batches(batch_size: 100) do |grp|
    grp.each do |designer_order|
      unless (((is_single_shipment = designer_order.bulk_shipment_id.blank?) && (designer_order.line_items.collect(&:received_on).compact.present? || designer_order.package_received_on.present?)) || (!is_single_shipment && designer_order.is_all_items_received))
        designer_order.fuckup!
        designer_order.add_notes_without_callback('Moved to Critical', 'state_change')
        DesignerMailer.delay.designer_order_critical_mail_to_vendor(designer_order.designer_id, designer_order.order.number)
      end
    end
  end
end

task update_freshdesk_tickets: :environment do
  Ticket.where(state: 'open').where('updated_at < ?',6.hours.ago).find_in_batches(batch_size: 200) do |grp|
    grp.each do |ticket|
      ticket.get_freshdesk_tickets
    end
  end
end

task process_paypal_refunds: :environment do
  return_ids = Return.where(pending_on: Date.yesterday.beginning_of_day..Date.yesterday.end_of_day).where(state: 'pending_payment').where(type_of_refund: 'Refund').joins(:order).where('length(trim(orders.paypal_txn_id)) = ?',17).pluck(:id)
  Return.paypal_automated_refund(return_ids,'<EMAIL>',automated: true)
end

task :process_domestic_refunds, [:types] => [:environment] do |t, args|
  args.with_defaults(types: 'razorpay')
  args[:types].split(',').each do |type|
    return_ids = Return.where(pending_on: Date.yesterday.beginning_of_day..Date.yesterday.end_of_day).where(state: 'pending_payment').where(type_of_refund: 'Refund').joins(:order).where(orders: {payment_gateway: type, billing_country: 'India'}).pluck(:id)
    Return.paypal_automated_refund(return_ids,'<EMAIL>',automated: true, type: type)
  end
end

task auto_assign_accounts_ticket: :environment do
  Return.where(state: 'pending_payment').where('lower(type_of_refund) not in (?) and date(pending_on) = ?',['coupon','replacement'],7.days.ago).each do |return1|
    Ticket.where(order_id: return1.order_id, department: 'accounts', state: ['open','working']).first_or_create(issue: 'Issue Auto Created By System',state: 'open',return_id: return1.id,message: 'Ticket Auto Created By System')
  end
end

task send_mail_for_ticket_violation: :environment do
  ticket_violation = Ticket.select('count(id) as count,department').where(state: ['open','working','reopen']).where('created_at < ?', 3.days.ago).group('department')
  TicketMailer.send_violation_mail_to_department_head(ticket_violation).deliver
end

desc "Send mail to vendors when their designs receive bad ratings"
task :send_design_review_notification_to_designer_weekly => :environment do
  designer_ids = Review.where('order_id is not null and rating < ? and created_at > ?', 3, Time.now - 1.week).uniq.pluck(:designer_id)
  designer_ids.each do |designer_id|
    DesignerMailer.delay.send_nps_feedback_to_designer_weekly(designer_id)
  end
end

desc 'Sends International adjustments invoice and report'
task generate_international_adjustment_report: :environment do
  if false#Time.current.day == 11
    time_period = 1.month.ago.beginning_of_day..Date.yesterday.end_of_day
    igst,cgst,sgst = (IGST.to_f/100), (CGST.to_f/100), (SGST.to_f/100)
    invoice_ids,invoice_date = [], Date.yesterday.strftime('%d/%m/%Y')
    vendor_adjustments = Adjustment.joins(order: :designer_orders).where('designer_orders.designer_id = adjustments.designer_id').where(designer_orders: {ship_to: 'mirraw'}).select('adjustments.designer_id,array_agg(distinct adjustments.id) as adj_id').where(payout_uploaded_on: time_period, status: 'paid').where("amount is not null and adjustments.return_designer_order_id is null and (adjustments.notes like 'RTV Cost%' OR adjustments.notes like 'OOS Cost%' OR lower(adjustments.notes) like 'shipping charge%' OR lower(adjustments.notes) like 'shipping cost%' OR adjustments.notes like 'Critical Cost%' OR adjustments.notes like 'QC Failed Cost%' OR lower(adjustments.notes) like '%reverse%') and lower(adjustments.notes) not like '%gst%'").group(:designer_id)
    vendor_adjustments.each_slice(100) do |adj|
      adjustments = Adjustment.select('*,amount * -1 as amount,lower(notes) as notes').where(id: adj.collect(&:adj_id).flatten.uniq).preload(:payment_order,:designer).group_by(&:designer_id)
      adjustments.each do |designer_id,adjs|
        total_adjustment = {rtv_cost: 0, oos_cost: 0, ship_cost: 0, qc_cost: 0, critical_cost: 0, other: 0}
        filepath = '/tmp/int_adjustment_report.csv'
        CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
          csv << ['Adjustment Mirraw Id','Invoice Date','Adjustment Creation Date','Reference Number','Order Confirmation Date','Taxable Value Total','GST Amount Total','Total Invoice Value','Notes','Adjustment Paid status']
          adjs.each do |adj|
            if adj.notes.include?('rtv cost')
              total_adjustment[:rtv_cost] += adj.amount
            elsif adj.notes.include?('oos cost')
              total_adjustment[:oos_cost] += adj.amount
            elsif adj.notes.include?('shipping charge') || adj.notes.include?('shipping cost')
              total_adjustment[:ship_cost] += adj.amount
            elsif adj.notes.include?('critical cost')
              total_adjustment[:critical_cost] += adj.amount
            elsif adj.notes.include?('qc failed cost')
              total_adjustment[:qc_cost] += adj.amount
            elsif adj.notes.include?('reverse')
              total_adjustment[:other] += adj.amount
            end
            gst = (adj.amount/(1+igst)).round(2)
            csv << [adj.id,invoice_date,adj.created_at.strftime('%d/%m/%Y'),adj.payment_order.number,adj.payment_order.confirmed_at.try(:strftime,'%d/%m/%Y'),gst,(adj.amount - gst).round(2),adj.amount,adj.notes,adj.status]
          end
        end
        adjustment_file = 'AdjustmentReport/' + designer_id.to_s + '/' + Date.today.strftime('%b%Y').to_s + 'adjustment.csv'
        AwsOperations.create_aws_file(adjustment_file, File.read(filepath), false)
        download_url = AwsOperations.get_aws_file_path(adjustment_file)
        total = total_adjustment.values.compact.sum
        des = adjs.first.designer
        if (des.business_state || des.state).try(:upcase) == SHIPPER_STATE.upcase
          taxable_value = (total/(1 + cgst + sgst)).round(2)
          cgst_tax, sgst_tax, igst_tax = (taxable_value * cgst).round(2), (taxable_value * sgst).round(2), 0
        else
          taxable_value = (total/(1 + igst)).round(2)
          cgst_tax, sgst_tax, igst_tax = 0, 0, (taxable_value * igst).round(2)
        end
        adjustment_invoice = AdjustmentReport.create(designer_id: designer_id, total: total, taxable_value: taxable_value, igst: igst_tax, cgst: cgst_tax, sgst: sgst_tax, adjustment_report_url: download_url)
        invoice_ids << adjustment_invoice.id
        total_adjustment.each {|key,val| total_adjustment[key] = (val/(1+igst)).round(2)}
        if total > 0
          year = Time.current.strftime("%y")
          year = %w(01 02 03).include?(Time.current.strftime("%m")) ? "#{year.to_i-1}#{year}" : "#{year}#{year.to_i+1}"
          inv_number = "A#{designer_id}/#{year}/#{adjustment_invoice.id}"
          filename  = designer_id.to_s + "_" + inv_number + ".pdf"
          pdf_content =  ActionController::Base.new().render_to_string(
            :template => 'designers/adjustment_invoice.html.haml',
            :layout   => false ,
            :locals   => {designer: des, adjustment_invoice: adjustment_invoice, adjustment_values: total_adjustment, inv_number: inv_number, inv_date: invoice_date}
          )
          payout_invoice = WickedPdf.new.pdf_from_string(pdf_content)
          AwsOperations.create_aws_file(filename, payout_invoice, false)
          download_url = AwsOperations.get_aws_file_path(filename)
          adjustment_invoice.adjustment_invoice_url = download_url
          adjustment_invoice.invoice_number         = inv_number
          adjustment_invoice.save
        end
      end
    end
    AdjustmentReport.delay(queue: 'low_priority', run_at: 15.minutes.from_now).mail_invoice_and_report_links(invoice_ids,DEPARTMENT_HEAD_EMAILS['accounts'])
  end
end

desc 'Sends International adjustments invoice and report'
task generate_international_adjustment_report_sidekiq: :environment do
  GenerateInternationalAdjustmentReportJob.perform_async()
end

desc "Send commercial shipment Report for matching data with invoice amount"
task generate_commercial_shipment_report: :environment do |t|
  if Time.current.day == 3
    dir = "tmp/reports/"
    FileUtils.mkdir_p(dir) unless File.directory?(dir)
    file_name = "Commercial_Shipment_Report"
    filepath = dir + "Commercial_Shipment_Report.csv"
    CSV.open(filepath, "wb", {:col_sep => ","}) do |csv|
      csv << %w(order_number invoice_number date_of_invoice source_of_payment date_of_order_confirmed Transation_Id currency total_FC_Invoice_value canceled_or_not_sent_items_value shipment_total_value payment_recd_in_fc currency difference Reason)
      Order.joins(:export_shipments).where('shipments.created_at >= ? AND shipments.created_at <= ?',1.month.ago.beginning_of_month,1.month.ago.end_of_month).preload(:paid_additional_payments, :versions, :events, line_items: [:line_item_addons,designer_order: [line_items: :replaced_product]], export_shipments: [:shipper, :shipment_invoice_items, :line_items]).group(:id).find_each(batch_size: 250) do |order|
        payment_gateway = order.payment_gateway.presence || order.pay_type
        if order.referral_discount.present? || order.refund_discount.present?
          source_payment = 'Mirraw Wallet and ' + payment_gateway.to_s
        else
          source_payment = payment_gateway
        end
        shipment_total_amt, difference, canceled_items_total, additional_charges,  = 0, nil, 0, 0
        if (shipper_names = order.export_shipments.collect(&:shipper).flatten.collect(&:name).compact.uniq).count == 1 && shipper_names.first.downcase == 'delhivery' && order.currency_rate_market_value.to_f > 0
        downcase_shipper_name = true
        else
        downcase_shipper_name = false
        end
        _, paypal_rate, _ = order.other_details['invoice_paypal_rate'].present? ? [nil, order.other_details['invoice_paypal_rate'].to_f, nil] : order.get_commercial_values(false, downcase_shipper_name)
        all_replaced_ids = order.line_items.collect(&:replacement_item_id)
        canceled_replaced_products_ids = order.line_items.select{|l| l.replacement_item_id.present? && l.shipment_id == nil}.collect(&:replacement_item_id)
        if (canceled_items = order.line_items.select{|l| l.shipment_id == nil && ((all_replaced_ids.exclude?(l.id) && l.replacement_item_id.blank?) || (all_replaced_ids.include?(l.id) && canceled_replaced_products_ids.include?(l.id) ))}).present?
          order_total_price = order.line_items.to_a.sum{|li| ((li.snapshot_price + li.line_item_addons.to_a.sum(&:snapshot_price)) * li.quantity)}.to_f
          order_discount = order.get_total_discount
          if downcase_shipper_name
            order_total_price = order.international_cod_price(order_total_price)
            order_discount    = order.international_cod_price(order_discount)
          end
          canceled_items.each do |item|
            dos_total = item.designer_order.get_item_total
            designer_discount = (item.snapshot_price * item.quantity * item.designer_order.discount.to_i/dos_total).round(2)
            item_price = (item.snapshot_price + item.line_item_addons.to_a.sum(&:snapshot_price)) * item.quantity
            if downcase_shipper_name
              item_price = order.international_cod_price(item_price)
              designer_discount = order.international_cod_price(designer_discount)
            end
            item_discount = ((item_price / order_total_price) * order_discount) + designer_discount
            item_total_price = (((item_price - item_discount) * item.quantity)/ (downcase_shipper_name ? 1 : paypal_rate)).round(2)
            canceled_items_total += item_total_price
          end
        end
        shipments = order.export_shipments.select{|s| s.line_items.present? }
        number_of_shipments = shipments.size
        shipments.each_with_index do |shipment, index|
          total_amount = (shipment.invoice_data['fob_value'].to_f.nonzero? || shipment.shipment_invoice_items.to_a.sum(&:total_amount))
          shipment_total_amt += total_amount
          if index == (number_of_shipments - 1) && order.paypal_mc_gross.to_f > 0
            additional_charges = order.paid_additional_payments.to_a.sum(&:total).to_f / paypal_rate
            order_paypal_total = order.paypal_mc_gross.to_f + additional_charges
            order_paypal_total = order_paypal_total * order.currency_rate_market_value.to_f if downcase_shipper_name
            difference = (order_paypal_total - (shipment_total_amt + canceled_items_total)).round(2)
          end
          reason = ''
          if difference.present? && difference.abs > 1
            if order.notes.include?('Removed line item with design')
              reason = 'Items Removed from Order'
            elsif shipment.shipper.name.try(:downcase) == 'atlantic' && order.should_convert_to_usd?(atlantic: true)
              reason = 'Exchange rate used instead of paid rate'
            elsif order.versions.collect{|o| [o.object.to_s[/shipping.*\n/]]}.flatten.uniq.compact.count > 1
              reason = 'Shipping cost changed after placing order'
            end
          end
          csv << [order.number, shipment.invoice_number, shipment.created_at.strftime('%d/%m/%Y'), source_payment, order.confirmed_at.try(:strftime, '%d/%m/%Y'), order.get_payment_transaction_id.values.first, shipment.invoice_data['currency_code'], total_amount, (index == 0 ? canceled_items_total : 0), (index == (number_of_shipments - 1) ? (shipment_total_amt + canceled_items_total) : 0), order.paypal_mc_gross, (order.paid_currency_code || order.currency_code), (difference.present? && difference.abs <= 1 ? 0 : difference), reason]
        end
      end
      csv.flush
    end
    filename  = 'CommercialReport/' + Date.today.strftime('%b%Y').to_s + '.csv'
    AwsOperations.create_aws_file(filename, File.read(filepath), false)
    download_url = AwsOperations.get_aws_file_path(filename)
    OrderMailer.report_mailer('CommercialReport',"Commercial Shipment Report with reasons for mismatches in invoice value - #{download_url}",{'to_email'=> DEPARTMENT_HEAD_EMAILS['accounts'],'from_email_with_name'=>'<EMAIL>'},{"CommercialReport_#{Date.today.strftime('%b%Y')}.csv"=>File.read(filepath)}).deliver
  end
end

desc "Sends payout invoice"
task :generate_monthly_commission_report,[:run_task,:einv_date,:payout_date,:invoice_date,:start_date,:end_date] => [:environment] do |t,args|
  args.with_defaults(run_task: 'false',einv_date: nil,payout_date: Date.today.strftime('%b%Y'),invoice_date: 1.month.ago.end_of_month.strftime('%d/%m/%Y'),start_date: 1.month.ago.beginning_of_month.beginning_of_day,end_date: 1.month.ago.end_of_month.end_of_day)
  if Time.current.day == 1 || args[:run_task] == 'true'
    invoice_ids = []
    errors_hash = {}
    irn_error_hash, irn_request = '',''
    irn_alert_email_array = ['<EMAIL>','<EMAIL>', DEPARTMENT_HEAD_EMAILS['accounts']]
    if Rails.env.production?
      logger = Logger.new(STDOUT)
      logger.level = Logger::DEBUG
      Rails.logger = Dalli.logger = ActiveRecord::Base.logger = logger
    end
    time_period = args[:start_date].to_datetime..args[:end_date].to_datetime
    igst,sgst,cgst = (IGST.to_f/100),(SGST.to_f/100),(CGST.to_f/100)
    designer_ids = DesignerOrder.unscoped.select('designer_orders.id,designer_id').joins(:order).where(confirmed_at: time_period).where(order: {country: 'India'}).where('invoice_id IS NULL and (ship_to is null OR ship_to = ?)','customer').uniq.group_by(&:designer_id)
    sales_header = ['Vendor Id','Vendor GSTIN','Order Id', 'Sales Invoice Number', 'Sales Invoice Date', 'Description', 'LineItem Quantity', 'SKU Code', 'Discounted Order', 'Sales Price', 'Tax Rate', 'HSN Code', 'Taxable Value', 'CGST', 'SGST','IGST', 'Total', 'State of customer', 'State of Vendor'].freeze
    sales_return_header = ['Vendor Id','Vendor GSTIN','Order Id', 'Credit Invoice Number','Credit Invoice Date','Reference Number', 'Reference Date', 'Description', 'LineItem Quantity', 'SKU Code', 'Discounted Order', 'Sales Price', 'Tax Rate', 'HSN Code', 'Taxable Value', 'CGST', 'SGST','IGST', 'Total', 'State of customer', 'State of Vendor'].freeze
    sales_file = '/tmp/combined_sales_report.csv'
    CSV.open(sales_file, "wb", {:col_sep => "\t"}) do |csv|
      csv << sales_header
    end
    sales_return_file = '/tmp/combined_sales_return_report.csv'
    CSV.open(sales_return_file, "wb", {:col_sep => "\t"}) do |csv|
      csv << sales_return_header
    end
    designer_ids.each_slice(100).each do |des_id|
      all_orders = LineItem.unscoped.where(designer_order_id: des_id.collect(&:last).flatten.compact.collect(&:id)).joins(:designer_order,:designer).preload(:payment_order, rack_lists_warehouse_line_items: [item: :warehouse_order], design: [:designer,:categories,property_values: :property]).where('designer_orders.invoice_id IS NULL and (designer_orders.ship_to is null OR designer_orders.ship_to = ?)','customer').select('designer_orders.invoice_number,designer_orders.confirmed_at,designer_orders.completed_at,designer_orders.pickup,designer_orders.created_at,designer_orders.designer_id,designer_orders.id,designer_orders.total as dos_total,designer_orders.payout,designer_orders.transaction_rate,designer_orders.discount as dos_discount,designer_orders.state,designers.gst_no,designers.business_name,designers.name as designer_name,designers.state as designer_state,designers.business_state as designer_business_state, designers.email as designer_email, line_items.design_id,line_items.quantity,line_items.snapshot_price,line_items.vendor_selling_price, line_items.available_in_warehouse, line_items.purchase_hsn_code,line_items.purchase_gst_rate,line_items.status, line_items.return_image_file_name, line_items.designer_order_id,line_items.id,designer_orders.order_id, designer_orders.designer_payout_status').group_by(&:designer_id)

      reverse_commissions = ReverseCommission.joins(:order).where('lower(orders.country)  = ? ',  'india').where(created_at: '2017-09-01'..args[:end_date].to_date.strftime('%Y-%m-%d'),invoice_id: nil,designer_id: des_id.collect(&:first)).preload(element: :payment_order).group_by(&:designer_id)

      #adjustments         = Adjustment.select('*,amount * -1 as amount').where("amount is not null and return_designer_order_id is null and (order_id is not null OR notes = 'Negative Commission') and commission_invoice_id is null and notes not like '%Canceled after payout%' and notes not like '%Refund for%' and notes not like 'Buyer Return for%' and notes not like '%after payout in order%' and notes not like 'Order marked sane%' and notes not like 'Shipping Cost for Order%'").preload(:payment_order).where(created_at: '2017-09-01'..args[:end_date].to_date.strftime('%Y-%m-%d'),designer_id: des_id.collect(&:first)).group_by(&:designer_id)
      s_clause = "designer_orders.designer_id as designer_id, vendor_payouts.id, vendor_payouts.shipment_id, designer_orders.confirmed_at as order_confirmed_date, designer_orders.created_at as order_created_date, designer_orders.pickup as order_pickup_date, designer_orders.completed_at as order_complete_date, orders.number as order_number, designer_orders.invoice_number as order_invoice_number, vendor_payouts.charge as order_shipping_charge, orders.buyer_state as customer_state, designer_orders.transaction_rate as transaction_rate "
      vendor_payouts = VendorPayout.select(s_clause).joins(shipment: {designer_order: :order}).where(status: 'passed', created_at: time_period, invoice_id: nil).where('vendor_payouts.shipment_id IS NOT NULL AND designer_orders.designer_id IN (?)', des_id.collect(&:first)).group_by(&:designer_id)

      all_orders.each do |designer_id,all_items|
        begin
          total_cgst,total_sgst,total_igst,total_amount,total_commission,cgst_sgst,total_tcs_cgst,total_tcs_sgst,total_tcs_igst = 0.0,0.0,0.0,0.0,0.0,true,0.0,0.0,0.0
          order_adjustments, other_adjustments, tcs_report, sales_report,sales_return_report= [],[],[],[],[],[]
          supplier_name = (all_items.first.business_name.presence || all_items.first.designer_name)
          gst_no = all_items.first.try(:design).try(:designer).try(:gst_no)
          vendor_state = all_items.first.try(:designer_business_state).try(:upcase) || all_items.first.try(:designer_state).try(:upcase)
          if vendor_state.blank?
            designer = Designer.find designer_id
            vendor_state = designer.try(:business_state).try(:upcase) || designer.try(:state).try(:upcase)
          end
          cgst_sgst = false if vendor_state != SHIPPER_STATE.upcase
          file=CSV.generate(headers: true) do |csv|
            csv << ['Vendor Id','Vendor GSTIN','Order Confirmed Date','Order Dispatch Date','Order Cancel Date','Order Id','Sale Invoice No','Vendor Selling Price','Transaction Rate','Commission','Commission Taxable Value','Commission CGST','Commission SGST','Commission IGST','Product Taxable Value','Product CGST','Product SGST','Product IGST','TCS-CGST','TCS-SGST','TCS-IGST','State of Customer', 'State of Vendor']

            all_items.each do |item|
              next if item.status.present?
              hsn_code,gst_rate = item.purchase_hsn_code,item.purchase_gst_rate
              name         = item.design.categories.first.name.gsub('-', ' ').camelize
              name        += ' [sku: ' + item.design.design_code + ']' if item.design.design_code.present?
              #vendor_selling_price   = item.sor_available? ? item.vendor_selling_price : ((item.vendor_selling_price.to_f > 0 ? item.vendor_selling_price.to_f : item.snapshot_price(RETURN_NORMAL)*(100-item.transaction_rate.to_f)/100.0).round(2))
              designer = Designer.find designer_id
              if designer.is_transfer_model?
                vendor_selling_price   = item.snapshot_price(RETURN_NORMAL)*((100-item.transaction_rate.to_f)/100.0).round(2)
              else
                vendor_selling_price   = item.sor_available? ? item.vendor_selling_price : ((item.vendor_selling_price.to_f > 0 ? item.vendor_selling_price.to_f : item.snapshot_price(RETURN_NORMAL)*(100-item.transaction_rate.to_f)/100.0).round(2))
              end
              vendor_selling_price = vendor_selling_price* item.quantity

              commission = if item.transaction_rate.to_f > 0
                  (vendor_selling_price * item.transaction_rate / 100).round(2)
                else
                  (vendor_selling_price - item.vendor_selling_price).to_f.round(2)
                end
              commission_tax_value = (commission * 100/118).round(2)
              _,gst_rate = item.find_hscode_gst(vendor_selling_price, hsn_code.to_s, false)
              product_taxable_value = (vendor_selling_price/(1+gst_rate/100.0)).round(2)
              product_gst = product_taxable_value*(gst_rate/100.0).round(2)
              tcs_value = (product_taxable_value * 0.5/100.0).round(2)
              cgst_sgst_p = item.payment_order.try(:buyer_state).to_s.downcase == vendor_state.to_s.downcase
              cgst_rate,igst_rate,cgst_tcs,igst_tcs,cgst_product,igst_product = 0,0,0,0,0,0
              if cgst_sgst
                cgst_rate = ((commission - commission_tax_value).to_f/2).round(2)
              else
                igst_rate = (commission - commission_tax_value).to_f.round(2)
              end
              if cgst_sgst_p
                cgst_tcs = (tcs_value.to_f/2).round(2)
                cgst_product = (product_gst.to_f/2).round(2)
              else
                igst_tcs = tcs_value.to_f.round(2)
                igst_product = product_gst.to_f.round(2)
              end

              item_desc = item.design.title + " - " + item.design.categories.first.name.singularize
              item_sku = item.design.design_code.present? ? item.design.design_code : ''
              is_discounted_order = item.dos_discount.to_f > 0  ? true : false
              product_total = product_taxable_value+cgst_product+cgst_product+igst_product

              sales_report << [designer_id,gst_no,item.payment_order.try(:number), item.invoice_number,(item.confirmed_at.presence || item.created_at).try(:strftime,'%d/%m/%Y'), item_desc, item.quantity, item_sku, is_discounted_order, vendor_selling_price, gst_rate, hsn_code, product_taxable_value, cgst_product, cgst_product, igst_product,product_total, item.payment_order.try(:buyer_state),vendor_state ]

              csv << [designer_id,gst_no,(item.confirmed_at.presence || item.created_at).try(:strftime,'%d/%m/%Y'),(item.pickup.presence || item.completed_at.presence || '').try(:strftime,'%d/%m/%Y'),'',item.payment_order.try(:number), item.invoice_number, vendor_selling_price,item.transaction_rate, commission, commission_tax_value, cgst_rate, cgst_rate, igst_rate,product_taxable_value, cgst_product, cgst_product, igst_product,cgst_tcs,cgst_tcs,igst_tcs,item.payment_order.try(:buyer_state),vendor_state]

              total_commission+=commission_tax_value; total_cgst+=cgst_rate; total_sgst+=cgst_rate; total_igst+=igst_rate;
              total_tcs_cgst += cgst_tcs; total_tcs_sgst += cgst_tcs; total_tcs_igst += igst_tcs;
            end

            reverse_commissions[designer_id].to_a.each do |rev_com|
              if (r_order = rev_com.element.try(:payment_order)).present?
                element_li = rev_com.element.is_a?(LineItem) ? [rev_com.element] : rev_com.element.line_items
                is_discounted_order = element_li.first.designer_order.try(:discount).to_f > 0  ? true : false
                element_li.each do |item|
                  hsn_code,gst_rate = item.purchase_hsn_code,item.purchase_gst_rate
                  #vendor_selling_price   = item.sor_available? ? item.vendor_selling_price : ((item.vendor_selling_price.to_f > 0 ? item.vendor_selling_price.to_f : item.snapshot_price(RETURN_NORMAL)*(100-item.designer_order.transaction_rate.to_f)/100.0).round(2))
                  #vendor_selling_price   = item.snapshot_price(RETURN_NORMAL)*(100-item.transaction_rate.to_f)/100.0).round(2))
                  designer = Designer.find designer_id
                  if designer.is_transfer_model?
                    vendor_selling_price   = item.snapshot_price(RETURN_NORMAL)*((100-item.designer_order.transaction_rate.to_f)/100.0).round(2)
                  else
                    vendor_selling_price   = item.sor_available? ? item.vendor_selling_price : ((item.vendor_selling_price.to_f > 0 ? item.vendor_selling_price.to_f : item.snapshot_price(RETURN_NORMAL)*(100-item.transaction_rate.to_f)/100.0).round(2))
                  end
                  vendor_selling_price = vendor_selling_price* item.quantity
                  _,gst_rate = item.find_hscode_gst(vendor_selling_price, hsn_code.to_s, false)
                  rev_tax_val = (vendor_selling_price/(1+gst_rate/100.0)).round(2)
                  product_gst = (rev_tax_val*(gst_rate/100.0)).round(2)

                  commission = if item.designer_order.transaction_rate.to_f == 0
                    (vendor_selling_price - item.vendor_selling_price).to_f.round(2)
                  else
                    (vendor_selling_price * item.designer_order.get_commission_factor).to_f.round(2)
                  end
                  commission_tax_value = (commission * 100/118).round(2)
                  cgst_sgst_p = r_order.try(:buyer_state).to_s.downcase == vendor_state.to_s.downcase
                  cgst_rate,igst_rate,cgst_tcs,igst_tcs,cgst_product,igst_product = 0,0,0,0,0,0
                  if cgst_sgst
                    cgst_rate = ((commission - commission_tax_value).to_f/2).round(2)
                  else
                    igst_rate = (commission - commission_tax_value).to_f.round(2)
                  end
                  if cgst_sgst_p
                    cgst_tcs = -(rev_tax_val * 0.5 / 200.0).round(2)
                    cgst_product = (product_gst.to_f/2).round(2)
                  else
                    igst_tcs = -(rev_tax_val * 0.5 / 100.0).round(2)
                    igst_product = product_gst.to_f.round(2)
                  end

                   item_desc = item.design.title + " - " + item.design.categories.first.name.singularize
                   item_sku = item.design.design_code.present? ? item.design.design_code : ''

                  total = rev_tax_val + cgst_product + cgst_product + igst_product
                  sales_return_report << [designer_id,gst_no,r_order.number, item.designer_order.invoice_number.to_s+'_C',rev_com.created_at.strftime('%d/%m/%Y'),item.designer_order.invoice_number,(r_order.confirmed_at.presence || r_order.created_at).strftime('%d/%m/%Y'), item_desc, item.quantity, item_sku, is_discounted_order, vendor_selling_price,gst_rate,hsn_code, rev_tax_val, cgst_product, cgst_product, igst_product, total,r_order.buyer_state,vendor_state]

                  csv << [designer_id,gst_no,(r_order.confirmed_at.presence || r_order.created_at).strftime('%d/%m/%Y'),'',rev_com.created_at.strftime('%d/%m/%Y'),r_order.number,item.designer_order.invoice_number,-1 * vendor_selling_price,item.designer_order.transaction_rate,-1*commission, -1*commission_tax_value,-1*cgst_rate,-1*cgst_rate,-1*igst_rate,-1*rev_tax_val,-1*cgst_product, -1*cgst_product, -1*igst_product,cgst_tcs,cgst_tcs,igst_tcs,r_order.buyer_state,vendor_state]

                  total_commission+=-commission_tax_value; total_cgst+=-cgst_rate; total_sgst+=-cgst_rate; total_igst+=-igst_rate;
                  total_tcs_cgst += cgst_tcs; total_tcs_sgst += cgst_tcs; total_tcs_igst += igst_tcs;
                end
              end
            end
            vendor_payouts[designer_id].to_a.each do |vendor_payout|
              commission = (vendor_payout.order_shipping_charge * vendor_payout.transaction_rate.to_f/100.0).round(2)
              amount_payable = vendor_payout.order_shipping_charge.to_f.round(2)
              net_commission,cgst_tax,sgst_tax,igst_tax = PayoutManagement.get_gst_tax(cgst_sgst,cgst,sgst,igst,amount_payable)
              csv <<[designer_id,gst_no,(vendor_payout.order_confirmed_date.presence || vendor_payout.order_created_date).try(:strftime,'%d/%m/%Y'),(vendor_payout.order_pickup_date.presence || vendor_payout.order_complete_date).try(:strftime,'%d/%m/%Y'),'', vendor_payout.order_number,vendor_payout.order_invoice_number,'',vendor_payout.transaction_rate,commission,net_commission,cgst_tax,sgst_tax,igst_tax,amount_payable,'','','','',vendor_payout.customer_state,vendor_state]
              total_commission+=net_commission; total_cgst+=cgst_tax; total_sgst+=sgst_tax; total_igst+=igst_tax;
            end
          end
          filename  = 'CommissionReport/' + designer_id.to_s + '/' + Date.today.strftime('%b%Y').to_s + '.csv'
          AwsOperations.create_aws_file(filename, file, false)
          download_url = AwsOperations.get_aws_file_path(filename)
          filename  = 'SalesReport/' + designer_id.to_s + '/' + Date.today.strftime('%b%Y').to_s + '.csv'
          file_s = CSV.generate(headers: true) do |csv|
            csv << sales_header
            sales_report.each {|row| csv << row}
          end
          CSV.open(sales_file, "a", {:col_sep => "\t"}) do |csv|
            sales_report.each {|row| csv << row}
          end
          AwsOperations.create_aws_file(filename, file_s, false)
          sales_url = AwsOperations.get_aws_file_path(filename)

          filename  = 'SalesReturnReport/' + designer_id.to_s + '/' + Date.today.strftime('%b%Y').to_s + '.csv'
          file_sr = CSV.generate(headers: true) do |csv|
            csv << sales_return_header
            sales_return_report.each {|row| csv << row}
          end
          CSV.open(sales_return_file, "a", {:col_sep => "\t"}) do |csv|
            sales_return_report.each {|row| csv << row}
          end
          AwsOperations.create_aws_file(filename, file_sr, false)
          sales_return_url = AwsOperations.get_aws_file_path(filename)

          commission_invoice = CommissionInvoice.create(designer_id: designer_id, commission: total_commission.round(2),total: total_amount, cgst: total_cgst.round(2),igst: total_igst.round(2),sgst: total_sgst.round(2), tcs_cgst: total_tcs_cgst.round(2), tcs_sgst: total_tcs_sgst.round(2), tcs_igst: total_tcs_igst.round(2), commission_report_url: download_url, tcs_report_url: nil, sales_report_url: sales_url, sales_return_report_url: sales_return_url)
          invoice_ids << commission_invoice.id
          DesignerOrder.unscoped.where(id: all_items.map(&:designer_order_id)).update_all(invoice_id: commission_invoice.id, invoice_type: commission_invoice.class.name)
          ReverseCommission.where(id: reverse_commissions[designer_id].to_a.collect(&:id)).update_all(invoice_id: commission_invoice.id, invoice_type: commission_invoice.class.name)
          #Adjustment.where(id: (other_adjustments + order_adjustments)).update_all(commission_invoice_id: commission_invoice.id)
          VendorPayout.where(id: vendor_payouts[designer_id].to_a.collect(&:id)).update_all(invoice_id: commission_invoice.id, invoice_type: commission_invoice.class.name)

          irn_number, irn_barcode, error_msg, items = nil, nil, nil, []
          # if DISABLE_ADMIN_FUCTIONALITY['irn_feature'] && (designer = Designer.where(id: designer_id).last).present? && designer.gst_no?
          #   items =[{:name=>"Online Marketing Commission", :quantity=>1, :total_price=> total_commission.round(2), igst: total_igst.round(2), cgst: total_cgst.round(2), sgst: total_sgst.round(2), :hsn_code=>"996211", :gst_rate=>18.00}]
          #   payout_invoice_id = commission_invoice.id + PAYOUT_INVOICE_ID
          #   begin
          #     irn_request  += "CommissionInvoiceId: #{commission_invoice.id}<br>Invoice Number: #{payout_invoice_id}<br>Items: #{items}<br><br>"
          #     generate_irn = GenerateIrnNumber.new(Order.new, items, commission_invoice, 1.0, 'INR', designer, payout_invoice_id, args[:einv_date])
          #     response = generate_irn.generate_forward_irn
          #     if response[:error] == false
          #       irn_number, irn_barcode = response[:irn_number], response[:irn_barcode]
          #       commission_invoice.update_columns(irn_number: irn_number, irn_barcode: irn_barcode)
          #     else
          #       error_msg  = response[:error_msg]
          #     end
          #   rescue => e
          #     error_msg = "#{e.message} ===> #{e.backtrace}"
          #   end
          #   irn_error_hash += "CommissionInvoiceId: #{commission_invoice.id}<br>Invoice Number: #{payout_invoice_id}<br>Items: #{items}<br>Reason: #{error_msg}<br><br>" if error_msg.present?
          # end

          if total_commission.to_f > 0
            method_args = [args[:payout_date],args[:invoice_date]]
            method_args << [irn_number, irn_barcode] if irn_number.present? && irn_barcode.present?
            commission_invoice.generate_payout_invoice(*method_args.flatten)
          else
            Adjustment.where(amount: (-1*total_commission).round(2),designer_id: designer_id,notes: 'Negative Commission',status: 'unpaid').first_or_create
          end
        rescue => e
          errors_hash[designer_id] = "#{e.message}#{e.backtrace}"
        end
      end
    end
    filename  = 'combinedSalesReports/' + Date.today.strftime('%b%Y').to_s + '.csv'
    AwsOperations.create_aws_file(filename, File.open(sales_file))
    sales_download = AwsOperations.get_aws_file_path(filename)

    filename  = 'combinedSalesReturnReports/' + Date.today.strftime('%b%Y').to_s + '.csv'
    AwsOperations.create_aws_file(filename, File.open(sales_return_file))
    sales_return_download = AwsOperations.get_aws_file_path(filename)

    OrderMailer.report_mailer("Combined_Sales_And_Return_Report - #{Date.today.strftime('%b%Y')}","Combined Sales Report - #{sales_download} <br> Combined Sales Return Report - #{sales_return_download}",{'to_email'=> irn_alert_email_array.third,'from_email_with_name'=>'<EMAIL>'},{}).deliver
    ReverseCommission.mail_invoice_and_report_links(invoice_ids,irn_alert_email_array.third).deliver
    if errors_hash.present?
      ExceptionNotify.sidekiq_delay.notify_exceptions("Commission Report Error","CommissionReport could not be generated for following designers.", {params: errors_hash})
    end
    OrderMailer.report_mailer("All B2B IRN Request #{Date.today.strftime('%b%Y')}","<br>#{irn_request}",{'to_email'=> irn_alert_email_array.first,'from_email_with_name'=>'<EMAIL>'},{}).deliver
    OrderMailer.report_mailer("Error While Creating B2B IRN Invoice","Failed to generate IRN number due to below reason <br>#{irn_error_hash}",{'to_email'=> irn_alert_email_array,'from_email_with_name'=>'<EMAIL>'},{}).deliver if irn_error_hash.present?
    ExceptionNotify.sidekiq_delay.notify_exceptions('B2B IRN NUMBER Generation Error',"Failed to generate IRN number due to below reason <br>#{irn_error_hash}",{ errors: irn_error_hash}) if irn_error_hash.present?
  end
end

desc "Sends payout invoice"
task :generate_monthly_commission_report_sidekiq,[:run_task,:einv_date,:payout_date,:invoice_date,:start_date,:end_date] => [:environment] do |t,args|
  args.with_defaults(run_task: 'false',einv_date: nil,payout_date: Date.today.strftime('%b%Y'),invoice_date: 1.month.ago.end_of_month.strftime('%d/%m/%Y'),start_date: 1.month.ago.beginning_of_month.beginning_of_day,end_date: 1.month.ago.end_of_month.end_of_day) 
  GenerateMonthlyCommissionReportJob.perform_async(args[:run_task], args[:einv_date], args[:payout_date], args[:invoice_date],args[:start_date], args[:end_date] )
end

desc "Calculate Vendor Score"
task calculate_vendor_score: :environment do
  designer_ids = Designer.where(score_flag: true).pluck(:id)
  designer_orders_count = DesignerOrder.unscoped.where(designer_id: designer_ids).where('created_at >= ?',Date.today.beginning_of_day).group(:designer_id).count
  total_design_count = Design.where(designer_id: designer_ids).group(:designer_id).count
  design_count = Design.published.where(designer_id: designer_ids).group(:designer_id).count
  Designer.where(id:designer_orders_count.keys).each do |designer|
    designer.update_per_day_sale_score({param1: designer_orders_count[designer.id]})
  end
  Designer.where(id:design_count.keys).each do |designer|
    designer.update_design_count_score({param1: design_count[designer.id],param2: total_design_count[designer.id]}) if total_design_count[designer.id].present?
  end
  Designer.where(id: designer_ids).update_all(score_flag: false)
end

desc "SMS to Tailor if greater than 4 days"
task delay_sms_to_tailor: :environment do
  Tailor.select(<<-SQL).
    tailors.id,
    tailors.contact_no,
    COUNT(tifs.id) AS pending_count
  SQL
  joins(<<-SQL).
    INNER JOIN (
      SELECT
        ti.id,
        ti.tailor_id,
        GREATEST(ti.created_at, ti.reassign_material_timestamp) AS last_outscanned_at,
        MAX(tib.created_at) AS last_inscanned_at
      FROM tailoring_infos AS ti
      INNER JOIN tailoring_bag_relations AS tbr
      ON ti.id = tbr.tailoring_info_id
      LEFT OUTER JOIN tailoring_inscan_bags AS tib
      ON tib.id = tbr.tailoring_inscan_bag_id
      WHERE ti.material_received_status <> true
      OR ti.material_received_status IS NULL
      GROUP BY ti.id
    ) AS tifs
    ON tailors.id = tifs.tailor_id
  SQL
  where(<<-SQL).
    tifs.last_inscanned_at IS NULL
    OR tifs.last_outscanned_at > tifs.last_inscanned_at
  SQL
  group('tailors.id').each do |t_info|
    phone = Order.get_mobile_num(t_info.contact_no) if t_info.contact_no.present?
    if phone && phone != 0 && phone.length == 12
      template = "Dear Sir/Madam, This is a notification message from MIRRAW.COM regarding #{t_info.pending_count} pending stitching products. Please submit stitching products before deadline, otherwise a penalty will be charged."
      res = SmsNotification::NotificationService.notify_later(phone, template)
    end
  end
end

task generate_monthly_order_sane_report: :environment do
  if Time.current.day == 1
    errors_hash = {}
    time_period = 1.month.ago.beginning_of_month.beginning_of_day..1.month.ago.end_of_month.end_of_day
    filepath = '/tmp/international_sane_order_report.csv'
    CSV.open(filepath, "wb", {:col_sep => "\t"}) do |csv|
      csv << ['Order Number','State','INVOICENO','INVOICE DT','CneeName','CneeAdd1','CneeAdd2','CneeCity','CneeState','CountryShortName','CneePostalCode','ItmDesc','Qty','UnitValue','CurrencyCode','FrtAmt','InsAmt','Discount','PkgChg','RITCNo','TAXABLE VALUE','IGST AMT']
    end
    select_clause = 'orders.id,number,orders.state,paid_currency_rate,paid_currency_code,currency_code,currency_rate,pay_type,other_details,order_notification,orders.shipping,orders.discount,currency_rate_market_value,additional_discount,name,pincode,street,city,country,state_code,country_code,orders.confirmed_at,orders.created_at,orders.express_delivery,sum(COALESCE(designer_orders.discount,0)) as designer_discount'
    market_rates = Hash[CurrencyConvert.select('distinct symbol,market_rate,exchange_rate').map{|cc| [cc.symbol,(cc.exchange_rate.presence || cc.market_rate)]}]
    countries    = Country.select('name,iso3166_alpha2').map{|c| [c.name,c.iso3166_alpha2]}.to_h
    Order.unscoped.select(select_clause).joins(:designer_orders).where(state: ['sane','dispatched','pickedup','partial_dispatch','ready_for_dispatch'],designer_orders: {state: ['pending','pickedup','dispatched','completed']}).preload(:order_addon,[line_items: [:line_item_addons, [design: [:categories,property_values: :property]]]]).where(confirmed_at: time_period).where('orders.country <> ? OR orders.actual_country_code <> ?','India','IN').group(:id).find_in_batches(batch_size: 250) do |orders|
      rows = []
      orders.each do |order|
        invoice_items        = []
        designer_discount    = order.designer_discount.to_i
        selected_items_group = order.line_items.select{|i| i.status.blank?}.group_by{|i| ((['saree','kurta','kurti','salwarkameez','lehenga','jewellery'].include? i.design.designable_type.try(:downcase)) ? i.design.invoice_category_name('fedex').titleize : i.design.categories.first.name)}
        inv_num = (order.other_details.keys.find{|k| k.to_s.include? 'proforma_invoice'}) if order.other_details.present?
        if order.international?
          international  = true
          rate           = (order.paid_currency_rate.presence || order.currency_rate.presence || 1).to_f
          currency_code  = (order.paid_currency_code.presence || order.currency_code.presence || 'INR')
        else
          international  = false
          rate           = 1
          currency_code  = 'INR'
        end
        if international && order.pay_type == 'Bank Deposit' && Order::INDIAN_CUSTOM_ALLOWED_CURRENCIES.exclude?(order.currency_code)
          if order.order_notification.present? && order.order_notification['paypal_rate'].present?
            rate = order.order_notification['paypal_rate']
            currency_code = 'USD'
          elsif (rates = CurrencyConvert.get_commercial_countries["#{order.country.downcase}"].to_f) > 0
            rate = order.currency_rate / rates
            currency_code = 'USD'
          end
        end
        selected_items_group.each do |(product_name, items)|
          items_count  = 0
          items_price  = 0
          gst_tax      = 0.0
          items.each do |item|
            items_count += item.quantity.to_i
            if international
              addon_price = item.line_item_addons.to_a.sum(&:snapshot_price)
              items_price += ((item.snapshot_price + addon_price) * item.quantity.to_i)
            else
              items_price += (order.currency_rate_market_value.present? ? order.international_cod_price(item.discount_price_with_addons) : item.discount_price_with_addons)
            end
          end
          item_price     = (items_price / items_count).round(2)
          items_price    = (items_price / rate).round(2)
          item_price     = (item_price / rate).round(2)
          shipping_cost  = order.shipping.to_i
          discount       = ((order.discount.to_i + order.additional_discount.to_i + designer_discount)/rate).round(2)
          code           = HS_CODE[(['other','bag','consumable','jacket'].include?(items.first.design.designable_type.try(:downcase))) ? 'other' : product_name.downcase]
          exchange_rate  = market_rates[currency_code].presence || 1
          gst_rate       = items.first.gst_rate.presence || Shipment.get_gst_rate(code,item_price,exchange_rate)
          gst_tax        = (items_price - items_price/(1+(gst_rate/100.0)))

          rows << [order.number,order.state,inv_num.to_s.gsub('proforma_invoice_',''),(order.confirmed_at.presence || order.created_at).strftime('%d/%m/%Y'),order.name,order.street.to_s.gsub(',',' '),order.city,order.city,order.state_code,(countries[order.country].presence || order.country_code),'="'+order.pincode.to_s.upcase+'"',product_name.to_s.gsub(',',' '),items_count,item_price,currency_code,((shipping_cost.to_f/rate)*0.4).round(2),0,discount,(((shipping_cost.to_f/rate)*0.6) + order.get_gift_wrap_price(rate)).round(2),(code.presence || HS_CODE['other']),((items_price - gst_tax)*exchange_rate.to_f).round(2),(gst_tax*exchange_rate.to_f).round(2)]
        end
      end
      CSV.open(filepath, "a", {:col_sep => "\t"}) do |csv|
        rows.each do |row|
          csv << row
        end
      end
    end
    OrderMailer.report_mailer('Sane Orders Report','Sane Orders Report',{'to_email'=> '<EMAIL>','from_email_with_name'=>'<EMAIL>','cc_email'=>'<EMAIL>'},{'Sane Orders Report.csv'=>File.read(filepath)}).deliver
  end
end

task :generate_monthly_purchase_report,[:run_task] => [:environment] do |t,args|
  if Rails.env.production?
    logger = Logger.new(STDOUT)
    logger.level = Logger::DEBUG
    Rails.logger = Dalli.logger = ActiveRecord::Base.logger = logger
  end
  args.with_defaults(run_task: 'false')
  if Time.current.day == 1 || args[:run_task] == 'true'
    purchase_report_month = Time.current.strftime('%B')
    errors_hash = {}
    start_time = 1.month.ago.beginning_of_month.beginning_of_day.utc
    end_time   = 1.month.ago.end_of_month.end_of_day.utc
    time_period = start_time..end_time
    sales_header  = ['Vendor ID','Vendor Gst Number','Mirraw GSTIN No','Supplier Name','Order Number', 'Sales Invoice No', 'Sales Invoice/Order Sane Date', 'Description', 'Quantity', 'HSN Code', 'SKU Code', 'Size', 'Warehouse Order', 'Sale Price', 'Addon Product Price', 'Total Sale Price','Taxable value', 'Rate of Tax', 'CGST', 'SGST', 'IGST', 'Total', 'Vendor State'].freeze
    sales_return_header  = ['Vendor ID','Vendor Gst Number','Mirraw GSTIN No','Supplier Name','Order Number', 'Sales Invoice No', 'Sales Invoice/Order Sane Date', 'Credit Note Invoice Number','Credit Note invoice Date','Description', 'Quantity', 'HSN Code', 'SKU Code', 'Size', 'Warehouse Order', 'Sale Price', 'Addon Product Price', 'Total Sale Price','Taxable value', 'Rate of Tax', 'CGST', 'SGST', 'IGST', 'Total', 'Vendor State'].freeze

    sales_file = '/tmp/combinePurchaseSalesReports.csv'
    CSV.open(sales_file, "wb", {:col_sep => ","}) do |csv|
      csv << sales_header
    end
    sales_return_file = '/tmp/combinePurchaseSalesReturnReports.csv'
    CSV.open(sales_return_file, "wb", {:col_sep => "\t"}) do |csv|
      csv << sales_return_header
    end
    designer_ids = DesignerOrder.unscoped.select('designer_orders.id,designer_id').joins(:order).where(confirmed_at: time_period).where('orders.country <> ? OR designer_orders.ship_to = ?','India','mirraw').uniq.group_by(&:designer_id)
    designer_ids.each_slice(50).each do |des_id|
      all_orders = LineItem.unscoped.where(designer_order_id: des_id.collect(&:last).flatten.compact.collect(&:id)).joins(:designer_order,:designer).preload(:payment_order, rack_lists_warehouse_line_items: [item: :warehouse_order], design: [:designer,:categories,property_values: :property]).select('designer_orders.invoice_number,designer_orders.confirmed_at,designer_orders.pickup,designer_orders.created_at,designer_orders.designer_id,designer_orders.id,designer_orders.total,designer_orders.payout,designer_orders.transaction_rate,designer_orders.discount,designer_orders.state,designers.gst_no,designers.business_name,designers.name as designer_name,designers.state as designer_state,designers.business_state as designer_business_state, designers.email as designer_email, line_items.design_id,line_items.variant_id,line_items.quantity,line_items.snapshot_price,line_items.vendor_selling_price, line_items.available_in_warehouse, line_items.purchase_hsn_code,line_items.purchase_gst_rate,line_items.status, line_items.return_image_file_name, line_items.designer_order_id,line_items.id,designer_orders.order_id').group_by(&:designer_id)
      reverse_commissions = ReverseCommission.joins(:order).where('lower(orders.country) <> ? ',  'india').where(created_at: '2017-09-01'..1.month.ago.end_of_month.end_of_day,invoice_id: nil,designer_id: des_id.collect(&:first)).preload(element: :payment_order).group_by(&:designer_id)
      purchase_reports = []
      all_orders.each do |designer_id,all_items|
        sales_report,sales_return_report= [],[]
        begin
          supplier_name = (all_items.first.business_name.presence || all_items.first.designer_name)
          gst_no = all_items.first.try(:design).try(:designer).try(:gst_no)
          file=CSV.generate(headers: true) do |csv|
            all_items.each do |item|
              #next if item.status.present?
              #hsn_code,gst_rate = item.purchase_hsn_code,item.purchase_gst_rate
              name = item.design.categories.first.name.gsub('-', ' ').camelize
              sku_code = 'sku: ' + item.design.design_code if item.design.design_code.present?
              item_size = item.variant_id.present? ? item.variant.option_type_values.try(:last).try(:p_name).to_s : ''
              is_warehouse_order = item.sor_available? ? 'YES' : 'NO'
              item_price = item.sor_available? ? item.vendor_selling_price : (((item.vendor_selling_price.to_f > 0 ? item.vendor_selling_price.to_f : item.snapshot_price(RETURN_NORMAL))*(100-item.transaction_rate.to_f)/100.0).round(2))
              addon_price = (item.vendor_addon_items.to_a.sum{|addon| addon.snapshot_price(RETURN_NORMAL)}*(100-item.transaction_rate.to_f)/100.0).round(2)
              sales_price = item_price + addon_price
              hsn_code,gst_rate = item.find_hscode_gst(sales_price, nil, false) #if hsn_code.blank? || gst_rate.blank?
              taxable_value = (sales_price/(1+gst_rate.to_f/100))
              cgst_sgst = (item.designer_business_state || item.designer_state).try(:upcase) == SHIPPER_STATE.upcase
              total_tax = ((sales_price - taxable_value)*item.quantity).round(2)

              sales_report << [designer_id, gst_no, MIRRAW_GST_NUMBER, supplier_name, item.payment_order.number, item.invoice_number, (item.confirmed_at || item.created_at).to_date.strftime('%d/%m/%Y'), name, item.quantity, hsn_code, sku_code, item_size, is_warehouse_order, item_price, addon_price,sales_price, (item.gst_no.present? ? (taxable_value * item.quantity).round(2) : '-'), gst_rate.to_f, cgst_sgst ? (total_tax/2).round(2) : '-', cgst_sgst ? (total_tax/2).round(2) : '-', cgst_sgst ? '-' : total_tax, ((taxable_value*item.quantity) + total_tax).round(2),(item.designer_business_state || item.designer_state)]
            end

            reverse_commissions[designer_id].to_a.each do |rev_com|
              if rev_com.present?
                if (r_order = rev_com.element.try(:payment_order)).present?
                  element_li = rev_com.element.is_a?(LineItem) ? [rev_com.element] : rev_com.element.line_items
                  element_li.each do |item|
                    ActiveRecord::Associations::Preloader.new.preload(item, [:designer_order, :designer])
                    #hsn_code,gst_rate = item.purchase_hsn_code,item.purchase_gst_rate
                    name = item.design.categories.first.name.gsub('-', ' ').camelize
                    sku_code = 'sku: ' + item.design.design_code if item.design.design_code.present?
                    item_size = item.variant_id.present? ? item.variant.option_type_values.try(:last).try(:p_name).to_s : ''
                    is_warehouse_order = item.sor_available? ? 'YES' : 'NO'
                    items_price = item.sor_available? ? item.vendor_selling_price : (((item.vendor_selling_price.to_f > 0 ? item.vendor_selling_price.to_f : item.snapshot_price(RETURN_NORMAL))*(100-item.designer_order.transaction_rate.to_f)/100.0).round(2))
                    addon_price = (item.vendor_addon_items.to_a.sum{|addon| addon.snapshot_price(RETURN_NORMAL)}*(100-item.designer_order.transaction_rate.to_f)/100.0).round(2)
                    sales_price = items_price + addon_price
                    hsn_code,gst_rate = item.find_hscode_gst(sales_price, nil, false) #if hsn_code.blank? || gst_rate.blank?
                    taxable_value = (sales_price/(1+gst_rate.to_f/100))
                    cgst_sgst = (item.designer.business_state || item.designer.state).try(:upcase) == SHIPPER_STATE.upcase
                    total_tax = ((sales_price - taxable_value)*item.quantity).round(2)

                    sales_return_report << [designer_id, gst_no, MIRRAW_GST_NUMBER, supplier_name, item.payment_order.number, item.designer_order.invoice_number, (item.designer_order.confirmed_at || item.created_at).to_date.strftime('%d/%m/%Y'),item.designer_order.credit_note_number, rev_com.created_at.strftime('%d/%m/%Y'),name, item.quantity, hsn_code, sku_code, item_size, is_warehouse_order, items_price, addon_price,sales_price, (gst_no.present? ? (taxable_value * item.quantity).round(2) : '-'), gst_rate.to_f, cgst_sgst ? (total_tax/2).round(2) : '-', cgst_sgst ? (total_tax/2).round(2) : '-', cgst_sgst ? '-' : total_tax, ((taxable_value*item.quantity) + total_tax).round(2),(item.designer.business_state || item.designer.state)]
                  end
                end
              end
            end
          end
          #generate vendor report url
          filename  = 'InternationalSalesReport/' + designer_id.to_s + '/' + Date.today.strftime('%b%Y').to_s + '.csv'
          file_s = CSV.generate(headers: true) do |csv|
            csv << sales_header
            sales_report.each {|row| csv << row}
          end
          AwsOperations.create_aws_file(filename, file_s, false)
          sales_url = AwsOperations.get_aws_file_path(filename)

          #combine url for accounts team
          CSV.open(sales_file, "a", {:col_sep => "\t"}) do |csv|
            sales_report.each {|row| csv << row}
          end

          #generate vendor report url
          filename  = 'InternationalSalesReturnReport/' + designer_id.to_s + '/' + Date.today.strftime('%b%Y').to_s + '.csv'
          file_sr = CSV.generate(headers: true) do |csv|
            csv << sales_return_header
            sales_return_report.each {|row| csv << row}
          end
          AwsOperations.create_aws_file(filename, file_sr, false)
          sales_return_url = AwsOperations.get_aws_file_path(filename)

          #combine url for accounts team
          CSV.open(sales_return_file, "a", {:col_sep => "\t"}) do |csv|
            sales_return_report.each {|row| csv << row}
          end

          purchase_report = PurchaseReport.create(designer_id: designer_id, sales_report_url: sales_url, sales_return_report_url: sales_return_url)
          DesignerOrder.unscoped.where(id: all_items.collect(&:designer_order_id)).update_all(invoice_id: purchase_report.id, invoice_type: purchase_report.class.name)
          ReverseCommission.where(id: reverse_commissions[designer_id].to_a.collect(&:id)).update_all(invoice_id: purchase_report.id, invoice_type: purchase_report.class.name)
          #DesignerMailer.delay(priority: -10).purchase_report_to_vendor(all_items.first.designer_email, download_url, purchase_report_month)
        rescue => e
          errors_hash[designer_id] = "#{e.message}#{e.backtrace}"
        end
      end
    end

    filename  = 'combinedInternationalSalesReports/' + Date.today.strftime('%b%Y').to_s + '.csv'
    AwsOperations.create_aws_file(filename, File.open(sales_file))
    sales_download = AwsOperations.get_aws_file_path(filename)

    filename  = 'combinedInternationalSalesReturnReports/' + Date.today.strftime('%b%Y').to_s + '.csv'
    AwsOperations.create_aws_file(filename, File.open(sales_return_file))
    sales_return_download = AwsOperations.get_aws_file_path(filename)

    #Mail to Account Team
    OrderMailer.report_mailer("PurchaseReport - #{Date.today.strftime('%b%Y')}","Combined Sales Report - #{sales_download} <br> Combined Sales Return Report - #{sales_return_download}",{'to_email'=> DEPARTMENT_HEAD_EMAILS['accounts'],'from_email_with_name'=>'<EMAIL>'},{}).deliver
    if errors_hash.present?
      ExceptionNotify.sidekiq_delay.notify_exceptions("Purchase Report Error","PurchaseReport could not be generated for following designers.", {params: errors_hash})
    end
  end
end

task :generate_monthly_purchase_report_sidekiq,[:run_task] => [:environment] do |t,args|
  args.with_defaults(run_task: 'false')
  GenerateMonthlyPurchaseReportJob.perform_async(args[:run_task])
end

task automated_aramex_pickup: :environment do
  shipper_id = Shipper.where(name: 'Aramex').pluck(:id)
  data = []
  designer_orders  = DesignerOrder.where(state: 'pickedup',shipper_id: shipper_id).preload(:designer,:order).group_by(&:designer_id)
  designer_orders.each do |designer_id,d_orders|
    designer = d_orders.first.designer unless designer.present?
    pickup_info = {}
    pickup_info[:pickup_date] = Date.current
    pickup_info[:ready_time] = DateTime.current.strftime('%FT13:00:00')
    pickup_info[:last_pickup_time] = DateTime.current.strftime('%FT18:00:00')
    pickup_info[:closing_time] =DateTime.current.strftime('%FT18:00:00')
    pickup_info[:product_group] = 'DOM'
    pickup_info[:product_type] = 'CDA'
    pickup_info[:number_of_shipments] = d_orders.length
    pickup_info[:weight] = {'Unit' => 'KG','Value' => 0.1}
    pickup_info[:volume] = {'Unit' => 'KG','Value' => 0.1}
    pickup_responses = AramexApi.aramex_create_pickup(designer,pickup_info,"PICKUP#{designer.id}")
    if pickup_responses[:error] == false
      data << ['Designer','Address','Pickup Id','GUID','Reference']
      data << [designer.name,designer.address,pickup_responses[:pickup_id],pickup_responses[:guid],pickup_responses[:reference1]]
      data << []
      data << ['Order Number','Tracking Number']
      d_orders.each{|designer_order| data << [designer_order.order.number, designer_order.tracking_num]}
      data << []
    else
      data << ['Designer','Adderss','Error']
      data << [designer.name,designer.address,pickup_responses[:error_text]]
      data << []
    end
  end
  file = CSV.generate do |csv|
    data.each{|d| csv << d}
  end
  files = {'Aramex Pickup Data.csv' => file}
  emails = {'to_email'=> ACCESSIBLE_EMAIL_ID['no_pickup_done_mail_recipient'],'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'}
  OrderMailer.report_mailer('Pickup Data','Please find attachment.',emails,files).deliver
end

task import_user_data_to_freshdesk: :environment do
  time = (Time.zone.now - 1.hour).beginning_of_hour
  orders = Order.select('name,id,number,email,country,city,buyer_state,phone,total,app_source').where('created_at >= ?',time)

  #Customer_class   company_id  condition
  #Diamond          3000138176 (total >= 75000)
  #Platinum         3000138189 (total >= 30000 && total < 75000)
  #Gold                        (total >= 10000 && total < 30000)
  #Silver                      (total >= 4000 && total < 10000)
  #Regular                     (total < 4000)
  #Rules can be customized by changing system constant value

  Order.classify_customer_type(orders)
  #updates tickets for freshdesk
end

task send_shipment_mail_dhl: :environment do
  shipper = Shipper.where('lower(name) = ?', 'dhl')
  time = 1.hour.ago
  shipments = Shipment.forward.where('designer_order_id is null and created_at >= ? ',time).where(shipper_id: shipper)
  if shipments.present?
    emails  = JSON.parse(SystemConstant.get('DHL_SHIPMENT_MAILS'))
    attachments = {}
    shipments.each do |shipment|
      shipment_invoice,pdf = CombinePDF.new,CombinePDF.new
      begin
        shipment_invoice << CombinePDF.parse(HTTParty.get(shipment.invoice.url).body)
        4.times{pdf << shipment_invoice.pages[0..shipment_invoice.pages.count-2]}
        attachments["#{shipment.number}.pdf"] = pdf.to_pdf
      rescue
      end
    end
    OrderMailer.report_mailer("Commercial Shipments for #{time.strftime('%d %b %l:%M %p')} - #{Time.current.strftime('%l:%M %p')}",'Please check the following attachments.',{'to_email'=> emails['to_email'],'from_email_with_name'=> "Mirraw.com <<EMAIL>>",'cc_email'=> emails['cc_email']},attachments).deliver
  end
end

task :check_app_rank_for_android,[:run_freq] => [:environment] do |t,args|
  if Date.current.strftime('%d').to_i % args[:run_freq].to_i == 0
    keywords = SystemConstant.get('KEYWORDS_FOR_APP_RANK_ALERT').split(',')
    prev_ranking_batch = AppRankingKeyword.where('ranking_batch like ?','ALERT-%').order(:created_at).uniq.last.try(:ranking_batch).to_s
    options = {email: ACCESSIBLE_EMAIL_ID['app_rank_alert'].join(','),project: "designers_collection",spider: "appstore_rank", region: 'US', batch_code: DateTime.current.strftime("ALERT-%d-%b-%y-%H-%M")}
    AppRankingKeyword.send_app_rank_crawler_request(keywords,options,true,prev_ranking_batch,true)
  end
end

task :delete_excess_recent_items => [:environment] do
  query = RecentItem.select('distinct on (device_id) device_id').where(created_at: (1.day.ago.beginning_of_day..1.day.ago.end_of_day))
  batch_size = 5000
  offset = 0
  begin
    found = query.limit(batch_size).offset(offset).inject(0) do |found, record|
      RecentItem.delete_excess_recents(record.device_id)
      found + 1
    end
    offset += found
  end while found == batch_size
end

task :send_faq_to_designer,[:run_freq] => [:environment] do |t,args|
  if Date.current.strftime('%d').to_i % args[:run_freq].to_i == 0
    faq=SupportText.designer_faq.where('category_of_faq is not null and order_for_category is not null').where{(domestic== false) | (domestic== nil)}.order(:id).first
    if faq.present?
      time = Time.current
      Designer.where(state_machine: ['approved','review']).select('id,name,email,state_machine').find_in_batches(batch_size: 600) do |designer_grp|
        designer_grp.each do |designer|
          DesignerMailer.delay(run_at: time).faq_mail_to_designer(designer,faq)
        end
        time = time + 30.minutes
      end
      faq.update_column(:domestic,true)
    end
  end
end

# desc "Updates Product performance metric of designs"
# task update_design_performance_metric: :environment do
#   start_date = Date.today - 7.day
#   end_date = Date.today - 1.day
#   last_run_at = DesignPerformance.maximum(:flush_key).to_i
#   if Date.today.mjd - last_run_at == 7 || last_run_at == 0
#     puts 'Running'
#     design_add_to_cart = LineItem.where(created_at: start_date..end_date).group('design_id').count
#     puts 'AddToCart'
#     design_sold = LineItem.joins(:designer_order).where('designer_orders.order_id is not null').where('line_items.designer_order_id is not null').where(line_items: {created_at: start_date..end_date}).group('design_id').count
#     puts 'Sold'
#     design_performance = {}
#     design_add_to_cart.each do |key, value|
#       design_performance[key] = {sell_count: design_sold[key].to_i, add_to_cart: value}
#     end

#     (design_sold.keys - design_performance.keys).each do |key|
#       design_performance[key] = {sell_count: design_sold[key].to_i, add_to_cart: 0}
#     end
#     GoogleApi::Analytics.gather_data(design_performance, start_date, end_date)
#   end
# end

desc "Sends reminder email regarding coupon to subscribed users who haven't placed order yet"
task send_subscription_coupon_reminder_mail: :environment do
  Subscription.send_coupon_reminder_mail if (Subscription::ENABLE_SUBSCRIPTION_COUPON_REMINDER_MAIL_TRIGGER)
end

task update_duplicate_inventory: :environment do
  designers = Designer.where('inventory_processed_at is null').limit(50) || Designer.where{inventory_processed_at.lte 1.week.ago}.limit(50)
  designers.each do |designer|
    designer.designs.published.find_each(batch_size: 500) do |design|
      result_set = design.get_similar_from_mongo
      design.update_similar{result_set.length}
    end
    designer.update_column(:inventory_processed_at, Time.now)
  end
end

desc "Send Shipping bill no report to accounts team"
task send_orders_csv_without_shipping_bill_no: :environment do
  if Date.today.end_of_month == Date.today || Date.today.day == 15
    Shipment.shipment_bill_csv
    Order.get_order_estimation_report(Date.today - 15.day, Date.today)
  end
end

desc "Exchange Rate Update notification"
task exchange_rate_update_notification: :environment do
  exchange_rate_notice = SystemConstant.where(name: 'EXCHANGE_RATE_NOTICE').first
  exchange_rate_hash = JSON.parse(exchange_rate_notice.value).deep_symbolize_keys!
  exchange_rate_page = Nokogiri::HTML.parse(open(exchange_rate_hash[:page]))
  exchange_rate_site_path = exchange_rate_hash[:page].match(/.*\/.*\/.*(?=\/)/).to_s
  exchange_rate_pdf_path = exchange_rate_page.at_css(exchange_rate_hash[:css_class]).try(:attr,'href').to_s
  if exchange_rate_pdf_path.present?
    file_url= exchange_rate_site_path + exchange_rate_pdf_path
    if file_url != exchange_rate_hash[:pdf_link]
      OrderMailer.report_mailer('NEW EXCHAGE RATES ARE AVAILABLE',"#{file_url}",{'to_email'=> exchange_rate_hash[:email],'from_email_with_name'=> '<EMAIL>'}).deliver
      exchange_rate_hash[:pdf_link] = file_url
      exchange_rate_notice.value = (exchange_rate_hash).to_json
      exchange_rate_notice.save
    end
  end
end

desc 'creates rapid delivery pickup for prepaid orders'
task automated_rapid_delivery_pickup: :environment do
  pickup_successful_orders = []
  credentials = Mirraw::Application.config.rapid_delivery
  designer_orders  = DesignerOrder.where(state: 'pickedup', tracking_partner: 'Rapid Delivery', shipment_status: 'created').preload(:designer,:payment_order).group_by(&:designer_id)
  dir = "tmp/rapid_delivery_pickups/"
  FileUtils.mkdir_p(dir) unless File.directory?(dir)
  filepath = dir + "pickup_report_#{Date.today.strftime('%d-%m-%Y %H:%M:%S')}"
  CSV.open(filepath, "wb", {:col_sep => ","}) do |data|
    designer_orders.each do |designer_id, d_orders|
      next if designer_id.blank?
      designer = d_orders.first.designer
      pickup_info = {}
      pickup_info[:token] = credentials[:token]
      pickup_info[:client] = credentials[:client]
      pickup_info[:day] = DateTime.now < DateTime.now.beginning_of_day.advance(hours: 14) ? 0 : 1
      pickup_info[:address] = designer.address
      pickup_info[:pincode] = designer.pincode
      pickup_info[:name] = designer.name
      pickup_info[:shipments] = d_orders.length
      pickup_info[:weight] = 0.0
      pickup_info[:phone] = designer.phone
      pickup_response = JSON.parse(HTTParty.post(credentials[:pickup_url], body: pickup_info).parsed_response)['Pickup'].try(:first)
      if pickup_response.present? && pickup_response['status'].try(:downcase) == 'scheduled' && pickup_response['pickup_no'].present?
        data << ['Designer','Address','Pickup Number']
        data << [designer.name, designer.address, pickup_response['pickup_no']]
        data << []
        data << ['Order Number','Tracking Number']
        des_file = CSV.generate do |csv|
          csv << ['Pickup Number', pickup_response['pickup_no']]
          csv << ['Rapid Delivery contactNo', ACCESSIBLE_EMAIL_ID['rapid_delivery_pickup_contact_no']]
          csv << []
          csv << ['Order Number','Tracking Number']
          d_orders.each{|designer_order| csv << [designer_order.payment_order.number, designer_order.tracking_num]}
          d_orders.each{|designer_order| data << [designer_order.payment_order.number, designer_order.tracking_num]}
        end
        data << []
        pickup_successful_orders << d_orders.collect(&:id)
        OrderMailer.report_mailer("Rapid Delivery Pickup Report #{Date.today.strftime('%d-%h-%Y')} #{designer.name}",'Please find attachment.', {'to_email'=> designer.email,'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'}, {"Rapid Delivery Pickup Data #{Date.today.strftime('%d-%h-%Y')}_#{designer.name}.csv" => des_file}).deliver
      else
        data << ['Designer','Adderss','Error']
        data << [designer.name, designer.address, pickup_response.try(:[], 'remarks')]
        data << []
      end
    end
    data.flush
  end
  DesignerOrder.where(id: pickup_successful_orders.flatten).update_all(shipment_status: 'pickedup') if pickup_successful_orders.present?
  if designer_orders.present?
    files = {"Rapid Delivery Pickup Data #{Date.today.strftime('%d-%h-%Y')}.csv" => File.read(filepath)}
    emails = {'to_email'=> ACCESSIBLE_EMAIL_ID['no_pickup_done_mail_recipient'],'from_email_with_name'=> 'Mirraw.com <<EMAIL>>', 'cc_email'=> '<EMAIL>'}
    OrderMailer.report_mailer("Rapid Delivery Pickup Report #{Date.today.strftime('%d-%h-%Y')}",'Please find attachment.',emails,files).deliver
  end
end

desc "Generate warehouse stock report"
task :generate_warehouse_stock_report,[:initial_date] => :environment do |t, args|
  file_content = {}
  count = 0
  detail_file = Tempfile.new(['detail_file','.csv'])
  CSV.open(detail_file, 'a') do |csv|
    header = %w(id design_id designable_type order_number quantity design_name dos_state s_price item_payout designer_payout designer_total designer_discount item_created_at)
    csv << header
    LineItem.select("line_items.id, line_items.created_at as item_created_at, orders.number as order_number,designer_orders.state as dos_state, designs.id as design_id, designs.designable_type, designs.title as design_name, line_items.quantity, (line_items.snapshot_price * line_items.quantity * line_items.scaling_factor) as s_price, designer_orders.payout as designer_payout, designer_orders.total as designer_total,designer_orders.discount as designer_discount,CASE designer_orders.total when 0 then 0 else (line_items.snapshot_price::float * line_items.quantity) * (designer_orders.payout::float/(coalesce(designer_orders.total)::float + coalesce(designer_orders.discount, 0))) end as item_payout").joins("inner join designs on line_items.design_id = designs.id
    inner join designer_orders on designer_orders.id = line_items.designer_order_id
    inner join orders on orders.id = designer_orders.order_id
    left outer join rtv_shipment_line_items
    on rtv_shipment_line_items.line_item_id = line_items.id").where("rtv_shipment_line_items.line_item_id is null and
    (line_items.shipment_id is null or (line_items.shipment_id is not null  and orders.out_of_mirraw_warehouse is null)) and
    line_items.received is not null and
    orders.state <> 'dispatched' and
    line_items.created_at >= ?", args[:initial_date] || '2017-04-01').find_in_batches(batch_size: 2000) do |line_items|
      puts count+=2000
      line_items_group = line_items.group_by{|li| li.designable_type}
      line_items_group.each do |group_name, lis|
        lis.each do |li|
          file_content[group_name] ||= Hash.new(0)
          file_content[group_name]['count'] += li.quantity.to_f
          file_content[group_name]['total'] = (file_content[group_name]['total'] + li.s_price.to_f).round(2)
          file_content[group_name]['payout'] = (file_content[group_name]['payout'] + li.item_payout.to_f).round(2)
          csv << header.map{|msg| li.send(msg)}
        end
      end
    end
  end

  s3_name = "warehouse_inventory_report/#{Date.current.strftime('%m%Y')}/warehouse_master_#{Date.current.strftime('%m%Y')}"
  tmpfile = Tempfile.new(['warehouse-master', '.csv'])
  header = %w(count total payout)
  File.open(tmpfile, 'w') do |file|
    begin
      file.write(open("https://s3-ap-southeast-1.amazonaws.com/#{ENV['AWS_BUCKET']}/#{s3_name}").read)
    rescue
      file.write("designable_type,#{header.join(',')}\n")
    ensure
      file.flush
    end
  end
  CSV.open(tmpfile, 'ab') do |csv|
    csv << ["#{Date.current.strftime('%d/%m/%Y')}"]
    file_content.each do |key, val|
      csv << [key, *val.values_at(*header)]
    end
    file_name = "warehouse_inventory_report/#{Date.current.strftime('%m%Y')}/warehouse-data-#{Time.now.strftime('%d-%m-%Y')}"
    AwsOperations::create_aws_file(file_name, detail_file)
    csv << [AwsOperations::get_aws_file_path(file_name)]
    csv << ['']
  end
  AwsOperations::create_aws_file(s3_name, tmpfile)
  #AwsOperations::get_aws_file_path(s3_name)
end

desc 'send cargo and e waybill orders data'
task daily_cargo_and_e_waybill_orders: :environment do
  select_clause = 'id, number, ((paid_amount/currency_rate)*currency_rate_market_value) as total_paid_amount, pickup, ready_for_dispatch_at, courier_company, best_shipper, state'
  cargo_orders = Order.select(select_clause).where('((state = ? and ready_for_dispatch_at::date >= ?) OR (state = ? and pickup::date >= ?)) AND paid_amount IS NOT NULL AND lower(country) <> ?', 'ready_for_dispatch',30.day.ago,'dispatched', 1.day.ago, 'india').group('id').having('((paid_amount/currency_rate)*currency_rate_market_value) >= 25000')
  file = CSV.generate do |csv|
    csv << ['Order Number', 'Total', 'Courier Partner', 'State', 'Ready For Dispatch Date', 'Dispatch Date']
    cargo_orders.each do |order|
      csv << [order.number, order.total_paid_amount.round(2), (order.courier_company || order.best_shipper), order.state, order.ready_for_dispatch_at.try(:to_date), order.pickup.try(:to_date)]
    end
  end
  files = {"Cargo Orders #{Date.today}.csv" => file}
  emails = {'to_email'=> ACCESSIBLE_EMAIL_ID['forced_paypal_refund_mail_recipient'], 'from_email_with_name'=> 'Mirraw.com <<EMAIL>>'}
  OrderMailer.report_mailer("Cargo Orders #{Date.today.strftime('%d-%m-%Y')}", 'Please find attachment.', emails, files).deliver
end

desc 'send remainder measurement suggestion mail to user'
task send_remainder_measurement_suggestion_mail_to_user: :environment do
  mail_sent_ids = []
  StitchingMeasurement.select("string_agg(id::text, ',') as mes_ids, order_id").where('state = ? AND suggested_measurements <> ? AND reject_mail_sent_at < ? AND stylist_id IS NOT NULL AND reject_mail_count < ?','rejected',{}.to_yaml,2.days.ago, 5).reorder('').group('order_id').each_slice(100) do |stitch_mes|
    stitch_mes.each do |mes|
      measurement_ids = mes.mes_ids.split(',')
      OrderMailer.send_measurement_rejected_with_suggestions_mail_to_user(measurement_ids).deliver
      mail_sent_ids << measurement_ids
    end
  end
  StitchingMeasurement.where(id: mail_sent_ids.flatten.uniq).update_all("reject_mail_count = reject_mail_count + 1, reject_mail_sent_at = '#{DateTime.now}'")
end

desc 'sends warning and banned email to designers'
task :send_warning_and_banned_email_to_designer, [:day_to_run] => :environment do |t,args|
  if true#Date.today.wday == args[:day_to_run].to_i
    w_exception_vendors = (vendor_ids = VENDOR_SUSPENSION_METRIC['exception_list']).present? ? "designers.id NOT IN (#{vendor_ids.join(',')})" : ''
    Designer.select('designers.*, max(value*100) AS max_odr_90_percent, count(distinct survey_answers.id) AS reviews_count').joins(:metric_values, :survey_answers).where('designers.state_machine NOT IN (?) AND metric_definition_id = ? AND value*100 > ? AND generated_on = ?',['banned', 'on_hold'],13, VENDOR_SUSPENSION_METRIC['min_odr_threshold'], Date.yesterday).where('survey_answers.question_id = ? AND survey_answers.created_at::date > ?', 21, 90.days.ago).where(w_exception_vendors).group('designers.id').having('count(distinct survey_answers.id) > ?', VENDOR_SUSPENSION_METRIC['warning_review_threshold']).find_each(batch_size: 100) do |designer|
      if designer.check_for_banned_warning
        DesignerMailer.mail_banned_warning_to_designer(designer).deliver
      elsif designer.reviews_count > VENDOR_SUSPENSION_METRIC['review_threshold']
        designer.banned_for_high_odr
        DesignerMailer.mail_banned_warning_to_designer(designer, true).deliver
      end
    end
  end
end

desc 'sends warning and banned email to designers'
task :send_warning_and_banned_email_to_designer_sidekiq, [:day_to_run] => :environment do |t,args|
  SendWarningAndBannedEmailToDesignerJob.perform_async()
end

desc 'Monthly gst report to accounts team'
task monthly_return_report_to_accounts: :environment do
  if Time.current.day == 1
    date = Time.current - 1.day
    file = Tempfile.new('return_report')
    CSV.open(file.path, 'wb') do |csv|
      csv << ['Order No',  'Invoice Number',  'Invoice date', 'Return Voucher','Return Id','Type of Return','State','Return Reason',  'Designer Order ID', 'HSN', 'Return Currency Code', 'Return Paypal Amount','Amount in INR
      ', 'Order Dispatch Date', 'Return Created Date', 'Return Completed Date']
      Return.joins(:order).where(created_at: date.beginning_of_month..date.end_of_month).where(order: {state: 'dispatched'}).where('returns.created_at::date > orders.pickup::date').preload(order: :coupon,discount_line_items: [line_item: [design: [:categories,property_values: :property],shipment: :shipper]],line_items: [:return_designer_order,[shipment: :shipper],design: [:categories,property_values: :property],designer_order: :line_items]).find_each(batch_size: 200) do |return1|
        order = return1.order
        if order.international?
          order_discount,order_total,inv_number = order.discount.to_i + order.additional_discount.to_i,0,nil
          order_discount = order.additional_discount.to_i if order.discount.to_i > 0 && (coupon = order.coupon).present? && coupon.coupon_type == 'COFF'
          if order_discount > 0
            order_total = order.line_items.to_a.sum{|li| (li.snapshot_price * li.quantity)}.to_f
          end
          if order.order_notification.present? && (key=order.order_notification.keys.find{|k| k.to_s.include? 'invoice'}).present?
            inv_number = key.to_s.split('_')[1]
          end
          return1.line_items.each do |item|
            dos=item.designer_order
            designer_order_total = dos.line_items.to_a.sum{|li| (li.snapshot_price * li.quantity)}.to_f if dos.discount.to_i > 0
            hsn_code = item.design.categories.hsn_code
            return_amt = ((item.return_designer_order.line_items.size == 1 ? item.return_designer_order.total : item.get_return_amount(order_total,designer_order_total,dos.discount.to_i,order_discount,order)[0])/(order.paid_currency_rate.presence || order.currency_rate)).round(2)
            return_amt_inr = return_amt * (order.currency_rate_market_value || 1)
            if ((dos.present? && dos.state == 'buyer_returned') || item.status.present?)
              csv << [order.number,inv_number,item.shipment.try(:created_at).try(:strftime,'%d/%m/%Y'), return1.voucher, return1.id, return1.type_of_refund, return1.state, return1.reason, dos.id, hsn_code, order.currency_code, return_amt, return_amt_inr, order.pickup.try(:strftime,'%d/%m/%Y'), return1.created_at.strftime('%d/%m/%Y'), return1.completed_on.try(:strftime,'%d/%m/%Y')]
            end
          end
          return1.discount_line_items.each do |item|
            line_item = item.line_item
            hsn_code = line_item.design.categories.hsn_code
            return_amt = ((item.price * item.quantity.to_i)/(order.paid_currency_rate.presence || order.currency_rate)).round(2)
            return_amt_inr = return_amt * (order.currency_rate_market_value || 1)
            csv << [order.number,inv_number,line_item.shipment.try(:created_at).try(:strftime,'%d/%m/%Y'),return1.voucher,return1.id,return1.type_of_refund,return1.state,return1.reason,line_item.designer_order_id,hsn_code,order.currency_code,return_amt, return_amt_inr,order.pickup.try(:strftime,'%d/%m/%Y'),return1.created_at.strftime('%d/%m/%Y'),return1.completed_on.try(:strftime,'%d/%m/%Y')]
          end
        end
      end
    end
    file_name = "/returns_report/Time.now.strftime('%d/%m/%Y').csv"
    AwsOperations.create_aws_file(file_name, file)
    url = AwsOperations.get_aws_file_path(file_name)
    OrderMailer.report_mailer('Returns Data',"Returns Data hsn code wise for #{Time.now.strftime('%M %Y')} \n #{url}", {'to_email'=> [DEPARTMENT_HEAD_EMAILS['accounts'],'<EMAIL>'],'from_email_with_name'=>'<EMAIL>'}).deliver
  end

end

desc 'Monthly gst report to accounts team'
task monthly_return_report_to_accounts_sidekiq: :environment do
  MonthlyReturnReportToAccountsJob.perform_async()
end

desc 'Tailor Defect rate update'
task update_tailor_score: :environment do
  # sql = "SELECT tailors.id, tailors.name,(count(scope_events.*)::float/(count(tailoring_infos.id) - count(scope_events.*))*100) AS defect_rate FROM
  # survey_questions sq, survey_answers sa, designer_orders dso, line_items li, reviews r, orders o, tailors, tailoring_infos LEFT OUTER JOIN
  # scope_scores ON scope_scores.scoring_klass_id = tailoring_infos.id AND scope_scores.scoring_klass_type = 'TailoringInfo' LEFT OUTER JOIN
  # scope_events_scope_scores ON scope_events_scope_scores.scope_score_id = scope_scores.id LEFT OUTER JOIN scope_events ON scope_events.id =
  # scope_events_scope_scores.scope_event_id AND scope_events.scope = scope_scores.scope AND scope_events.name = 'NegativeTailoringFeedback' WHERE
  # tailoring_infos.created_at > Now() - Interval'90 Days' AND li.designer_order_id = dso.id AND dso.order_id = o.id AND li.id =
  # tailoring_infos.line_item_id AND r.design_id = li.design_id AND r.order_id = o.id AND sa.question_id = sq.id AND sa.surveyable_id = r.id AND sq.id
  # IN (9,10) AND tailors.name = tailoring_infos.tailor_name GROUP BY tailors.id"
  tailor_scores = []
  Tailor.preload(:osr_90).each do |tailor|
    tailor_scores.push([tailor.id, tailor.name, (tailor.osr_90.try(:value).to_f * 100.0).round(2)])
  end
  Tailor.import [:id, :name, :defect_rate], tailor_scores, validate: false, on_duplicate_key_update: { conflict_target: [:id], columns: [:defect_rate]}
end

def process_long_image_desing_batch(image_ids)
  Image.where(id: image_ids).each do |img|
    img.processed = true
    img.photo.reprocess_without_delay! :long
  end
end

#rake reprocess_long_image[200000]
desc 'Design image reprocess for long div variant'
task :reprocess_long_image, [:per_day_image] => :environment do |t, args|
  image_count = args[:per_day_image].to_i
  design_offset = SystemConstant.where(name: 'LONG_DESIGN_OFFSET').first
  last_img_id, count = design_offset.value, 0
  Image.select('images.id').unscoped.joins(design: :categories).where(categories: {id: LONG_DIV_CATEGORY_IDS }, images: {kind: 'master'}).where{(id > design_offset.value.to_i) & (photo_updated_at <= LONG_DIV_RUN_DATE)}.where.not(images: {photo_file_name: nil}).find_in_batches(batch_size: 1000) do |img_ids|
    img_ids = img_ids.collect(&:id)
    img_ids.each_slice(100){|ids|delay(queue: 'long_image_queue').process_long_image_desing_batch(ids)}
    last_img_id = img_ids.last
    break if (count += img_ids.size) >= image_count
  end
  design_offset.update_column(:value, last_img_id)
end

#Reprocess Instock designs Images
desc 'InStock Designs Images reprocess for long div variant'
task designs_reprocess_long_image: :environment do
  Image.joins(:design).where(kind: 'master').where(designs: {designable_type: ['Saree','SalwarKameez', 'Lehenga', 'Kurti', 'Islamic'], state: 'in_stock'}).where("images.updated_at::date >= '2011-01-01'").where("images.updated_at::date <= '2018-05-30'").find_each do |img|
    img.processed = true
    img.photo.reprocess_without_delay! :long
  end
end

desc 'inform Designer if they are in active for long time'
task :inactive_designer_alert => :environment do |t|
    VENDOR_BAN_ALERT_CONFIG = SystemConstant.get('VENDOR_BAN_ALERT_CONFIG', :to_h)

    alert_count = (VENDOR_BAN_ALERT_CONFIG['max_alert_count'] || 3).to_i
    inactive_designers_ids = Designer.where do
      (inactive_alert_count > 0) & (state_machine >> ['review', 'approved'])
    end.pluck(:id).to_set

    Designer.joins(:account, :designer_orders).
    select do [
      'designers.*',
      '(CASE WHEN accounts.current_sign_in_at > accounts.last_sign_in_at
         THEN accounts.current_sign_in_at
         ELSE accounts.last_sign_in_at
        END) as last_signed_in'
    ] end.
    where do
      (accounts.last_sign_in_at <= (VENDOR_BAN_ALERT_CONFIG['inactive_days'] || 90).to_i.days.ago) &
      (accounts.current_sign_in_at <= (VENDOR_BAN_ALERT_CONFIG['inactive_days'] || 90).to_i.days.ago) &
      (designers.state_machine >> ['review', 'approved'])
    end.
    group{[id,accounts.id]}.
    having('max(designer_orders.pickup) <= ? OR max(designer_orders.pickup) IS NULL',(VENDOR_BAN_ALERT_CONFIG['pickup_days'] || 90).to_i.days.ago).
    find_each(batch_size: 200) do |designer|
      inactive_designers_ids.delete(designer.id)
      designer.increment!(:inactive_alert_count)
      designer.inactive_alert_count >= alert_count && designer.to_on_hold
      DesignerMailer.inactive_designer_alert(designer).deliver_now
    end

    Designer.where(id: inactive_designers_ids).update_all(inactive_alert_count: 0)
end

desc 'Reject design with low rating'
task :reject_low_rated_design, [:review_count, :min_review, :day_to_run] => :environment do |t, args|
  if Date.current.wday == args[:day_to_run].to_i
    review_count, min_review = args.values_at(:review_count,:min_review).map(&:to_i)
    mail_data = Hash.new
    Design.select('designs.*, ROUND(avg(reviews.rating)::decimal ,2) as avg_rating, count(reviews.id) as review_count').
    joins(:designer,:reviews).
    preload(:designer,:images).
    where(designs: {state: 'in_stock'}, designers: {state_machine: ['review', 'approved']}, reviews: {system_user: false, approved: true}).
    group('designs.id, designers.id').
    having('count(reviews.id) >= ? and ROUND(avg(reviews.rating)::decimal ,2) <= ?',review_count, min_review).
    find_each(batch_size: 500) do |design|
      if design.reject_design
        design.update_column(:notes, "#{design.notes.to_s}. #{Time.now.strftime('%d-%m-%Y')} rejected due to low rating.")
        designer = design.designer
        mail_data[designer.id] ||= Hash.new
        mail_data[designer.id][:designer_id] ||= designer.id
        mail_data[designer.id][:name] ||= designer.name
        mail_data[designer.id][:email] ||= designer.email
        (mail_data[designer.id][:design] ||= []) << {
          id: design.id,
          title: design.title,
          thumb: design.master_image.photo.url(:thumb),
          avg_rating: design.avg_rating,
          total_rating: design.review_count
        }
      end
    end
    mail_data.each do |designer_id, value|
      DesignerMailer.send_low_rating_rejected_design_notification(value).deliver
    end
  end
end

desc 'Listing Report for designs updated'
task :listing_report => :environment do |t|
  if date_range = (Time.current.day == Time.current.beginning_of_week.day ? (7.days.ago.beginning_of_day..1.day.ago.end_of_day) : (Time.current.day == 1 ? (1.day.ago.beginning_of_month..1.day.ago.end_of_month) : false))
    dir = "tmp/reports/"
    FileUtils.mkdir_p(dir) unless File.directory?(dir)
    filepath = "#{dir}Listing_Report.csv"
    CSV.open(filepath, "wb", {:col_sep => ","}) do |csv|
      csv << %w(DesignId BrandId Brand BusinessCategory Classification Size InternalCode MRP DiscountPercent DisplayedPrice SizeChartNeeded State Reason DesignUploadTime Source BatchID SizechartAdded InventoryAdded VendorTransactionRate LastApprovedOn)
      Design.where(created_at: date_range).preload(:dynamic_size_charts,:designer, variants: :option_type_values, categories: :dynamic_size_charts).find_each(batch_size: 500) do |design|
        des_cat_parent = design.categories.first.cached_self_and_ancestors_name
        size_chart_added = design.dynamic_size_chart ? 'Yes' : 'No'
        csv << [design.id, design.designer_id, design.designer.name, design.categories.collect(&:name).join(', '), des_cat_parent, '', design.design_code, design[:price], design[:discount_percent].to_i, design.effective_price, (design.variants.present? ? 'Yes' : 'No'), design.state, (design.reject? ? design.notes : ''), design.created_at.strftime('%d/%m/%Y %H:%M'), (design.designer_batch_id.blank? ? 'Terminal' : 'Panel'), design.designer_batch_id, size_chart_added, (design.quantity > 0 ? 'Yes' : 'No'), design.designer.transaction_rate, (design.designer.last_approved_on || design.designer.last_review_on).try(:strftime, '%d/%m/%Y %H:%M')]
        design.variants.each do |variant|
          csv << [variant.id, design.designer_id, design.designer.name, design.categories.collect(&:name).join(', '), des_cat_parent, variant.option_type_values.first.try(:name), variant.design_code, variant[:price], design[:discount_percent], variant.effective_price, 'Yes', design.state, (design.reject? ? design.notes : ''), variant.created_at.strftime('%d/%m/%Y %H:%M'), (design.designer_batch_id.blank? ? 'Terminal' : 'Panel'), design.designer_batch_id, size_chart_added, (variant.quantity > 0 ? 'Yes' : 'No'), design.designer.transaction_rate, (design.designer.last_approved_on || design.designer.last_review_on).try(:strftime, '%d/%m/%Y %H:%M')]
        end
      end
      csv.flush
      VersionFailedDesign.where(created_at: date_range).preload(designer_batch: :designer).find_each do |vfd|
        csv << ['', vfd.designer_batch.designer_id, vfd.designer_batch.designer.try(:name), vfd.row[2], '', '', vfd.row[0], '', '', '', (BulkUpload.meta_data[vfd.designer_batch.version.to_sym].try(:keys).to_a.include?(:variants) ? 'Yes' : 'No'), 'Failed', vfd.error, vfd.created_at.strftime('%d/%m/%Y %H:%M'), 'Panel', vfd.designer_batch_id, 'No', '', vfd.designer_batch.designer.try(:transaction_rate), vfd.designer_batch.designer.try(:last_review_on).try(:strftime, '%d/%m/%Y %H:%M')]
      end
    end
    to_email = Account.joins(:role).where(role: {name: ['vendor_team','senior_vendor_team','listing']}).pluck(:email)
    begin
      OrderMailer.report_mailer("Listing Report from #{date_range.first.to_date} to #{date_range.last.to_date}","Following Designs were uploaded from #{date_range.first.to_date} to #{date_range.last.to_date}",{'to_email'=> to_email, 'from_email_with_name'=>'<EMAIL>'},{"Listing Report from #{date_range.first.to_date} to #{date_range.last.to_date}.csv"=>File.read(filepath)}).deliver
    rescue
      file = 'ListingReport/' + Date.today.strftime('%b%Y').to_s + 'Listing_Report.csv'
      AwsOperations.create_aws_file(file, File.read(filepath), false)
      download_url = AwsOperations.get_aws_file_path(file)
      OrderMailer.report_mailer("Listing Report from #{date_range.first.to_date} to #{date_range.last.to_date}","Following Designs were uploaded from #{date_range.first.to_date} to #{date_range.last.to_date}. Please check the file here: #{download_url}",{'to_email'=> to_email, 'from_email_with_name'=>'<EMAIL>'},{}).deliver
    end
  end
end

desc 'Credit note report to accounts team'
task :credit_note_report_monthly, [:day_to_run] => :environment do |t, args|
  if Time.current.day == args[:day_to_run].to_i
    date = Date.new(Time.current.year,(Time.current.month - 1.month).month)
    date_range = date.beginning_of_month..date.end_of_month
    DesignerOrder.generate_credit_report(date_range)
  end
end

desc 'sends pending orders mail to designers'
task send_pending_order_mail_to_designer: :environment do
  DesignerOrder.select("array_agg(DISTINCT CONCAT(orders.number, ',', (current_date - DATE(designer_orders.confirmed_at)), ',', designers.id)) as order_details, designers.name as designer_name, designers.email as designer_email").joins(:payment_order, :designer, :line_items).where('line_items.available_in_warehouse is not true').where(state: 'pending', ship_to: 'mirraw').where('designer_orders.created_at > ? and designer_orders.confirmed_at < ?',6.months.ago, THRESHOLD_PENDING_DAYS.days.ago).reorder('').group('designers.id').each do |designer_pending_details|
    DesignerMailer.mail_pending_orders_followup(designer_pending_details).deliver
  end
end

desc 'auto moved orders to ready for dispatch'
task auto_move_orders_to_ready_for_dispatch: :environment do
  if Time.current.hour % 4 == 0
    Order.mark_orders_ready_for_dispatch
  end
end

desc 'update vendor discont percent to solr'
task update_vendor_discount_percent_to_solr: :environment do
  time_range = 1.day.ago.all_day
  designer = Designer.arel_table
  designers = Designer.where(designer[:additional_discount_start_date].between(time_range).or(designer[:additional_discount_end_date].between(time_range)))
  designers.find_each(&:sunspot_index_products_without_delay)
end

desc 'send ipending report to responsible team'
task send_ipending_report_team_wise: :environment do
  start_date = (DateTime.now.beginning_of_day-2.months).strftime('%A, %d %B, %Y')
  end_date = (DateTime.now.end_of_day + 1.day).strftime('%A, %d %B, %Y')
  preload_array = [:latest_event, :delivery_nps_info, :tags, line_items: [:design, :designer, :stitching_measurements, :ipending_related_scans, tailoring_info: [:tailoring_inscan_bags]]]
  ipending_orders_hash = Hash.new
  Order.unscoped.preload(preload_array).where(state: 'sane').where('country <> ?', 'India').orders_between(start_date, end_date).order('orders.created_at DESC').find_each(batch_size: 100) do |order|
    promised_date = order.delivery_nps_info.try(:promised_delivery_date)
    latest_event = order.latest_event
    latest_note = latest_event ? "#{latest_event.event_timestamp.strftime('%m/%d')}:#{latest_event.done_by}:#{latest_event.notes}" : ''
    stitching_items = order.line_items.select{|l| l.status.blank? && ['canceled', 'vendor_canceled'].exclude?(l.designer_order.state) && l.stitching_required == 'Y'}
    all_stitching_done_status = (stitching_items.present? && (stitching_items.count{|l| l.stitching_done_on.present?} == stitching_items.size) ? 'Yes' : 'No')
    order_level_data = [order.number, order.items_received_on.try(:strftime, '%d/%m/%Y %H:%M'), latest_note, promised_date.try(:to_date), (promised_date ? ((promised_date - Time.now)/1.day).round(1) : ''), all_stitching_done_status]
    order.line_items.each do |item|
      next if item.status.present? || ['canceled', 'vendor_canceled'].include?(item.designer_order.state)
      tag_names = order.try(:tags).to_a.map(&:name).reject{|tag| tag.include?('convert-mkt')}
      process_name, team_name = (tag_names.include?('addon') ? ['Addon Tag Present', 'Support'] : item.get_item_status)
      next if all_stitching_done_status == 'No' && process_name == 'Stitching Done'
      measurement_received = (item.stitching_measurements.present? ? 'Yes' : 'No')
      next if (item.stitching_required.blank? || measurement_received == 'Yes') && process_name == 'Addon Tag Present'
      stitch_status = (item.stitching_required != 'Y' ? 'No Stitching' : (item.stitching_done_on.present? ? 'Stitching Done' : 'Stitching Pending'))
      tailor_names = item.tailoring_info.map(&:tailor_name).join(', ')
      item_data = (order_level_data + [item.design_id, item.design.designable_type, team_name, process_name, tailor_names, (item.received_on.present? ? 'Yes' : 'No'), (item.qc_done_on.present? ? 'QC Done' : 'QC Pending'), stitch_status, order.confirmed_at.try(:strftime, '%d/%m/%Y %H:%M'), order.name, order.total, tag_names.join(','), item.designer.name, item.designer_order.designer_id, order.country, item.designer.address])
      (ipending_orders_hash[team_name.to_s] ||= []) << item_data
    end
  end
  ipending_orders_hash.each do |team, data|
    file = CSV.generate do |csv|
      csv << ['Order Number', 'All Items Received On', 'Notes', 'Promised Date', 'Promised Day Remaining', 'All Stitching Done', 'Product ID', 'Designable Type', 'Team', 'Last Process', 'Tailors', 'Receive Status', 'QC Status', 'Stitching Status', 'Confirmed', 'Name', 'Total', 'Tags', 'Designer Name', 'Designer ID', 'COUNTRY', 'Vendor Location', 'Remarks', 'ETA For Clearance']
      data.each{|d| csv << d}
    end
    ShipmentMailer.mail_ipending_orders(file, team, data.size).deliver_now
  end
end

desc 'sends open inward bag report'
task mail_open_inward_bags: :environment do
  start_date, end_date = 10.days.ago.beginning_of_day, Date.today.end_of_day
  PackageManagement.send_open_inward_bags_details(start_date, end_date)
end

desc 'Daily Aramex Invoice emails'
task Aramex_Invoice_emails_for_outscanned_shipments: :environment do
  Shipment.where('shipper_id = ? AND designer_order_id is null AND out_scan_date > ? AND out_scan_date < ?',Shipper::ALL_SHIPPERS['ARAMEX'],1.day.ago.beginning_of_day,1.day.ago.end_of_day).find_in_batches(batch_size: 250) do |grp|
    grp.each_slice(50) do |shipments|
      other_data = {}
      shipments.each do |shipment|
        begin
          other_data["#{shipment.mirraw_reference}.pdf"] = open(shipment.invoice.url).read
        rescue => e
          next
        end
      end
      OrderMailer.report_mailer("Aramex Outscanned Shipments - #{1.day.ago.strftime('%d%b%Y')}",'PFA',{'to_email'=> ACCESSIBLE_EMAIL_ID['aramex_invoice_mail'],'from_email_with_name'=>'<EMAIL>','cc_email'=>'<EMAIL>'},other_data).deliver
    end
  end
end

desc 'Daily Rack Audit Queue Insertion'
task daily_rack_audit_queue_insertion: :environment do
  RackAudit.queue_daily_auditable_racks({source_type: 'system'})
end

desc 'Weekly Rack Audit Queue Insertion'
task weekly_rack_audit_queue_insertion: :environment do
  RackAudit.queue_weekly_auditable_racks({source_type: 'system'})
end

desc 'Daily Re-activate Skipped Rack Audits'
task reactivate_skipped_rack_audits: :environment do
  RackAudit.reactivate_skipped_audits
end

desc 'Generate batch order numbers'
task generate_unique_order_numbers: :environment do
  if Redis.current.scard('order_number') < 2000
    order_numbers = []
      ENV['REDIS_ORDER_BATCH_SIZE'].to_i.times do
        number = Order.generate_unique_order_number
        order_numbers.push(number) unless order_numbers.include?(number)
      end
    Redis.current.sadd('order_number', order_numbers)
  end
end

desc 'Daily update pending return state'
task update_pending_return_payment_sidekiq: :environment do
  UpdatePendingReturnPaymentJob.perform_async()
end

desc 'Daily update pending return state'
task update_pending_return_payment: :environment do
  Return.update_pending_return_payment_state
end


task :dummy_raker,[:run_task] => [:environment] do |t,args|
  args.with_defaults(run_task: 'false')
  if Time.current.day == 26 || args[:run_task] == 'true'
    report_month = Time.current.strftime('%B')
    errors_hash = {}
    time_period = 1.month.ago.beginning_of_month.beginning_of_day..1.month.ago.end_of_month.end_of_day
    sales_header  = ['Vendor ID','Vendor Gst Number','Mirraw GSTIN No','Supplier Name','Order Number', 'Sales Invoice No', 'Sales Invoice/Order Sane Date', 'Description', 'Quantity', 'HSN Code', 'SKU Code', 'Size', 'Warehouse Order', 'Sale Price', 'Addon Product Price', 'Total Sale Price','Taxable value', 'Rate of Tax', 'CGST', 'SGST', 'IGST', 'Total', 'Vendor State'].freeze
    sales_return_header  = ['Vendor ID','Vendor Gst Number','Mirraw GSTIN No','Supplier Name','Order Number', 'Sales Invoice No', 'Sales Invoice/Order Sane Date', 'Credit Note Invoice Number','Credit Note invoice Date','Description', 'Quantity', 'HSN Code', 'SKU Code', 'Size', 'Warehouse Order', 'Sale Price', 'Addon Product Price', 'Total Sale Price','Taxable value', 'Rate of Tax', 'CGST', 'SGST', 'IGST', 'Total', 'Vendor State'].freeze
    unless Rails.env.production?
      logger = Logger.new(STDOUT)
      logger.level = Logger::DEBUG
      Rails.logger = Dalli.logger = ActiveRecord::Base.logger = logger
    end
    sales_file = '/tmp/dummyReport.csv'
    CSV.open(sales_file, "wb", {:col_sep => ","}) do |csv|
      csv << sales_header
    end
    sales_return_file = '/tmp/dummyReturnReports.csv'
    CSV.open(sales_return_file, "wb", {:col_sep => "\t"}) do |csv|
      csv << sales_return_header
    end
    designer_ids = DesignerOrder.unscoped.select('designer_orders.id,designer_id').joins(:order).where(confirmed_at: time_period).where('orders.country <> ? OR designer_orders.ship_to = ?','India','mirraw').uniq.group_by(&:designer_id)
    designer_ids.each_slice(50).each do |des_id|
      all_orders = LineItem.unscoped.where(designer_order_id: des_id.collect(&:last).flatten.compact.collect(&:id)).joins(:designer_order,:designer).preload(:payment_order, rack_lists_warehouse_line_items: [item: :warehouse_order], design: [:designer,:categories,property_values: :property]).select('designer_orders.invoice_number,designer_orders.confirmed_at,designer_orders.pickup,designer_orders.created_at,designer_orders.designer_id,designer_orders.id,designer_orders.total,designer_orders.payout,designer_orders.transaction_rate,designer_orders.discount,designer_orders.state,designers.gst_no,designers.business_name,designers.name as designer_name,designers.state as designer_state,designers.business_state as designer_business_state, designers.email as designer_email, line_items.design_id,line_items.variant_id,line_items.quantity,line_items.snapshot_price,line_items.vendor_selling_price, line_items.available_in_warehouse, line_items.purchase_hsn_code,line_items.purchase_gst_rate,line_items.status, line_items.return_image_file_name, line_items.designer_order_id,line_items.id,designer_orders.order_id').group_by(&:designer_id)
      reverse_commissions = ReverseCommission.joins(:order).where('lower(orders.country) <> ? ',  'india').where(created_at: '2017-09-01'..1.month.ago.end_of_month.end_of_day,invoice_id: nil,designer_id: des_id.collect(&:first)).preload(element: :payment_order).group_by(&:designer_id)
      purchase_reports = []
      all_orders.each do |designer_id,all_items|
        sales_report,sales_return_report= [],[]
        begin
          supplier_name = (all_items.first.business_name.presence || all_items.first.designer_name)
          gst_no = all_items.first.try(:design).try(:designer).try(:gst_no)
          file=CSV.generate(headers: true) do |csv|
            all_items.each do |item|
              #next if item.status.present?
              hsn_code,gst_rate = item.purchase_hsn_code,item.purchase_gst_rate
              name = item.design.categories.first.name.gsub('-', ' ').camelize
              sku_code = 'sku: ' + item.design.design_code if item.design.design_code.present?
              item_size = item.variant_id.present? ? item.variant.option_type_values.try(:last).try(:p_name).to_s : ''
              is_warehouse_order = item.sor_available? ? 'YES' : 'NO'
              item_price = item.sor_available? ? item.vendor_selling_price : (((item.vendor_selling_price.to_f > 0 ? item.vendor_selling_price.to_f : item.snapshot_price(RETURN_NORMAL))*(100-item.transaction_rate.to_f)/100.0).round(2))
              addon_price = (item.vendor_addon_items.to_a.sum{|addon| addon.snapshot_price(RETURN_NORMAL)}*(100-item.transaction_rate.to_f)/100.0).round(2)
              sales_price = item_price + addon_price
              hsn_code,gst_rate = item.find_hscode_gst(sales_price,false) if hsn_code.blank? || gst_rate.blank?
              taxable_value = (sales_price/(1+gst_rate.to_f/100))
              cgst_sgst = (item.designer_business_state || item.designer_state).try(:upcase) == SHIPPER_STATE.upcase
              total_tax = ((sales_price - taxable_value)*item.quantity).round(2)

              sales_report << [designer_id, gst_no, MIRRAW_GST_NUMBER, supplier_name, item.payment_order.number, item.invoice_number, (item.confirmed_at || item.created_at).to_date.strftime('%d/%m/%Y'), name, item.quantity, hsn_code, sku_code, item_size, is_warehouse_order, item_price, addon_price,sales_price, (item.gst_no.present? ? (taxable_value * item.quantity).round(2) : '-'), gst_rate.to_f, cgst_sgst ? (total_tax/2).round(2) : '-', cgst_sgst ? (total_tax/2).round(2) : '-', cgst_sgst ? '-' : total_tax, ((taxable_value*item.quantity) + total_tax).round(2),(item.designer_business_state || item.designer_state)]
            end
          end
          #generate vendor report url
          filename  = 'DummySalesReport/' + designer_id.to_s + '/' + Date.today.strftime('%b%Y').to_s + '.csv'
          file_s = CSV.generate(headers: true) do |csv|
            csv << sales_header
            sales_report.each {|row| csv << row}
          end
          AwsOperations.create_aws_file(filename, file_s, false)
          sales_url = AwsOperations.get_aws_file_path(filename)

          #combine url for accounts team
          CSV.open(sales_file, "a", {:col_sep => "\t"}) do |csv|
            sales_report.each {|row| csv << row}
          end

          #generate vendor report url
          filename  = 'DummySalesReturnReport/' + designer_id.to_s + '/' + Date.today.strftime('%b%Y').to_s + '.csv'
          file_sr = CSV.generate(headers: true) do |csv|
            csv << sales_return_header
            sales_return_report.each {|row| csv << row}
          end
          AwsOperations.create_aws_file(filename, file_sr, false)
          sales_return_url = AwsOperations.get_aws_file_path(filename)

          #combine url for accounts team
          CSV.open(sales_return_file, "a", {:col_sep => "\t"}) do |csv|
            sales_return_report.each {|row| csv << row}
          end

        rescue => e
          errors_hash[designer_id] = "#{e.message}#{e.backtrace}"
        end
      end
    end

    filename  = 'dummyInternationalSalesReports/' + Date.today.strftime('%b%Y').to_s + '.csv'
    AwsOperations.create_aws_file(filename, File.open(sales_file))
    sales_download = AwsOperations.get_aws_file_path(filename)

    filename  = 'dummyInternationalSalesReturnReports/' + Date.today.strftime('%b%Y').to_s + '.csv'
    AwsOperations.create_aws_file(filename, File.open(sales_return_file))
    sales_return_download = AwsOperations.get_aws_file_path(filename)

    #Mail to Account Team
    OrderMailer.report_mailer("Dummy PurchaseReport - #{Date.today.strftime('%b%Y')}","Dummy Combined Sales Report - #{sales_download} <br> Dummy Combined Sales Return Report - #{sales_return_download}",{'to_email'=> '<EMAIL>','from_email_with_name'=>'<EMAIL>'},{}).deliver
    if errors_hash.present?
      ExceptionNotify.sidekiq_delay.notify_exceptions( "Dummy Purchase Report Error","Dummy PurchaseReport could not be generated for following designers.", {params: errors_hash})
    end
  end
end

desc "This task syncs the clickpost serviceable pincodes for all courier partners on clickpost with the live db"
task :sync_clickpost_mirraw_pincodes,[:run_task,:courier_partner_id] => [:environment] do |t,args|
    args.with_defaults(run_task: 'false',courier_partner_id: nil)
    puts args[:run_task]
    logger = Logger.new(STDOUT)
    logger.level = Logger::DEBUG
    Rails.logger = Dalli.logger = ActiveRecord::Base.logger = logger
    if args[:run_task] == 'true'
        #setting up request default url
        cp_id = args[:courier_partner_id].to_i
        clickpost_config = Mirraw::Application.config.click_post
        get_bulk_serviceable_pincodes_url = clickpost_config[:bulk_serviceable]
        username = clickpost_config[:username]
        key = clickpost_config[:key]
        import_common = Array.new
        import_pickup = Array.new
        import_drop = Array.new
        filter_code = Array.new
        #data hash :- key -> cp id of courier partners , value -> [account code (as per cp panel),shipper_id from live db]
        courier_data_hash = {
            6 => ["Xpressbees Domestic",30],
            9 => ["Shadowfax Domestic",24],
            41 => ["Xpressbees Domestic Reverse",41],
            # 55 => ["Ekart",43], #inactive
            3 => ["EcomExpress Forward",6],
            2 => ["Aramex",11],
            49 => ["DHL",22],
            1 => ["FedEx",2],
            # 11 => ["SHADOWFAX"], #Shadowfax Reverse
            14 => ["WOWExpress",42],
            # 16 => ["Shipdelight Domestic",16],#inactive
            4 => ["Delhivery Domestic Forward",9],
            5 => ["Bluedart%20-%20%20Domestic",1],
            25 => ["Domestic RVP",33], #Delhivery Reverse
            # 35 => ["Ship Delight",16],
            42 => ["Rapid Domestic",27],
            72 => ["KerryIndev",45],
            114 => ["Atlantic",28]}
        #courier partner id and acc code either as raker arg or fetch all from list
        #if courier partner id is given in raker
        if cp_id != 0
            acc_code = courier_data_hash[cp_id][0]
            shipper_id = courier_data_hash[cp_id][1]
            request_url = get_bulk_serviceable_pincodes_url + "?username=#{username}&key=#{key}&courier_partner_id=#{cp_id}&account_code=#{acc_code}"
            response = HTTParty.get(request_url.to_str, :headers => {'Content-Type' => 'application/json'})
            data = JSON.parse(response.body)
            if !(data['result']['pickup_pincodes']).empty? && !(data['result']['drop_pincodes']).empty?
                pickup_pincodes = data['result']['pickup_pincodes']['PREPAID']
                if (shipper_id == 41 || shipper_id == 33)
                  drop_pincodes = data['result']['drop_pincodes']['PREPAID']
                else
                  drop_pincodes = data['result']['drop_pincodes']['PREPAID'] - data['result']['drop_pincodes']['COD'] == [] ? data['result']['drop_pincodes']['PREPAID'] : []
                end
                common = drop_pincodes & pickup_pincodes
                diff_pickup = (pickup_pincodes - common)
                diff_drop = drop_pincodes.empty? ? [] : (drop_pincodes - common)
                #assigning cod = y for pickup = y and delivery = y for common pincodes between pickup and drop
                common.each do |pincode|
                    puts "Common ----------------------------------------------------"
                    shipper_name = Shipper::ALL_SHIPPERS.key(shipper_id)
                    object = Courier.where(shipper_id: shipper_id, pincode: pincode).first_or_initialize
                    object.assign_attributes(
                        name: shipper_name,
                        oda: false,
                        pickup: true,
                        delivery: true
                    )
                    object.cod = ((COD_ACTIVE_SHIPPERS.include?(shipper_name) && ([object.pickup, object.delivery] & [true]).present?) ? 'Y' : 'N')
                    import_common << object
                end
                #insert new rows if not present already
                import_common.each do |common|
                  if (Courier.where(pincode: common['pincode'], shipper_id: shipper_id).present? == false)
                      filter_code << common
                  end
                end
                if !filter_code.empty?
                  Courier.import filter_code, validate: false, raise_error: true
                  filter_code.clear
                end
                #bulk import
                Courier.import import_common.uniq, validate: false,
                on_duplicate_key_update: {conflict_target: [:id], columns: [:pickup, :delivery, :cod]}
                #assigning pickup = y for diff in pickup
                diff_pickup.each do |pickup|
                    puts "Pickup ----------------------------------------------------"
                    shipper_name = Shipper::ALL_SHIPPERS.key(shipper_id)
                    object = Courier.where(shipper_id: shipper_id, pincode: pickup).first_or_initialize
                    object.assign_attributes(
                        name: shipper_name,
                        oda: false,
                        pickup: true,
                        delivery: false,
                        cod: 'N'
                    )
                    import_pickup << object
                end
                import_pickup.each do |pickup|
                  if (Courier.where(pincode: pickup['pincode'], shipper_id: shipper_id).present? == false)
                      filter_code << pickup
                  end
                end
                if !filter_code.empty?
                  Courier.import filter_code, validate: false, raise_error: true
                  filter_code.clear
                end
                Courier.import import_pickup.uniq, validate: false,
                on_duplicate_key_update: {conflict_target: [:id], columns: [:pickup, :delivery, :cod]}
                if !diff_drop.empty?
                    diff_drop.each do |drop|
                        puts "Drop ----------------------------------------------------"
                        shipper_name = Shipper::ALL_SHIPPERS.key(shipper_id)
                        object = Courier.where(shipper_id: shipper_id, pincode: drop).first_or_initialize
                        object.assign_attributes(
                            name: shipper_name,
                            oda: false,
                            pickup: false,
                            delivery: true,
                            cod: 'N'
                        )
                        import_drop << object
                    end
                    import_drop.each do |drop|
                      if (Courier.where(pincode: drop['pincode'], shipper_id: shipper_id).present? == false)
                          filter_code << drop
                      end
                    end
                    if !filter_code.empty?
                      Courier.import filter_code, validate: false, raise_error: true
                      filter_code.clear
                    end
                    Courier.import import_drop.uniq, validate: false,
                    on_duplicate_key_update: {conflict_target: [:id], columns: [:pickup, :delivery, :cod]}
                end
            end
        #if courier_partner_id is not given in raker
        else
            courier_data_hash.each do |clickpost_id,value|
                request_url = get_bulk_serviceable_pincodes_url + "?username=#{username}&key=#{key}&courier_partner_id=#{clickpost_id}&account_code=#{value[0]}"
                response = HTTParty.get(request_url.to_str, :headers => {'Content-Type' => 'application/json'})
                data = JSON.parse(response.body)
                if !(data['result']['pickup_pincodes']).empty? && !(data['result']['drop_pincodes']).empty?
                    pickup_pincodes = data['result']['pickup_pincodes']['PREPAID']
                    if (shipper_id == 41 || shipper_id == 33)
                      drop_pincodes = data['result']['drop_pincodes']['PREPAID']
                    else
                      drop_pincodes = data['result']['drop_pincodes']['PREPAID'] - data['result']['drop_pincodes']['COD'] == [] ? data['result']['drop_pincodes']['PREPAID'] : []
                    end
                    common = drop_pincodes & pickup_pincodes
                    diff_pickup = (pickup_pincodes - common)
                    diff_drop = drop_pincodes.empty? ? [] : (drop_pincodes - common)
                    shipper_id = value[1]
                    common.each do |pincode|
                        shipper_name = Shipper::ALL_SHIPPERS.key(shipper_id)
                        object = Courier.where(shipper_id: shipper_id, pincode: pincode).first_or_initialize
                        object.assign_attributes(
                            name: shipper_name,
                            oda: false,
                            pickup: true,
                            delivery: true
                        )
                        object.cod = ((COD_ACTIVE_SHIPPERS.include?(shipper_name) && ([object.pickup, object.delivery] & [true]).present?) ? 'Y' : 'N')
                        import_common << object
                    end
                    import_common.each do |common|
                      if (Courier.where(pincode: common['pincode'], shipper_id: shipper_id).present? == false)
                          filter_code << common
                      end
                    end
                    if !filter_code.empty?
                      Courier.import filter_code, validate: false, raise_error: true
                      filter_code.clear
                    end
                    Courier.import import_common.uniq, validate: false,
                    on_duplicate_key_update: {conflict_target: [:id], columns: [:pickup, :delivery, :cod]}
                    diff_pickup.each do |pickup|
                        shipper_name = Shipper::ALL_SHIPPERS.key(shipper_id)
                        object = Courier.where(shipper_id: shipper_id, pincode: pickup).first_or_initialize
                        object.assign_attributes(
                            name: shipper_name,
                            oda: false,
                            pickup: true,
                            delivery: false,
                            cod: 'N'
                        )
                        import_pickup << object
                    end
                    import_pickup.each do |pickup|
                      if (Courier.where(pincode: pickup['pincode'], shipper_id: shipper_id).present? == false)
                          filter_code << pickup
                      end
                    end
                    Courier.import filter_code, validate: false, raise_error: true
                    filter_code.clear
                    Courier.import import_pickup.uniq, validate: false,
                    on_duplicate_key_update: {conflict_target: [:id], columns: [:pickup, :delivery, :cod]}
                    if !diff_drop.empty?
                        diff_drop.each do |drop|
                            shipper_name = Shipper::ALL_SHIPPERS.key(shipper_id)
                            object = Courier.where(shipper_id: shipper_id, pincode: drop).first_or_initialize
                            object.assign_attributes(
                                name: shipper_name,
                                oda: false,
                                pickup: false,
                                delivery: true,
                                cod: 'N'
                            )
                            import_drop << object
                        end
                        import_drop.each do |drop|
                          if (Courier.where(pincode: drop['pincode'], shipper_id: shipper_id).present? == false)
                              filter_code << drop
                          end
                        end
                        if !filter_code.empty?
                          Courier.import filter_code, validate: false, raise_error: true
                          filter_code.clear
                        end
                        Courier.import import_drop.uniq, validate: false,
                        on_duplicate_key_update: {conflict_target: [:id], columns: [:pickup, :delivery, :cod]}
                    end
                end
            end
        end
    end
end

desc 'test cache clear'
task single_key_cache_clear: :environment do
  puts "start\n"
  cmd = "redis-cli -h #{ ENV["REDIS_HOST"] } -p #{ ENV["REDIS_PORT"] } -a #{ ENV["REDIS_PASSWORD"] } --scan --pattern nidhi* | xargs -d '\n' redis-cli -h #{ ENV["REDIS_HOST"] } -p #{ ENV["REDIS_PORT"] } -a #{ ENV["REDIS_PASSWORD"] } UNLINK"

  didItWork = system(cmd)

  puts didItWork
  puts "end"
end


#require 'tasks/delayed_tasks'