class XindusApi 
  
  def login_api
    set_credentials
    Rails.cache.fetch('xindus_auth_token', expires_in: 90.days){
    url = @credentials["base_url"] + "xos/api/auth/login"
    phone_number = @credentials["phone_number"]
    phone_prefix = @credentials["phone_prefix"]
    password = @credentials["password"]
    username = @credentials["email"]
    body = {'username' => username , 'password' => password }
    headers = {'Content-Type' => 'application/json'}
    response = HTTParty.post(url, :body => body.to_json, :headers => headers)
    response = handle_exceptions(response, "Login API Error:")
    return response["data"].first["access_token"]
    }
  end 

  def create_shipment_api(request_payload)
    response = post_api(request_payload, "xos/api/partner/shipment", "Create Shipment Error:")
    return response["data"].first["scancode"]
  end

  def payment_api(awb_number)
    body = {"scancode" => awb_number , "is_coins_used" => "true"}
    response = post_api(body , "xos/api/partner/shipment/pay", "Payment API Error:")   
  end

  def shipping_label_api(awb_number) 
    type = '4X6'
    body = {"awb" => awb_number , "type" => type}
    set_credentials
    url = URI(@credentials['base_url'] + "xos/api/partner/shipment/print-label?scancode=#{awb_number}&type=#{type}")
    auth_token = login_api
    https = Net::HTTP.new(url.host, url.port)
    https.use_ssl = true
    request = Net::HTTP::Get.new(url)
    request["Authorization"] = "Bearer #{auth_token}"
    request["Content-Type"] = "application/json"
    response = https.request(request)
    parsed_response = JSON.parse(response.body)
    return parsed_response['data']
  end

  def shipment_invoice_api(awb_number)
    body = {"scancode" => awb_number, "confirm" => true}
    response = post_api(body, "xos/api/partner/shipment/invoice"  , "Get Invoice Error:")
    return response['data'].first['data']
  end

  def post_api(request_payload , end_point , method_message)
    set_credentials
    auth_token = login_api
    url = @credentials["base_url"] + end_point
    headers = {
      'Authorization' => "Bearer #{auth_token}",
      'Content-Type' => 'application/json'
    }
    response = HTTParty.post(url, :body => request_payload.to_json, :headers => headers)
    response = handle_exceptions(response, method_message) 
  end 

  def tracking_api(awb_number)
    set_credentials
    auth_token = login_api
    url = URI(@credentials["base_url"] + "xos/api/partner/shipment/tracking?scancode=#{awb_number}")
    https = Net::HTTP.new(url.host, url.port)
    https.use_ssl = true
    request = Net::HTTP::Get.new(url)
    request["Authorization"] = "Bearer #{auth_token}"
    request["Content-Type"] = "application/json"
    response = https.request(request)
    parsed_response = JSON.parse(response.body)
    return parsed_response
  end

  def handle_exceptions(response, message)
    return response if !response.has_key?("errors") && response['status'] != 'error' 
    raise message + (response['errorDescription'] || "API Internal Server error")
  end

  def notify(klass, message, req, res, error)
    ExceptionNotifier.notify_exception(
      Exception.new("Xindus Shipment Error- #{message}"),
      data: {payload: req, response: res, error: error.inspect}
    )
  end 

  private 

  def set_credentials
    env = (Rails.env.production? || Rails.env.admin?) ? 'production' : 'stagging'
    @credentials = YAML.load_file("#{Rails.root}/config/xindus.yml")[env]
  end 


end