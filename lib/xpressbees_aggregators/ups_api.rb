module XpressbeesAggregators
    class UpsApi
        
        def generate_token
            url = "#{ENV['XPRESS_BEES_API_URL']}franchise/api/v1/access/token"
            username = "#{ENV['XPRESS_BEES_API_USERNAME']}"
            password = "#{ENV['XPRESS_BEES_API_PASSWORD']}"
            body = {username: username , password: password}
            headers = {'Content-Type' => 'application/json'}
            response = HTTParty.post(url, body: body.to_json, headers: headers)
            if response["accessToken"]
              Rails.cache.fetch('xpressbees_ups_auth_token', expires_in: 2.hours) do
                response["accessToken"]
              end
            end
        end
        
        def shipper_hash
            company_name, shipping_telephone, shipping_address_1, shipping_address_2, shipping_city, shipping_pincode, shipping_state, shipping_state_code = @order.get_warehouse_shipping_address
            shipping_address_1 = shipping_address_1[0..20]
            { 
              firstName: company_name,
              lastName: "",
              typeOfKyc: "privateLimitedCompany",
              email: "<EMAIL>",
              companyName: company_name,
              addressLine1: shipping_address_1,
              addressLine2: shipping_address_2,
              city: shipping_city, 
              state: shipping_state_code, 
              pincode: shipping_pincode, 
              country: "IN", 
              phone: shipping_telephone
            }.to_json
        end
        
        def reciever_hash
            country_code = Country.find_by_name(@order.country).try(:iso3166_alpha2) || @order.country_code
            { 
              firstName:  @order.name,
              lastName: "",
              email: @order.email,
              addressLine1: @order.street,
              addressLine2: "",
              city: @order.city, 
              state: @order.state_code,
              pincode: @order.pincode,
              country: country_code, 
              phone: @order.phone
            }.to_json
        end
        
        def product_details
            commodities = []
            @invoice_items.each_with_index do |item,index|
              difference = 8 - item[:hsn_code].length
              diff = "0" * difference
              hsn_code = "#{diff}#{item[:hsn_code]}"
              commodity =   {
                  'productName': item[:name], 
                  'productSku': "",
                  'productQty': item[:quantity].to_s,
                  'productPrice': item[:item_discounted_price].to_s,
                  'productHsnCode': hsn_code,
              }
              commodities << commodity
            end
            commodities.to_json
          end
        
        def create_shipment(request_payload, order, invoice_items)
            @order = order
            @invoice_items = invoice_items
            request_payload << ['consignorInfo', shipper_hash]
            request_payload << ['consigneeInfo', reciever_hash]
            request_payload << ['products', product_details.to_s]
            request_payload_hash = Hash[request_payload]
            response = post_api_net_http(request_payload_hash)
            json_data = JSON.parse(response.read_body)
            return awbList = [json_data["domesticAWB"], json_data["partnerAWB"]]
        end
        
        def download_label_zip(awb_list)
            request_payoad = JSON.dump({
              "awbList": awb_list
            })
            response, status_code = post_api(request_payoad, "franchise/api/v1/shipment/label")
            return response, status_code
        end
        
        def post_api_net_http(request_payload)
            auth_token = generate_token
            url = URI("#{ENV['XPRESS_BEES_API_URL']}" +  "franchise/api/v1/shipment")
            https = Net::HTTP.new(url.host, url.port)
            https.use_ssl = true  
            request = Net::HTTP::Post.new(url)
            request["authorization"] = "Bearer #{auth_token}"
            request.set_form request_payload, 'multipart/form-data'
            response = https.request(request)
        end
        
        def post_api(request_payload , end_point)
            auth_token = generate_token
            url = URI("#{ENV['XPRESS_BEES_API_URL']}#{end_point}")
            https = Net::HTTP.new(url.host, url.port)
            https.use_ssl = true
            request = Net::HTTP::Post.new(url)
            request["Authorization"] = "Bearer #{auth_token}"
            request["Content-Type"] = "application/json"
            request.body = request_payload
            response = https.request(request)
            [response, response.code]
        end
    end
end