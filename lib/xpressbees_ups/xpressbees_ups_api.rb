class XpressbeesUpsApi
    
    def generate_token
        set_credentials
        Rails.cache.fetch('xpressbees_ups_auth_token', expires_in: 2.hours){
            url = "https://preprodfranchise.xbees.in/" + "franchise/api/v1/access/token"
            username = "<EMAIL>"
            password = "Doh@xb123456789"
            body = {username: username , password: password}
            headers = {'Content-Type' => 'application/json'}
            response = HTTParty.post(url, body: body.to_json, headers: headers)
            return response["accessToken"]
        }
    end
    
    def create_shipment(request_payload)
        response = post_api(request_payload, "franchise/api/v1/shipment")
        return response
    end
    
    def post_api(request_payload , end_point , method_message)
        set_credentials
        auth_token = generate_token
        url = @credentials["base_url"] + end_point
        headers = {'Content-Type' => 'application/json' , 'at' => "Bearer #{auth_token}"}
        response = HTTParty.post(url, :body => request_payload.to_json, :headers => headers)
      end
    
    def set_credentials
        env = (Rails.env.production? || Rails.env.admin?) ? 'production' : 'staging'
        @credentials = YAML.load_file("#{Rails.root}/config/xpressbees_ups.yml")[env]
    end 
    
    
end
