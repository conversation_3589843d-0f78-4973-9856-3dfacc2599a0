# FactoryGirl.define do
#   factory :menu do
#     sequence(:title) { |n| "Menu #{n}" }
#     sequence(:link) { |n| "/menu-#{n}" }
#     sequence(:position) { |n| n }
#     hide false
#     country nil
#     app_source nil
#     app_name nil

#     trait :hidden do
#       hide true
#     end

#     trait :mirraw do
#       app_name nil
#     end

#     trait :luxe do
#       app_name 'luxe'
#     end

#     trait :with_country do
#       country 'IN'
#     end

#     # Specific menu types
#     trait :sarees do
#       title 'Sarees'
#       link '/sarees'
#     end

#     trait :lehengas do
#       title 'Lehengas'
#       link '/lehengas'
#     end

#     trait :kurtis do
#       title 'Kurtis'
#       link '/kurtis'
#     end
#   end
# end
